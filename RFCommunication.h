#ifndef RFCOMMUNICATION_H_
#define RFCOMMUNICATION_H_

#include "Devicetype.h"

typedef enum {eRFNone,eRFListen0toM,eRFListenMto0,eRFListenXtoM,eRFListenMtoX,eRFListenXto0,eRFListen0toX,eRFMeasurement,eRFReturnResults0,eRFReturnResultsX,eRFReadSamples0,eRFReadSamplesX,eRFArraySection,eRFXMeasureXto0  } RxPacketType;

RxPacketType RFCommunication_CheckReceivedPacket();
void RFCommunication_TXMeasurements(uint16_t DistanceMM, uint16_t BatterymV,uint16_t TemperatureDeciDegrees);
void RFCommunication_TXArraySection(uint16_t* Array, uint16_t Section );
void RFCommunication_TXListenToSound(DeviceTypes Source, DeviceTypes Target);
void RFCommunication_TXReturnResults(DeviceTypes From);
void RFCommunication_TXReadSamples(DeviceTypes From);
void RFCommunication_TXAskXToMeasureXto0(void);
void RFCommunication_TXChar(char c);

uint16_t RFCommunication_GetDistanceFromReceivedMeasurementPacket();
uint16_t RFCommunication_GetBatteryFromReceivedMeasurementPacket();
uint8_t  RFCommunication_GetSectionReceivedArraySectionPacket();
void     RFCommunication_GetDataReceivedArraySectionPacket(uint16_t *Buffer);
void     RFCommunication_CheckForReceivedMeasurement(void);
char     RFCommunication_GetDeviceIdentifierFromReceivedPacket();
void     RFCommunication_init();

#endif /* RFCOMMUNICATION_H_ */
