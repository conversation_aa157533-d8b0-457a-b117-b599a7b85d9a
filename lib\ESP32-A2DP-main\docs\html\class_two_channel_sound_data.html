<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: TwoChannelSoundData Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="class_two_channel_sound_data-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">TwoChannelSoundData Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Data is provided in two channels of int16 data: so len is in 4 byte entries (int16 + int16)  
 <a href="class_two_channel_sound_data.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_sound_data_8h_source.html">SoundData.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for TwoChannelSoundData:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_two_channel_sound_data.png" usemap="#TwoChannelSoundData_map" alt=""/>
  <map id="TwoChannelSoundData_map" name="TwoChannelSoundData_map">
<area href="class_sound_data.html" title="Sound data as byte stream. We support TwoChannelSoundData (uint16_t + uint16_t) and OneChannelSoundDa..." alt="SoundData" shape="rect" coords="0,0,144,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a76443112ac16d5744a96dc2fa2a46ff3"><td class="memItemLeft" align="right" valign="top"><a id="a76443112ac16d5744a96dc2fa2a46ff3"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>TwoChannelSoundData</b> (bool loop=false)</td></tr>
<tr class="separator:a76443112ac16d5744a96dc2fa2a46ff3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae66d339668c00d667a44dcafe6421810"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_two_channel_sound_data.html#ae66d339668c00d667a44dcafe6421810">TwoChannelSoundData</a> (Frame *data, int32_t len, bool loop=false)</td></tr>
<tr class="separator:ae66d339668c00d667a44dcafe6421810"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a95b5433f0d703ebd91e89f158cc5f181"><td class="memItemLeft" align="right" valign="top"><a id="a95b5433f0d703ebd91e89f158cc5f181"></a>
int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>count</b> ()</td></tr>
<tr class="separator:a95b5433f0d703ebd91e89f158cc5f181"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac79933ed3379cf5ef58d5675aa4bf12e"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_sound_data.html#ac79933ed3379cf5ef58d5675aa4bf12e">doLoop</a> ()</td></tr>
<tr class="separator:ac79933ed3379cf5ef58d5675aa4bf12e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a39d70aca39dbeca3d20676635b7615"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_two_channel_sound_data.html#a0a39d70aca39dbeca3d20676635b7615">get2ChannelData</a> (int32_t pos, int32_t len, uint8_t *data)</td></tr>
<tr class="separator:a0a39d70aca39dbeca3d20676635b7615"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa63c7361d68a496e01d198f29bf4fbbc"><td class="memItemLeft" align="right" valign="top"><a id="aa63c7361d68a496e01d198f29bf4fbbc"></a>
int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>getData</b> (int32_t pos, Frame &amp;channels)</td></tr>
<tr class="separator:aa63c7361d68a496e01d198f29bf4fbbc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba4f4df71c470c20f80900b406ae509a"><td class="memItemLeft" align="right" valign="top"><a id="aba4f4df71c470c20f80900b406ae509a"></a>
int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>getData</b> (int32_t pos, int32_t len, Frame *data)</td></tr>
<tr class="separator:aba4f4df71c470c20f80900b406ae509a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00dc7b9c10596dfadb911f3213c9a588"><td class="memItemLeft" align="right" valign="top"><a id="a00dc7b9c10596dfadb911f3213c9a588"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>setData</b> (Frame *data, int32_t len)</td></tr>
<tr class="separator:a00dc7b9c10596dfadb911f3213c9a588"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa56a59a0370a96695473e71fefc8fa3b"><td class="memItemLeft" align="right" valign="top"><a id="aa56a59a0370a96695473e71fefc8fa3b"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>setDataRaw</b> (uint8_t *data, int32_t len)</td></tr>
<tr class="separator:aa56a59a0370a96695473e71fefc8fa3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed157f28a14d6fd8ef1571db60afd003"><td class="memItemLeft" align="right" valign="top"><a id="aed157f28a14d6fd8ef1571db60afd003"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>setLoop</b> (bool loop)</td></tr>
<tr class="separator:aed157f28a14d6fd8ef1571db60afd003"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Data is provided in two channels of int16 data: so len is in 4 byte entries (int16 + int16) </p>
<dl class="section author"><dt>Author</dt><dd>Phil Schatzmann </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Apache License Version 2 </dd></dl>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="ae66d339668c00d667a44dcafe6421810"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae66d339668c00d667a44dcafe6421810">&#9670;&nbsp;</a></span>TwoChannelSoundData()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">TwoChannelSoundData::TwoChannelSoundData </td>
          <td>(</td>
          <td class="paramtype">Frame *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>len</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>loop</em> = <code>false</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Constructor for Data containing 2 channels </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="ac79933ed3379cf5ef58d5675aa4bf12e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac79933ed3379cf5ef58d5675aa4bf12e">&#9670;&nbsp;</a></span>doLoop()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool SoundData::doLoop </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Automatic restart playing on end </p>

</div>
</div>
<a id="a0a39d70aca39dbeca3d20676635b7615"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0a39d70aca39dbeca3d20676635b7615">&#9670;&nbsp;</a></span>get2ChannelData()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int32_t TwoChannelSoundData::get2ChannelData </td>
          <td>(</td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>pos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>len</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *&#160;</td>
          <td class="paramname"><em>data</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>pos and len in bytes </p>

<p>Implements <a class="el" href="class_sound_data.html">SoundData</a>.</p>

</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>src/<a class="el" href="_sound_data_8h_source.html">SoundData.h</a></li>
<li>src/SoundData.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
