#pragma once
#include <cstdint>

constexpr uint32_t SAMPLE_RATE = 16000;  // 16 kHz - optimiert für 1-3.5 kHz Chirp
constexpr float CHIRP_SEC = 0.050f;      // 50 ms - bessere Energieverteilung für lange Wege
constexpr uint16_t CHIRP_DURATION_MS = static_cast<uint16_t>(CHIRP_SEC * 1000);
constexpr uint16_t RECORD_DURATION_MS = 50;

// Chirp parameters for sine sweep - optimiert für Reichweite
constexpr float F_START = 800.0f;        // 800 Hz - gute Reichwei<PERSON>, weniger Raumdämpfung
constexpr float F_END = 3500.0f;         // 3500 Hz - noch gut hörbar, geringe Luftdämpfung
constexpr uint32_t CHIRP_TOTAL = static_cast<uint32_t>(SAMPLE_RATE * CHIRP_SEC);  // Total samples = 2400
constexpr float CHIRP_K = (F_END - F_START) / CHIRP_TOTAL;  // Phase slope

constexpr uint32_t calcSamples(uint32_t ms) {
    return (SAMPLE_RATE * ms) / 1000;
}
