<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: BluetoothOutput Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="class_bluetooth_output-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">BluetoothOutput Class Reference<span class="mlabels"><span class="mlabel">abstract</span></span><div class="ingroups"><a class="el" href="group__a2dp.html">ESP32 A2DP</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Abstract Output Class.  
 <a href="class_bluetooth_output.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_bluetooth_output_8h_source.html">BluetoothOutput.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for BluetoothOutput:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_bluetooth_output.png" usemap="#BluetoothOutput_map" alt=""/>
  <map id="BluetoothOutput_map" name="BluetoothOutput_map">
<area href="class_bluetooth_output_audio_tools.html" title="Output Class using AudioTools library: https://github.com/pschatzmann/arduino-audio-tools." alt="BluetoothOutputAudioTools" shape="rect" coords="0,56,164,80"/>
<area href="class_bluetooth_output_default.html" title="Default Output Class providing both the Legacy I2S and the AudioTools I2S functionality." alt="BluetoothOutputDefault" shape="rect" coords="174,56,338,80"/>
<area href="class_bluetooth_output_legacy.html" title="Legacy I2S Output Class." alt="BluetoothOutputLegacy" shape="rect" coords="348,56,512,80"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a2661a162889c796d8579b8b7d2276ad5"><td class="memItemLeft" align="right" valign="top"><a id="a2661a162889c796d8579b8b7d2276ad5"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>begin</b> ()=0</td></tr>
<tr class="separator:a2661a162889c796d8579b8b7d2276ad5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af55dcb4c80917dd19ba30468ad3d32e0"><td class="memItemLeft" align="right" valign="top"><a id="af55dcb4c80917dd19ba30468ad3d32e0"></a>
virtual size_t&#160;</td><td class="memItemRight" valign="bottom"><b>write</b> (const uint8_t *data, size_t len)=0</td></tr>
<tr class="separator:af55dcb4c80917dd19ba30468ad3d32e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa9cb893f9b493f79b4776a250b63f7ab"><td class="memItemLeft" align="right" valign="top"><a id="aa9cb893f9b493f79b4776a250b63f7ab"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>end</b> ()=0</td></tr>
<tr class="separator:aa9cb893f9b493f79b4776a250b63f7ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afb9449fbe5740d691b6323ae018e609a"><td class="memItemLeft" align="right" valign="top"><a id="afb9449fbe5740d691b6323ae018e609a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>set_sample_rate</b> (int rate)</td></tr>
<tr class="separator:afb9449fbe5740d691b6323ae018e609a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3bfa6b845881687b0a26b5a48257e343"><td class="memItemLeft" align="right" valign="top"><a id="a3bfa6b845881687b0a26b5a48257e343"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>set_output_active</b> (bool active)</td></tr>
<tr class="separator:a3bfa6b845881687b0a26b5a48257e343"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a60317f7ee78ed580d61b5e15ab7c3267"><td class="memItemLeft" align="right" valign="top"><a id="a60317f7ee78ed580d61b5e15ab7c3267"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_output.html#a60317f7ee78ed580d61b5e15ab7c3267">set_output</a> (AudioOutput &amp;output)</td></tr>
<tr class="memdesc:a60317f7ee78ed580d61b5e15ab7c3267"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not implemented. <br /></td></tr>
<tr class="separator:a60317f7ee78ed580d61b5e15ab7c3267"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2b08d63e4ae8fddf96a0447f9ddebc6f"><td class="memItemLeft" align="right" valign="top"><a id="a2b08d63e4ae8fddf96a0447f9ddebc6f"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_output.html#a2b08d63e4ae8fddf96a0447f9ddebc6f">set_output</a> (AudioStream &amp;output)</td></tr>
<tr class="memdesc:a2b08d63e4ae8fddf96a0447f9ddebc6f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not implemented. <br /></td></tr>
<tr class="separator:a2b08d63e4ae8fddf96a0447f9ddebc6f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a637f0ee236742c8ab58a5d9d831c5de7"><td class="memItemLeft" align="right" valign="top"><a id="a637f0ee236742c8ab58a5d9d831c5de7"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_output.html#a637f0ee236742c8ab58a5d9d831c5de7">set_output</a> (Print &amp;output)</td></tr>
<tr class="memdesc:a637f0ee236742c8ab58a5d9d831c5de7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not implemented. <br /></td></tr>
<tr class="separator:a637f0ee236742c8ab58a5d9d831c5de7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Abstract Output Class. </p>
<dl class="section author"><dt>Author</dt><dd>Phil Schatzmann</dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Apache License Version 2 </dd></dl>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li>src/<a class="el" href="_bluetooth_output_8h_source.html">BluetoothOutput.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
