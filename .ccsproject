<?xml version="1.0" encoding="UTF-8" ?>
<?ccsproject version="1.0"?>
<projectOptions>
	<ccsVersion value="10.2.0"/>
	<deviceVariant value="Cortex M.CC1312R1F3"/>
	<deviceFamily value="TMS470"/>
	<deviceEndianness value="little"/>
	<codegenToolVersion value="20.2.4.LTS"/>
	<isElfFormat value="true"/>
	<connection value="common/targetdb/connections/TIXDS110_Connection.xml"/>
	<rts value=""/>
	<createSlaveProjects value=""/>
	<ignoreDefaultDeviceSettings value="true"/>
	<ignoreDefaultCCSSettings value="true"/>
	<templateProperties value="id=gpiointerrupt_CC1312R1_LAUNCHXL_nortos_ccs.projectspec.gpiointerrupt_CC1312R1_LAUNCHXL_nortos_ccs,buildProfile=release,isHybrid=true"/>
	<origin value="C:\ti\simplelink_cc13x2_26x2_sdk_4_40_04_04\examples\nortos\CC1312R1_LAUNCHXL\drivers\gpiointerrupt\ccs\gpiointerrupt_CC1312R1_LAUNCHXL_nortos_ccs.projectspec"/>
	<filesToOpen value=""/>
	<isTargetManual value="false"/>
</projectOptions>
