******************************************************************************
                  TI ARM Linker PC v20.2.4                     
******************************************************************************
>> Linked Sat Feb 20 13:20:18 2021

OUTPUT FILE NAME:   <gpiointerrupt_CC1312R1_LAUNCHXL_nortos_ccs.out>
ENTRY POINT SYMBOL: "resetISR"  address: 00002e9d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00058000  00003228  00054dd8  R  X
  GPRAM                 11000000   00002000  00000000  00002000  RW X
  SRAM                  20000000   00014000  00004a36  0000f5ca  RW X
  LOG_DATA              90000000   00040000  00000000  00040000  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00003150   00003150    r-x
  00000000    00000000    000000d8   000000d8    r-- .intvecs
  000000d8    000000d8    00002f78   00002f78    r-x .text
  00003050    00003050    00000100   00000100    r-- .const
00003150    00003150    00000008   00000008    rw-
  00003150    00003150    00000008   00000008    rw- .args
00003158    00003158    00000078   00000078    r--
  00003158    00003158    00000078   00000078    r-- .cinit
00057fa8    00057fa8    00000058   00000058    r--
  00057fa8    00057fa8    00000058   00000058    r-- .ccfg
20000000    20000000    00004560   00000000    rw-
  20000000    20000000    00004000   00000000    rw- .sysmem
  20004000    20004000    000002c6   00000000    rw- .data
  200042c8    200042c8    00000298   00000000    rw- .bss
20004600    20004600    000000d8   00000000    rw-
  20004600    20004600    000000d8   00000000    rw- .vtable_ram
20013c00    20013c00    00000400   00000000    rw-
  20013c00    20013c00    00000400   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000d8     
                  00000000    000000d8     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.intvecs)

.text      0    000000d8    00002f78     
                  000000d8    000001e0     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_init)
                  000002b8    00000198                      : PowerCC26X2.oem4f (.text:Power_sleep)
                  00000450    00000190                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf1)
                  000005e0    0000018c                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:runCalibrateFsm)
                  0000076c    00000174                      : PowerCC26X2.oem4f (.text:Power_init)
                  000008e0    00000120                      : PowerCC26X2.oem4f (.text:configureXOSCHF)
                  00000a00    00000114                      : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfg)
                  00000b14    00000110                      : GPIOCC26XX.oem4f (.text:GPIO_setConfig)
                  00000c24    00000100     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:aligned_alloc)
                  00000d24    000000f0     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlStandby)
                  00000e14    000000e4     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free)
                  00000ef8    000000e0     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_standbyPolicy)
                  00000fd8    000000d8                     : SwiP_nortos.oem4f (.text:SwiP_construct)
                  000010b0    000000c4     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_init)
                  00001174    000000c0                      : PowerCC26X2.oem4f (.text:Power_releaseDependency)
                  00001234    000000bc     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_construct)
                  000012f0    000000b8     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_setDependency)
                  000013a8    000000b4     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_startup)
                  0000145c    000000ac                     : ClockPTimer_nortos.oem4f (.text:ClockP_workFuncDynamic)
                  00001508    000000ac     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_open)
                  000015b4    000000ac     driverlib.lib : setup.obj (.text:TrimAfterColdResetWakeupFromShutDown)
                  00001660    000000a0     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_pend)
                  00001700    0000009c                     : SwiP_nortos.oem4f (.text:SwiP_dispatch)
                  0000179c    0000009c     rtsv7M4_T_le_v4SPD16_eabi.lib : memcpy_t2.asm.obj (.text)
                  00001838    00000098     driverlib.lib : setup.obj (.text:NOROM_SetupTrimDevice)
                  000018d0    00000096     rtsv7M4_T_le_v4SPD16_eabi.lib : ull_div_t2.asm.obj (.text)
                  00001966    00000002     ti_drivers_config.obj (.text:Board_initHook)
                  00001968    0000008c     main_nortos.obj (.text:main)
                  000019f4    00000088     driverlib.lib : osc.obj (.text:NOROM_OSCHF_AttemptToSwitchToXosc)
                  00001a7c    00000088     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf2)
                  00001b04    00000088                      : PowerCC26X2.oem4f (.text:disableLFClockQualifiers)
                  00001b8c    00000088                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:updateSubSecInc)
                  00001c14    00000084     ti_drivers_config.obj (.text:Board_sendExtFlashByte)
                  00001c98    00000080     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_start)
                  00001d18    00000080                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_construct)
                  00001d98    00000080     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlSetRechargeBeforePowerDown)
                  00001e18    00000078     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_walkQueueDynamic)
                  00001e90    00000074     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_auxISR)
                  00001f04    00000070                      : PINCC26XX.oem4f (.text:PIN_add)
                  00001f74    00000070     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:split)
                  00001fe4    0000006c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipType)
                  00002050    00000068                   : osc.obj (.text:NOROM_OSCHF_SwitchToRcOscTurnOffXosc)
                  000020b8    00000068     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_post)
                  00002120    00000068                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_start)
                  00002188    00000068     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_lzss.c.obj (.text:decompress:lzss:__TI_decompress_lzss)
                  000021f0    00000060     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_write)
                  00002250    0000005c                      : PINCC26XX.oem4f (.text:PIN_remove)
                  000022ac    00000058                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_initiateCalibration)
                  00002304    00000054     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOff)
                  00002358    00000050     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_construct)
                  000023a8    0000004c     ti_drivers_config.obj (.text:Board_wakeUpExtFlash)
                  000023f4    0000004c     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOn)
                  00002440    00000048     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_construct)
                  00002488    00000048     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_RCOSC_clockFunc)
                  000024d0    00000048                      : PowerCC26X2.oem4f (.text:switchXOSCHF)
                  00002518    00000044     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetHwRevision)
                  0000255c    00000044     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_swi)
                  000025a0    00000044     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_post)
                  000025e4    00000044                     : SwiP_nortos.oem4f (.text:SwiP_restore)
                  00002628    00000044     rtsv7M4_T_le_v4SPD16_eabi.lib : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit:__TI_auto_init_nobinit_nopinit)
                  0000266c    00000040     ti_drivers_config.obj (.text:Board_shutDownExtFlash)
                  000026ac    00000040     driverlib.lib : osc.obj (.text:NOROM_OSC_HPOSCRelativeFrequencyOffsetGet)
                  000026ec    0000003c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputValue)
                  00002728    00000038                      : PINCC26XX.oem4f (.text:PIN_setConfig)
                  00002760    00000038     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCurrentTick)
                  00002798    00000038                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_initDevice)
                  000027d0    00000038     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:notify)
                  00002808    00000036     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setNextTick)
                  0000283e    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.text:_nop)
                  00002840    00000034     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicksUntilInterrupt)
                  00002874    00000034     driverlib.lib : aux_sysif.obj (.text:NOROM_AUXSYSIFOpModeChange)
                  000028a8    00000034                   : interrupt.obj (.text:NOROM_IntRegister)
                  000028dc    00000034                   : sys_ctrl.obj (.text:NOROM_SysCtrlIdle)
                  00002910    00000030     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_hwiIntFxn)
                  00002940    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_setCallback)
                  00002970    00000030                      : PowerCC26X2.oem4f (.text:Power_releaseConstraint)
                  000029a0    00000030                      : PowerCC26X2.oem4f (.text:Power_setConstraint)
                  000029d0    00000030     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.text:_c_int00:_c_int00)
                  00002a00    0000002c     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setThreshold)
                  00002a2c    00000028     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_insert)
                  00002a54    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:oscillatorISR)
                  00002a7c    00000024     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicks)
                  00002aa0    00000024                     : ClockPTimer_nortos.oem4f (.text:ClockP_scheduleNextTick)
                  00002ac4    00000024                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_dispatch)
                  00002ae8    00000024     driverlib.lib : osc.obj (.text:NOROM_OSCHF_TurnOnXosc)
                  00002b0c    00000024     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getMaxTicks)
                  00002b30    00000024     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:getTdcSemaphore)
                  00002b54    00000020     ti_drivers_config.obj (.text:Board_init)
                  00002b74    00000020     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_add)
                  00002b94    00000020     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfgMux)
                  00002bb4    00000020     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_put)
                  00002bd4    00000020                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_dynamicStub)
                  00002bf4    0000001e     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_close)
                  00002c12    0000001e                      : PowerCC26X2.oem4f (.text:Power_getTransitionLatency)
                  00002c30    0000001e     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_constructBinary)
                  00002c4e    0000001e                     : SwiP_nortos.oem4f (.text:SwiP_or)
                  00002c6c    0000001c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipFamily)
                  00002c88    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_hwi)
                  00002ca4    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_disable)
                  00002cc0    0000001c     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset:__TI_zero_init_nomemset)
                  00002cdc    0000001c                                   : memory.c.obj (.text:free_list_remove)
                  00002cf8    0000001a     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:PowerCC26XX_calibrate)
                  00002d12    0000001a     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_get)
                  00002d2c    0000001a                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_createBinary)
                  00002d46    00000018                     : ClockPTimer_nortos.oem4f (.text:ClockP_destruct)
                  00002d5e    00000002                     : SemaphoreP_nortos.oem4f (.text:clkFxn)
                  00002d60    00000018                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_inISR)
                  00002d78    00000018     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetPackageType)
                  00002d90    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_idleFunc)
                  00002da8    00000018     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_startup)
                  00002dc0    00000018     rtsv7M4_T_le_v4SPD16_eabi.lib : args_main.c.obj (.text:_args_main)
                  00002dd8    00000018                                   : ll_mul_t2.asm.obj (.text)
                  00002df0    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:switchToTCXO)
                  00002e08    00000016     driverlib.lib : chipinfo.obj (.text:NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated)
                  00002e1e    00000016     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_create)
                  00002e34    00000016                     : SwiP_nortos.oem4f (.text:maxbit)
                  00002e4a    00000014     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_calibrate)
                  00002e5e    00000002                      : PowerCC26X2.oem4f (.text:emptyClockFunc)
                  00002e60    00000014                      : PowerCC26X2.oem4f (.text:Power_disablePolicy)
                  00002e74    00000014     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_handleHwi)
                  00002e88    00000014                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCount64)
                  00002e9c    00000014                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:resetISR)
                  00002eb0    00000012                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_Params_init)
                  00002ec2    00000002                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:faultISR)
                  00002ec4    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_Params_init)
                  00002ed4    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_disable)
                  00002ee4    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_post)
                  00002ef4    00000010     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_getPinCount)
                  00002f04    00000010     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerDisable)
                  00002f14    00000010     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_enablePolicy)
                  00002f24    00000010     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_Params_init)
                  00002f34    00000010                     : SwiP_nortos.oem4f (.text:SwiP_Params_init)
                  00002f44    00000010                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_Params_init)
                  00002f54    0000000e                     : QueueP_nortos.oem4f (.text:QueueP_empty)
                  00002f62    0000000e                     : QueueP_nortos.oem4f (.text:QueueP_remove)
                  00002f70    0000000e     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_none.c.obj (.text:decompress:none:__TI_decompress_none)
                  00002f7e    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:intDefaultHandler)
                  00002f80    0000000c                     : ClockPTimer_nortos.oem4f (.text:ClockP_doTick)
                  00002f8c    0000000c                     : ClockPTimer_nortos.oem4f (.text:ClockP_getSystemTickPeriod)
                  00002f98    0000000c     driverlib.lib : cpu.obj (.text:NOROM_CPUcpsid)
                  00002fa4    0000000c                   : cpu.obj (.text:NOROM_CPUcpsie)
                  00002fb0    0000000c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_registerIntCb)
                  00002fbc    0000000c     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerRestore)
                  00002fc8    0000000c     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_getConstraintMask)
                  00002fd4    0000000c                      : PowerCC26X2.oem4f (.text:Power_getDependencyCount)
                  00002fe0    0000000c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_getTrigger)
                  00002fec    0000000c                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getFreq)
                  00002ff8    00000008                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_restore)
                  00003000    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:malloc)
                  00003008    00000006     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_stop)
                  0000300e    00000006     driverlib.lib : cpu.obj (.text:NOROM_CPUdelay)
                  00003014    00000006     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_init)
                  0000301a    00000006     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:delayUs)
                  00003020    00000004     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:CPUwfi)
                  00003024    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_isActive)
                  00003028    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_setTimeout)
                  0000302c    00000004                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_enable)
                  00003030    00000004                     : QueueP_nortos.oem4f (.text:QueueP_head)
                  00003034    00000004                     : QueueP_nortos.oem4f (.text:QueueP_next)
                  00003038    00000004                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_delete)
                  0000303c    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : pre_init.c.obj (.text:_system_pre_init)
                  00003040    00000004                                   : exit.c.obj (.text:abort:abort)
                  00003044    00000004     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureRFCoreClocks)
                  00003048    00000004                      : PowerCC26X2.oem4f (.text:nopResourceHandler)
                  0000304c    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:nmiISR)
                  0000304e    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : div0.asm.obj (.text)

.const     0    00003050    00000100     
                  00003050    00000054     drivers_cc13x2.a : PowerCC26X2.oem4f (.const:resourceDB)
                  000030a4    00000018                      : GPIOCC26XX.oem4f (.const:interruptType)
                  000030bc    00000014     ti_drivers_config.obj (.const:$P$T1$2)
                  000030d0    00000014     ti_drivers_config.obj (.const:GPIOCC26XX_config)
                  000030e4    00000014     ti_drivers_config.obj (.const:PowerCC26X2_config)
                  000030f8    00000010     nortos_cc13x2.a : SwiP_nortos.oem4f (.const:SwiP_defaultParams)
                  00003108    00000010     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:outPinTypes)
                  00003118    0000000c                      : GPIOCC26XX.oem4f (.const:inPinTypes)
                  00003124    0000000c                      : GPIOCC26XX.oem4f (.const:outPinStrengths)
                  00003130    00000008     driverlib.lib : aux_sysif.obj (.const:$O1$$)
                  00003138    00000008     ti_drivers_config.obj (.const:$P$T0$1)
                  00003140    00000008     ti_drivers_config.obj (.const:BoardGpioInitTable)
                  00003148    00000008     ti_drivers_config.obj (.const:PINCC26XX_hwAttrs)

.binit     0    00000000    00000000     

.cinit     0    00003158    00000078     
                  00003158    00000041     (.cinit..data.load) [load image, compression = lzss]
                  00003199    00000003     --HOLE-- [fill = 0]
                  0000319c    0000000c     (__TI_handler_table)
                  000031a8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000031b0    00000008     (.cinit..vtable_ram.load) [load image, compression = zero_init]
                  000031b8    00000018     (__TI_cinit_table)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.ccfg      0    00057fa8    00000058     
                  00057fa8    00000058     ti_devices_config.obj (.ccfg:retain)

.sysmem    0    20000000    00004000     UNINITIALIZED
                  20000000    00000010     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.sysmem)
                  20000010    00003ff0     --HOLE--

.vtable_ram 
*          0    20004600    000000d8     UNINITIALIZED
                  20004600    000000d8     driverlib.lib : interrupt.obj (.vtable_ram)

.data      0    20004000    000002c6     UNINITIALIZED
                  20004000    00000170     drivers_cc13x2.a : PowerCC26X2.oem4f (.data:PowerCC26X2_module)
                  20004170    000000d8     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data:HwiP_dispatchTable)
                  20004248    00000024     driverlib.lib : osc.obj (.data:$O1$$)
                  2000426c    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data:ClockP_defaultParams)
                  20004278    0000000c                     : TimerPCC26XX_nortos.oem4f (.data:TimerP_defaultParams)
                  20004284    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.data:$O1$$)
                  2000428c    00000008                                   : memory.c.obj (.data:$O1$$)
                  20004294    00000008     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data:SemaphoreP_defaultParams)
                  2000429c    00000008     drivers_cc13x2.a : PINCC26XX.oem4f (.data)
                  200042a4    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data)
                  200042ac    00000005     drivers_cc13x2.a : GPIOCC26XX.oem4f (.data)
                  200042b1    00000003     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data)
                  200042b4    00000004     ti_drivers_config.obj (.data:gpioCallbackFunctions)
                  200042b8    00000004     ti_drivers_config.obj (.data:gpioPinConfigs)
                  200042bc    00000004     drivers_cc13x2.a : GPIOCC26XX.oem4f (.data:gpioPinTable)
                  200042c0    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data)
                  200042c4    00000001                     : SwiP_nortos.oem4f (.data)
                  200042c5    00000001                     : TimerPCC26XX_nortos.oem4f (.data)

.bss       0    200042c8    00000298     UNINITIALIZED
                  200042c8    000000a0     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:ClockP_module)
                  20004368    0000007c     (.common:pinHandleTable)
                  200043e4    00000050     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinSemaphore)
                  20004434    00000034                      : PINCC26XX.oem4f (.bss:pinSwi)
                  20004468    00000020     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_readyList)
                  20004488    00000020     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioCallbackInfo)
                  200044a8    00000020                      : PINCC26XX.oem4f (.bss:pinGpioConfigTable)
                  200044c8    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_hwiStruct)
                  200044e4    0000001c                     : TimerPCC26XX_nortos.oem4f (.bss:TimerP_hwiStruct)
                  20004500    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinHwi)
                  2000451c    0000000d     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss)
                  20004529    00000001     (.common:driverlib_release_0_59848)
                  2000452a    00000002     --HOLE--
                  2000452c    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioPinState)
                  20004538    00000008                      : GPIOCC26XX.oem4f (.bss)
                  20004540    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:timerFreq)
                  20004548    00000004     drivers_cc13x2.a : PINCC26XX.oem4f (.bss)
                  2000454c    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss)
                  20004550    00000004                     : PowerCC26X2_nortos.oem4f (.bss)
                  20004554    00000004                     : TimerPCC26XX_nortos.oem4f (.bss)
                  20004558    00000004     (.common:i)
                  2000455c    00000004     (.common:j)

.stack     0    20013c00    00000400     UNINITIALIZED
                  20013c00    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.stack)
                  20013c04    000003fc     --HOLE--

.log_data 
*          0    90000000    00000000     COPY SECTION

__llvm_prf_cnts 
*          0    20000000    00000000     UNINITIALIZED

.args      0    00003150    00000008     
                  00003150    00000008     --HOLE-- [fill = 0]

MODULE SUMMARY

       Module                             code    ro data   rw data
       ------                             ----    -------   -------
    .\
       main_nortos.obj                    140     0         8      
    +--+----------------------------------+-------+---------+---------+
       Total:                             140     0         8      
                                                                   
    .\syscfg\
       ti_drivers_config.obj              306     84        8      
       ti_devices_config.obj              0       88        0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             306     172       8      
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos/lib/ccs/m4f/nortos_cc13x2.a
       ClockPTimer_nortos.oem4f           906     0         192    
       SwiP_nortos.oem4f                  672     16        74     
       TimerPCC26XX_nortos.oem4f          642     0         45     
       HwiPCC26XX_nortos.oem4f            250     0         220    
       SemaphoreP_nortos.oem4f            408     0         11     
       PowerCC26X2_nortos.oem4f           256     0         4      
       startup_cc13x2_cc26x2_ccs.oem4f    26      216       0      
       QueueP_nortos.oem4f                100     0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             3260    232       546    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/cc13x2_cc26x2/driverlib/bin/ccs/driverlib.lib
       sys_ctrl.obj                       420     0         0      
       osc.obj                            340     0         36     
       setup.obj                          324     0         0      
       interrupt.obj                      52      0         216    
       chipinfo.obj                       250     0         0      
       prcm.obj                           160     0         0      
       aux_sysif.obj                      52      8         0      
       cpu.obj                            30      0         0      
       driverlib_release.obj              0       0         1      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1628    8         253    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/lib/ccs/m4f/drivers_cc13x2.a
       PowerCC26X2.oem4f                  2024    84        368    
       PINCC26XX.oem4f                    1434    0         328    
       PowerCC26X2_calibrateRCOSC.oem4f   1400    0         0      
       GPIOCC26XX.oem4f                   660     64        61     
    +--+----------------------------------+-------+---------+---------+
       Total:                             5518    148       757    
                                                                   
    C:\ti\ccs1011\ccs\tools\compiler\ti-cgt-arm_20.2.4.LTS\lib\rtsv7M4_T_le_v4SPD16_eabi.lib
       memory.c.obj                       672     0         8      
       memcpy_t2.asm.obj                  156     0         0      
       ull_div_t2.asm.obj                 150     0         0      
       copy_decompress_lzss.c.obj         104     0         0      
       autoinit.c.obj                     68      0         0      
       boot_cortex_m.c.obj                48      0         0      
       copy_zero_init.c.obj               28      0         0      
       args_main.c.obj                    24      0         0      
       ll_mul_t2.asm.obj                  24      0         0      
       copy_decompress_none.c.obj         14      0         0      
       _lock.c.obj                        2       0         8      
       exit.c.obj                         4       0         0      
       pre_init.c.obj                     4       0         0      
       div0.asm.obj                       2       0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1300    0         16     
                                                                   
       Heap:                              0       0         16384  
       Stack:                             0       0         1024   
       Linker Generated:                  0       117       0      
    +--+----------------------------------+-------+---------+---------+
       Grand Total:                       12152   677       18996  


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000031b8 records: 3, size/record: 8, table size: 24
	.data: load addr=00003158, load size=00000041 bytes, run addr=20004000, run size=000002c6 bytes, compression=lzss
	.bss: load addr=000031a8, load size=00000008 bytes, run addr=200042c8, run size=00000298 bytes, compression=zero_init
	.vtable_ram: load addr=000031b0, load size=00000008 bytes, run addr=20004600, run size=000000d8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000319c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_lzss
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                                               
-------   ----                                                               
00003140  BoardGpioInitTable                                                 
00002b55  Board_init                                                         
00001967  Board_initHook                                                     
00001c15  Board_sendExtFlashByte                                             
0000266d  Board_shutDownExtFlash                                             
000023a9  Board_wakeUpExtFlash                                               
00003041  C$$EXIT                                                            
00002ec5  ClockP_Params_init                                                 
00002b75  ClockP_add                                                         
00002441  ClockP_construct                                                   
00002d47  ClockP_destruct                                                    
00002f81  ClockP_doTick                                                      
00002f8d  ClockP_getSystemTickPeriod                                         
00002a7d  ClockP_getTicks                                                    
00002841  ClockP_getTicksUntilInterrupt                                      
00003025  ClockP_isActive                                                    
00002aa1  ClockP_scheduleNextTick                                            
00003029  ClockP_setTimeout                                                  
00001c99  ClockP_start                                                       
000013a9  ClockP_startup                                                     
00003009  ClockP_stop                                                        
200042a8  ClockP_tickPeriod                                                  
00001e19  ClockP_walkQueueDynamic                                            
0000145d  ClockP_workFuncDynamic                                             
000030d0  GPIOCC26XX_config                                                  
00002911  GPIO_hwiIntFxn                                                     
000010b1  GPIO_init                                                          
00002941  GPIO_setCallback                                                   
00000b15  GPIO_setConfig                                                     
000021f1  GPIO_write                                                         
00002eb1  HwiP_Params_init                                                   
00001d19  HwiP_construct                                                     
00002ed5  HwiP_disable                                                       
0000302d  HwiP_enable                                                        
00002d61  HwiP_inISR                                                         
00002ee5  HwiP_post                                                          
00002ff9  HwiP_restore                                                       
200042c0  HwiP_swiPIntNum                                                    
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
00002875  NOROM_AUXSYSIFOpModeChange                                         
00002f99  NOROM_CPUcpsid                                                     
00002fa5  NOROM_CPUcpsie                                                     
0000300f  NOROM_CPUdelay                                                     
00002c6d  NOROM_ChipInfo_GetChipFamily                                       
00001fe5  NOROM_ChipInfo_GetChipType                                         
00002519  NOROM_ChipInfo_GetHwRevision                                       
00002d79  NOROM_ChipInfo_GetPackageType                                      
000028a9  NOROM_IntRegister                                                  
000019f5  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002051  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
00002ae9  NOROM_OSCHF_TurnOnXosc                                             
000026ad  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00002305  NOROM_PRCMPowerDomainsAllOff                                       
000023f5  NOROM_PRCMPowerDomainsAllOn                                        
00001839  NOROM_SetupTrimDevice                                              
000028dd  NOROM_SysCtrlIdle                                                  
00001d99  NOROM_SysCtrlSetRechargeBeforePowerDown                            
00000d25  NOROM_SysCtrlStandby                                               
00002e09  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
0000302d  NoRTOS_start                                                       
00002ef5  PINCC26XX_getPinCount                                              
00003148  PINCC26XX_hwAttrs                                                  
00001f05  PIN_add                                                            
00002bf5  PIN_close                                                          
000000d9  PIN_init                                                           
00001509  PIN_open                                                           
00002fb1  PIN_registerIntCb                                                  
00002251  PIN_remove                                                         
00002729  PIN_setConfig                                                      
000026ed  PIN_setOutputValue                                                 
00002489  PowerCC26X2_RCOSC_clockFunc                                        
00001e91  PowerCC26X2_auxISR                                                 
00002e4b  PowerCC26X2_calibrate                                              
000030e4  PowerCC26X2_config                                                 
000022ad  PowerCC26X2_initiateCalibration                                    
20004000  PowerCC26X2_module                                                 
00002cf9  PowerCC26XX_calibrate                                              
00002f05  PowerCC26XX_schedulerDisable                                       
00002fbd  PowerCC26XX_schedulerRestore                                       
00000ef9  PowerCC26XX_standbyPolicy                                          
00002e61  Power_disablePolicy                                                
00002f15  Power_enablePolicy                                                 
00002fc9  Power_getConstraintMask                                            
00002fd5  Power_getDependencyCount                                           
00002c13  Power_getTransitionLatency                                         
00002d91  Power_idleFunc                                                     
0000076d  Power_init                                                         
00002971  Power_releaseConstraint                                            
00001175  Power_releaseDependency                                            
000029a1  Power_setConstraint                                                
000012f1  Power_setDependency                                                
000002b9  Power_sleep                                                        
00002f55  QueueP_empty                                                       
00002d13  QueueP_get                                                         
00003031  QueueP_head                                                        
00003015  QueueP_init                                                        
00003035  QueueP_next                                                        
00002bb5  QueueP_put                                                         
00002f63  QueueP_remove                                                      
00002f25  SemaphoreP_Params_init                                             
00002359  SemaphoreP_construct                                               
00002c31  SemaphoreP_constructBinary                                         
00002e1f  SemaphoreP_create                                                  
00002d2d  SemaphoreP_createBinary                                            
20004294  SemaphoreP_defaultParams                                           
00003039  SemaphoreP_delete                                                  
00001661  SemaphoreP_pend                                                    
000025a1  SemaphoreP_post                                                    
00002f35  SwiP_Params_init                                                   
00000fd9  SwiP_construct                                                     
00002ca5  SwiP_disable                                                       
00001701  SwiP_dispatch                                                      
00002fe1  SwiP_getTrigger                                                    
00002c4f  SwiP_or                                                            
000020b9  SwiP_post                                                          
000025e5  SwiP_restore                                                       
00002f45  TimerP_Params_init                                                 
00001235  TimerP_construct                                                   
00002bd5  TimerP_dynamicStub                                                 
00002e89  TimerP_getCount64                                                  
00002761  TimerP_getCurrentTick                                              
00002fed  TimerP_getFreq                                                     
00002b0d  TimerP_getMaxTicks                                                 
00002799  TimerP_initDevice                                                  
00002809  TimerP_setNextTick                                                 
00002a01  TimerP_setThreshold                                                
00002121  TimerP_start                                                       
00002da9  TimerP_startup                                                     
20014000  __STACK_END                                                        
00000400  __STACK_SIZE                                                       
00004000  __SYSMEM_SIZE                                                      
000031b8  __TI_CINIT_Base                                                    
000031d0  __TI_CINIT_Limit                                                   
0000319c  __TI_Handler_Table_Base                                            
000031a8  __TI_Handler_Table_Limit                                           
00002629  __TI_auto_init_nobinit_nopinit                                     
00002189  __TI_decompress_lzss                                               
00002f71  __TI_decompress_none                                               
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
00000000  __TI_static_base__                                                 
00002cc1  __TI_zero_init_nomemset                                            
0000304f  __aeabi_idiv0                                                      
0000304f  __aeabi_ldiv0                                                      
00002dd9  __aeabi_lmul                                                       
0000179d  __aeabi_memcpy                                                     
0000179d  __aeabi_memcpy4                                                    
0000179d  __aeabi_memcpy8                                                    
000018d1  __aeabi_uldivmod                                                   
ffffffff  __binit__                                                          
00003150  __c_args__                                                         
00057fa8  __ccfg                                                             
UNDEFED   __mpu_init                                                         
20013c00  __stack                                                            
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
00002dc1  _args_main                                                         
000029d1  _c_int00                                                           
20004248  _hposcCoeffs                                                       
20004284  _lock                                                              
0000283f  _nop                                                               
20000000  _sys_memory                                                        
UNDEFED   _system_post_cinit                                                 
0000303d  _system_pre_init                                                   
20004288  _unlock                                                            
00003041  abort                                                              
00000c25  aligned_alloc                                                      
ffffffff  binit                                                              
00002d5f  clkFxn                                                             
20004529  driverlib_release_0_59848                                          
00000e15  free                                                               
20004600  g_pfnRAMVectors                                                    
00000000  g_pfnVectors                                                       
200042b4  gpioCallbackFunctions                                              
200042b8  gpioPinConfigs                                                     
20004558  i                                                                  
00003118  inPinTypes                                                         
2000455c  j                                                                  
00001969  main                                                               
00003001  malloc                                                             
00000c25  memalign                                                           
0000179d  memcpy                                                             
00003124  outPinStrengths                                                    
00003108  outPinTypes                                                        
20004368  pinHandleTable                                                     
200042a0  pinLowerBound                                                      
2000429c  pinUpperBound                                                      
00002e9d  resetISR                                                           
00003050  resourceDB                                                         


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                                               
-------   ----                                                               
00000000  __TI_static_base__                                                 
00000000  g_pfnVectors                                                       
000000d9  PIN_init                                                           
000002b9  Power_sleep                                                        
00000400  __STACK_SIZE                                                       
0000076d  Power_init                                                         
00000b15  GPIO_setConfig                                                     
00000c25  aligned_alloc                                                      
00000c25  memalign                                                           
00000d25  NOROM_SysCtrlStandby                                               
00000e15  free                                                               
00000ef9  PowerCC26XX_standbyPolicy                                          
00000fd9  SwiP_construct                                                     
000010b1  GPIO_init                                                          
00001175  Power_releaseDependency                                            
00001235  TimerP_construct                                                   
000012f1  Power_setDependency                                                
000013a9  ClockP_startup                                                     
0000145d  ClockP_workFuncDynamic                                             
00001509  PIN_open                                                           
00001661  SemaphoreP_pend                                                    
00001701  SwiP_dispatch                                                      
0000179d  __aeabi_memcpy                                                     
0000179d  __aeabi_memcpy4                                                    
0000179d  __aeabi_memcpy8                                                    
0000179d  memcpy                                                             
00001839  NOROM_SetupTrimDevice                                              
000018d1  __aeabi_uldivmod                                                   
00001967  Board_initHook                                                     
00001969  main                                                               
000019f5  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00001c15  Board_sendExtFlashByte                                             
00001c99  ClockP_start                                                       
00001d19  HwiP_construct                                                     
00001d99  NOROM_SysCtrlSetRechargeBeforePowerDown                            
00001e19  ClockP_walkQueueDynamic                                            
00001e91  PowerCC26X2_auxISR                                                 
00001f05  PIN_add                                                            
00001fe5  NOROM_ChipInfo_GetChipType                                         
00002051  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
000020b9  SwiP_post                                                          
00002121  TimerP_start                                                       
00002189  __TI_decompress_lzss                                               
000021f1  GPIO_write                                                         
00002251  PIN_remove                                                         
000022ad  PowerCC26X2_initiateCalibration                                    
00002305  NOROM_PRCMPowerDomainsAllOff                                       
00002359  SemaphoreP_construct                                               
000023a9  Board_wakeUpExtFlash                                               
000023f5  NOROM_PRCMPowerDomainsAllOn                                        
00002441  ClockP_construct                                                   
00002489  PowerCC26X2_RCOSC_clockFunc                                        
00002519  NOROM_ChipInfo_GetHwRevision                                       
000025a1  SemaphoreP_post                                                    
000025e5  SwiP_restore                                                       
00002629  __TI_auto_init_nobinit_nopinit                                     
0000266d  Board_shutDownExtFlash                                             
000026ad  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
000026ed  PIN_setOutputValue                                                 
00002729  PIN_setConfig                                                      
00002761  TimerP_getCurrentTick                                              
00002799  TimerP_initDevice                                                  
00002809  TimerP_setNextTick                                                 
0000283f  _nop                                                               
00002841  ClockP_getTicksUntilInterrupt                                      
00002875  NOROM_AUXSYSIFOpModeChange                                         
000028a9  NOROM_IntRegister                                                  
000028dd  NOROM_SysCtrlIdle                                                  
00002911  GPIO_hwiIntFxn                                                     
00002941  GPIO_setCallback                                                   
00002971  Power_releaseConstraint                                            
000029a1  Power_setConstraint                                                
000029d1  _c_int00                                                           
00002a01  TimerP_setThreshold                                                
00002a7d  ClockP_getTicks                                                    
00002aa1  ClockP_scheduleNextTick                                            
00002ae9  NOROM_OSCHF_TurnOnXosc                                             
00002b0d  TimerP_getMaxTicks                                                 
00002b55  Board_init                                                         
00002b75  ClockP_add                                                         
00002bb5  QueueP_put                                                         
00002bd5  TimerP_dynamicStub                                                 
00002bf5  PIN_close                                                          
00002c13  Power_getTransitionLatency                                         
00002c31  SemaphoreP_constructBinary                                         
00002c4f  SwiP_or                                                            
00002c6d  NOROM_ChipInfo_GetChipFamily                                       
00002ca5  SwiP_disable                                                       
00002cc1  __TI_zero_init_nomemset                                            
00002cf9  PowerCC26XX_calibrate                                              
00002d13  QueueP_get                                                         
00002d2d  SemaphoreP_createBinary                                            
00002d47  ClockP_destruct                                                    
00002d5f  clkFxn                                                             
00002d61  HwiP_inISR                                                         
00002d79  NOROM_ChipInfo_GetPackageType                                      
00002d91  Power_idleFunc                                                     
00002da9  TimerP_startup                                                     
00002dc1  _args_main                                                         
00002dd9  __aeabi_lmul                                                       
00002e09  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00002e1f  SemaphoreP_create                                                  
00002e4b  PowerCC26X2_calibrate                                              
00002e61  Power_disablePolicy                                                
00002e89  TimerP_getCount64                                                  
00002e9d  resetISR                                                           
00002eb1  HwiP_Params_init                                                   
00002ec5  ClockP_Params_init                                                 
00002ed5  HwiP_disable                                                       
00002ee5  HwiP_post                                                          
00002ef5  PINCC26XX_getPinCount                                              
00002f05  PowerCC26XX_schedulerDisable                                       
00002f15  Power_enablePolicy                                                 
00002f25  SemaphoreP_Params_init                                             
00002f35  SwiP_Params_init                                                   
00002f45  TimerP_Params_init                                                 
00002f55  QueueP_empty                                                       
00002f63  QueueP_remove                                                      
00002f71  __TI_decompress_none                                               
00002f81  ClockP_doTick                                                      
00002f8d  ClockP_getSystemTickPeriod                                         
00002f99  NOROM_CPUcpsid                                                     
00002fa5  NOROM_CPUcpsie                                                     
00002fb1  PIN_registerIntCb                                                  
00002fbd  PowerCC26XX_schedulerRestore                                       
00002fc9  Power_getConstraintMask                                            
00002fd5  Power_getDependencyCount                                           
00002fe1  SwiP_getTrigger                                                    
00002fed  TimerP_getFreq                                                     
00002ff9  HwiP_restore                                                       
00003001  malloc                                                             
00003009  ClockP_stop                                                        
0000300f  NOROM_CPUdelay                                                     
00003015  QueueP_init                                                        
00003025  ClockP_isActive                                                    
00003029  ClockP_setTimeout                                                  
0000302d  HwiP_enable                                                        
0000302d  NoRTOS_start                                                       
00003031  QueueP_head                                                        
00003035  QueueP_next                                                        
00003039  SemaphoreP_delete                                                  
0000303d  _system_pre_init                                                   
00003041  C$$EXIT                                                            
00003041  abort                                                              
0000304f  __aeabi_idiv0                                                      
0000304f  __aeabi_ldiv0                                                      
00003050  resourceDB                                                         
000030d0  GPIOCC26XX_config                                                  
000030e4  PowerCC26X2_config                                                 
00003108  outPinTypes                                                        
00003118  inPinTypes                                                         
00003124  outPinStrengths                                                    
00003140  BoardGpioInitTable                                                 
00003148  PINCC26XX_hwAttrs                                                  
00003150  __c_args__                                                         
0000319c  __TI_Handler_Table_Base                                            
000031a8  __TI_Handler_Table_Limit                                           
000031b8  __TI_CINIT_Base                                                    
000031d0  __TI_CINIT_Limit                                                   
00004000  __SYSMEM_SIZE                                                      
00057fa8  __ccfg                                                             
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
20000000  _sys_memory                                                        
20004000  PowerCC26X2_module                                                 
20004248  _hposcCoeffs                                                       
20004284  _lock                                                              
20004288  _unlock                                                            
20004294  SemaphoreP_defaultParams                                           
2000429c  pinUpperBound                                                      
200042a0  pinLowerBound                                                      
200042a8  ClockP_tickPeriod                                                  
200042b4  gpioCallbackFunctions                                              
200042b8  gpioPinConfigs                                                     
200042c0  HwiP_swiPIntNum                                                    
20004368  pinHandleTable                                                     
20004529  driverlib_release_0_59848                                          
20004558  i                                                                  
2000455c  j                                                                  
20004600  g_pfnRAMVectors                                                    
20013c00  __stack                                                            
20014000  __STACK_END                                                        
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
ffffffff  __binit__                                                          
ffffffff  binit                                                              
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
UNDEFED   __mpu_init                                                         
UNDEFED   _system_post_cinit                                                 

[191 symbols]
