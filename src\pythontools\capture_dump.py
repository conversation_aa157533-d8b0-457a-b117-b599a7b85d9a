import serial
import time

# Simple script to capture dump data from NodeA
ser = serial.Serial('COM3', 115200)

# Wait for connection
time.sleep(2)

# Send dump command
print("Sending 'dump' command...")
ser.write(b'd\n')
ser.flush()

# Capture data to file
print("Capturing dump data to chirp.txt...")
capturing_data = False
sample_count = 0

with open("chirp.txt", "w") as f:
    try:
        while True:
            line = ser.readline().decode(errors="ignore").strip()

            if line:
                print(line)  # Show on screen

                # Check if dump data starts
                if "--- DATA START ---" in line:
                    print("Found data start, capturing samples...")
                    capturing_data = True
                    continue

                # Check if dump data ends
                if "--- DATA END ---" in line:
                    print(f"Data capture complete! Saved {sample_count} samples to chirp.txt")
                    break

                # Save sample data (only numeric lines)
                if capturing_data and line.lstrip('-').isdigit():
                    f.write(line + "\n")
                    sample_count += 1

                    if sample_count % 500 == 0:
                        print(f"Captured {sample_count} samples...")

    except KeyboardInterrupt:
        print(f"\nStopped. Saved {sample_count} samples.")
    finally:
        ser.close()
        print("Serial connection closed.")

# Very simple version - exactly like your original but sends 'd' command
ser = serial.Serial('COM3', 115200)

# Wait for connection
time.sleep(2)

# Send dump command
print("Sending 'dump' command...")
ser.write(b'd\n')
ser.flush()

# Capture data to file
print("Capturing to chirp.txt...")
with open("chirp.txt", "w") as f:
    try:
        while True:
            line = ser.readline().decode(errors="ignore")
            print(line, end="")
            f.write(line)
    except KeyboardInterrupt:
        print("\nStopped.")
    finally:
        ser.close()
