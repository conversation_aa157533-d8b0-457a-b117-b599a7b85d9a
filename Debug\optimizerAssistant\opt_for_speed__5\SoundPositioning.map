******************************************************************************
                  TI ARM Linker PC v20.2.4                     
******************************************************************************
>> Linked Mon Feb 22 19:51:20 2021

OUTPUT FILE NAME:   <SoundPositioning.out>
ENTRY POINT SYMBOL: "resetISR"  address: 000049ed


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00058000  00005022  00052fde  R  X
  GPRAM                 11000000   00002000  00000000  00002000  RW X
  SRAM                  20000000   00014000  00004ee2  0000f11e  RW X
  LOG_DATA              90000000   00040000  00000000  00040000  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004ee4   00004ee4    r-x
  00000000    00000000    000000d8   000000d8    r-- .intvecs
  000000d8    000000d8    00004b72   00004b72    r-x .text
  00004c4c    00004c4c    00000298   00000298    r-- .const
00004ee4    00004ee4    00000008   00000008    rw-
  00004ee4    00004ee4    00000008   00000008    rw- .args
00004ef0    00004ef0    000000e0   000000e0    r--
  00004ef0    00004ef0    000000e0   000000e0    r-- .cinit
00057fa8    00057fa8    00000058   00000058    r--
  00057fa8    00057fa8    00000058   00000058    r-- .ccfg
20000000    20000000    000009ea   00000000    rw-
  20000000    20000000    00000604   00000000    rw- .bss
  20000604    20000604    000003e6   00000000    rw- .data
20000a00    20000a00    000000d8   00000000    rw-
  20000a00    20000a00    000000d8   00000000    rw- .vtable_ram
20001870    20001870    00000010   00000000    rw-
  20001870    20001870    00000010   00000000    rw- .TI.bound:dmaADCPriControlTableEntry
20001a70    20001a70    00004010   00000000    rw-
  20001a70    20001a70    00000010   00000000    rw- .TI.bound:dmaADCAltControlTableEntry
  20001a80    20001a80    00004000   00000000    rw- .sysmem
20013c00    20013c00    00000400   00000000    rw-
  20013c00    20013c00    00000400   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000d8     
                  00000000    000000d8     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.intvecs)

.text      0    000000d8    00004b72     
                  000000d8    000001e0     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_init)
                  000002b8    00000198                      : PowerCC26X2.oem4f (.text:Power_sleep)
                  00000450    00000190                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf1)
                  000005e0    0000018c                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:runCalibrateFsm)
                  0000076c    00000174                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_open)
                  000008e0    00000174                      : PowerCC26X2.oem4f (.text:Power_init)
                  00000a54    00000164                      : UARTCC26XX.oem4f (.text:UARTCC26XX_open)
                  00000bb8    00000154                      : UARTCC26XX.oem4f (.text:UARTCC26XX_hwiIntFxn)
                  00000d0c    0000013a                      : UARTCC26XX.oem4f (.text:UARTCC26XX_read)
                  00000e46    00000002     driverlib.lib : interrupt.obj (.text:IntDefaultHandler)
                  00000e48    00000120     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureXOSCHF)
                  00000f68    00000114                      : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfg)
                  0000107c    00000110                      : GPIOCC26XX.oem4f (.text:GPIO_setConfig)
                  0000118c    00000100                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convert)
                  0000128c    00000100     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:aligned_alloc)
                  0000138c    000000f0     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlStandby)
                  0000147c    00000004     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_getResolution)
                  00001480    000000e6     SoundTX.obj (.text:Timer2AInterruptHandler)
                  00001566    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_destruct)
                  00001568    000000e4     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free)
                  0000164c    000000e0     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_standbyPolicy)
                  0000172c    000000e0     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_write)
                  0000180c    00000004     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:CPUwfi)
                  00001810    000000dc     SoundTX.obj (.text:InitTimer2)
                  000018ec    000000d8     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_construct)
                  000019c4    000000c4     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_init)
                  00001a88    000000c0                      : PowerCC26X2.oem4f (.text:Power_releaseDependency)
                  00001b48    000000c0                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initIO)
                  00001c08    000000bc     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_construct)
                  00001cc4    000000b8     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_setDependency)
                  00001d7c    000000b8                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initHw)
                  00001e34    000000b4     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_startup)
                  00001ee8    000000ac                     : ClockPTimer_nortos.oem4f (.text:ClockP_workFuncDynamic)
                  00001f94    000000ac     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_open)
                  00002040    000000ac     driverlib.lib : setup.obj (.text:TrimAfterColdResetWakeupFromShutDown)
                  000020ec    000000a0     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_pend)
                  0000218c    0000009c                     : SwiP_nortos.oem4f (.text:SwiP_dispatch)
                  00002228    0000009c     rtsv7M4_T_le_v4SPD16_eabi.lib : memcpy_t2.asm.obj (.text)
                  000022c4    00000098     driverlib.lib : setup.obj (.text:NOROM_SetupTrimDevice)
                  0000235c    00000098     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeTxFifoFlush)
                  000023f4    00000096     rtsv7M4_T_le_v4SPD16_eabi.lib : ull_div_t2.asm.obj (.text)
                  0000248a    00000092     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_writeCancel)
                  0000251c    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_isActive)
                  00002520    00000092     mainNew.obj (.text:main)
                  000025b2    00000006     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_stop)
                  000025b8    0000008e     ti_drivers_config.obj (.text:Board_sendExtFlashByte)
                  00002646    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.text:_nop)
                  00002648    00000088     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_open)
                  000026d0    00000088     driverlib.lib : osc.obj (.text:NOROM_OSCHF_AttemptToSwitchToXosc)
                  00002758    00000088     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf2)
                  000027e0    00000088                      : PowerCC26X2.oem4f (.text:disableLFClockQualifiers)
                  00002868    00000088                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:updateSubSecInc)
                  000028f0    00000084                      : UARTCC26XX.oem4f (.text:UARTCC26XX_close)
                  00002974    00000080                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_hwiFxn)
                  000029f4    00000080     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_start)
                  00002a74    00000080                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_construct)
                  00002af4    00000080     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlSetRechargeBeforePowerDown)
                  00002b74    0000007a     rtsv7M4_T_le_v4SPD16_eabi.lib : memset_t2.asm.obj (.text)
                  00002bee    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:clkFxn)
                  00002bf0    00000078                     : ClockPTimer_nortos.oem4f (.text:ClockP_walkQueueDynamic)
                  00002c68    00000074     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_resetHw)
                  00002cdc    00000074                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_auxISR)
                  00002d50    00000070                      : PINCC26XX.oem4f (.text:PIN_add)
                  00002dc0    00000070     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:split)
                  00002e30    0000006c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipType)
                  00002e9c    00000068                   : osc.obj (.text:NOROM_OSCHF_SwitchToRcOscTurnOffXosc)
                  00002f04    00000068     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_post)
                  00002f6c    00000068                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_start)
                  00002fd4    00000068     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_lzss.c.obj (.text:decompress:lzss:__TI_decompress_lzss)
                  0000303c    00000064     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_control)
                  000030a0    00000062                      : UARTCC26XX.oem4f (.text:UARTCC26XX_swiIntFxn)
                  00003102    00000002                      : PowerCC26X2.oem4f (.text:emptyClockFunc)
                  00003104    00000060                      : GPIOCC26XX.oem4f (.text:GPIO_write)
                  00003164    0000005c                      : PINCC26XX.oem4f (.text:PIN_remove)
                  000031c0    0000005c                      : UARTCC26XX.oem4f (.text:startTxFifoEmptyClk)
                  0000321c    00000058                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_loadDMAControlTableEntry)
                  00003274    00000058                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_stop)
                  000032cc    00000058                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_initiateCalibration)
                  00003324    00000054                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_adjustRawValues)
                  00003378    00000054     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOff)
                  000033cc    00000050     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_construct)
                  0000341c    0000004e     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_close)
                  0000346a    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:faultISR)
                  0000346c    0000004c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_cleanADC)
                  000034b8    0000004c     ti_drivers_config.obj (.text:Board_wakeUpExtFlash)
                  00003504    0000004c     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_initHw)
                  00003550    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_start)
                  0000359c    0000004c     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOn)
                  000035e8    0000004c     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readCancel)
                  00003634    0000004c                      : UDMACC26XX.oem4f (.text:UDMACC26XX_open)
                  00003680    0000004a                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_control)
                  000036ca    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:intDefaultHandler)
                  000036cc    00000048     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertAdjustedToMicroVolts)
                  00003714    00000048     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_construct)
                  0000375c    00000048     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputEnable)
                  000037a4    00000048                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_RCOSC_clockFunc)
                  000037ec    00000048                      : PowerCC26X2.oem4f (.text:switchXOSCHF)
                  00003834    00000048                      : UARTCC26XX.oem4f (.text:writeFinishedDoCallback)
                  0000387c    00000046                      : RingBuf.oem4f (.text:RingBuf_put)
                  000038c2    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:nmiISR)
                  000038c4    00000044     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_init)
                  00003908    00000044     mainNew.obj (.text:InitUart)
                  0000394c    00000044     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetHwRevision)
                  00003990    00000044     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_swi)
                  000039d4    00000044     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_post)
                  00003a18    00000044                     : SwiP_nortos.oem4f (.text:SwiP_restore)
                  00003a5c    00000044     drivers_cc13x2.a : UART.oem4f (.text:UART_init)
                  00003aa0    00000044     rtsv7M4_T_le_v4SPD16_eabi.lib : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit:__TI_auto_init_nobinit_nopinit)
                  00003ae4    00000042     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_swiFxn)
                  00003b26    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : div0.asm.obj (.text)
                  00003b28    00000042     ti_drivers_config.obj (.text:Board_shutDownExtFlash)
                  00003b6a    00000006     driverlib.lib : cpu.obj (.text:NOROM_CPUdelay)
                  00003b70    00000042     SoundTX.obj (.text:SoundTransmit)
                  00003bb2    00000002     --HOLE-- [fill = 0]
                  00003bb4    00000040     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertCancel)
                  00003bf4    00000040     driverlib.lib : osc.obj (.text:NOROM_OSC_HPOSCRelativeFrequencyOffsetGet)
                  00003c34    00000040                   : timer.obj (.text:NOROM_TimerIntRegister)
                  00003c74    00000040     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_get)
                  00003cb4    00000040                      : UART.oem4f (.text:UART_open)
                  00003cf4    0000003c                      : PINCC26XX.oem4f (.text:PIN_setOutputValue)
                  00003d30    00000038                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_configDMA)
                  00003d68    00000038                      : PINCC26XX.oem4f (.text:PIN_setConfig)
                  00003da0    00000038     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCurrentTick)
                  00003dd8    00000038                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_initDevice)
                  00003e10    00000038     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:notify)
                  00003e48    00000038                      : UARTCC26XX.oem4f (.text:readData)
                  00003e80    00000038                      : UARTCC26XX.oem4f (.text:writeData)
                  00003eb8    00000036     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setNextTick)
                  00003eee    00000036     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_close)
                  00003f24    00000034     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicksUntilInterrupt)
                  00003f58    00000034     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XXSetLoadMatch)
                  00003f8c    00000034                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_configureDebugStall)
                  00003fc0    00000034     driverlib.lib : aux_sysif.obj (.text:NOROM_AUXSYSIFOpModeChange)
                  00003ff4    00000034                   : interrupt.obj (.text:NOROM_IntRegister)
                  00004028    00000034                   : sys_ctrl.obj (.text:NOROM_SysCtrlIdle)
                  0000405c    00000034     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_destruct)
                  00004090    00000034     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:readData2RingBuf)
                  000040c4    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_hwiIntFxn)
                  000040f4    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_setCallback)
                  00004124    00000030                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_close)
                  00004154    00000030                      : PINCC26XX.oem4f (.text:PINCC26XX_setMux)
                  00004184    00000030                      : PowerCC26X2.oem4f (.text:Power_releaseConstraint)
                  000041b4    00000030                      : PowerCC26X2.oem4f (.text:Power_setConstraint)
                  000041e4    00000030     driverlib.lib : timer.obj (.text:TimerIntNumberGet)
                  00004214    00000030     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.text:_c_int00:_c_int00)
                  00004244    0000002c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_acquireADCSemaphore)
                  00004270    0000002c                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_releaseADCSemaphore)
                  0000429c    0000002c     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setThreshold)
                  000042c8    0000002a     ti_drivers_config.obj (.text:Board_init)
                  000042f2    00000006     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_init)
                  000042f8    0000002a     SoundTX.obj (.text:TimerLoadSet)
                  00004322    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readPolling)
                  00004328    0000002a     SoundTX.obj (.text:TimerMatchSet)
                  00004352    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_writePolling)
                  00004358    0000002a     SoundTX.obj (.text:TimerPrescaleMatchSet)
                  00004382    00000006     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:delayUs)
                  00004388    0000002a     SoundTX.obj (.text:TimerPrescaleSet)
                  000043b2    00000002     --HOLE-- [fill = 0]
                  000043b4    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_registerNotify)
                  000043dc    00000028     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_insert)
                  00004404    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:oscillatorISR)
                  0000442c    00000026                      : List.oem4f (.text:List_put)
                  00004452    00000026                      : List.oem4f (.text:List_remove)
                  00004478    00000024     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicks)
                  0000449c    00000024                     : ClockPTimer_nortos.oem4f (.text:ClockP_scheduleNextTick)
                  000044c0    00000024                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_dispatch)
                  000044e4    00000024     driverlib.lib : osc.obj (.text:NOROM_OSCHF_TurnOnXosc)
                  00004508    00000024     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getMaxTicks)
                  0000452c    00000024     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:getTdcSemaphore)
                  00004550    00000020     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_add)
                  00004570    00000020     driverlib.lib : aux_adc.obj (.text:NOROM_AUXADCEnableSync)
                  00004590    00000020     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfgMux)
                  000045b0    00000020                      : PowerCC26X2.oem4f (.text:Power_unregisterNotify)
                  000045d0    00000020     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_put)
                  000045f0    00000020                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_dynamicStub)
                  00004610    00000020     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:threadSafeStdbyDisRelease)
                  00004630    00000020                      : UARTCC26XX.oem4f (.text:threadSafeStdbyDisSet)
                  00004650    0000001e                      : PINCC26XX.oem4f (.text:PIN_close)
                  0000466e    0000001e                      : PowerCC26X2.oem4f (.text:Power_getTransitionLatency)
                  0000468c    0000001e     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_constructBinary)
                  000046aa    0000001e                     : SwiP_nortos.oem4f (.text:SwiP_or)
                  000046c8    0000001e     SoundTX.obj (.text:TimerDisable)
                  000046e6    00000002     --HOLE-- [fill = 0]
                  000046e8    0000001e     SoundTX.obj (.text:TimerEnable)
                  00004706    00000002     --HOLE-- [fill = 0]
                  00004708    0000001c     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_destruct)
                  00004724    0000001c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipFamily)
                  00004740    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_hwi)
                  0000475c    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_setTimeout)
                  00004760    0000001c     SoundTX.obj (.text:PRCMLoadGet)
                  0000477c    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_disable)
                  00004798    0000001c     SoundTX.obj (.text:TimerMatchGet)
                  000047b4    0000001c     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_initHw)
                  000047d0    0000001c     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_remove)
                  000047ec    0000001a     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:PowerCC26XX_calibrate)
                  00004806    0000001a     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_get)
                  00004820    0000001a                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_createBinary)
                  0000483a    00000018                     : ClockPTimer_nortos.oem4f (.text:ClockP_destruct)
                  00004852    00000002     --HOLE-- [fill = 0]
                  00004854    00000018                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_inISR)
                  0000486c    00000018     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetPackageType)
                  00004884    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_idleFunc)
                  0000489c    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_enable)
                  000048a0    00000018     SoundTX.obj (.text:TimerIntEnable)
                  000048b8    00000018     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_startup)
                  000048d0    00000018     drivers_cc13x2.a : UART.oem4f (.text:UART_Params_init)
                  000048e8    00000018     rtsv7M4_T_le_v4SPD16_eabi.lib : args_main.c.obj (.text:_args_main)
                  00004900    00000018                                   : ll_mul_t2.asm.obj (.text)
                  00004918    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:switchToTCXO)
                  00004930    00000016     driverlib.lib : chipinfo.obj (.text:NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated)
                  00004946    00000016     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_create)
                  0000495c    00000016                     : SwiP_nortos.oem4f (.text:maxbit)
                  00004972    00000014     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_freqToCounts)
                  00004986    00000002     --HOLE-- [fill = 0]
                  00004988    00000014     driverlib.lib : interrupt.obj (.text:NOROM_IntUnregister)
                  0000499c    00000014     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_calibrate)
                  000049b0    00000014                      : PowerCC26X2.oem4f (.text:Power_disablePolicy)
                  000049c4    00000014     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_handleHwi)
                  000049d8    00000014                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCount64)
                  000049ec    00000014                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:resetISR)
                  00004a00    00000012     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_Params_init)
                  00004a12    00000012     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_Params_init)
                  00004a24    00000012     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_construct)
                  00004a36    00000002     --HOLE-- [fill = 0]
                  00004a38    00000012     SoundTX.obj (.text:TimerIntClear)
                  00004a4a    00000002     --HOLE-- [fill = 0]
                  00004a4c    00000010     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_Params_init)
                  00004a5c    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_getCpuFreq)
                  00004a6c    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_clearInterrupt)
                  00004a7c    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_disable)
                  00004a8c    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_post)
                  00004a9c    00000010     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_getPinCount)
                  00004aac    00000010     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerDisable)
                  00004abc    00000010     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_enablePolicy)
                  00004acc    00000010     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_Params_init)
                  00004adc    00000010                     : SwiP_nortos.oem4f (.text:SwiP_Params_init)
                  00004aec    00000010                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_Params_init)
                  00004afc    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:uartPostNotify)
                  00004b0c    00000004     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_head)
                  00004b10    0000000e     SoundTX.obj (.text:PRCMLoadSet)
                  00004b1e    0000000e     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_empty)
                  00004b2c    0000000e                     : QueueP_nortos.oem4f (.text:QueueP_remove)
                  00004b3a    0000000e     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_none.c.obj (.text:decompress:none:__TI_decompress_none)
                  00004b48    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_doTick)
                  00004b54    0000000c                     : ClockPTimer_nortos.oem4f (.text:ClockP_getSystemTickPeriod)
                  00004b60    0000000c     driverlib.lib : cpu.obj (.text:NOROM_CPUcpsid)
                  00004b6c    0000000c                   : cpu.obj (.text:NOROM_CPUcpsie)
                  00004b78    0000000c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_registerIntCb)
                  00004b84    0000000c     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerRestore)
                  00004b90    0000000c     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_getConstraintMask)
                  00004b9c    0000000c                      : PowerCC26X2.oem4f (.text:Power_getDependencyCount)
                  00004ba8    0000000c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_getTrigger)
                  00004bb4    0000000c                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getFreq)
                  00004bc0    0000000c     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init)
                  00004bcc    0000000a     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_convertCancel)
                  00004bd6    0000000a                      : UDMACC26XX.oem4f (.text:UDMACC26XX_hwiIntFxn)
                  00004be0    0000000a                      : UARTCC26XX.oem4f (.text:readSemCallback)
                  00004bea    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_conversionCallback)
                  00004bf2    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_init)
                  00004bfa    00000008                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_setLoadValue)
                  00004c02    00000008     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_restore)
                  00004c0a    00000004                     : QueueP_nortos.oem4f (.text:QueueP_next)
                  00004c0e    00000002     --HOLE-- [fill = 0]
                  00004c10    00000008     ADC.obj (.text:InitADC)
                  00004c18    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_init)
                  00004c20    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:malloc)
                  00004c28    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeSemCallback)
                  00004c30    00000004     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_delete)
                  00004c34    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : pre_init.c.obj (.text:_system_pre_init)
                  00004c38    00000004                                   : exit.c.obj (.text:abort:abort)
                  00004c3c    00000004     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureRFCoreClocks)
                  00004c40    00000004                      : PowerCC26X2.oem4f (.text:nopResourceHandler)
                  00004c44    00000004     --HOLE-- [fill = 0]
                  00004c48    00000002     ti_drivers_config.obj (.text:Board_initHook)

.const     0    00004c4c    00000298     
                  00004c4c    00000054     drivers_cc13x2.a : PowerCC26X2.oem4f (.const:resourceDB)
                  00004ca0    00000028     ti_drivers_config.obj (.const:BoardGpioInitTable)
                  00004cc8    00000028     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:UARTCC26XX_fxnTable)
                  00004cf0    00000028     ti_drivers_config.obj (.const:uartCC26XXHWAttrs)
                  00004d18    00000024     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.const:ADCBufCC26X2_fxnTable)
                  00004d3c    00000024                      : UART.oem4f (.const:UART_defaultParams)
                  00004d60    0000001c                      : GPTimerCC26XX.oem4f (.const:GPT_LUT)
                  00004d7c    00000018     ti_drivers_config.obj (.const:GPTimerCC26XX_config)
                  00004d94    00000018     ti_drivers_config.obj (.const:gptimerCC26XXHWAttrs)
                  00004dac    00000018     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:interruptType)
                  00004dc4    00000014     ti_drivers_config.obj (.const:$P$T1$2)
                  00004dd8    00000014     ti_drivers_config.obj (.const:GPIOCC26XX_config)
                  00004dec    00000014     ti_drivers_config.obj (.const:PowerCC26X2_config)
                  00004e00    00000014     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:parityType)
                  00004e14    00000010     nortos_cc13x2.a : SwiP_nortos.oem4f (.const:SwiP_defaultParams)
                  00004e24    00000010     ti_drivers_config.obj (.const:adcbufCC26XXHWAttrs)
                  00004e34    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:dataLength)
                  00004e44    00000010                      : GPIOCC26XX.oem4f (.const:outPinTypes)
                  00004e54    0000000e     ti_drivers_config.obj (.const)
                  00004e62    00000002     --HOLE-- [fill = 0]
                  00004e64    0000000c     ti_drivers_config.obj (.const:ADCBuf_config)
                  00004e70    0000000c     ti_drivers_config.obj (.const:UART_config)
                  00004e7c    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:inPinTypes)
                  00004e88    0000000c                      : GPIOCC26XX.oem4f (.const:outPinStrengths)
                  00004e94    00000008     driverlib.lib : aux_sysif.obj (.const:$O1$$)
                  00004e9c    00000008     ti_drivers_config.obj (.const:$P$T0$1)
                  00004ea4    00000008     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.const:GPT_DefaultParams)
                  00004eac    00000008     ti_drivers_config.obj (.const:PINCC26XX_hwAttrs)
                  00004eb4    00000008     ti_drivers_config.obj (.const:UDMACC26XX_config)
                  00004ebc    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:stopBits)
                  00004ec4    00000008     ti_drivers_config.obj (.const:udmaCC26XXHWAttrs)
                  00004ecc    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:.string:rxFifoBytes)
                  00004ed2    00000006                      : UARTCC26XX.oem4f (.const:.string:rxFifoThreshold)
                  00004ed8    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoBytes)
                  00004ede    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoThreshold)

.binit     0    00000000    00000000     

.cinit     0    00004ef0    000000e0     
                  00004ef0    00000088     (.cinit..data.load) [load image, compression = lzss]
                  00004f78    0000000c     (__TI_handler_table)
                  00004f84    00000004     --HOLE-- [fill = 0]
                  00004f88    00000008     (.cinit..TI.bound:dmaADCAltControlTableEntry.load) [load image, compression = zero_init]
                  00004f90    00000008     (.cinit..TI.bound:dmaADCPriControlTableEntry.load) [load image, compression = zero_init]
                  00004f98    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004fa0    00000008     (.cinit..vtable_ram.load) [load image, compression = zero_init]
                  00004fa8    00000028     (__TI_cinit_table)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.ccfg      0    00057fa8    00000058     
                  00057fa8    00000058     ti_devices_config.obj (.ccfg:retain)

.bss       0    20000000    00000604     UNINITIALIZED
                  20000000    000001b4     (.common:uartCC26XXObjects)
                  200001b4    000000dc     (.common:adcbufCC26XXbjects)
                  20000290    000000a0     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:ClockP_module)
                  20000330    00000098     (.common:gptimerCC26XXObjects)
                  200003c8    0000007c     (.common:pinHandleTable)
                  20000444    00000050     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinSemaphore)
                  20000494    00000034                      : PINCC26XX.oem4f (.bss:pinSwi)
                  200004c8    00000020     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_readyList)
                  200004e8    00000020     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioCallbackInfo)
                  20000508    00000020                      : PINCC26XX.oem4f (.bss:pinGpioConfigTable)
                  20000528    00000020     ti_drivers_config.obj (.bss:uartCC26XXRingBuffer0)
                  20000548    00000020     (.common:udmaCC26XXObject)
                  20000568    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_hwiStruct)
                  20000584    0000001c                     : TimerPCC26XX_nortos.oem4f (.bss:TimerP_hwiStruct)
                  200005a0    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinHwi)
                  200005bc    0000000d     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss)
                  200005c9    00000001     (.common:driverlib_release_0_59848)
                  200005ca    00000002     --HOLE--
                  200005cc    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioPinState)
                  200005d8    00000008                      : GPIOCC26XX.oem4f (.bss)
                  200005e0    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:timerFreq)
                  200005e8    00000004     drivers_cc13x2.a : PINCC26XX.oem4f (.bss)
                  200005ec    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss)
                  200005f0    00000004                     : PowerCC26X2_nortos.oem4f (.bss)
                  200005f4    00000004                     : TimerPCC26XX_nortos.oem4f (.bss)
                  200005f8    00000004     (.common:i)
                  200005fc    00000004     (.common:j)
                  20000600    00000004     (.common:uart)

.vtable_ram 
*          0    20000a00    000000d8     UNINITIALIZED
                  20000a00    000000d8     driverlib.lib : interrupt.obj (.vtable_ram)

.data      0    20000604    000003e6     UNINITIALIZED
                  20000604    00000170     drivers_cc13x2.a : PowerCC26X2.oem4f (.data:PowerCC26X2_module)
                  20000774    000000d8     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data:HwiP_dispatchTable)
                  2000084c    0000007c     SoundTX.obj (.data:ChirpDelay)
                  200008c8    0000007c     SoundTX.obj (.data:ChirpState)
                  20000944    00000024     driverlib.lib : osc.obj (.data:$O1$$)
                  20000968    00000010     ti_drivers_config.obj (.data:gpioCallbackFunctions)
                  20000978    00000010     ti_drivers_config.obj (.data:gpioPinConfigs)
                  20000988    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data:ClockP_defaultParams)
                  20000994    0000000c                     : TimerPCC26XX_nortos.oem4f (.data:TimerP_defaultParams)
                  200009a0    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.data:$O1$$)
                  200009a8    00000008                                   : memory.c.obj (.data:$O1$$)
                  200009b0    00000008     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data:SemaphoreP_defaultParams)
                  200009b8    00000008     SoundTX.obj (.data)
                  200009c0    00000008     drivers_cc13x2.a : PINCC26XX.oem4f (.data)
                  200009c8    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data)
                  200009d0    00000006     ti_drivers_config.obj (.data:adcBufCC26XXChannelLut0)
                  200009d6    00000001     drivers_cc13x2.a : ADCBuf.oem4f (.data)
                  200009d7    00000001                      : UART.oem4f (.data)
                  200009d8    00000005                      : GPIOCC26XX.oem4f (.data)
                  200009dd    00000003     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data)
                  200009e0    00000004     drivers_cc13x2.a : GPIOCC26XX.oem4f (.data:gpioPinTable)
                  200009e4    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data)
                  200009e8    00000001                     : SwiP_nortos.oem4f (.data)
                  200009e9    00000001                     : TimerPCC26XX_nortos.oem4f (.data)

.sysmem    0    20001a80    00004000     UNINITIALIZED
                  20001a80    00000010     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.sysmem)
                  20001a90    00003ff0     --HOLE--

.stack     0    20013c00    00000400     UNINITIALIZED
                  20013c00    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.stack)
                  20013c04    000003fc     --HOLE--

.log_data 
*          0    90000000    00000000     COPY SECTION

__llvm_prf_cnts 
*          0    20000000    00000000     UNINITIALIZED

.TI.bound:dmaADCPriControlTableEntry 
*          0    20001870    00000010     UNINITIALIZED
                  20001870    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCPriControlTableEntry)

.TI.bound:dmaADCAltControlTableEntry 
*          0    20001a70    00000010     UNINITIALIZED
                  20001a70    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCAltControlTableEntry)

.args      0    00004ee4    00000008     
                  00004ee4    00000008     --HOLE-- [fill = 0]

MODULE SUMMARY

       Module                             code    ro data   rw data
       ------                             ----    -------   -------
    .\
       SoundTX.obj                        856     0         256    
       mainNew.obj                        214     0         12     
       ADC.obj                            8       0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1078    0         268    
                                                                   
    .\syscfg\
       ti_drivers_config.obj              328     274       910    
       ti_devices_config.obj              0       88        0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             328     362       910    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos/lib/ccs/m4f/nortos_cc13x2.a
       ClockPTimer_nortos.oem4f           922     0         192    
       SwiP_nortos.oem4f                  724     16        74     
       TimerPCC26XX_nortos.oem4f          642     0         45     
       HwiPCC26XX_nortos.oem4f            294     0         220    
       SemaphoreP_nortos.oem4f            410     0         11     
       PowerCC26X2_nortos.oem4f           256     0         4      
       startup_cc13x2_cc26x2_ccs.oem4f    26      216       0      
       QueueP_nortos.oem4f                100     0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             3374    232       546    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/cc13x2_cc26x2/driverlib/bin/ccs/driverlib.lib
       sys_ctrl.obj                       420     0         0      
       osc.obj                            340     0         36     
       setup.obj                          324     0         0      
       interrupt.obj                      74      0         216    
       chipinfo.obj                       250     0         0      
       prcm.obj                           160     0         0      
       timer.obj                          112     0         0      
       aux_sysif.obj                      52      8         0      
       aux_adc.obj                        32      0         0      
       cpu.obj                            30      0         0      
       driverlib_release.obj              0       0         1      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1794    8         253    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/lib/ccs/m4f/drivers_cc13x2.a
       UARTCC26XX.oem4f                   2760    108       0      
       PowerCC26X2.oem4f                  2096    84        368    
       PINCC26XX.oem4f                    1554    0         328    
       ADCBufCC26X2.oem4f                 1542    36        32     
       PowerCC26X2_calibrateRCOSC.oem4f   1400    0         0      
       GPIOCC26XX.oem4f                   660     64        61     
       GPTimerCC26XX.oem4f                670     36        0      
       UART.oem4f                         156     36        1      
       UDMACC26XX.oem4f                   168     0         0      
       RingBuf.oem4f                      152     0         0      
       ADCBuf.oem4f                       78      0         1      
       List.oem4f                         76      0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             11312   364       791    
                                                                   
    C:\ti\ccs1011\ccs\tools\compiler\ti-cgt-arm_20.2.4.LTS\lib\rtsv7M4_T_le_v4SPD16_eabi.lib
       memory.c.obj                       672     0         8      
       memcpy_t2.asm.obj                  156     0         0      
       ull_div_t2.asm.obj                 150     0         0      
       memset_t2.asm.obj                  122     0         0      
       copy_decompress_lzss.c.obj         104     0         0      
       autoinit.c.obj                     68      0         0      
       boot_cortex_m.c.obj                48      0         0      
       args_main.c.obj                    24      0         0      
       ll_mul_t2.asm.obj                  24      0         0      
       copy_decompress_none.c.obj         14      0         0      
       copy_zero_init.c.obj               12      0         0      
       _lock.c.obj                        2       0         8      
       exit.c.obj                         4       0         0      
       pre_init.c.obj                     4       0         0      
       div0.asm.obj                       2       0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1406    0         16     
                                                                   
       Heap:                              0       0         16384  
       Stack:                             0       0         1024   
       Linker Generated:                  0       220       0      
    +--+----------------------------------+-------+---------+---------+
       Grand Total:                       19292   1186      20192  


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004fa8 records: 5, size/record: 8, table size: 40
	.data: load addr=00004ef0, load size=00000088 bytes, run addr=20000604, run size=000003e6 bytes, compression=lzss
	.TI.bound:dmaADCAltControlTableEntry: load addr=00004f88, load size=00000008 bytes, run addr=20001a70, run size=00000010 bytes, compression=zero_init
	.TI.bound:dmaADCPriControlTableEntry: load addr=00004f90, load size=00000008 bytes, run addr=20001870, run size=00000010 bytes, compression=zero_init
	.bss: load addr=00004f98, load size=00000008 bytes, run addr=20000000, run size=00000604 bytes, compression=zero_init
	.vtable_ram: load addr=00004fa0, load size=00000008 bytes, run addr=20000a00, run size=000000d8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004f78 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_lzss
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                                               
-------   ----                                                               
00004e57  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004e54  ADCBUF_CONST                                                       
00004e55  ADCBUF_SOUND_CONST                                                 
00004e56  ADCBUF_TEMPERATURE_CONST                                           
00003325  ADCBufCC26X2_adjustRawValues                                       
0000341d  ADCBufCC26X2_close                                                 
00003681  ADCBufCC26X2_control                                               
0000118d  ADCBufCC26X2_convert                                               
000036cd  ADCBufCC26X2_convertAdjustedToMicroVolts                           
00003bb5  ADCBufCC26X2_convertCancel                                         
00004d18  ADCBufCC26X2_fxnTable                                              
0000147d  ADCBufCC26X2_getResolution                                         
00004bf3  ADCBufCC26X2_init                                                  
0000076d  ADCBufCC26X2_open                                                  
00004e64  ADCBuf_config                                                      
00004bcd  ADCBuf_convertCancel                                               
00004e58  ADCBuf_count                                                       
000038c5  ADCBuf_init                                                        
00004ca0  BoardGpioInitTable                                                 
000042c9  Board_init                                                         
00004c49  Board_initHook                                                     
000025b9  Board_sendExtFlashByte                                             
00003b29  Board_shutDownExtFlash                                             
000034b9  Board_wakeUpExtFlash                                               
00004c39  C$$EXIT                                                            
00004e5f  CONFIG_GPTIMER_0_CONST                                             
00004e60  CONFIG_GPTIMER_1_CONST                                             
2000084c  ChirpDelay                                                         
200009bc  ChirpIndex                                                         
200009b8  ChirpSize                                                          
200008c8  ChirpState                                                         
00004a4d  ClockP_Params_init                                                 
00004551  ClockP_add                                                         
00003715  ClockP_construct                                                   
0000483b  ClockP_destruct                                                    
00004b49  ClockP_doTick                                                      
00004a5d  ClockP_getCpuFreq                                                  
00004b55  ClockP_getSystemTickPeriod                                         
00004479  ClockP_getTicks                                                    
00003f25  ClockP_getTicksUntilInterrupt                                      
0000251d  ClockP_isActive                                                    
0000449d  ClockP_scheduleNextTick                                            
0000475d  ClockP_setTimeout                                                  
000029f5  ClockP_start                                                       
00001e35  ClockP_startup                                                     
000025b3  ClockP_stop                                                        
200009cc  ClockP_tickPeriod                                                  
00002bf1  ClockP_walkQueueDynamic                                            
00001ee9  ClockP_workFuncDynamic                                             
00004dd8  GPIOCC26XX_config                                                  
000040c5  GPIO_hwiIntFxn                                                     
000019c5  GPIO_init                                                          
000040f5  GPIO_setCallback                                                   
0000107d  GPIO_setConfig                                                     
00003105  GPIO_write                                                         
00004a01  GPTimerCC26XX_Params_init                                          
00004125  GPTimerCC26XX_close                                                
00004d7c  GPTimerCC26XX_config                                               
00003f8d  GPTimerCC26XX_configureDebugStall                                  
00002649  GPTimerCC26XX_open                                                 
00004bfb  GPTimerCC26XX_setLoadValue                                         
00003551  GPTimerCC26XX_start                                                
00003275  GPTimerCC26XX_stop                                                 
00004e61  GPTimer_count                                                      
00004a13  HwiP_Params_init                                                   
00004a6d  HwiP_clearInterrupt                                                
00002a75  HwiP_construct                                                     
00004709  HwiP_destruct                                                      
00004a7d  HwiP_disable                                                       
0000489d  HwiP_enable                                                        
00004855  HwiP_inISR                                                         
00004a8d  HwiP_post                                                          
00004c03  HwiP_restore                                                       
200009e4  HwiP_swiPIntNum                                                    
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
00004c11  InitADC                                                            
00001811  InitTimer2                                                         
00003909  InitUart                                                           
0000442d  List_put                                                           
00004453  List_remove                                                        
00004571  NOROM_AUXADCEnableSync                                             
00003fc1  NOROM_AUXSYSIFOpModeChange                                         
00004b61  NOROM_CPUcpsid                                                     
00004b6d  NOROM_CPUcpsie                                                     
00003b6b  NOROM_CPUdelay                                                     
00004725  NOROM_ChipInfo_GetChipFamily                                       
00002e31  NOROM_ChipInfo_GetChipType                                         
0000394d  NOROM_ChipInfo_GetHwRevision                                       
0000486d  NOROM_ChipInfo_GetPackageType                                      
00003ff5  NOROM_IntRegister                                                  
00004989  NOROM_IntUnregister                                                
000026d1  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002e9d  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
000044e5  NOROM_OSCHF_TurnOnXosc                                             
00003bf5  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00003379  NOROM_PRCMPowerDomainsAllOff                                       
0000359d  NOROM_PRCMPowerDomainsAllOn                                        
000022c5  NOROM_SetupTrimDevice                                              
00004029  NOROM_SysCtrlIdle                                                  
00002af5  NOROM_SysCtrlSetRechargeBeforePowerDown                            
0000138d  NOROM_SysCtrlStandby                                               
00004931  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00003c35  NOROM_TimerIntRegister                                             
0000489d  NoRTOS_start                                                       
00004a9d  PINCC26XX_getPinCount                                              
00004eac  PINCC26XX_hwAttrs                                                  
00004155  PINCC26XX_setMux                                                   
00004e5b  PIN_DRIVE_SPEAKER_A_CONST                                          
00004e5c  PIN_DRIVE_SPEAKER_B_CONST                                          
00004e59  PIN_TEST1_CONST                                                    
00004e5a  PIN_TEST2_CONST                                                    
00002d51  PIN_add                                                            
00004651  PIN_close                                                          
000000d9  PIN_init                                                           
00001f95  PIN_open                                                           
00004b79  PIN_registerIntCb                                                  
00003165  PIN_remove                                                         
00003d69  PIN_setConfig                                                      
0000375d  PIN_setOutputEnable                                                
00003cf5  PIN_setOutputValue                                                 
000037a5  PowerCC26X2_RCOSC_clockFunc                                        
00002cdd  PowerCC26X2_auxISR                                                 
0000499d  PowerCC26X2_calibrate                                              
00004dec  PowerCC26X2_config                                                 
000032cd  PowerCC26X2_initiateCalibration                                    
20000604  PowerCC26X2_module                                                 
000047ed  PowerCC26XX_calibrate                                              
00004aad  PowerCC26XX_schedulerDisable                                       
00004b85  PowerCC26XX_schedulerRestore                                       
0000164d  PowerCC26XX_standbyPolicy                                          
000049b1  Power_disablePolicy                                                
00004abd  Power_enablePolicy                                                 
00004b91  Power_getConstraintMask                                            
00004b9d  Power_getDependencyCount                                           
0000466f  Power_getTransitionLatency                                         
00004885  Power_idleFunc                                                     
000008e1  Power_init                                                         
000043b5  Power_registerNotify                                               
00004185  Power_releaseConstraint                                            
00001a89  Power_releaseDependency                                            
000041b5  Power_setConstraint                                                
00001cc5  Power_setDependency                                                
000002b9  Power_sleep                                                        
000045b1  Power_unregisterNotify                                             
00004b1f  QueueP_empty                                                       
00004807  QueueP_get                                                         
00004b0d  QueueP_head                                                        
000042f3  QueueP_init                                                        
00004c0b  QueueP_next                                                        
000045d1  QueueP_put                                                         
00004b2d  QueueP_remove                                                      
00004a25  RingBuf_construct                                                  
00003c75  RingBuf_get                                                        
0000387d  RingBuf_put                                                        
00004acd  SemaphoreP_Params_init                                             
000033cd  SemaphoreP_construct                                               
0000468d  SemaphoreP_constructBinary                                         
00004947  SemaphoreP_create                                                  
00004821  SemaphoreP_createBinary                                            
200009b0  SemaphoreP_defaultParams                                           
00004c31  SemaphoreP_delete                                                  
00001567  SemaphoreP_destruct                                                
000020ed  SemaphoreP_pend                                                    
000039d5  SemaphoreP_post                                                    
00003b71  SoundTransmit                                                      
00004add  SwiP_Params_init                                                   
000018ed  SwiP_construct                                                     
0000405d  SwiP_destruct                                                      
0000477d  SwiP_disable                                                       
0000218d  SwiP_dispatch                                                      
00004ba9  SwiP_getTrigger                                                    
000046ab  SwiP_or                                                            
00002f05  SwiP_post                                                          
00003a19  SwiP_restore                                                       
00001481  Timer2AInterruptHandler                                            
00004aed  TimerP_Params_init                                                 
00001c09  TimerP_construct                                                   
000045f1  TimerP_dynamicStub                                                 
000049d9  TimerP_getCount64                                                  
00003da1  TimerP_getCurrentTick                                              
00004bb5  TimerP_getFreq                                                     
00004509  TimerP_getMaxTicks                                                 
00003dd9  TimerP_initDevice                                                  
00003eb9  TimerP_setNextTick                                                 
0000429d  TimerP_setThreshold                                                
00002f6d  TimerP_start                                                       
000048b9  TimerP_startup                                                     
000028f1  UARTCC26XX_close                                                   
0000303d  UARTCC26XX_control                                                 
00004cc8  UARTCC26XX_fxnTable                                                
00000bb9  UARTCC26XX_hwiIntFxn                                               
00004c19  UARTCC26XX_init                                                    
00000a55  UARTCC26XX_open                                                    
00000d0d  UARTCC26XX_read                                                    
000035e9  UARTCC26XX_readCancel                                              
00004323  UARTCC26XX_readPolling                                             
000030a1  UARTCC26XX_swiIntFxn                                               
0000172d  UARTCC26XX_write                                                   
0000248b  UARTCC26XX_writeCancel                                             
00004353  UARTCC26XX_writePolling                                            
00004e5d  UART_0_CONST                                                       
000048d1  UART_Params_init                                                   
00004e70  UART_config                                                        
00004e5e  UART_count                                                         
00004d3c  UART_defaultParams                                                 
00003a5d  UART_init                                                          
00003cb5  UART_open                                                          
00003eef  UDMACC26XX_close                                                   
00004eb4  UDMACC26XX_config                                                  
00004bd7  UDMACC26XX_hwiIntFxn                                               
00003635  UDMACC26XX_open                                                    
20014000  __STACK_END                                                        
00000400  __STACK_SIZE                                                       
00004000  __SYSMEM_SIZE                                                      
00004fa8  __TI_CINIT_Base                                                    
00004fd0  __TI_CINIT_Limit                                                   
00004f78  __TI_Handler_Table_Base                                            
00004f84  __TI_Handler_Table_Limit                                           
00003aa1  __TI_auto_init_nobinit_nopinit                                     
00002fd5  __TI_decompress_lzss                                               
00004b3b  __TI_decompress_none                                               
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
00000000  __TI_static_base__                                                 
00004bc1  __TI_zero_init                                                     
00003b27  __aeabi_idiv0                                                      
00003b27  __aeabi_ldiv0                                                      
00004901  __aeabi_lmul                                                       
00002b75  __aeabi_memclr                                                     
00002b75  __aeabi_memclr4                                                    
00002b75  __aeabi_memclr8                                                    
00002229  __aeabi_memcpy                                                     
00002229  __aeabi_memcpy4                                                    
00002229  __aeabi_memcpy8                                                    
00002b77  __aeabi_memset                                                     
00002b77  __aeabi_memset4                                                    
00002b77  __aeabi_memset8                                                    
000023f5  __aeabi_uldivmod                                                   
ffffffff  __binit__                                                          
00004ee4  __c_args__                                                         
00057fa8  __ccfg                                                             
UNDEFED   __mpu_init                                                         
20013c00  __stack                                                            
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
000048e9  _args_main                                                         
00004215  _c_int00                                                           
20000944  _hposcCoeffs                                                       
200009a0  _lock                                                              
00002647  _nop                                                               
20001a80  _sys_memory                                                        
UNDEFED   _system_post_cinit                                                 
00004c35  _system_pre_init                                                   
200009a4  _unlock                                                            
00004c39  abort                                                              
200009d0  adcBufCC26XXChannelLut0                                            
00004e24  adcbufCC26XXHWAttrs                                                
200001b4  adcbufCC26XXbjects                                                 
0000128d  aligned_alloc                                                      
ffffffff  binit                                                              
00002bef  clkFxn                                                             
200005c9  driverlib_release_0_59848                                          
00001569  free                                                               
20000a00  g_pfnRAMVectors                                                    
00000000  g_pfnVectors                                                       
20000968  gpioCallbackFunctions                                              
20000978  gpioPinConfigs                                                     
00004d94  gptimerCC26XXHWAttrs                                               
20000330  gptimerCC26XXObjects                                               
200005f8  i                                                                  
00004e7c  inPinTypes                                                         
200005fc  j                                                                  
00002521  main                                                               
00004c21  malloc                                                             
0000128d  memalign                                                           
00002229  memcpy                                                             
00002b7d  memset                                                             
00004e88  outPinStrengths                                                    
00004e44  outPinTypes                                                        
200003c8  pinHandleTable                                                     
200009c4  pinLowerBound                                                      
200009c0  pinUpperBound                                                      
000049ed  resetISR                                                           
00004c4c  resourceDB                                                         
20000600  uart                                                               
20000000  uartCC26XXObjects                                                  
00004ec4  udmaCC26XXHWAttrs                                                  
20000548  udmaCC26XXObject                                                   


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                                               
-------   ----                                                               
00000000  __TI_static_base__                                                 
00000000  g_pfnVectors                                                       
000000d9  PIN_init                                                           
000002b9  Power_sleep                                                        
00000400  __STACK_SIZE                                                       
0000076d  ADCBufCC26X2_open                                                  
000008e1  Power_init                                                         
00000a55  UARTCC26XX_open                                                    
00000bb9  UARTCC26XX_hwiIntFxn                                               
00000d0d  UARTCC26XX_read                                                    
0000107d  GPIO_setConfig                                                     
0000118d  ADCBufCC26X2_convert                                               
0000128d  aligned_alloc                                                      
0000128d  memalign                                                           
0000138d  NOROM_SysCtrlStandby                                               
0000147d  ADCBufCC26X2_getResolution                                         
00001481  Timer2AInterruptHandler                                            
00001567  SemaphoreP_destruct                                                
00001569  free                                                               
0000164d  PowerCC26XX_standbyPolicy                                          
0000172d  UARTCC26XX_write                                                   
00001811  InitTimer2                                                         
000018ed  SwiP_construct                                                     
000019c5  GPIO_init                                                          
00001a89  Power_releaseDependency                                            
00001c09  TimerP_construct                                                   
00001cc5  Power_setDependency                                                
00001e35  ClockP_startup                                                     
00001ee9  ClockP_workFuncDynamic                                             
00001f95  PIN_open                                                           
000020ed  SemaphoreP_pend                                                    
0000218d  SwiP_dispatch                                                      
00002229  __aeabi_memcpy                                                     
00002229  __aeabi_memcpy4                                                    
00002229  __aeabi_memcpy8                                                    
00002229  memcpy                                                             
000022c5  NOROM_SetupTrimDevice                                              
000023f5  __aeabi_uldivmod                                                   
0000248b  UARTCC26XX_writeCancel                                             
0000251d  ClockP_isActive                                                    
00002521  main                                                               
000025b3  ClockP_stop                                                        
000025b9  Board_sendExtFlashByte                                             
00002647  _nop                                                               
00002649  GPTimerCC26XX_open                                                 
000026d1  NOROM_OSCHF_AttemptToSwitchToXosc                                  
000028f1  UARTCC26XX_close                                                   
000029f5  ClockP_start                                                       
00002a75  HwiP_construct                                                     
00002af5  NOROM_SysCtrlSetRechargeBeforePowerDown                            
00002b75  __aeabi_memclr                                                     
00002b75  __aeabi_memclr4                                                    
00002b75  __aeabi_memclr8                                                    
00002b77  __aeabi_memset                                                     
00002b77  __aeabi_memset4                                                    
00002b77  __aeabi_memset8                                                    
00002b7d  memset                                                             
00002bef  clkFxn                                                             
00002bf1  ClockP_walkQueueDynamic                                            
00002cdd  PowerCC26X2_auxISR                                                 
00002d51  PIN_add                                                            
00002e31  NOROM_ChipInfo_GetChipType                                         
00002e9d  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
00002f05  SwiP_post                                                          
00002f6d  TimerP_start                                                       
00002fd5  __TI_decompress_lzss                                               
0000303d  UARTCC26XX_control                                                 
000030a1  UARTCC26XX_swiIntFxn                                               
00003105  GPIO_write                                                         
00003165  PIN_remove                                                         
00003275  GPTimerCC26XX_stop                                                 
000032cd  PowerCC26X2_initiateCalibration                                    
00003325  ADCBufCC26X2_adjustRawValues                                       
00003379  NOROM_PRCMPowerDomainsAllOff                                       
000033cd  SemaphoreP_construct                                               
0000341d  ADCBufCC26X2_close                                                 
000034b9  Board_wakeUpExtFlash                                               
00003551  GPTimerCC26XX_start                                                
0000359d  NOROM_PRCMPowerDomainsAllOn                                        
000035e9  UARTCC26XX_readCancel                                              
00003635  UDMACC26XX_open                                                    
00003681  ADCBufCC26X2_control                                               
000036cd  ADCBufCC26X2_convertAdjustedToMicroVolts                           
00003715  ClockP_construct                                                   
0000375d  PIN_setOutputEnable                                                
000037a5  PowerCC26X2_RCOSC_clockFunc                                        
0000387d  RingBuf_put                                                        
000038c5  ADCBuf_init                                                        
00003909  InitUart                                                           
0000394d  NOROM_ChipInfo_GetHwRevision                                       
000039d5  SemaphoreP_post                                                    
00003a19  SwiP_restore                                                       
00003a5d  UART_init                                                          
00003aa1  __TI_auto_init_nobinit_nopinit                                     
00003b27  __aeabi_idiv0                                                      
00003b27  __aeabi_ldiv0                                                      
00003b29  Board_shutDownExtFlash                                             
00003b6b  NOROM_CPUdelay                                                     
00003b71  SoundTransmit                                                      
00003bb5  ADCBufCC26X2_convertCancel                                         
00003bf5  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00003c35  NOROM_TimerIntRegister                                             
00003c75  RingBuf_get                                                        
00003cb5  UART_open                                                          
00003cf5  PIN_setOutputValue                                                 
00003d69  PIN_setConfig                                                      
00003da1  TimerP_getCurrentTick                                              
00003dd9  TimerP_initDevice                                                  
00003eb9  TimerP_setNextTick                                                 
00003eef  UDMACC26XX_close                                                   
00003f25  ClockP_getTicksUntilInterrupt                                      
00003f8d  GPTimerCC26XX_configureDebugStall                                  
00003fc1  NOROM_AUXSYSIFOpModeChange                                         
00003ff5  NOROM_IntRegister                                                  
00004000  __SYSMEM_SIZE                                                      
00004029  NOROM_SysCtrlIdle                                                  
0000405d  SwiP_destruct                                                      
000040c5  GPIO_hwiIntFxn                                                     
000040f5  GPIO_setCallback                                                   
00004125  GPTimerCC26XX_close                                                
00004155  PINCC26XX_setMux                                                   
00004185  Power_releaseConstraint                                            
000041b5  Power_setConstraint                                                
00004215  _c_int00                                                           
0000429d  TimerP_setThreshold                                                
000042c9  Board_init                                                         
000042f3  QueueP_init                                                        
00004323  UARTCC26XX_readPolling                                             
00004353  UARTCC26XX_writePolling                                            
000043b5  Power_registerNotify                                               
0000442d  List_put                                                           
00004453  List_remove                                                        
00004479  ClockP_getTicks                                                    
0000449d  ClockP_scheduleNextTick                                            
000044e5  NOROM_OSCHF_TurnOnXosc                                             
00004509  TimerP_getMaxTicks                                                 
00004551  ClockP_add                                                         
00004571  NOROM_AUXADCEnableSync                                             
000045b1  Power_unregisterNotify                                             
000045d1  QueueP_put                                                         
000045f1  TimerP_dynamicStub                                                 
00004651  PIN_close                                                          
0000466f  Power_getTransitionLatency                                         
0000468d  SemaphoreP_constructBinary                                         
000046ab  SwiP_or                                                            
00004709  HwiP_destruct                                                      
00004725  NOROM_ChipInfo_GetChipFamily                                       
0000475d  ClockP_setTimeout                                                  
0000477d  SwiP_disable                                                       
000047ed  PowerCC26XX_calibrate                                              
00004807  QueueP_get                                                         
00004821  SemaphoreP_createBinary                                            
0000483b  ClockP_destruct                                                    
00004855  HwiP_inISR                                                         
0000486d  NOROM_ChipInfo_GetPackageType                                      
00004885  Power_idleFunc                                                     
0000489d  HwiP_enable                                                        
0000489d  NoRTOS_start                                                       
000048b9  TimerP_startup                                                     
000048d1  UART_Params_init                                                   
000048e9  _args_main                                                         
00004901  __aeabi_lmul                                                       
00004931  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00004947  SemaphoreP_create                                                  
00004989  NOROM_IntUnregister                                                
0000499d  PowerCC26X2_calibrate                                              
000049b1  Power_disablePolicy                                                
000049d9  TimerP_getCount64                                                  
000049ed  resetISR                                                           
00004a01  GPTimerCC26XX_Params_init                                          
00004a13  HwiP_Params_init                                                   
00004a25  RingBuf_construct                                                  
00004a4d  ClockP_Params_init                                                 
00004a5d  ClockP_getCpuFreq                                                  
00004a6d  HwiP_clearInterrupt                                                
00004a7d  HwiP_disable                                                       
00004a8d  HwiP_post                                                          
00004a9d  PINCC26XX_getPinCount                                              
00004aad  PowerCC26XX_schedulerDisable                                       
00004abd  Power_enablePolicy                                                 
00004acd  SemaphoreP_Params_init                                             
00004add  SwiP_Params_init                                                   
00004aed  TimerP_Params_init                                                 
00004b0d  QueueP_head                                                        
00004b1f  QueueP_empty                                                       
00004b2d  QueueP_remove                                                      
00004b3b  __TI_decompress_none                                               
00004b49  ClockP_doTick                                                      
00004b55  ClockP_getSystemTickPeriod                                         
00004b61  NOROM_CPUcpsid                                                     
00004b6d  NOROM_CPUcpsie                                                     
00004b79  PIN_registerIntCb                                                  
00004b85  PowerCC26XX_schedulerRestore                                       
00004b91  Power_getConstraintMask                                            
00004b9d  Power_getDependencyCount                                           
00004ba9  SwiP_getTrigger                                                    
00004bb5  TimerP_getFreq                                                     
00004bc1  __TI_zero_init                                                     
00004bcd  ADCBuf_convertCancel                                               
00004bd7  UDMACC26XX_hwiIntFxn                                               
00004bf3  ADCBufCC26X2_init                                                  
00004bfb  GPTimerCC26XX_setLoadValue                                         
00004c03  HwiP_restore                                                       
00004c0b  QueueP_next                                                        
00004c11  InitADC                                                            
00004c19  UARTCC26XX_init                                                    
00004c21  malloc                                                             
00004c31  SemaphoreP_delete                                                  
00004c35  _system_pre_init                                                   
00004c39  C$$EXIT                                                            
00004c39  abort                                                              
00004c49  Board_initHook                                                     
00004c4c  resourceDB                                                         
00004ca0  BoardGpioInitTable                                                 
00004cc8  UARTCC26XX_fxnTable                                                
00004d18  ADCBufCC26X2_fxnTable                                              
00004d3c  UART_defaultParams                                                 
00004d7c  GPTimerCC26XX_config                                               
00004d94  gptimerCC26XXHWAttrs                                               
00004dd8  GPIOCC26XX_config                                                  
00004dec  PowerCC26X2_config                                                 
00004e24  adcbufCC26XXHWAttrs                                                
00004e44  outPinTypes                                                        
00004e54  ADCBUF_CONST                                                       
00004e55  ADCBUF_SOUND_CONST                                                 
00004e56  ADCBUF_TEMPERATURE_CONST                                           
00004e57  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004e58  ADCBuf_count                                                       
00004e59  PIN_TEST1_CONST                                                    
00004e5a  PIN_TEST2_CONST                                                    
00004e5b  PIN_DRIVE_SPEAKER_A_CONST                                          
00004e5c  PIN_DRIVE_SPEAKER_B_CONST                                          
00004e5d  UART_0_CONST                                                       
00004e5e  UART_count                                                         
00004e5f  CONFIG_GPTIMER_0_CONST                                             
00004e60  CONFIG_GPTIMER_1_CONST                                             
00004e61  GPTimer_count                                                      
00004e64  ADCBuf_config                                                      
00004e70  UART_config                                                        
00004e7c  inPinTypes                                                         
00004e88  outPinStrengths                                                    
00004eac  PINCC26XX_hwAttrs                                                  
00004eb4  UDMACC26XX_config                                                  
00004ec4  udmaCC26XXHWAttrs                                                  
00004ee4  __c_args__                                                         
00004f78  __TI_Handler_Table_Base                                            
00004f84  __TI_Handler_Table_Limit                                           
00004fa8  __TI_CINIT_Base                                                    
00004fd0  __TI_CINIT_Limit                                                   
00057fa8  __ccfg                                                             
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
20000000  uartCC26XXObjects                                                  
200001b4  adcbufCC26XXbjects                                                 
20000330  gptimerCC26XXObjects                                               
200003c8  pinHandleTable                                                     
20000548  udmaCC26XXObject                                                   
200005c9  driverlib_release_0_59848                                          
200005f8  i                                                                  
200005fc  j                                                                  
20000600  uart                                                               
20000604  PowerCC26X2_module                                                 
2000084c  ChirpDelay                                                         
200008c8  ChirpState                                                         
20000944  _hposcCoeffs                                                       
20000968  gpioCallbackFunctions                                              
20000978  gpioPinConfigs                                                     
200009a0  _lock                                                              
200009a4  _unlock                                                            
200009b0  SemaphoreP_defaultParams                                           
200009b8  ChirpSize                                                          
200009bc  ChirpIndex                                                         
200009c0  pinUpperBound                                                      
200009c4  pinLowerBound                                                      
200009cc  ClockP_tickPeriod                                                  
200009d0  adcBufCC26XXChannelLut0                                            
200009e4  HwiP_swiPIntNum                                                    
20000a00  g_pfnRAMVectors                                                    
20001a80  _sys_memory                                                        
20013c00  __stack                                                            
20014000  __STACK_END                                                        
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
ffffffff  __binit__                                                          
ffffffff  binit                                                              
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
UNDEFED   __mpu_init                                                         
UNDEFED   _system_post_cinit                                                 

[290 symbols]
