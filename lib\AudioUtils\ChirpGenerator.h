#pragma once
#include "AudioUtils.h"
#include <cstdint>
#include <cstddef>

class ChirpGenerator {
public:
    ChirpGenerator();
    const int16_t* generate();
    std::size_t size() const;
    void transmitChirp(void (*outputFunction)(int16_t));
    
private:
    static constexpr uint32_t NUM_SAMPLES = calcSamples(CHIRP_DURATION_MS);
    int16_t chirp[NUM_SAMPLES];
    bool generated = false;
    
    // TI-style chirp parameters
    static constexpr int16_t CHIRP_AMPLITUDE = 30000;
};

// Static array definitions must be outside the class
inline constexpr int16_t CHIRP_STATES[31] = { 
    1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,0 
};

inline constexpr int16_t CHIRP_DELAYS_US[31] = { 
    800,533,800,533,800,800,800,800,800,1067,800,1067,1067,1067,1067,1333,1067,1333,1067,
    1333,1333,1333,1333,1600,1333,1600,1333,1600,1600,1600,1000 
};
