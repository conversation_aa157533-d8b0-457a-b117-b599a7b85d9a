<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: BluetoothA2DPOutputDefault Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-attribs">Protected Attributes</a> &#124;
<a href="class_bluetooth_a2_d_p_output_default-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">BluetoothA2DPOutputDefault Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Default Output Class providing both the Legacy I2S and the AudioTools I2S functionality.  
 <a href="class_bluetooth_a2_d_p_output_default.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_bluetooth_a2_d_p_output_8h_source.html">BluetoothA2DPOutput.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for BluetoothA2DPOutputDefault:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_bluetooth_a2_d_p_output_default.png" usemap="#BluetoothA2DPOutputDefault_map" alt=""/>
  <map id="BluetoothA2DPOutputDefault_map" name="BluetoothA2DPOutputDefault_map">
<area href="class_bluetooth_a2_d_p_output.html" title="Abstract Output Class." alt="BluetoothA2DPOutput" shape="rect" coords="0,0,174,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aa813d6b6dfdb816c10faad14e87f401d"><td class="memItemLeft" align="right" valign="top"><a id="aa813d6b6dfdb816c10faad14e87f401d"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>begin</b> ()</td></tr>
<tr class="separator:aa813d6b6dfdb816c10faad14e87f401d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a151135e63fdff09d8e21b3582057f783"><td class="memItemLeft" align="right" valign="top"><a id="a151135e63fdff09d8e21b3582057f783"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>end</b> () override</td></tr>
<tr class="separator:a151135e63fdff09d8e21b3582057f783"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a69efd35a5b96a8c4ea45286ec02cb550"><td class="memItemLeft" align="right" valign="top"><a id="a69efd35a5b96a8c4ea45286ec02cb550"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_output_default.html#a69efd35a5b96a8c4ea45286ec02cb550">set_output</a> (audio_tools::AudioOutput &amp;output) override</td></tr>
<tr class="memdesc:a69efd35a5b96a8c4ea45286ec02cb550"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output AudioStream using AudioTools library. <br /></td></tr>
<tr class="separator:a69efd35a5b96a8c4ea45286ec02cb550"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74df73b612e2c4b99956e367d9f69656"><td class="memItemLeft" align="right" valign="top"><a id="a74df73b612e2c4b99956e367d9f69656"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_output_default.html#a74df73b612e2c4b99956e367d9f69656">set_output</a> (audio_tools::AudioStream &amp;output) override</td></tr>
<tr class="memdesc:a74df73b612e2c4b99956e367d9f69656"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output AudioStream using AudioTools library. <br /></td></tr>
<tr class="separator:a74df73b612e2c4b99956e367d9f69656"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad03aa92e57182e2a24eb3596fe5461ee"><td class="memItemLeft" align="right" valign="top"><a id="ad03aa92e57182e2a24eb3596fe5461ee"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_output_default.html#ad03aa92e57182e2a24eb3596fe5461ee">set_output</a> (Print &amp;output) override</td></tr>
<tr class="memdesc:ad03aa92e57182e2a24eb3596fe5461ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output to Arduino Print. <br /></td></tr>
<tr class="separator:ad03aa92e57182e2a24eb3596fe5461ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae4ae1de1538f9470f888562013d15de9"><td class="memItemLeft" align="right" valign="top"><a id="ae4ae1de1538f9470f888562013d15de9"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_output_active</b> (bool active) override</td></tr>
<tr class="separator:ae4ae1de1538f9470f888562013d15de9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a81288588e336b1ad85555973b027424f"><td class="memItemLeft" align="right" valign="top"><a id="a81288588e336b1ad85555973b027424f"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_sample_rate</b> (int rate) override</td></tr>
<tr class="separator:a81288588e336b1ad85555973b027424f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a018f4fee7dde7d234de08d12b4b39a2e"><td class="memItemLeft" align="right" valign="top"><a id="a018f4fee7dde7d234de08d12b4b39a2e"></a>
size_t&#160;</td><td class="memItemRight" valign="bottom"><b>write</b> (const uint8_t *data, size_t len)</td></tr>
<tr class="separator:a018f4fee7dde7d234de08d12b4b39a2e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-attribs"></a>
Protected Attributes</h2></td></tr>
<tr class="memitem:acfeaee69a33c77ed615d039782125d4d"><td class="memItemLeft" align="right" valign="top"><a id="acfeaee69a33c77ed615d039782125d4d"></a>
<a class="el" href="class_bluetooth_a2_d_p_output_legacy.html">BluetoothA2DPOutputLegacy</a>&#160;</td><td class="memItemRight" valign="bottom"><b>out_legacy</b></td></tr>
<tr class="separator:acfeaee69a33c77ed615d039782125d4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac64e70f029d282e19a36cae336f32fc0"><td class="memItemLeft" align="right" valign="top"><a id="ac64e70f029d282e19a36cae336f32fc0"></a>
<a class="el" href="class_bluetooth_a2_d_p_output_audio_tools.html">BluetoothA2DPOutputAudioTools</a>&#160;</td><td class="memItemRight" valign="bottom"><b>out_tools</b></td></tr>
<tr class="separator:ac64e70f029d282e19a36cae336f32fc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Default Output Class providing both the Legacy I2S and the AudioTools I2S functionality. </p>
<dl class="section author"><dt>Author</dt><dd>Phil Schatzmann </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Apache License Version 2 </dd></dl>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li>src/<a class="el" href="_bluetooth_a2_d_p_output_8h_source.html">BluetoothA2DPOutput.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
