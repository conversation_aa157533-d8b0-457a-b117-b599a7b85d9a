# FIXED

ControlCenter.obj: ../ControlCenter.c
ControlCenter.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Devicetype.h
ControlCenter.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdint.h
ControlCenter.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_ti_config.h
ControlCenter.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/linkage.h
ControlCenter.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_stdint40.h
ControlCenter.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/stdint.h
ControlCenter.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/cdefs.h
ControlCenter.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_types.h
ControlCenter.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_types.h
ControlCenter.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_stdint.h
ControlCenter.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_stdint.h
ControlCenter.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stddef.h
ControlCenter.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdio.h
ControlCenter.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdarg.h
ControlCenter.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Serial.h
ControlCenter.obj: ../time.h
ControlCenter.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/SoundTransmit.h
ControlCenter.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/WirelessCommunication.h
ControlCenter.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Debug/syscfg/ti_drivers_config.h
ControlCenter.obj: C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/DeviceFamily.h
ControlCenter.obj: C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/PIN.h
ControlCenter.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdbool.h
ControlCenter.obj: ../SoundReceive.h
ControlCenter.obj: ../Temperature.h
ControlCenter.obj: ../Batteryvoltage.h
ControlCenter.obj: ../ControlCenter.h
ControlCenter.obj: ../AnalyseAndStoreMeasurements.h

../ControlCenter.c: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Devicetype.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_ti_config.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/linkage.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_stdint40.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/cdefs.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_types.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_types.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stddef.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdio.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdarg.h: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Serial.h: 
../time.h: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/SoundTransmit.h: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/WirelessCommunication.h: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Debug/syscfg/ti_drivers_config.h: 
C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/DeviceFamily.h: 
C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/PIN.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdbool.h: 
../SoundReceive.h: 
../Temperature.h: 
../Batteryvoltage.h: 
../ControlCenter.h: 
../AnalyseAndStoreMeasurements.h: 
