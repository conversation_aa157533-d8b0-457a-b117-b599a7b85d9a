******************************************************************************
                  TI ARM Linker PC v20.2.4                     
******************************************************************************
>> Linked Mon Feb 22 19:51:04 2021

OUTPUT FILE NAME:   <SoundPositioning.out>
ENTRY POINT SYMBOL: "resetISR"  address: 000048cd


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00058000  00004f2e  000530d2  R  X
  GPRAM                 11000000   00002000  00000000  00002000  RW X
  SRAM                  20000000   00014000  00004ee2  0000f11e  RW X
  LOG_DATA              90000000   00040000  00000000  00040000  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004df0   00004df0    r-x
  00000000    00000000    000000d8   000000d8    r-- .intvecs
  000000d8    000000d8    00004a7e   00004a7e    r-x .text
  00004b58    00004b58    00000298   00000298    r-- .const
00004df0    00004df0    00000008   00000008    rw-
  00004df0    00004df0    00000008   00000008    rw- .args
00004df8    00004df8    000000e0   000000e0    r--
  00004df8    00004df8    000000e0   000000e0    r-- .cinit
00057fa8    00057fa8    00000058   00000058    r--
  00057fa8    00057fa8    00000058   00000058    r-- .ccfg
20000000    20000000    000009ea   00000000    rw-
  20000000    20000000    00000604   00000000    rw- .bss
  20000604    20000604    000003e6   00000000    rw- .data
20000a00    20000a00    000000d8   00000000    rw-
  20000a00    20000a00    000000d8   00000000    rw- .vtable_ram
20001870    20001870    00000010   00000000    rw-
  20001870    20001870    00000010   00000000    rw- .TI.bound:dmaADCPriControlTableEntry
20001a70    20001a70    00004010   00000000    rw-
  20001a70    20001a70    00000010   00000000    rw- .TI.bound:dmaADCAltControlTableEntry
  20001a80    20001a80    00004000   00000000    rw- .sysmem
20013c00    20013c00    00000400   00000000    rw-
  20013c00    20013c00    00000400   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000d8     
                  00000000    000000d8     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.intvecs)

.text      0    000000d8    00004a7e     
                  000000d8    000001e0     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_init)
                  000002b8    00000198                      : PowerCC26X2.oem4f (.text:Power_sleep)
                  00000450    00000190                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf1)
                  000005e0    0000018c                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:runCalibrateFsm)
                  0000076c    00000174                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_open)
                  000008e0    00000174                      : PowerCC26X2.oem4f (.text:Power_init)
                  00000a54    00000164                      : UARTCC26XX.oem4f (.text:UARTCC26XX_open)
                  00000bb8    00000154                      : UARTCC26XX.oem4f (.text:UARTCC26XX_hwiIntFxn)
                  00000d0c    0000013a                      : UARTCC26XX.oem4f (.text:UARTCC26XX_read)
                  00000e46    00000002     ti_drivers_config.obj (.text:Board_initHook)
                  00000e48    00000120     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureXOSCHF)
                  00000f68    00000114                      : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfg)
                  0000107c    00000110                      : GPIOCC26XX.oem4f (.text:GPIO_setConfig)
                  0000118c    00000100                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convert)
                  0000128c    00000100     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:aligned_alloc)
                  0000138c    000000f0     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlStandby)
                  0000147c    000000e4     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free)
                  00001560    000000e0     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_standbyPolicy)
                  00001640    000000e0     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_write)
                  00001720    000000d8     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_construct)
                  000017f8    000000c4     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_init)
                  000018bc    000000c0                      : PowerCC26X2.oem4f (.text:Power_releaseDependency)
                  0000197c    000000c0                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initIO)
                  00001a3c    000000bc     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_construct)
                  00001af8    000000b8     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_setDependency)
                  00001bb0    000000b8                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initHw)
                  00001c68    000000b4     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_startup)
                  00001d1c    000000ac                     : ClockPTimer_nortos.oem4f (.text:ClockP_workFuncDynamic)
                  00001dc8    000000ac     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_open)
                  00001e74    000000ac     driverlib.lib : setup.obj (.text:TrimAfterColdResetWakeupFromShutDown)
                  00001f20    000000a4     SoundTX.obj (.text:Timer2AInterruptHandler)
                  00001fc4    000000a0     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_pend)
                  00002064    0000009c                     : SwiP_nortos.oem4f (.text:SwiP_dispatch)
                  00002100    0000009c     rtsv7M4_T_le_v4SPD16_eabi.lib : memcpy_t2.asm.obj (.text)
                  0000219c    00000098     driverlib.lib : setup.obj (.text:NOROM_SetupTrimDevice)
                  00002234    00000098     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeTxFifoFlush)
                  000022cc    00000096     rtsv7M4_T_le_v4SPD16_eabi.lib : ull_div_t2.asm.obj (.text)
                  00002362    00000092     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_writeCancel)
                  000023f4    00000090     SoundTX.obj (.text:InitTimer2)
                  00002484    00000088     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_open)
                  0000250c    00000088     driverlib.lib : osc.obj (.text:NOROM_OSCHF_AttemptToSwitchToXosc)
                  00002594    00000088     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf2)
                  0000261c    00000088                      : PowerCC26X2.oem4f (.text:disableLFClockQualifiers)
                  000026a4    00000088                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:updateSubSecInc)
                  0000272c    00000084     ti_drivers_config.obj (.text:Board_sendExtFlashByte)
                  000027b0    00000084     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_close)
                  00002834    00000080                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_hwiFxn)
                  000028b4    00000080     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_start)
                  00002934    00000080                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_construct)
                  000029b4    00000080     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlSetRechargeBeforePowerDown)
                  00002a34    0000007a     rtsv7M4_T_le_v4SPD16_eabi.lib : memset_t2.asm.obj (.text)
                  00002aae    00000002     driverlib.lib : interrupt.obj (.text:IntDefaultHandler)
                  00002ab0    00000078     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_walkQueueDynamic)
                  00002b28    00000074     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_resetHw)
                  00002b9c    00000074                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_auxISR)
                  00002c10    00000070                      : PINCC26XX.oem4f (.text:PIN_add)
                  00002c80    00000070     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:split)
                  00002cf0    0000006c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipType)
                  00002d5c    00000068                   : osc.obj (.text:NOROM_OSCHF_SwitchToRcOscTurnOffXosc)
                  00002dc4    00000068     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_post)
                  00002e2c    00000068                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_start)
                  00002e94    00000068     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_lzss.c.obj (.text:decompress:lzss:__TI_decompress_lzss)
                  00002efc    00000068     mainNew.obj (.text:main)
                  00002f64    00000064     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_control)
                  00002fc8    00000062                      : UARTCC26XX.oem4f (.text:UARTCC26XX_swiIntFxn)
                  0000302a    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_destruct)
                  0000302c    00000060     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_write)
                  0000308c    0000005c                      : PINCC26XX.oem4f (.text:PIN_remove)
                  000030e8    0000005c                      : UARTCC26XX.oem4f (.text:startTxFifoEmptyClk)
                  00003144    00000058                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_loadDMAControlTableEntry)
                  0000319c    00000058                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_stop)
                  000031f4    00000058                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_initiateCalibration)
                  0000324c    00000054                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_adjustRawValues)
                  000032a0    00000054     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOff)
                  000032f4    00000050     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_construct)
                  00003344    0000004e     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_close)
                  00003392    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.text:_nop)
                  00003394    0000004c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_cleanADC)
                  000033e0    0000004c     ti_drivers_config.obj (.text:Board_wakeUpExtFlash)
                  0000342c    0000004c     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_initHw)
                  00003478    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_start)
                  000034c4    0000004c     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOn)
                  00003510    0000004c     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readCancel)
                  0000355c    0000004c                      : UDMACC26XX.oem4f (.text:UDMACC26XX_open)
                  000035a8    0000004a                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_control)
                  000035f2    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:clkFxn)
                  000035f4    00000048     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertAdjustedToMicroVolts)
                  0000363c    00000048     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_construct)
                  00003684    00000048     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputEnable)
                  000036cc    00000048                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_RCOSC_clockFunc)
                  00003714    00000048                      : PowerCC26X2.oem4f (.text:switchXOSCHF)
                  0000375c    00000048                      : UARTCC26XX.oem4f (.text:writeFinishedDoCallback)
                  000037a4    00000046                      : RingBuf.oem4f (.text:RingBuf_put)
                  000037ea    00000002                      : PowerCC26X2.oem4f (.text:emptyClockFunc)
                  000037ec    00000044                      : ADCBuf.oem4f (.text:ADCBuf_init)
                  00003830    00000044     mainNew.obj (.text:InitUart)
                  00003874    00000044     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetHwRevision)
                  000038b8    00000044     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_swi)
                  000038fc    00000044     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_post)
                  00003940    00000044                     : SwiP_nortos.oem4f (.text:SwiP_restore)
                  00003984    00000044     drivers_cc13x2.a : UART.oem4f (.text:UART_init)
                  000039c8    00000044     rtsv7M4_T_le_v4SPD16_eabi.lib : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit:__TI_auto_init_nobinit_nopinit)
                  00003a0c    00000042     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_swiFxn)
                  00003a4e    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:faultISR)
                  00003a50    00000040     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertCancel)
                  00003a90    00000040     ti_drivers_config.obj (.text:Board_shutDownExtFlash)
                  00003ad0    00000040     driverlib.lib : osc.obj (.text:NOROM_OSC_HPOSCRelativeFrequencyOffsetGet)
                  00003b10    00000040                   : timer.obj (.text:NOROM_TimerIntRegister)
                  00003b50    00000040     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_get)
                  00003b90    00000040                      : UART.oem4f (.text:UART_open)
                  00003bd0    0000003c                      : PINCC26XX.oem4f (.text:PIN_setOutputValue)
                  00003c0c    00000038                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_configDMA)
                  00003c44    00000038                      : PINCC26XX.oem4f (.text:PIN_setConfig)
                  00003c7c    00000038     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCurrentTick)
                  00003cb4    00000038                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_initDevice)
                  00003cec    00000038     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:notify)
                  00003d24    00000038                      : UARTCC26XX.oem4f (.text:readData)
                  00003d5c    00000038                      : UARTCC26XX.oem4f (.text:writeData)
                  00003d94    00000036     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setNextTick)
                  00003dca    00000036     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_close)
                  00003e00    00000034     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicksUntilInterrupt)
                  00003e34    00000034     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XXSetLoadMatch)
                  00003e68    00000034                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_configureDebugStall)
                  00003e9c    00000034     driverlib.lib : aux_sysif.obj (.text:NOROM_AUXSYSIFOpModeChange)
                  00003ed0    00000034                   : interrupt.obj (.text:NOROM_IntRegister)
                  00003f04    00000034                   : sys_ctrl.obj (.text:NOROM_SysCtrlIdle)
                  00003f38    00000034     SoundTX.obj (.text:SoundTransmit)
                  00003f6c    00000034     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_destruct)
                  00003fa0    00000034     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:readData2RingBuf)
                  00003fd4    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_hwiIntFxn)
                  00004004    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_setCallback)
                  00004034    00000030                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_close)
                  00004064    00000030                      : PINCC26XX.oem4f (.text:PINCC26XX_setMux)
                  00004094    00000030                      : PowerCC26X2.oem4f (.text:Power_releaseConstraint)
                  000040c4    00000030                      : PowerCC26X2.oem4f (.text:Power_setConstraint)
                  000040f4    00000030     driverlib.lib : timer.obj (.text:TimerIntNumberGet)
                  00004124    00000030     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.text:_c_int00:_c_int00)
                  00004154    0000002c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_acquireADCSemaphore)
                  00004180    0000002c                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_releaseADCSemaphore)
                  000041ac    0000002c     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setThreshold)
                  000041d8    0000002a     SoundTX.obj (.text:TimerLoadSet)
                  00004202    0000002a     SoundTX.obj (.text:TimerMatchSet)
                  0000422c    0000002a     SoundTX.obj (.text:TimerPrescaleMatchSet)
                  00004256    0000002a     SoundTX.obj (.text:TimerPrescaleSet)
                  00004280    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_registerNotify)
                  000042a8    00000028     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_insert)
                  000042d0    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:oscillatorISR)
                  000042f8    00000026                      : List.oem4f (.text:List_put)
                  0000431e    00000026                      : List.oem4f (.text:List_remove)
                  00004344    00000024     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicks)
                  00004368    00000024                     : ClockPTimer_nortos.oem4f (.text:ClockP_scheduleNextTick)
                  0000438c    00000024                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_dispatch)
                  000043b0    00000024     driverlib.lib : osc.obj (.text:NOROM_OSCHF_TurnOnXosc)
                  000043d4    00000024     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getMaxTicks)
                  000043f8    00000024     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:getTdcSemaphore)
                  0000441c    00000020     ti_drivers_config.obj (.text:Board_init)
                  0000443c    00000020     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_add)
                  0000445c    00000020     driverlib.lib : aux_adc.obj (.text:NOROM_AUXADCEnableSync)
                  0000447c    00000020     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfgMux)
                  0000449c    00000020                      : PowerCC26X2.oem4f (.text:Power_unregisterNotify)
                  000044bc    00000020     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_put)
                  000044dc    00000020                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_dynamicStub)
                  000044fc    00000020     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:threadSafeStdbyDisRelease)
                  0000451c    00000020                      : UARTCC26XX.oem4f (.text:threadSafeStdbyDisSet)
                  0000453c    0000001e                      : PINCC26XX.oem4f (.text:PIN_close)
                  0000455a    0000001e                      : PowerCC26X2.oem4f (.text:Power_getTransitionLatency)
                  00004578    0000001e     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_constructBinary)
                  00004596    0000001e                     : SwiP_nortos.oem4f (.text:SwiP_or)
                  000045b4    0000001e     SoundTX.obj (.text:TimerDisable)
                  000045d2    0000001e     SoundTX.obj (.text:TimerEnable)
                  000045f0    0000001c     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_destruct)
                  0000460c    0000001c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipFamily)
                  00004628    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_hwi)
                  00004644    0000001c     SoundTX.obj (.text:PRCMLoadGet)
                  00004660    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_disable)
                  0000467c    0000001c     SoundTX.obj (.text:TimerMatchGet)
                  00004698    0000001c     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_initHw)
                  000046b4    0000001c     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_remove)
                  000046d0    0000001a     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:PowerCC26XX_calibrate)
                  000046ea    0000001a     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_get)
                  00004704    0000001a                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_createBinary)
                  0000471e    00000018                     : ClockPTimer_nortos.oem4f (.text:ClockP_destruct)
                  00004736    00000002                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:intDefaultHandler)
                  00004738    00000018                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_inISR)
                  00004750    00000018     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetPackageType)
                  00004768    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_idleFunc)
                  00004780    00000018     SoundTX.obj (.text:TimerIntEnable)
                  00004798    00000018     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_startup)
                  000047b0    00000018     drivers_cc13x2.a : UART.oem4f (.text:UART_Params_init)
                  000047c8    00000018     rtsv7M4_T_le_v4SPD16_eabi.lib : args_main.c.obj (.text:_args_main)
                  000047e0    00000018                                   : ll_mul_t2.asm.obj (.text)
                  000047f8    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:switchToTCXO)
                  00004810    00000016     driverlib.lib : chipinfo.obj (.text:NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated)
                  00004826    00000016     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_create)
                  0000483c    00000016                     : SwiP_nortos.oem4f (.text:maxbit)
                  00004852    00000014     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_freqToCounts)
                  00004866    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:nmiISR)
                  00004868    00000014     driverlib.lib : interrupt.obj (.text:NOROM_IntUnregister)
                  0000487c    00000014     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_calibrate)
                  00004890    00000014                      : PowerCC26X2.oem4f (.text:Power_disablePolicy)
                  000048a4    00000014     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_handleHwi)
                  000048b8    00000014                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCount64)
                  000048cc    00000014                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:resetISR)
                  000048e0    00000012     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_Params_init)
                  000048f2    00000012     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_Params_init)
                  00004904    00000012     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_construct)
                  00004916    00000012     SoundTX.obj (.text:TimerIntClear)
                  00004928    00000010     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_Params_init)
                  00004938    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_getCpuFreq)
                  00004948    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_clearInterrupt)
                  00004958    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_disable)
                  00004968    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_post)
                  00004978    00000010     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_getPinCount)
                  00004988    00000010     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerDisable)
                  00004998    00000010     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_enablePolicy)
                  000049a8    00000010     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_Params_init)
                  000049b8    00000010                     : SwiP_nortos.oem4f (.text:SwiP_Params_init)
                  000049c8    00000010                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_Params_init)
                  000049d8    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:uartPostNotify)
                  000049e8    0000000e     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_empty)
                  000049f6    0000000e                     : QueueP_nortos.oem4f (.text:QueueP_remove)
                  00004a04    0000000e     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_none.c.obj (.text:decompress:none:__TI_decompress_none)
                  00004a12    00000002                                   : div0.asm.obj (.text)
                  00004a14    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_doTick)
                  00004a20    0000000c                     : ClockPTimer_nortos.oem4f (.text:ClockP_getSystemTickPeriod)
                  00004a2c    0000000c     driverlib.lib : cpu.obj (.text:NOROM_CPUcpsid)
                  00004a38    0000000c                   : cpu.obj (.text:NOROM_CPUcpsie)
                  00004a44    0000000c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_registerIntCb)
                  00004a50    0000000c     SoundTX.obj (.text:PRCMLoadSet)
                  00004a5c    0000000c     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerRestore)
                  00004a68    0000000c     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_getConstraintMask)
                  00004a74    0000000c                      : PowerCC26X2.oem4f (.text:Power_getDependencyCount)
                  00004a80    0000000c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_getTrigger)
                  00004a8c    0000000c                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getFreq)
                  00004a98    0000000c     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init)
                  00004aa4    0000000a     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_convertCancel)
                  00004aae    0000000a                      : UDMACC26XX.oem4f (.text:UDMACC26XX_hwiIntFxn)
                  00004ab8    0000000a                      : UARTCC26XX.oem4f (.text:readSemCallback)
                  00004ac2    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_conversionCallback)
                  00004aca    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_init)
                  00004ad2    00000008                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_setLoadValue)
                  00004ada    00000008     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_restore)
                  00004ae2    00000008     ADC.obj (.text:InitADC)
                  00004aea    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_init)
                  00004af2    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:malloc)
                  00004afa    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeSemCallback)
                  00004b02    00000006     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_stop)
                  00004b08    00000006     driverlib.lib : cpu.obj (.text:NOROM_CPUdelay)
                  00004b0e    00000006     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_init)
                  00004b14    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readPolling)
                  00004b1a    00000006                      : UARTCC26XX.oem4f (.text:UARTCC26XX_writePolling)
                  00004b20    00000006                      : PowerCC26X2.oem4f (.text:delayUs)
                  00004b26    00000004                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_getResolution)
                  00004b2a    00000004     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:CPUwfi)
                  00004b2e    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_isActive)
                  00004b32    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_setTimeout)
                  00004b36    00000004                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_enable)
                  00004b3a    00000004                     : QueueP_nortos.oem4f (.text:QueueP_head)
                  00004b3e    00000004                     : QueueP_nortos.oem4f (.text:QueueP_next)
                  00004b42    00000004                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_delete)
                  00004b46    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : pre_init.c.obj (.text:_system_pre_init)
                  00004b4a    00000004                                   : exit.c.obj (.text:abort:abort)
                  00004b4e    00000004     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureRFCoreClocks)
                  00004b52    00000004                      : PowerCC26X2.oem4f (.text:nopResourceHandler)

.const     0    00004b58    00000298     
                  00004b58    00000054     drivers_cc13x2.a : PowerCC26X2.oem4f (.const:resourceDB)
                  00004bac    00000028     ti_drivers_config.obj (.const:BoardGpioInitTable)
                  00004bd4    00000028     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:UARTCC26XX_fxnTable)
                  00004bfc    00000028     ti_drivers_config.obj (.const:uartCC26XXHWAttrs)
                  00004c24    00000024     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.const:ADCBufCC26X2_fxnTable)
                  00004c48    00000024                      : UART.oem4f (.const:UART_defaultParams)
                  00004c6c    0000001c                      : GPTimerCC26XX.oem4f (.const:GPT_LUT)
                  00004c88    00000018     ti_drivers_config.obj (.const:GPTimerCC26XX_config)
                  00004ca0    00000018     ti_drivers_config.obj (.const:gptimerCC26XXHWAttrs)
                  00004cb8    00000018     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:interruptType)
                  00004cd0    00000014     ti_drivers_config.obj (.const:$P$T1$2)
                  00004ce4    00000014     ti_drivers_config.obj (.const:GPIOCC26XX_config)
                  00004cf8    00000014     ti_drivers_config.obj (.const:PowerCC26X2_config)
                  00004d0c    00000014     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:parityType)
                  00004d20    00000010     nortos_cc13x2.a : SwiP_nortos.oem4f (.const:SwiP_defaultParams)
                  00004d30    00000010     ti_drivers_config.obj (.const:adcbufCC26XXHWAttrs)
                  00004d40    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:dataLength)
                  00004d50    00000010                      : GPIOCC26XX.oem4f (.const:outPinTypes)
                  00004d60    0000000e     ti_drivers_config.obj (.const)
                  00004d6e    00000002     --HOLE-- [fill = 0]
                  00004d70    0000000c     ti_drivers_config.obj (.const:ADCBuf_config)
                  00004d7c    0000000c     ti_drivers_config.obj (.const:UART_config)
                  00004d88    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:inPinTypes)
                  00004d94    0000000c                      : GPIOCC26XX.oem4f (.const:outPinStrengths)
                  00004da0    00000008     driverlib.lib : aux_sysif.obj (.const:$O1$$)
                  00004da8    00000008     ti_drivers_config.obj (.const:$P$T0$1)
                  00004db0    00000008     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.const:GPT_DefaultParams)
                  00004db8    00000008     ti_drivers_config.obj (.const:PINCC26XX_hwAttrs)
                  00004dc0    00000008     ti_drivers_config.obj (.const:UDMACC26XX_config)
                  00004dc8    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:stopBits)
                  00004dd0    00000008     ti_drivers_config.obj (.const:udmaCC26XXHWAttrs)
                  00004dd8    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:.string:rxFifoBytes)
                  00004dde    00000006                      : UARTCC26XX.oem4f (.const:.string:rxFifoThreshold)
                  00004de4    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoBytes)
                  00004dea    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoThreshold)

.binit     0    00000000    00000000     

.cinit     0    00004df8    000000e0     
                  00004df8    00000088     (.cinit..data.load) [load image, compression = lzss]
                  00004e80    0000000c     (__TI_handler_table)
                  00004e8c    00000004     --HOLE-- [fill = 0]
                  00004e90    00000008     (.cinit..TI.bound:dmaADCAltControlTableEntry.load) [load image, compression = zero_init]
                  00004e98    00000008     (.cinit..TI.bound:dmaADCPriControlTableEntry.load) [load image, compression = zero_init]
                  00004ea0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004ea8    00000008     (.cinit..vtable_ram.load) [load image, compression = zero_init]
                  00004eb0    00000028     (__TI_cinit_table)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.ccfg      0    00057fa8    00000058     
                  00057fa8    00000058     ti_devices_config.obj (.ccfg:retain)

.bss       0    20000000    00000604     UNINITIALIZED
                  20000000    000001b4     (.common:uartCC26XXObjects)
                  200001b4    000000dc     (.common:adcbufCC26XXbjects)
                  20000290    000000a0     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:ClockP_module)
                  20000330    00000098     (.common:gptimerCC26XXObjects)
                  200003c8    0000007c     (.common:pinHandleTable)
                  20000444    00000050     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinSemaphore)
                  20000494    00000034                      : PINCC26XX.oem4f (.bss:pinSwi)
                  200004c8    00000020     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_readyList)
                  200004e8    00000020     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioCallbackInfo)
                  20000508    00000020                      : PINCC26XX.oem4f (.bss:pinGpioConfigTable)
                  20000528    00000020     ti_drivers_config.obj (.bss:uartCC26XXRingBuffer0)
                  20000548    00000020     (.common:udmaCC26XXObject)
                  20000568    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_hwiStruct)
                  20000584    0000001c                     : TimerPCC26XX_nortos.oem4f (.bss:TimerP_hwiStruct)
                  200005a0    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinHwi)
                  200005bc    0000000d     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss)
                  200005c9    00000001     (.common:driverlib_release_0_59848)
                  200005ca    00000002     --HOLE--
                  200005cc    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioPinState)
                  200005d8    00000008                      : GPIOCC26XX.oem4f (.bss)
                  200005e0    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:timerFreq)
                  200005e8    00000004     drivers_cc13x2.a : PINCC26XX.oem4f (.bss)
                  200005ec    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss)
                  200005f0    00000004                     : PowerCC26X2_nortos.oem4f (.bss)
                  200005f4    00000004                     : TimerPCC26XX_nortos.oem4f (.bss)
                  200005f8    00000004     (.common:i)
                  200005fc    00000004     (.common:j)
                  20000600    00000004     (.common:uart)

.vtable_ram 
*          0    20000a00    000000d8     UNINITIALIZED
                  20000a00    000000d8     driverlib.lib : interrupt.obj (.vtable_ram)

.data      0    20000604    000003e6     UNINITIALIZED
                  20000604    00000170     drivers_cc13x2.a : PowerCC26X2.oem4f (.data:PowerCC26X2_module)
                  20000774    000000d8     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data:HwiP_dispatchTable)
                  2000084c    0000007c     SoundTX.obj (.data:ChirpDelay)
                  200008c8    0000007c     SoundTX.obj (.data:ChirpState)
                  20000944    00000024     driverlib.lib : osc.obj (.data:$O1$$)
                  20000968    00000010     ti_drivers_config.obj (.data:gpioCallbackFunctions)
                  20000978    00000010     ti_drivers_config.obj (.data:gpioPinConfigs)
                  20000988    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data:ClockP_defaultParams)
                  20000994    0000000c                     : TimerPCC26XX_nortos.oem4f (.data:TimerP_defaultParams)
                  200009a0    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.data:$O1$$)
                  200009a8    00000008                                   : memory.c.obj (.data:$O1$$)
                  200009b0    00000008     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data:SemaphoreP_defaultParams)
                  200009b8    00000008     SoundTX.obj (.data)
                  200009c0    00000008     drivers_cc13x2.a : PINCC26XX.oem4f (.data)
                  200009c8    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data)
                  200009d0    00000006     ti_drivers_config.obj (.data:adcBufCC26XXChannelLut0)
                  200009d6    00000001     drivers_cc13x2.a : ADCBuf.oem4f (.data)
                  200009d7    00000001                      : UART.oem4f (.data)
                  200009d8    00000005                      : GPIOCC26XX.oem4f (.data)
                  200009dd    00000003     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data)
                  200009e0    00000004     drivers_cc13x2.a : GPIOCC26XX.oem4f (.data:gpioPinTable)
                  200009e4    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data)
                  200009e8    00000001                     : SwiP_nortos.oem4f (.data)
                  200009e9    00000001                     : TimerPCC26XX_nortos.oem4f (.data)

.sysmem    0    20001a80    00004000     UNINITIALIZED
                  20001a80    00000010     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.sysmem)
                  20001a90    00003ff0     --HOLE--

.stack     0    20013c00    00000400     UNINITIALIZED
                  20013c00    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.stack)
                  20013c04    000003fc     --HOLE--

.log_data 
*          0    90000000    00000000     COPY SECTION

__llvm_prf_cnts 
*          0    20000000    00000000     UNINITIALIZED

.TI.bound:dmaADCPriControlTableEntry 
*          0    20001870    00000010     UNINITIALIZED
                  20001870    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCPriControlTableEntry)

.TI.bound:dmaADCAltControlTableEntry 
*          0    20001a70    00000010     UNINITIALIZED
                  20001a70    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCAltControlTableEntry)

.args      0    00004df0    00000008     
                  00004df0    00000008     --HOLE-- [fill = 0]

MODULE SUMMARY

       Module                             code    ro data   rw data
       ------                             ----    -------   -------
    .\
       SoundTX.obj                        698     0         256    
       mainNew.obj                        172     0         12     
       ADC.obj                            8       0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             878     0         268    
                                                                   
    .\syscfg\
       ti_drivers_config.obj              306     274       910    
       ti_devices_config.obj              0       88        0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             306     362       910    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos/lib/ccs/m4f/nortos_cc13x2.a
       ClockPTimer_nortos.oem4f           922     0         192    
       SwiP_nortos.oem4f                  724     16        74     
       TimerPCC26XX_nortos.oem4f          642     0         45     
       HwiPCC26XX_nortos.oem4f            294     0         220    
       SemaphoreP_nortos.oem4f            410     0         11     
       PowerCC26X2_nortos.oem4f           256     0         4      
       startup_cc13x2_cc26x2_ccs.oem4f    26      216       0      
       QueueP_nortos.oem4f                100     0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             3374    232       546    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/cc13x2_cc26x2/driverlib/bin/ccs/driverlib.lib
       sys_ctrl.obj                       420     0         0      
       osc.obj                            340     0         36     
       setup.obj                          324     0         0      
       interrupt.obj                      74      0         216    
       chipinfo.obj                       250     0         0      
       prcm.obj                           160     0         0      
       timer.obj                          112     0         0      
       aux_sysif.obj                      52      8         0      
       aux_adc.obj                        32      0         0      
       cpu.obj                            30      0         0      
       driverlib_release.obj              0       0         1      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1794    8         253    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/lib/ccs/m4f/drivers_cc13x2.a
       UARTCC26XX.oem4f                   2760    108       0      
       PowerCC26X2.oem4f                  2096    84        368    
       PINCC26XX.oem4f                    1554    0         328    
       ADCBufCC26X2.oem4f                 1542    36        32     
       PowerCC26X2_calibrateRCOSC.oem4f   1400    0         0      
       GPIOCC26XX.oem4f                   660     64        61     
       GPTimerCC26XX.oem4f                670     36        0      
       UART.oem4f                         156     36        1      
       UDMACC26XX.oem4f                   168     0         0      
       RingBuf.oem4f                      152     0         0      
       ADCBuf.oem4f                       78      0         1      
       List.oem4f                         76      0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             11312   364       791    
                                                                   
    C:\ti\ccs1011\ccs\tools\compiler\ti-cgt-arm_20.2.4.LTS\lib\rtsv7M4_T_le_v4SPD16_eabi.lib
       memory.c.obj                       672     0         8      
       memcpy_t2.asm.obj                  156     0         0      
       ull_div_t2.asm.obj                 150     0         0      
       memset_t2.asm.obj                  122     0         0      
       copy_decompress_lzss.c.obj         104     0         0      
       autoinit.c.obj                     68      0         0      
       boot_cortex_m.c.obj                48      0         0      
       args_main.c.obj                    24      0         0      
       ll_mul_t2.asm.obj                  24      0         0      
       copy_decompress_none.c.obj         14      0         0      
       copy_zero_init.c.obj               12      0         0      
       _lock.c.obj                        2       0         8      
       exit.c.obj                         4       0         0      
       pre_init.c.obj                     4       0         0      
       div0.asm.obj                       2       0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1406    0         16     
                                                                   
       Heap:                              0       0         16384  
       Stack:                             0       0         1024   
       Linker Generated:                  0       220       0      
    +--+----------------------------------+-------+---------+---------+
       Grand Total:                       19070   1186      20192  


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004eb0 records: 5, size/record: 8, table size: 40
	.data: load addr=00004df8, load size=00000088 bytes, run addr=20000604, run size=000003e6 bytes, compression=lzss
	.TI.bound:dmaADCAltControlTableEntry: load addr=00004e90, load size=00000008 bytes, run addr=20001a70, run size=00000010 bytes, compression=zero_init
	.TI.bound:dmaADCPriControlTableEntry: load addr=00004e98, load size=00000008 bytes, run addr=20001870, run size=00000010 bytes, compression=zero_init
	.bss: load addr=00004ea0, load size=00000008 bytes, run addr=20000000, run size=00000604 bytes, compression=zero_init
	.vtable_ram: load addr=00004ea8, load size=00000008 bytes, run addr=20000a00, run size=000000d8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004e80 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_lzss
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                                               
-------   ----                                                               
00004d63  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004d60  ADCBUF_CONST                                                       
00004d61  ADCBUF_SOUND_CONST                                                 
00004d62  ADCBUF_TEMPERATURE_CONST                                           
0000324d  ADCBufCC26X2_adjustRawValues                                       
00003345  ADCBufCC26X2_close                                                 
000035a9  ADCBufCC26X2_control                                               
0000118d  ADCBufCC26X2_convert                                               
000035f5  ADCBufCC26X2_convertAdjustedToMicroVolts                           
00003a51  ADCBufCC26X2_convertCancel                                         
00004c24  ADCBufCC26X2_fxnTable                                              
00004b27  ADCBufCC26X2_getResolution                                         
00004acb  ADCBufCC26X2_init                                                  
0000076d  ADCBufCC26X2_open                                                  
00004d70  ADCBuf_config                                                      
00004aa5  ADCBuf_convertCancel                                               
00004d64  ADCBuf_count                                                       
000037ed  ADCBuf_init                                                        
00004bac  BoardGpioInitTable                                                 
0000441d  Board_init                                                         
00000e47  Board_initHook                                                     
0000272d  Board_sendExtFlashByte                                             
00003a91  Board_shutDownExtFlash                                             
000033e1  Board_wakeUpExtFlash                                               
00004b4b  C$$EXIT                                                            
00004d6b  CONFIG_GPTIMER_0_CONST                                             
00004d6c  CONFIG_GPTIMER_1_CONST                                             
2000084c  ChirpDelay                                                         
200009bc  ChirpIndex                                                         
200009b8  ChirpSize                                                          
200008c8  ChirpState                                                         
00004929  ClockP_Params_init                                                 
0000443d  ClockP_add                                                         
0000363d  ClockP_construct                                                   
0000471f  ClockP_destruct                                                    
00004a15  ClockP_doTick                                                      
00004939  ClockP_getCpuFreq                                                  
00004a21  ClockP_getSystemTickPeriod                                         
00004345  ClockP_getTicks                                                    
00003e01  ClockP_getTicksUntilInterrupt                                      
00004b2f  ClockP_isActive                                                    
00004369  ClockP_scheduleNextTick                                            
00004b33  ClockP_setTimeout                                                  
000028b5  ClockP_start                                                       
00001c69  ClockP_startup                                                     
00004b03  ClockP_stop                                                        
200009cc  ClockP_tickPeriod                                                  
00002ab1  ClockP_walkQueueDynamic                                            
00001d1d  ClockP_workFuncDynamic                                             
00004ce4  GPIOCC26XX_config                                                  
00003fd5  GPIO_hwiIntFxn                                                     
000017f9  GPIO_init                                                          
00004005  GPIO_setCallback                                                   
0000107d  GPIO_setConfig                                                     
0000302d  GPIO_write                                                         
000048e1  GPTimerCC26XX_Params_init                                          
00004035  GPTimerCC26XX_close                                                
00004c88  GPTimerCC26XX_config                                               
00003e69  GPTimerCC26XX_configureDebugStall                                  
00002485  GPTimerCC26XX_open                                                 
00004ad3  GPTimerCC26XX_setLoadValue                                         
00003479  GPTimerCC26XX_start                                                
0000319d  GPTimerCC26XX_stop                                                 
00004d6d  GPTimer_count                                                      
000048f3  HwiP_Params_init                                                   
00004949  HwiP_clearInterrupt                                                
00002935  HwiP_construct                                                     
000045f1  HwiP_destruct                                                      
00004959  HwiP_disable                                                       
00004b37  HwiP_enable                                                        
00004739  HwiP_inISR                                                         
00004969  HwiP_post                                                          
00004adb  HwiP_restore                                                       
200009e4  HwiP_swiPIntNum                                                    
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
00004ae3  InitADC                                                            
000023f5  InitTimer2                                                         
00003831  InitUart                                                           
000042f9  List_put                                                           
0000431f  List_remove                                                        
0000445d  NOROM_AUXADCEnableSync                                             
00003e9d  NOROM_AUXSYSIFOpModeChange                                         
00004a2d  NOROM_CPUcpsid                                                     
00004a39  NOROM_CPUcpsie                                                     
00004b09  NOROM_CPUdelay                                                     
0000460d  NOROM_ChipInfo_GetChipFamily                                       
00002cf1  NOROM_ChipInfo_GetChipType                                         
00003875  NOROM_ChipInfo_GetHwRevision                                       
00004751  NOROM_ChipInfo_GetPackageType                                      
00003ed1  NOROM_IntRegister                                                  
00004869  NOROM_IntUnregister                                                
0000250d  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002d5d  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
000043b1  NOROM_OSCHF_TurnOnXosc                                             
00003ad1  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
000032a1  NOROM_PRCMPowerDomainsAllOff                                       
000034c5  NOROM_PRCMPowerDomainsAllOn                                        
0000219d  NOROM_SetupTrimDevice                                              
00003f05  NOROM_SysCtrlIdle                                                  
000029b5  NOROM_SysCtrlSetRechargeBeforePowerDown                            
0000138d  NOROM_SysCtrlStandby                                               
00004811  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00003b11  NOROM_TimerIntRegister                                             
00004b37  NoRTOS_start                                                       
00004979  PINCC26XX_getPinCount                                              
00004db8  PINCC26XX_hwAttrs                                                  
00004065  PINCC26XX_setMux                                                   
00004d67  PIN_DRIVE_SPEAKER_A_CONST                                          
00004d68  PIN_DRIVE_SPEAKER_B_CONST                                          
00004d65  PIN_TEST1_CONST                                                    
00004d66  PIN_TEST2_CONST                                                    
00002c11  PIN_add                                                            
0000453d  PIN_close                                                          
000000d9  PIN_init                                                           
00001dc9  PIN_open                                                           
00004a45  PIN_registerIntCb                                                  
0000308d  PIN_remove                                                         
00003c45  PIN_setConfig                                                      
00003685  PIN_setOutputEnable                                                
00003bd1  PIN_setOutputValue                                                 
000036cd  PowerCC26X2_RCOSC_clockFunc                                        
00002b9d  PowerCC26X2_auxISR                                                 
0000487d  PowerCC26X2_calibrate                                              
00004cf8  PowerCC26X2_config                                                 
000031f5  PowerCC26X2_initiateCalibration                                    
20000604  PowerCC26X2_module                                                 
000046d1  PowerCC26XX_calibrate                                              
00004989  PowerCC26XX_schedulerDisable                                       
00004a5d  PowerCC26XX_schedulerRestore                                       
00001561  PowerCC26XX_standbyPolicy                                          
00004891  Power_disablePolicy                                                
00004999  Power_enablePolicy                                                 
00004a69  Power_getConstraintMask                                            
00004a75  Power_getDependencyCount                                           
0000455b  Power_getTransitionLatency                                         
00004769  Power_idleFunc                                                     
000008e1  Power_init                                                         
00004281  Power_registerNotify                                               
00004095  Power_releaseConstraint                                            
000018bd  Power_releaseDependency                                            
000040c5  Power_setConstraint                                                
00001af9  Power_setDependency                                                
000002b9  Power_sleep                                                        
0000449d  Power_unregisterNotify                                             
000049e9  QueueP_empty                                                       
000046eb  QueueP_get                                                         
00004b3b  QueueP_head                                                        
00004b0f  QueueP_init                                                        
00004b3f  QueueP_next                                                        
000044bd  QueueP_put                                                         
000049f7  QueueP_remove                                                      
00004905  RingBuf_construct                                                  
00003b51  RingBuf_get                                                        
000037a5  RingBuf_put                                                        
000049a9  SemaphoreP_Params_init                                             
000032f5  SemaphoreP_construct                                               
00004579  SemaphoreP_constructBinary                                         
00004827  SemaphoreP_create                                                  
00004705  SemaphoreP_createBinary                                            
200009b0  SemaphoreP_defaultParams                                           
00004b43  SemaphoreP_delete                                                  
0000302b  SemaphoreP_destruct                                                
00001fc5  SemaphoreP_pend                                                    
000038fd  SemaphoreP_post                                                    
00003f39  SoundTransmit                                                      
000049b9  SwiP_Params_init                                                   
00001721  SwiP_construct                                                     
00003f6d  SwiP_destruct                                                      
00004661  SwiP_disable                                                       
00002065  SwiP_dispatch                                                      
00004a81  SwiP_getTrigger                                                    
00004597  SwiP_or                                                            
00002dc5  SwiP_post                                                          
00003941  SwiP_restore                                                       
00001f21  Timer2AInterruptHandler                                            
000049c9  TimerP_Params_init                                                 
00001a3d  TimerP_construct                                                   
000044dd  TimerP_dynamicStub                                                 
000048b9  TimerP_getCount64                                                  
00003c7d  TimerP_getCurrentTick                                              
00004a8d  TimerP_getFreq                                                     
000043d5  TimerP_getMaxTicks                                                 
00003cb5  TimerP_initDevice                                                  
00003d95  TimerP_setNextTick                                                 
000041ad  TimerP_setThreshold                                                
00002e2d  TimerP_start                                                       
00004799  TimerP_startup                                                     
000027b1  UARTCC26XX_close                                                   
00002f65  UARTCC26XX_control                                                 
00004bd4  UARTCC26XX_fxnTable                                                
00000bb9  UARTCC26XX_hwiIntFxn                                               
00004aeb  UARTCC26XX_init                                                    
00000a55  UARTCC26XX_open                                                    
00000d0d  UARTCC26XX_read                                                    
00003511  UARTCC26XX_readCancel                                              
00004b15  UARTCC26XX_readPolling                                             
00002fc9  UARTCC26XX_swiIntFxn                                               
00001641  UARTCC26XX_write                                                   
00002363  UARTCC26XX_writeCancel                                             
00004b1b  UARTCC26XX_writePolling                                            
00004d69  UART_0_CONST                                                       
000047b1  UART_Params_init                                                   
00004d7c  UART_config                                                        
00004d6a  UART_count                                                         
00004c48  UART_defaultParams                                                 
00003985  UART_init                                                          
00003b91  UART_open                                                          
00003dcb  UDMACC26XX_close                                                   
00004dc0  UDMACC26XX_config                                                  
00004aaf  UDMACC26XX_hwiIntFxn                                               
0000355d  UDMACC26XX_open                                                    
20014000  __STACK_END                                                        
00000400  __STACK_SIZE                                                       
00004000  __SYSMEM_SIZE                                                      
00004eb0  __TI_CINIT_Base                                                    
00004ed8  __TI_CINIT_Limit                                                   
00004e80  __TI_Handler_Table_Base                                            
00004e8c  __TI_Handler_Table_Limit                                           
000039c9  __TI_auto_init_nobinit_nopinit                                     
00002e95  __TI_decompress_lzss                                               
00004a05  __TI_decompress_none                                               
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
00000000  __TI_static_base__                                                 
00004a99  __TI_zero_init                                                     
00004a13  __aeabi_idiv0                                                      
00004a13  __aeabi_ldiv0                                                      
000047e1  __aeabi_lmul                                                       
00002a35  __aeabi_memclr                                                     
00002a35  __aeabi_memclr4                                                    
00002a35  __aeabi_memclr8                                                    
00002101  __aeabi_memcpy                                                     
00002101  __aeabi_memcpy4                                                    
00002101  __aeabi_memcpy8                                                    
00002a37  __aeabi_memset                                                     
00002a37  __aeabi_memset4                                                    
00002a37  __aeabi_memset8                                                    
000022cd  __aeabi_uldivmod                                                   
ffffffff  __binit__                                                          
00004df0  __c_args__                                                         
00057fa8  __ccfg                                                             
UNDEFED   __mpu_init                                                         
20013c00  __stack                                                            
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
000047c9  _args_main                                                         
00004125  _c_int00                                                           
20000944  _hposcCoeffs                                                       
200009a0  _lock                                                              
00003393  _nop                                                               
20001a80  _sys_memory                                                        
UNDEFED   _system_post_cinit                                                 
00004b47  _system_pre_init                                                   
200009a4  _unlock                                                            
00004b4b  abort                                                              
200009d0  adcBufCC26XXChannelLut0                                            
00004d30  adcbufCC26XXHWAttrs                                                
200001b4  adcbufCC26XXbjects                                                 
0000128d  aligned_alloc                                                      
ffffffff  binit                                                              
000035f3  clkFxn                                                             
200005c9  driverlib_release_0_59848                                          
0000147d  free                                                               
20000a00  g_pfnRAMVectors                                                    
00000000  g_pfnVectors                                                       
20000968  gpioCallbackFunctions                                              
20000978  gpioPinConfigs                                                     
00004ca0  gptimerCC26XXHWAttrs                                               
20000330  gptimerCC26XXObjects                                               
200005f8  i                                                                  
00004d88  inPinTypes                                                         
200005fc  j                                                                  
00002efd  main                                                               
00004af3  malloc                                                             
0000128d  memalign                                                           
00002101  memcpy                                                             
00002a3d  memset                                                             
00004d94  outPinStrengths                                                    
00004d50  outPinTypes                                                        
200003c8  pinHandleTable                                                     
200009c4  pinLowerBound                                                      
200009c0  pinUpperBound                                                      
000048cd  resetISR                                                           
00004b58  resourceDB                                                         
20000600  uart                                                               
20000000  uartCC26XXObjects                                                  
00004dd0  udmaCC26XXHWAttrs                                                  
20000548  udmaCC26XXObject                                                   


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                                               
-------   ----                                                               
00000000  __TI_static_base__                                                 
00000000  g_pfnVectors                                                       
000000d9  PIN_init                                                           
000002b9  Power_sleep                                                        
00000400  __STACK_SIZE                                                       
0000076d  ADCBufCC26X2_open                                                  
000008e1  Power_init                                                         
00000a55  UARTCC26XX_open                                                    
00000bb9  UARTCC26XX_hwiIntFxn                                               
00000d0d  UARTCC26XX_read                                                    
00000e47  Board_initHook                                                     
0000107d  GPIO_setConfig                                                     
0000118d  ADCBufCC26X2_convert                                               
0000128d  aligned_alloc                                                      
0000128d  memalign                                                           
0000138d  NOROM_SysCtrlStandby                                               
0000147d  free                                                               
00001561  PowerCC26XX_standbyPolicy                                          
00001641  UARTCC26XX_write                                                   
00001721  SwiP_construct                                                     
000017f9  GPIO_init                                                          
000018bd  Power_releaseDependency                                            
00001a3d  TimerP_construct                                                   
00001af9  Power_setDependency                                                
00001c69  ClockP_startup                                                     
00001d1d  ClockP_workFuncDynamic                                             
00001dc9  PIN_open                                                           
00001f21  Timer2AInterruptHandler                                            
00001fc5  SemaphoreP_pend                                                    
00002065  SwiP_dispatch                                                      
00002101  __aeabi_memcpy                                                     
00002101  __aeabi_memcpy4                                                    
00002101  __aeabi_memcpy8                                                    
00002101  memcpy                                                             
0000219d  NOROM_SetupTrimDevice                                              
000022cd  __aeabi_uldivmod                                                   
00002363  UARTCC26XX_writeCancel                                             
000023f5  InitTimer2                                                         
00002485  GPTimerCC26XX_open                                                 
0000250d  NOROM_OSCHF_AttemptToSwitchToXosc                                  
0000272d  Board_sendExtFlashByte                                             
000027b1  UARTCC26XX_close                                                   
000028b5  ClockP_start                                                       
00002935  HwiP_construct                                                     
000029b5  NOROM_SysCtrlSetRechargeBeforePowerDown                            
00002a35  __aeabi_memclr                                                     
00002a35  __aeabi_memclr4                                                    
00002a35  __aeabi_memclr8                                                    
00002a37  __aeabi_memset                                                     
00002a37  __aeabi_memset4                                                    
00002a37  __aeabi_memset8                                                    
00002a3d  memset                                                             
00002ab1  ClockP_walkQueueDynamic                                            
00002b9d  PowerCC26X2_auxISR                                                 
00002c11  PIN_add                                                            
00002cf1  NOROM_ChipInfo_GetChipType                                         
00002d5d  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
00002dc5  SwiP_post                                                          
00002e2d  TimerP_start                                                       
00002e95  __TI_decompress_lzss                                               
00002efd  main                                                               
00002f65  UARTCC26XX_control                                                 
00002fc9  UARTCC26XX_swiIntFxn                                               
0000302b  SemaphoreP_destruct                                                
0000302d  GPIO_write                                                         
0000308d  PIN_remove                                                         
0000319d  GPTimerCC26XX_stop                                                 
000031f5  PowerCC26X2_initiateCalibration                                    
0000324d  ADCBufCC26X2_adjustRawValues                                       
000032a1  NOROM_PRCMPowerDomainsAllOff                                       
000032f5  SemaphoreP_construct                                               
00003345  ADCBufCC26X2_close                                                 
00003393  _nop                                                               
000033e1  Board_wakeUpExtFlash                                               
00003479  GPTimerCC26XX_start                                                
000034c5  NOROM_PRCMPowerDomainsAllOn                                        
00003511  UARTCC26XX_readCancel                                              
0000355d  UDMACC26XX_open                                                    
000035a9  ADCBufCC26X2_control                                               
000035f3  clkFxn                                                             
000035f5  ADCBufCC26X2_convertAdjustedToMicroVolts                           
0000363d  ClockP_construct                                                   
00003685  PIN_setOutputEnable                                                
000036cd  PowerCC26X2_RCOSC_clockFunc                                        
000037a5  RingBuf_put                                                        
000037ed  ADCBuf_init                                                        
00003831  InitUart                                                           
00003875  NOROM_ChipInfo_GetHwRevision                                       
000038fd  SemaphoreP_post                                                    
00003941  SwiP_restore                                                       
00003985  UART_init                                                          
000039c9  __TI_auto_init_nobinit_nopinit                                     
00003a51  ADCBufCC26X2_convertCancel                                         
00003a91  Board_shutDownExtFlash                                             
00003ad1  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00003b11  NOROM_TimerIntRegister                                             
00003b51  RingBuf_get                                                        
00003b91  UART_open                                                          
00003bd1  PIN_setOutputValue                                                 
00003c45  PIN_setConfig                                                      
00003c7d  TimerP_getCurrentTick                                              
00003cb5  TimerP_initDevice                                                  
00003d95  TimerP_setNextTick                                                 
00003dcb  UDMACC26XX_close                                                   
00003e01  ClockP_getTicksUntilInterrupt                                      
00003e69  GPTimerCC26XX_configureDebugStall                                  
00003e9d  NOROM_AUXSYSIFOpModeChange                                         
00003ed1  NOROM_IntRegister                                                  
00003f05  NOROM_SysCtrlIdle                                                  
00003f39  SoundTransmit                                                      
00003f6d  SwiP_destruct                                                      
00003fd5  GPIO_hwiIntFxn                                                     
00004000  __SYSMEM_SIZE                                                      
00004005  GPIO_setCallback                                                   
00004035  GPTimerCC26XX_close                                                
00004065  PINCC26XX_setMux                                                   
00004095  Power_releaseConstraint                                            
000040c5  Power_setConstraint                                                
00004125  _c_int00                                                           
000041ad  TimerP_setThreshold                                                
00004281  Power_registerNotify                                               
000042f9  List_put                                                           
0000431f  List_remove                                                        
00004345  ClockP_getTicks                                                    
00004369  ClockP_scheduleNextTick                                            
000043b1  NOROM_OSCHF_TurnOnXosc                                             
000043d5  TimerP_getMaxTicks                                                 
0000441d  Board_init                                                         
0000443d  ClockP_add                                                         
0000445d  NOROM_AUXADCEnableSync                                             
0000449d  Power_unregisterNotify                                             
000044bd  QueueP_put                                                         
000044dd  TimerP_dynamicStub                                                 
0000453d  PIN_close                                                          
0000455b  Power_getTransitionLatency                                         
00004579  SemaphoreP_constructBinary                                         
00004597  SwiP_or                                                            
000045f1  HwiP_destruct                                                      
0000460d  NOROM_ChipInfo_GetChipFamily                                       
00004661  SwiP_disable                                                       
000046d1  PowerCC26XX_calibrate                                              
000046eb  QueueP_get                                                         
00004705  SemaphoreP_createBinary                                            
0000471f  ClockP_destruct                                                    
00004739  HwiP_inISR                                                         
00004751  NOROM_ChipInfo_GetPackageType                                      
00004769  Power_idleFunc                                                     
00004799  TimerP_startup                                                     
000047b1  UART_Params_init                                                   
000047c9  _args_main                                                         
000047e1  __aeabi_lmul                                                       
00004811  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00004827  SemaphoreP_create                                                  
00004869  NOROM_IntUnregister                                                
0000487d  PowerCC26X2_calibrate                                              
00004891  Power_disablePolicy                                                
000048b9  TimerP_getCount64                                                  
000048cd  resetISR                                                           
000048e1  GPTimerCC26XX_Params_init                                          
000048f3  HwiP_Params_init                                                   
00004905  RingBuf_construct                                                  
00004929  ClockP_Params_init                                                 
00004939  ClockP_getCpuFreq                                                  
00004949  HwiP_clearInterrupt                                                
00004959  HwiP_disable                                                       
00004969  HwiP_post                                                          
00004979  PINCC26XX_getPinCount                                              
00004989  PowerCC26XX_schedulerDisable                                       
00004999  Power_enablePolicy                                                 
000049a9  SemaphoreP_Params_init                                             
000049b9  SwiP_Params_init                                                   
000049c9  TimerP_Params_init                                                 
000049e9  QueueP_empty                                                       
000049f7  QueueP_remove                                                      
00004a05  __TI_decompress_none                                               
00004a13  __aeabi_idiv0                                                      
00004a13  __aeabi_ldiv0                                                      
00004a15  ClockP_doTick                                                      
00004a21  ClockP_getSystemTickPeriod                                         
00004a2d  NOROM_CPUcpsid                                                     
00004a39  NOROM_CPUcpsie                                                     
00004a45  PIN_registerIntCb                                                  
00004a5d  PowerCC26XX_schedulerRestore                                       
00004a69  Power_getConstraintMask                                            
00004a75  Power_getDependencyCount                                           
00004a81  SwiP_getTrigger                                                    
00004a8d  TimerP_getFreq                                                     
00004a99  __TI_zero_init                                                     
00004aa5  ADCBuf_convertCancel                                               
00004aaf  UDMACC26XX_hwiIntFxn                                               
00004acb  ADCBufCC26X2_init                                                  
00004ad3  GPTimerCC26XX_setLoadValue                                         
00004adb  HwiP_restore                                                       
00004ae3  InitADC                                                            
00004aeb  UARTCC26XX_init                                                    
00004af3  malloc                                                             
00004b03  ClockP_stop                                                        
00004b09  NOROM_CPUdelay                                                     
00004b0f  QueueP_init                                                        
00004b15  UARTCC26XX_readPolling                                             
00004b1b  UARTCC26XX_writePolling                                            
00004b27  ADCBufCC26X2_getResolution                                         
00004b2f  ClockP_isActive                                                    
00004b33  ClockP_setTimeout                                                  
00004b37  HwiP_enable                                                        
00004b37  NoRTOS_start                                                       
00004b3b  QueueP_head                                                        
00004b3f  QueueP_next                                                        
00004b43  SemaphoreP_delete                                                  
00004b47  _system_pre_init                                                   
00004b4b  C$$EXIT                                                            
00004b4b  abort                                                              
00004b58  resourceDB                                                         
00004bac  BoardGpioInitTable                                                 
00004bd4  UARTCC26XX_fxnTable                                                
00004c24  ADCBufCC26X2_fxnTable                                              
00004c48  UART_defaultParams                                                 
00004c88  GPTimerCC26XX_config                                               
00004ca0  gptimerCC26XXHWAttrs                                               
00004ce4  GPIOCC26XX_config                                                  
00004cf8  PowerCC26X2_config                                                 
00004d30  adcbufCC26XXHWAttrs                                                
00004d50  outPinTypes                                                        
00004d60  ADCBUF_CONST                                                       
00004d61  ADCBUF_SOUND_CONST                                                 
00004d62  ADCBUF_TEMPERATURE_CONST                                           
00004d63  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004d64  ADCBuf_count                                                       
00004d65  PIN_TEST1_CONST                                                    
00004d66  PIN_TEST2_CONST                                                    
00004d67  PIN_DRIVE_SPEAKER_A_CONST                                          
00004d68  PIN_DRIVE_SPEAKER_B_CONST                                          
00004d69  UART_0_CONST                                                       
00004d6a  UART_count                                                         
00004d6b  CONFIG_GPTIMER_0_CONST                                             
00004d6c  CONFIG_GPTIMER_1_CONST                                             
00004d6d  GPTimer_count                                                      
00004d70  ADCBuf_config                                                      
00004d7c  UART_config                                                        
00004d88  inPinTypes                                                         
00004d94  outPinStrengths                                                    
00004db8  PINCC26XX_hwAttrs                                                  
00004dc0  UDMACC26XX_config                                                  
00004dd0  udmaCC26XXHWAttrs                                                  
00004df0  __c_args__                                                         
00004e80  __TI_Handler_Table_Base                                            
00004e8c  __TI_Handler_Table_Limit                                           
00004eb0  __TI_CINIT_Base                                                    
00004ed8  __TI_CINIT_Limit                                                   
00057fa8  __ccfg                                                             
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
20000000  uartCC26XXObjects                                                  
200001b4  adcbufCC26XXbjects                                                 
20000330  gptimerCC26XXObjects                                               
200003c8  pinHandleTable                                                     
20000548  udmaCC26XXObject                                                   
200005c9  driverlib_release_0_59848                                          
200005f8  i                                                                  
200005fc  j                                                                  
20000600  uart                                                               
20000604  PowerCC26X2_module                                                 
2000084c  ChirpDelay                                                         
200008c8  ChirpState                                                         
20000944  _hposcCoeffs                                                       
20000968  gpioCallbackFunctions                                              
20000978  gpioPinConfigs                                                     
200009a0  _lock                                                              
200009a4  _unlock                                                            
200009b0  SemaphoreP_defaultParams                                           
200009b8  ChirpSize                                                          
200009bc  ChirpIndex                                                         
200009c0  pinUpperBound                                                      
200009c4  pinLowerBound                                                      
200009cc  ClockP_tickPeriod                                                  
200009d0  adcBufCC26XXChannelLut0                                            
200009e4  HwiP_swiPIntNum                                                    
20000a00  g_pfnRAMVectors                                                    
20001a80  _sys_memory                                                        
20013c00  __stack                                                            
20014000  __STACK_END                                                        
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
ffffffff  __binit__                                                          
ffffffff  binit                                                              
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
UNDEFED   __mpu_init                                                         
UNDEFED   _system_post_cinit                                                 

[290 symbols]
