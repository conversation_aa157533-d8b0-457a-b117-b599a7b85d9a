#include <stdint.h>
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include "adc.h"
#include <ti/drivers/Board.h>
#include "ti_drivers_config.h"
#include "Devicetype.h"

uint16_t Battery0=0, BatteryX=0;

//*********************************************************************************************************
// return battery voltage in mV from ADC
//*********************************************************************************************************
uint16_t BatteryVoltage_GetMyVoltage()
{
    uint16_t x ;
    x= ADC_ReadSingleValueADCmV(ADCBUF_BATTERY_VOLTAGE)+270;  // add offset caused by protection diode
    return(x);
}

//*********************************************************************************************************
// temporary store a wireless received battery voltage
//*********************************************************************************************************
void BatteryVoltage_Store(DeviceTypes device, uint16_t BatteryVoltage)
{
    if (device==eDevX) BatteryX=BatteryVoltage;
    if (device==eDev0) Battery0=BatteryVoltage;
}

//*********************************************************************************************************
// read back the stored battery voltage
//*********************************************************************************************************
uint16_t BatteryVoltage_GetStoredValue(DeviceTypes device)
{
    uint16_t b=0;
    if (device==eDevX) b=BatteryX;
    if (device==eDev0) b=Battery0;
    return (b);
}


