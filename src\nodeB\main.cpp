#include <Arduino.h>
#include "i2s_receiver.h"
#include <ESPNowManager.h>
#include <AudioUtils.h>

// Konstanten für die Distanzberechnung (angepasst an TI-Projekt)
#define SPEED_OF_SOUND 343.0  // m/s bei 20°C
#define SAMPLE_RATE 20000     // Hz (angepasst von 60kHz im TI-Projekt)
#define TEMPERATURE_CELSIUS 20.0 // Konstante Temperatur
#define MIN_CORRELATION_THRESHOLD 50000 // Minimaler Korrelationswert für gültige Erkennung

// Signatur für Korrelation (vereinfachte Version der TI-Signatur)
#define SIGNATURE_SIZE 132
const int16_t SIGNATURE[SIGNATURE_SIZE] = { 
    1, 1, 1, -1, -1, 1, 1, 1, -1, -1, 1, 1, 1, -1, -1, -1, 1, 1, 1, -1, -1, -1, 1, 1, 1, -1, -1, -1, -1, 1, 1, 1,
    -1, -1, -1, -1, 1, 1, 1, 1, -1, -1, -1, -1, 1, 1, 1, 1, -1, -1, -1, -1, -1, 1, 1, 1, 1, -1, -1, -1, -1, -1, 1,
    1, 1, 1, -1, -1, -1, -1, -1, 1, 1, 1, 1, 1, -1, -1, -1, -1, -1, 1, 1, 1, 1, 1, -1, -1, -1, -1, -1, -1, 1, 1, 1,
    1, 1, -1, -1, -1, -1, -1, -1, 1, 1, 1, 1, 1, -1, -1, -1, -1, -1, -1, 1, 1, 1, 1, 1, 1, -1, -1, -1, -1, -1, -1,
    1, 1, 1, 1, 1, 1
};

I2SReceiver i2s;
ESPNowManager espNow;

const uint8_t SYNC_COMMAND = 0xAA;  // Sync command byte
uint32_t messageCount = 0;
uint32_t syncCount = 0;
unsigned long lastMessageTime = 0;

// Buffer für Audiosamples (angepasst an TI-Projekt)
#define NUMBER_OF_SOUND_SAMPLES (SAMPLE_RATE * RECORD_DURATION_MS / 1000)
int16_t audioBuffer[NUMBER_OF_SOUND_SAMPLES];

// Funktionsprototypenv
void onDataReceived(const uint8_t *mac, const uint8_t *data, int len);
uint16_t calculateDistance(int16_t* samples, size_t sampleCount);
float speedOfSound(float temperatureCelsius);

void setup() {
    Serial.begin(115200);
    while(!Serial);
    
    Serial.println("\n\n--- NodeB (Receiver) Starting ---");
    
    // MAC-Adresse ausgeben
    uint8_t macAddr[6];
    WiFi.macAddress(macAddr);
    Serial.printf("My MAC: %02X:%02X:%02X:%02X:%02X:%02X\n", 
                 macAddr[0], macAddr[1], macAddr[2], macAddr[3], macAddr[4], macAddr[5]);
    
    Serial.println("Initializing ESP-NOW...");
    espNow.init(false, onDataReceived);
    
    Serial.println("Initializing I2S...");
    i2s.init();
    
    Serial.printf("Ready to receive. Sample rate: %u Hz, Record duration: %u ms\n", 
                 SAMPLE_RATE, RECORD_DURATION_MS);
    Serial.printf("Will record %u samples on sync command (0x%02X)\n", 
                 calcSamples(RECORD_DURATION_MS), SYNC_COMMAND);
    
    Serial.println("NodeB initialized and ready");
}

void loop() {
    // Hauptschleife ist leer, da wir Callbacks verwenden
    delay(100);
}

// Callback für ESP-NOW Datenempfang
void onDataReceived(const uint8_t *mac, const uint8_t *data, int len) {
    messageCount++;
    lastMessageTime = millis();
    
    // Prüfen, ob es ein Sync-Kommando ist (ähnlich wie RFCommunication_TXListenToSound im TI-Projekt)
    if (len == 1 && data[0] == SYNC_COMMAND) {
        syncCount++;
        Serial.printf("Sync command received (#%u). Starting audio recording...\n", syncCount);
        
        // Audio aufnehmen (ähnlich wie SoundReceive_SampleSound im TI-Projekt)
        size_t samples = calcSamples(RECORD_DURATION_MS);
        i2s.read(audioBuffer, samples);
        
        // DC-Offset entfernen (wie im TI-Projekt)
        int32_t sum = 0;
        for (size_t i = 0; i < samples; i++) {
            sum += audioBuffer[i];
        }
        int16_t dcOffset = sum / samples;
        
        for (size_t i = 0; i < samples; i++) {
            audioBuffer[i] -= dcOffset;
        }
        
        // Distanz berechnen (ähnlich wie SoundReceive_CalculateDistance im TI-Projekt)
        uint16_t distanceIndex = calculateDistance(audioBuffer, samples);
        
        // Distanz in Meter umrechnen (ähnlich wie ConvertDistanceInSamplesToMeter im TI-Projekt)
        float temperatureCelsius = TEMPERATURE_CELSIUS; // In einer realen Anwendung würde man die Temperatur messen
        float speedOfSoundValue = speedOfSound(temperatureCelsius);
        float distanceMeters = (float)distanceIndex * speedOfSoundValue / SAMPLE_RATE;
        
        // Ergebnis an NodeA zurücksenden (ähnlich wie RFCommunication_TXMeasurements im TI-Projekt)
        uint8_t response[8];
        uint16_t distanceValue = (uint16_t)distanceIndex;
        memcpy(response, &distanceValue, sizeof(distanceValue));
        memcpy(response + 2, &distanceMeters, sizeof(distanceMeters));
        
        // Batteriespannung simulieren (im TI-Projekt wird die echte Batteriespannung gesendet)
        uint16_t batteryVoltage = 330; // 3.3V simuliert
        memcpy(response + 6, &batteryVoltage, sizeof(batteryVoltage));
        
        // Antwort senden
        espNow.send(mac, response, sizeof(response));
        
        Serial.printf("Distance calculation: %u samples (%.2f meters)\n", 
                     distanceIndex, distanceMeters);
    }
}

// Distanz durch Korrelation berechnen (verbesserte Version)
uint16_t calculateDistance(int16_t* samples, size_t sampleCount) {
    int32_t maxCorrelation = 0;
    uint16_t maxIndex = 0;
    
    // Korrelation für jede mögliche Position berechnen
    for (size_t i = 0; i < (sampleCount - SIGNATURE_SIZE); i++) {
        int32_t correlation = 0;
        
        // Korrelation mit der Signatur berechnen
        for (size_t j = 0; j < SIGNATURE_SIZE; j++) {
            correlation += (int32_t)(SIGNATURE[j] * samples[i + j]);
        }
        
        // Maximum finden
        if (correlation > maxCorrelation) {
            maxCorrelation = correlation;
            maxIndex = i;
        }
    }
    
    // Debug-Ausgabe
    Serial.printf("Max correlation: %ld, Raw index: %u\n", maxCorrelation, maxIndex);
    
    // Plausibilitätsprüfung: Korrelationswert muss über dem Schwellenwert liegen
    if (maxCorrelation < MIN_CORRELATION_THRESHOLD) {
        Serial.println("Warning: Correlation below threshold, result may be unreliable");
    }
    
    // Plausibilitätsprüfung: Distanz muss im sinnvollen Bereich liegen
    // Typischer Bereich für Ultraschall: 0.2m bis 10m
    float distanceMeters = (float)maxIndex * speedOfSound(TEMPERATURE_CELSIUS) / SAMPLE_RATE;
    if (distanceMeters > 10.0 || distanceMeters < 0.2) {
        Serial.printf("Warning: Calculated distance (%.2f m) outside of plausible range\n", distanceMeters);
    }
    
    return maxIndex;
}

// Schallgeschwindigkeit basierend auf Temperatur berechnen (ähnlich wie SpeedOfSound im TI-Projekt)
float speedOfSound(float temperatureCelsius) {
    // Im TI-Projekt: return (20.05 * sqrt(273.15 + temperatureCelsius));
    // Standardformel: return 331.3 + 0.606 * temperatureCelsius;
    
    // TI-Formel verwenden
    return (20.05 * sqrt(273.15 + temperatureCelsius));
}
