#include <ChirpGenerator.h>
#include <AudioUtils.h>
#include <cassert>
#include <cmath>

void testChirp() {
    ChirpGenerator gen;
    const int16_t* chirp = gen.generate();

    // Verify properties
    assert(gen.size() == CHIRP_TOTAL);  // Should be 2400 samples at 48kHz for 50ms
    assert(gen.size() == 2400);         // Explicit check for expected sample count

    // Check that chirp starts with low amplitude (Hann fade-in)
    assert(abs(chirp[0]) == 0);         // Should start at zero due to Hann fade-in

    // Check that middle samples have high amplitude (full range)
    assert(abs(chirp[gen.size()/2]) > 25000);  // Middle should have high amplitude

    // Check that chirp ends with low amplitude (Hann fade-out)
    assert(abs(chirp[gen.size()-1]) < 5000);   // Should end quiet due to Hann fade-out

    // Verify sample rate and duration calculations
    assert(SAMPLE_RATE == 48000);
    assert(CHIRP_TOTAL == 2400);        // 50ms * 48kHz = 2400 samples
    assert(F_START == 800.0f);          // New frequency range
    assert(F_END == 3500.0f);
}

int main() {
    testChirp();
    return 0;
}