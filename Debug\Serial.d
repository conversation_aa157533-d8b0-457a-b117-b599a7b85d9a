# FIXED

Serial.obj: ../Serial.c
Serial.obj: C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/UART.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stddef.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_ti_config.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/linkage.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdint.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_stdint40.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/stdint.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/cdefs.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_types.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_types.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_stdint.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_stdint.h
Serial.obj: C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/Board.h
Serial.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Debug/syscfg/ti_drivers_config.h
Serial.obj: C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/DeviceFamily.h
Serial.obj: C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/PIN.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdbool.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdio.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdarg.h
Serial.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/string.h
Serial.obj: ../serial.h
Serial.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/RFCommunication.h
Serial.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Devicetype.h
Serial.obj: ../SoundReceive.h

../Serial.c: 
C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/UART.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stddef.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_ti_config.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/linkage.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_stdint40.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/cdefs.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_types.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_types.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_stdint.h: 
C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/Board.h: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Debug/syscfg/ti_drivers_config.h: 
C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/DeviceFamily.h: 
C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/PIN.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdbool.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdio.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdarg.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/string.h: 
../serial.h: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/RFCommunication.h: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Devicetype.h: 
../SoundReceive.h: 
