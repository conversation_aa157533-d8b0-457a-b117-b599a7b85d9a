import serial
import time

def debug_microphone():
    """Debug script to see what ESP32 is sending"""
    
    print("🔍 ESP32 Microphone Debug Tool")
    print("=" * 40)
    
    # Connect to ESP32 on COM4
    print("📡 Connecting to ESP32 on COM4...")
    try:
        ser = serial.Serial('COM4', 115200, timeout=2)
        time.sleep(2)
        print("✅ Connected successfully!")
    except Exception as e:
        print(f"❌ Failed to connect to COM4: {e}")
        return
    
    print("📤 Sending 'r' command...")
    ser.write(b'r\n')
    ser.flush()
    
    print("👂 Listening to ESP32 output...")
    print("⏹️  Press Ctrl+C to stop")
    print("-" * 40)
    
    line_count = 0
    start_time = time.time()
    
    try:
        while True:
            line = ser.readline().decode(errors="ignore").strip()
            
            if line:
                line_count += 1
                elapsed = time.time() - start_time
                
                # Show first 20 lines, then every 1000th line
                if line_count <= 20 or line_count % 1000 == 0:
                    print(f"[{line_count:6d}] ({elapsed:6.1f}s) {line}")
                
                # Check for special markers
                if "--- AUDIO DATA START ---" in line:
                    print("🎵 AUDIO DATA STARTED!")
                    start_time = time.time()  # Reset timer for data capture
                    
                elif "--- AUDIO DATA END ---" in line:
                    print("🏁 AUDIO DATA ENDED!")
                    print(f"📊 Total lines received: {line_count}")
                    print(f"⏱️  Total time: {elapsed:.1f} seconds")
                    break
                    
                elif "Progress:" in line:
                    print(f"📊 {line}")
            
            # Safety timeout
            if time.time() - start_time > 30:
                print("⏰ 30 second timeout reached!")
                break
                
    except KeyboardInterrupt:
        print("\n⏹️  Stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        ser.close()
        print("🔌 Serial connection closed")
        print(f"📈 Final stats: {line_count} lines in {time.time() - start_time:.1f} seconds")

if __name__ == "__main__":
    debug_microphone()
