import numpy as np
import soundfile as sf
import matplotlib.pyplot as plt
from scipy.signal import correlate, hann

# === Parameter aus deinem C++ Code ===
SAMPLE_RATE = 48000
CHIRP_DURATION = 0.050  # 50 ms
CHIRP_SAMPLES = int(SAMPLE_RATE * CHIRP_DURATION)
F_START = 800.0
F_END = 3500.0
CHIRP_K = (F_END - F_START) / CHIRP_SAMPLES
FADE_SAMPLES = 144

# === Chirp-Signal generieren ===
def generate_sine_chirp():
    n = np.arange(CHIRP_SAMPLES)
    t = n / SAMPLE_RATE
    phase = 2.0 * np.pi * (F_START * t + CHIRP_K * n * t / 2.0)
    chirp = np.sin(phase)

    # Hann-Fenster für Fade-in/out
    ramp = np.ones_like(chirp)
    hann_window = hann(2 * FADE_SAMPLES)
    ramp[:FADE_SAMPLES] = hann_window[:FADE_SAMPLES]
    ramp[-FADE_SAMPLES:] = hann_window[-FADE_SAMPLES:]
    chirp *= ramp

    return chirp

# === WAV-Datei laden ===
def load_wav(filepath):
    signal, fs = sf.read(filepath)
    if fs != SAMPLE_RATE:
        raise ValueError(f"Sample rate mismatch! Expected {SAMPLE_RATE}, got {fs}")
    if signal.ndim > 1:
        signal = signal[:, 0]  # nur erster Kanal
    return signal

# === Korrelation durchführen ===
def correlate_with_chirp(recorded, chirp):
    correlation = correlate(recorded, chirp, mode='valid')
    return correlation

# === Hauptfunktion ===
def main():
    # Pfade zu den WAV-Dateien
    wav_paths = [
        "aufnahme.wav",  # Pfad zur aufgenommenen Datei
        "noise.wav"      # Pfad zur Noise-Datei
    ]

    chirp = generate_sine_chirp()

    # Verarbeite beide Dateien
    for i, wav_path in enumerate(wav_paths):
        try:
            print(f"\n=== Verarbeitung: {wav_path} ===")
            recorded = load_wav(wav_path)
            corr = correlate_with_chirp(recorded, chirp)

            # === Visualisieren ===
            plt.figure(figsize=(12, 8))

            # Subplot 1: Original Signal
            plt.subplot(2, 1, 1)
            time_axis = np.arange(len(recorded)) / SAMPLE_RATE * 1000  # in ms
            plt.plot(time_axis, recorded)
            plt.title(f"Original Signal: {wav_path}")
            plt.xlabel("Zeit (ms)")
            plt.ylabel("Amplitude")
            plt.grid()

            # Subplot 2: Korrelation
            plt.subplot(2, 1, 2)
            corr_time = np.arange(len(corr)) / SAMPLE_RATE * 1000  # in ms
            plt.plot(corr_time, corr)
            plt.title(f"Korrelation mit Chirp: {wav_path}")
            plt.xlabel("Zeit (ms)")
            plt.ylabel("Korrelationswert")
            plt.grid()

            plt.tight_layout()
            plt.show()

            # Maxima ausgeben
            max_idx = np.argmax(corr)
            max_value = corr[max_idx]
            print(f"Maximale Korrelation bei Index {max_idx} (Zeit: {max_idx / SAMPLE_RATE * 1000:.2f} ms)")
            print(f"Korrelationswert: {max_value:.6f}")

        except FileNotFoundError:
            print(f"❌ Datei nicht gefunden: {wav_path}")
        except Exception as e:
            print(f"❌ Fehler bei {wav_path}: {e}")

if __name__ == "__main__":
    main()
