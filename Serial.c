#include <ti/drivers/UART.h>
#include <ti/drivers/Board.h>
#include "ti_drivers_config.h"
#include <stdint.h>
#include <stddef.h>
#include <stdio.h>
#include <string.h>
#include "serial.h"
#include <RFCommunication.h>
#include <stdbool.h>
#include "SoundReceive.h"

#define MaxRXSize 15
char UARTbuffer[100];
UART_Handle uart;
char UARTRXbuffer[MaxRXSize];
volatile uint16_t RxIndex=0;
volatile bool SerialLineReceived=false;
char receivedByte=0;

void Uart_ReadCallback(UART_Handle handle, void *rxBuf, size_t size);


//*********************************************************************************************************
// Initialize UART
//*********************************************************************************************************
void Serial_Init(void)
{
 UART_init();
 UART_Params params;
 UART_Params_init(&params);
 params.baudRate = 115200;
 params.readMode      = UART_MODE_CALLBACK;
 params.readDataMode  = UART_DATA_BINARY;
 params.writeDataMode = UART_DATA_BINARY;
 params.readCallback  = Uart_ReadCallback;
 params.writeMode = UART_MODE_BLOCKING;
 params.readTimeout = UART_WAIT_FOREVER;
 params.writeTimeout = UART_WAIT_FOREVER;
 uart = UART_open(UART_0, &params);
 UART_read(uart, &receivedByte, 1);   // start the first serial read.
}

//*********************************************************************************************************
// Add received serial byte to buffer until until newline received
//*********************************************************************************************************
void Uart_ReadCallback(UART_Handle handle, void *rxBuf, size_t size)
{
 if (!SerialLineReceived) // ignore serial port if previous command not processed yet
 {
   if ( (RxIndex>=MaxRXSize) || (receivedByte==13) )  SerialLineReceived=true; else UARTRXbuffer[RxIndex++]=receivedByte;
 }
 UART_read(uart, &receivedByte, 1);
}

//*********************************************************************************************************
// Analyze received serial command
//*********************************************************************************************************
SerialCommandType Serial_CheckCommand(void)
{
 SerialCommandType CT=eSCNone;
 if (SerialLineReceived )
  {

    if (RxIndex==3)
    {
     if ((UARTRXbuffer[0]=='M') && (UARTRXbuffer[1]=='m') && (UARTRXbuffer[2]=='x')) CT=eSCMeasureMX;
     if ((UARTRXbuffer[0]=='M') && (UARTRXbuffer[1]=='m') && (UARTRXbuffer[2]=='0')) CT=eSCMeasureM0;
     if ((UARTRXbuffer[0]=='M') && (UARTRXbuffer[1]=='x') && (UARTRXbuffer[2]=='0')) CT=eSCMeasureX0;
     if ((UARTRXbuffer[0]=='M') && (UARTRXbuffer[1]=='a') && (UARTRXbuffer[2]=='l')) CT=eSCMeasureAll;
     if ((UARTRXbuffer[0]=='R') && (UARTRXbuffer[1]=='e') && (UARTRXbuffer[2]=='0')) CT=eSCReadSamples0;
     if ((UARTRXbuffer[0]=='R') && (UARTRXbuffer[1]=='e') && (UARTRXbuffer[2]=='x')) CT=eSCReadSamplesX;
     if ((UARTRXbuffer[0]=='R') && (UARTRXbuffer[1]=='e') && (UARTRXbuffer[2]=='m')) CT=eSCReadSamplesM;

    }
  RxIndex=0;
  SerialLineReceived=0;
  }
 return(CT);
}

//*********************************************************************************************************
// Functions to send content over serial port
//*********************************************************************************************************
void SerialPrintByte(uint8_t Byte )
{
  static uint8_t temp;
  temp = Byte;
  UART_write(uart, &temp, 1);
}

void Serial_PrintString(char* S)
{
int16_t i=0;
while (S[i]) {  UART_write(uart, S+i, 1);i++; }
}

void Serial_PrintInteger(int16_t i)
{
 char tmp[10];
 sprintf(tmp,"%d",i);
 Serial_PrintString(tmp);
}

void SerialPrintInt16Array(uint16_t* Buffer,uint16_t BufferSize)
{
 uint16_t i=0;
 uint8_t j;
 for (i=0;i<BufferSize;i++)
  {
   j=(uint8_t)(Buffer[i]>>8); SerialPrintByte(j);
   j=(uint8_t)(Buffer[i]); SerialPrintByte(j);
  }
}

void Serial_PrintMeasurements(uint8_t Type, uint8_t NumberOfMeasurements,uint16_t P1, uint16_t P2,uint16_t P3,uint16_t P4,uint16_t P5,uint16_t P6,uint16_t P7 )
{
    Serial_PrintString("DT");
    SerialPrintByte(Type);
    SerialPrintByte(NumberOfMeasurements);
    uint16_t Buffer[7];
    Buffer[0]=P1;
    Buffer[1]=P2;
    Buffer[2]=P3;
    Buffer[3]=P4;
    Buffer[4]=P5;
    Buffer[5]=P6;
    Buffer[6]=P7;
    SerialPrintInt16Array(Buffer,NumberOfMeasurements);
}

void Serial_CopyReceivedArraySectionToSerial()
{
    Serial_PrintString("DT");
    SerialPrintByte(RFCommunication_GetSectionReceivedArraySectionPacket());
    SerialPrintByte(SamplesInASection);
    uint16_t Buffer[SamplesInASection];
    RFCommunication_GetDataReceivedArraySectionPacket(Buffer);
    SerialPrintInt16Array(Buffer,SamplesInASection);
   }

void Serial_CopyArraySectionToSerial(uint8_t Section,uint16_t* Array )
{
    Serial_PrintString("DT");
    SerialPrintByte(Section);
    SerialPrintByte(SamplesInASection);
    uint16_t i;
    uint16_t Buffer[SamplesInASection];
    for (i=0;i<SamplesInASection;i++) {  Buffer[i]= Array[i+Section*64]; }
    SerialPrintInt16Array(Buffer,SamplesInASection);
}


