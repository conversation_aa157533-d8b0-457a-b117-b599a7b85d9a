
#include "i2s_receiver.h"

void I2SReceiver::init() {
    // Use the same pin configuration as in the microphone test
    pinConfig = {
        .bck_io_num = 2,          // I2S_SCK
        .ws_io_num = 15,          // I2S_WS
        .data_out_num = I2S_PIN_NO_CHANGE,
        .data_in_num = 13         // I2S_SD
    };
    
    i2s_config_t i2s_config = {
        .mode = (i2s_mode_t)(I2S_MODE_MASTER | I2S_MODE_RX),
        .sample_rate = 44100,     // Same as microphone test
        .bits_per_sample = I2S_BITS_PER_SAMPLE_16BIT, // Use 16-bit like in the test
        .channel_format = I2S_CHANNEL_FMT_ONLY_LEFT,  // Mono config
        .communication_format = I2S_COMM_FORMAT_STAND_I2S,
        .intr_alloc_flags = ESP_INTR_FLAG_LEVEL1,
        .dma_buf_count = 8,
        .dma_buf_len = 64,
        .use_apll = false,
        .tx_desc_auto_clear = false,
        .fixed_mclk = 0
    };
    
    i2s_driver_install(I2S_NUM_0, &i2s_config, 0, NULL);
    i2s_set_pin(I2S_NUM_0, &pinConfig);
}

bool I2SReceiver::read(int16_t* buffer, size_t sampleCount) {
    size_t bytesRead = 0;
    size_t bytesToRead = sampleCount * sizeof(int16_t);
    
    esp_err_t result = i2s_read(
        I2S_NUM_0,
        (void*)buffer,
        bytesToRead,
        &bytesRead,
        portMAX_DELAY
    );
    
    return (result == ESP_OK && bytesRead == bytesToRead);
}
