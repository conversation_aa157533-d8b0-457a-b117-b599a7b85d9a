******************************************************************************
                  TI ARM Linker PC v20.2.4                     
******************************************************************************
>> Linked Mon Feb 22 19:52:03 2021

OUTPUT FILE NAME:   <SoundPositioning.out>
ENTRY POINT SYMBOL: "resetISR"  address: 000048d5


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00058000  00004f36  000530ca  R  X
  GPRAM                 11000000   00002000  00000000  00002000  RW X
  SRAM                  20000000   00014000  00004ee2  0000f11e  RW X
  LOG_DATA              90000000   00040000  00000000  00040000  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004df8   00004df8    r-x
  00000000    00000000    000000d8   000000d8    r-- .intvecs
  000000d8    000000d8    00004a86   00004a86    r-x .text
  00004b60    00004b60    00000298   00000298    r-- .const
00004df8    00004df8    00000008   00000008    rw-
  00004df8    00004df8    00000008   00000008    rw- .args
00004e00    00004e00    000000e0   000000e0    r--
  00004e00    00004e00    000000e0   000000e0    r-- .cinit
00057fa8    00057fa8    00000058   00000058    r--
  00057fa8    00057fa8    00000058   00000058    r-- .ccfg
20000000    20000000    000009ea   00000000    rw-
  20000000    20000000    00000604   00000000    rw- .bss
  20000604    20000604    000003e6   00000000    rw- .data
20000a00    20000a00    000000d8   00000000    rw-
  20000a00    20000a00    000000d8   00000000    rw- .vtable_ram
20001870    20001870    00000010   00000000    rw-
  20001870    20001870    00000010   00000000    rw- .TI.bound:dmaADCPriControlTableEntry
20001a70    20001a70    00004010   00000000    rw-
  20001a70    20001a70    00000010   00000000    rw- .TI.bound:dmaADCAltControlTableEntry
  20001a80    20001a80    00004000   00000000    rw- .sysmem
20013c00    20013c00    00000400   00000000    rw-
  20013c00    20013c00    00000400   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000d8     
                  00000000    000000d8     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.intvecs)

.text      0    000000d8    00004a86     
                  000000d8    000001e0     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_init)
                  000002b8    00000198                      : PowerCC26X2.oem4f (.text:Power_sleep)
                  00000450    00000190                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf1)
                  000005e0    0000018c                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:runCalibrateFsm)
                  0000076c    00000174                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_open)
                  000008e0    00000174                      : PowerCC26X2.oem4f (.text:Power_init)
                  00000a54    00000164                      : UARTCC26XX.oem4f (.text:UARTCC26XX_open)
                  00000bb8    00000154                      : UARTCC26XX.oem4f (.text:UARTCC26XX_hwiIntFxn)
                  00000d0c    0000013a                      : UARTCC26XX.oem4f (.text:UARTCC26XX_read)
                  00000e46    00000002     ti_drivers_config.obj (.text:Board_initHook)
                  00000e48    00000120     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureXOSCHF)
                  00000f68    00000114                      : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfg)
                  0000107c    00000110                      : GPIOCC26XX.oem4f (.text:GPIO_setConfig)
                  0000118c    00000100                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convert)
                  0000128c    00000100     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:aligned_alloc)
                  0000138c    000000f0     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlStandby)
                  0000147c    000000e4     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free)
                  00001560    000000e0     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_standbyPolicy)
                  00001640    000000e0     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_write)
                  00001720    000000d8     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_construct)
                  000017f8    000000c4     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_init)
                  000018bc    000000c0                      : PowerCC26X2.oem4f (.text:Power_releaseDependency)
                  0000197c    000000c0                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initIO)
                  00001a3c    000000bc     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_construct)
                  00001af8    000000b8     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_setDependency)
                  00001bb0    000000b8                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initHw)
                  00001c68    000000b4     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_startup)
                  00001d1c    000000ac                     : ClockPTimer_nortos.oem4f (.text:ClockP_workFuncDynamic)
                  00001dc8    000000ac     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_open)
                  00001e74    000000ac     driverlib.lib : setup.obj (.text:TrimAfterColdResetWakeupFromShutDown)
                  00001f20    000000a4     SoundTX.obj (.text:Timer2AInterruptHandler)
                  00001fc4    000000a0     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_pend)
                  00002064    0000009c                     : SwiP_nortos.oem4f (.text:SwiP_dispatch)
                  00002100    0000009c     rtsv7M4_T_le_v4SPD16_eabi.lib : memcpy_t2.asm.obj (.text)
                  0000219c    00000098     SoundTX.obj (.text:InitTimer2)
                  00002234    00000098     driverlib.lib : setup.obj (.text:NOROM_SetupTrimDevice)
                  000022cc    00000098     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeTxFifoFlush)
                  00002364    00000096     rtsv7M4_T_le_v4SPD16_eabi.lib : ull_div_t2.asm.obj (.text)
                  000023fa    00000092     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_writeCancel)
                  0000248c    00000088                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_open)
                  00002514    00000088     driverlib.lib : osc.obj (.text:NOROM_OSCHF_AttemptToSwitchToXosc)
                  0000259c    00000088     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf2)
                  00002624    00000088                      : PowerCC26X2.oem4f (.text:disableLFClockQualifiers)
                  000026ac    00000088                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:updateSubSecInc)
                  00002734    00000084     ti_drivers_config.obj (.text:Board_sendExtFlashByte)
                  000027b8    00000084     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_close)
                  0000283c    00000080                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_hwiFxn)
                  000028bc    00000080     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_start)
                  0000293c    00000080                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_construct)
                  000029bc    00000080     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlSetRechargeBeforePowerDown)
                  00002a3c    0000007a     rtsv7M4_T_le_v4SPD16_eabi.lib : memset_t2.asm.obj (.text)
                  00002ab6    00000002     driverlib.lib : interrupt.obj (.text:IntDefaultHandler)
                  00002ab8    00000078     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_walkQueueDynamic)
                  00002b30    00000074     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_resetHw)
                  00002ba4    00000074                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_auxISR)
                  00002c18    00000070                      : PINCC26XX.oem4f (.text:PIN_add)
                  00002c88    00000070     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:split)
                  00002cf8    0000006c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipType)
                  00002d64    00000068                   : osc.obj (.text:NOROM_OSCHF_SwitchToRcOscTurnOffXosc)
                  00002dcc    00000068     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_post)
                  00002e34    00000068                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_start)
                  00002e9c    00000068     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_lzss.c.obj (.text:decompress:lzss:__TI_decompress_lzss)
                  00002f04    00000068     mainNew.obj (.text:main)
                  00002f6c    00000064     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_control)
                  00002fd0    00000062                      : UARTCC26XX.oem4f (.text:UARTCC26XX_swiIntFxn)
                  00003032    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_destruct)
                  00003034    00000060     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_write)
                  00003094    0000005c                      : PINCC26XX.oem4f (.text:PIN_remove)
                  000030f0    0000005c                      : UARTCC26XX.oem4f (.text:startTxFifoEmptyClk)
                  0000314c    00000058                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_loadDMAControlTableEntry)
                  000031a4    00000058                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_stop)
                  000031fc    00000058                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_initiateCalibration)
                  00003254    00000054                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_adjustRawValues)
                  000032a8    00000054     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOff)
                  000032fc    00000050     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_construct)
                  0000334c    0000004e     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_close)
                  0000339a    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.text:_nop)
                  0000339c    0000004c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_cleanADC)
                  000033e8    0000004c     ti_drivers_config.obj (.text:Board_wakeUpExtFlash)
                  00003434    0000004c     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_initHw)
                  00003480    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_start)
                  000034cc    0000004c     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOn)
                  00003518    0000004c     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readCancel)
                  00003564    0000004c                      : UDMACC26XX.oem4f (.text:UDMACC26XX_open)
                  000035b0    0000004a                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_control)
                  000035fa    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:clkFxn)
                  000035fc    00000048     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertAdjustedToMicroVolts)
                  00003644    00000048     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_construct)
                  0000368c    00000048     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputEnable)
                  000036d4    00000048                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_RCOSC_clockFunc)
                  0000371c    00000048                      : PowerCC26X2.oem4f (.text:switchXOSCHF)
                  00003764    00000048                      : UARTCC26XX.oem4f (.text:writeFinishedDoCallback)
                  000037ac    00000046                      : RingBuf.oem4f (.text:RingBuf_put)
                  000037f2    00000002                      : PowerCC26X2.oem4f (.text:emptyClockFunc)
                  000037f4    00000044                      : ADCBuf.oem4f (.text:ADCBuf_init)
                  00003838    00000044     mainNew.obj (.text:InitUart)
                  0000387c    00000044     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetHwRevision)
                  000038c0    00000044     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_swi)
                  00003904    00000044     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_post)
                  00003948    00000044                     : SwiP_nortos.oem4f (.text:SwiP_restore)
                  0000398c    00000044     drivers_cc13x2.a : UART.oem4f (.text:UART_init)
                  000039d0    00000044     rtsv7M4_T_le_v4SPD16_eabi.lib : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit:__TI_auto_init_nobinit_nopinit)
                  00003a14    00000042     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_swiFxn)
                  00003a56    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:faultISR)
                  00003a58    00000040     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertCancel)
                  00003a98    00000040     ti_drivers_config.obj (.text:Board_shutDownExtFlash)
                  00003ad8    00000040     driverlib.lib : osc.obj (.text:NOROM_OSC_HPOSCRelativeFrequencyOffsetGet)
                  00003b18    00000040                   : timer.obj (.text:NOROM_TimerIntRegister)
                  00003b58    00000040     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_get)
                  00003b98    00000040                      : UART.oem4f (.text:UART_open)
                  00003bd8    0000003c                      : PINCC26XX.oem4f (.text:PIN_setOutputValue)
                  00003c14    00000038                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_configDMA)
                  00003c4c    00000038                      : PINCC26XX.oem4f (.text:PIN_setConfig)
                  00003c84    00000038     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCurrentTick)
                  00003cbc    00000038                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_initDevice)
                  00003cf4    00000038     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:notify)
                  00003d2c    00000038                      : UARTCC26XX.oem4f (.text:readData)
                  00003d64    00000038                      : UARTCC26XX.oem4f (.text:writeData)
                  00003d9c    00000036     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setNextTick)
                  00003dd2    00000036     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_close)
                  00003e08    00000034     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicksUntilInterrupt)
                  00003e3c    00000034     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XXSetLoadMatch)
                  00003e70    00000034                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_configureDebugStall)
                  00003ea4    00000034     driverlib.lib : aux_sysif.obj (.text:NOROM_AUXSYSIFOpModeChange)
                  00003ed8    00000034                   : interrupt.obj (.text:NOROM_IntRegister)
                  00003f0c    00000034                   : sys_ctrl.obj (.text:NOROM_SysCtrlIdle)
                  00003f40    00000034     SoundTX.obj (.text:SoundTransmit)
                  00003f74    00000034     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_destruct)
                  00003fa8    00000034     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:readData2RingBuf)
                  00003fdc    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_hwiIntFxn)
                  0000400c    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_setCallback)
                  0000403c    00000030                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_close)
                  0000406c    00000030                      : PINCC26XX.oem4f (.text:PINCC26XX_setMux)
                  0000409c    00000030                      : PowerCC26X2.oem4f (.text:Power_releaseConstraint)
                  000040cc    00000030                      : PowerCC26X2.oem4f (.text:Power_setConstraint)
                  000040fc    00000030     driverlib.lib : timer.obj (.text:TimerIntNumberGet)
                  0000412c    00000030     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.text:_c_int00:_c_int00)
                  0000415c    0000002c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_acquireADCSemaphore)
                  00004188    0000002c                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_releaseADCSemaphore)
                  000041b4    0000002c     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setThreshold)
                  000041e0    0000002a     SoundTX.obj (.text:TimerLoadSet)
                  0000420a    0000002a     SoundTX.obj (.text:TimerMatchSet)
                  00004234    0000002a     SoundTX.obj (.text:TimerPrescaleMatchSet)
                  0000425e    0000002a     SoundTX.obj (.text:TimerPrescaleSet)
                  00004288    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_registerNotify)
                  000042b0    00000028     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_insert)
                  000042d8    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:oscillatorISR)
                  00004300    00000026                      : List.oem4f (.text:List_put)
                  00004326    00000026                      : List.oem4f (.text:List_remove)
                  0000434c    00000024     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicks)
                  00004370    00000024                     : ClockPTimer_nortos.oem4f (.text:ClockP_scheduleNextTick)
                  00004394    00000024                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_dispatch)
                  000043b8    00000024     driverlib.lib : osc.obj (.text:NOROM_OSCHF_TurnOnXosc)
                  000043dc    00000024     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getMaxTicks)
                  00004400    00000024     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:getTdcSemaphore)
                  00004424    00000020     ti_drivers_config.obj (.text:Board_init)
                  00004444    00000020     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_add)
                  00004464    00000020     driverlib.lib : aux_adc.obj (.text:NOROM_AUXADCEnableSync)
                  00004484    00000020     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfgMux)
                  000044a4    00000020                      : PowerCC26X2.oem4f (.text:Power_unregisterNotify)
                  000044c4    00000020     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_put)
                  000044e4    00000020                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_dynamicStub)
                  00004504    00000020     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:threadSafeStdbyDisRelease)
                  00004524    00000020                      : UARTCC26XX.oem4f (.text:threadSafeStdbyDisSet)
                  00004544    0000001e                      : PINCC26XX.oem4f (.text:PIN_close)
                  00004562    0000001e                      : PowerCC26X2.oem4f (.text:Power_getTransitionLatency)
                  00004580    0000001e     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_constructBinary)
                  0000459e    0000001e                     : SwiP_nortos.oem4f (.text:SwiP_or)
                  000045bc    0000001e     SoundTX.obj (.text:TimerDisable)
                  000045da    0000001e     SoundTX.obj (.text:TimerEnable)
                  000045f8    0000001c     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_destruct)
                  00004614    0000001c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipFamily)
                  00004630    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_hwi)
                  0000464c    0000001c     SoundTX.obj (.text:PRCMLoadGet)
                  00004668    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_disable)
                  00004684    0000001c     SoundTX.obj (.text:TimerMatchGet)
                  000046a0    0000001c     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_initHw)
                  000046bc    0000001c     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_remove)
                  000046d8    0000001a     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:PowerCC26XX_calibrate)
                  000046f2    0000001a     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_get)
                  0000470c    0000001a                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_createBinary)
                  00004726    00000018                     : ClockPTimer_nortos.oem4f (.text:ClockP_destruct)
                  0000473e    00000002                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:intDefaultHandler)
                  00004740    00000018                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_inISR)
                  00004758    00000018     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetPackageType)
                  00004770    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_idleFunc)
                  00004788    00000018     SoundTX.obj (.text:TimerIntEnable)
                  000047a0    00000018     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_startup)
                  000047b8    00000018     drivers_cc13x2.a : UART.oem4f (.text:UART_Params_init)
                  000047d0    00000018     rtsv7M4_T_le_v4SPD16_eabi.lib : args_main.c.obj (.text:_args_main)
                  000047e8    00000018                                   : ll_mul_t2.asm.obj (.text)
                  00004800    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:switchToTCXO)
                  00004818    00000016     driverlib.lib : chipinfo.obj (.text:NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated)
                  0000482e    00000016     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_create)
                  00004844    00000016                     : SwiP_nortos.oem4f (.text:maxbit)
                  0000485a    00000014     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_freqToCounts)
                  0000486e    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:nmiISR)
                  00004870    00000014     driverlib.lib : interrupt.obj (.text:NOROM_IntUnregister)
                  00004884    00000014     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_calibrate)
                  00004898    00000014                      : PowerCC26X2.oem4f (.text:Power_disablePolicy)
                  000048ac    00000014     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_handleHwi)
                  000048c0    00000014                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCount64)
                  000048d4    00000014                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:resetISR)
                  000048e8    00000012     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_Params_init)
                  000048fa    00000012     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_Params_init)
                  0000490c    00000012     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_construct)
                  0000491e    00000012     SoundTX.obj (.text:TimerIntClear)
                  00004930    00000010     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_Params_init)
                  00004940    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_getCpuFreq)
                  00004950    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_clearInterrupt)
                  00004960    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_disable)
                  00004970    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_post)
                  00004980    00000010     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_getPinCount)
                  00004990    00000010     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerDisable)
                  000049a0    00000010     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_enablePolicy)
                  000049b0    00000010     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_Params_init)
                  000049c0    00000010                     : SwiP_nortos.oem4f (.text:SwiP_Params_init)
                  000049d0    00000010                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_Params_init)
                  000049e0    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:uartPostNotify)
                  000049f0    0000000e     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_empty)
                  000049fe    0000000e                     : QueueP_nortos.oem4f (.text:QueueP_remove)
                  00004a0c    0000000e     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_none.c.obj (.text:decompress:none:__TI_decompress_none)
                  00004a1a    00000002                                   : div0.asm.obj (.text)
                  00004a1c    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_doTick)
                  00004a28    0000000c                     : ClockPTimer_nortos.oem4f (.text:ClockP_getSystemTickPeriod)
                  00004a34    0000000c     driverlib.lib : cpu.obj (.text:NOROM_CPUcpsid)
                  00004a40    0000000c                   : cpu.obj (.text:NOROM_CPUcpsie)
                  00004a4c    0000000c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_registerIntCb)
                  00004a58    0000000c     SoundTX.obj (.text:PRCMLoadSet)
                  00004a64    0000000c     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerRestore)
                  00004a70    0000000c     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_getConstraintMask)
                  00004a7c    0000000c                      : PowerCC26X2.oem4f (.text:Power_getDependencyCount)
                  00004a88    0000000c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_getTrigger)
                  00004a94    0000000c                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getFreq)
                  00004aa0    0000000c     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init)
                  00004aac    0000000a     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_convertCancel)
                  00004ab6    0000000a                      : UDMACC26XX.oem4f (.text:UDMACC26XX_hwiIntFxn)
                  00004ac0    0000000a                      : UARTCC26XX.oem4f (.text:readSemCallback)
                  00004aca    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_conversionCallback)
                  00004ad2    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_init)
                  00004ada    00000008                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_setLoadValue)
                  00004ae2    00000008     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_restore)
                  00004aea    00000008     ADC.obj (.text:InitADC)
                  00004af2    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_init)
                  00004afa    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:malloc)
                  00004b02    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeSemCallback)
                  00004b0a    00000006     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_stop)
                  00004b10    00000006     driverlib.lib : cpu.obj (.text:NOROM_CPUdelay)
                  00004b16    00000006     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_init)
                  00004b1c    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readPolling)
                  00004b22    00000006                      : UARTCC26XX.oem4f (.text:UARTCC26XX_writePolling)
                  00004b28    00000006                      : PowerCC26X2.oem4f (.text:delayUs)
                  00004b2e    00000004                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_getResolution)
                  00004b32    00000004     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:CPUwfi)
                  00004b36    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_isActive)
                  00004b3a    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_setTimeout)
                  00004b3e    00000004                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_enable)
                  00004b42    00000004                     : QueueP_nortos.oem4f (.text:QueueP_head)
                  00004b46    00000004                     : QueueP_nortos.oem4f (.text:QueueP_next)
                  00004b4a    00000004                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_delete)
                  00004b4e    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : pre_init.c.obj (.text:_system_pre_init)
                  00004b52    00000004                                   : exit.c.obj (.text:abort:abort)
                  00004b56    00000004     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureRFCoreClocks)
                  00004b5a    00000004                      : PowerCC26X2.oem4f (.text:nopResourceHandler)

.const     0    00004b60    00000298     
                  00004b60    00000054     drivers_cc13x2.a : PowerCC26X2.oem4f (.const:resourceDB)
                  00004bb4    00000028     ti_drivers_config.obj (.const:BoardGpioInitTable)
                  00004bdc    00000028     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:UARTCC26XX_fxnTable)
                  00004c04    00000028     ti_drivers_config.obj (.const:uartCC26XXHWAttrs)
                  00004c2c    00000024     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.const:ADCBufCC26X2_fxnTable)
                  00004c50    00000024                      : UART.oem4f (.const:UART_defaultParams)
                  00004c74    0000001c                      : GPTimerCC26XX.oem4f (.const:GPT_LUT)
                  00004c90    00000018     ti_drivers_config.obj (.const:GPTimerCC26XX_config)
                  00004ca8    00000018     ti_drivers_config.obj (.const:gptimerCC26XXHWAttrs)
                  00004cc0    00000018     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:interruptType)
                  00004cd8    00000014     ti_drivers_config.obj (.const:$P$T1$2)
                  00004cec    00000014     ti_drivers_config.obj (.const:GPIOCC26XX_config)
                  00004d00    00000014     ti_drivers_config.obj (.const:PowerCC26X2_config)
                  00004d14    00000014     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:parityType)
                  00004d28    00000010     nortos_cc13x2.a : SwiP_nortos.oem4f (.const:SwiP_defaultParams)
                  00004d38    00000010     ti_drivers_config.obj (.const:adcbufCC26XXHWAttrs)
                  00004d48    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:dataLength)
                  00004d58    00000010                      : GPIOCC26XX.oem4f (.const:outPinTypes)
                  00004d68    0000000e     ti_drivers_config.obj (.const)
                  00004d76    00000002     --HOLE-- [fill = 0]
                  00004d78    0000000c     ti_drivers_config.obj (.const:ADCBuf_config)
                  00004d84    0000000c     ti_drivers_config.obj (.const:UART_config)
                  00004d90    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:inPinTypes)
                  00004d9c    0000000c                      : GPIOCC26XX.oem4f (.const:outPinStrengths)
                  00004da8    00000008     driverlib.lib : aux_sysif.obj (.const:$O1$$)
                  00004db0    00000008     ti_drivers_config.obj (.const:$P$T0$1)
                  00004db8    00000008     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.const:GPT_DefaultParams)
                  00004dc0    00000008     ti_drivers_config.obj (.const:PINCC26XX_hwAttrs)
                  00004dc8    00000008     ti_drivers_config.obj (.const:UDMACC26XX_config)
                  00004dd0    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:stopBits)
                  00004dd8    00000008     ti_drivers_config.obj (.const:udmaCC26XXHWAttrs)
                  00004de0    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:.string:rxFifoBytes)
                  00004de6    00000006                      : UARTCC26XX.oem4f (.const:.string:rxFifoThreshold)
                  00004dec    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoBytes)
                  00004df2    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoThreshold)

.binit     0    00000000    00000000     

.cinit     0    00004e00    000000e0     
                  00004e00    00000088     (.cinit..data.load) [load image, compression = lzss]
                  00004e88    0000000c     (__TI_handler_table)
                  00004e94    00000004     --HOLE-- [fill = 0]
                  00004e98    00000008     (.cinit..TI.bound:dmaADCAltControlTableEntry.load) [load image, compression = zero_init]
                  00004ea0    00000008     (.cinit..TI.bound:dmaADCPriControlTableEntry.load) [load image, compression = zero_init]
                  00004ea8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004eb0    00000008     (.cinit..vtable_ram.load) [load image, compression = zero_init]
                  00004eb8    00000028     (__TI_cinit_table)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.ccfg      0    00057fa8    00000058     
                  00057fa8    00000058     ti_devices_config.obj (.ccfg:retain)

.bss       0    20000000    00000604     UNINITIALIZED
                  20000000    000001b4     (.common:uartCC26XXObjects)
                  200001b4    000000dc     (.common:adcbufCC26XXbjects)
                  20000290    000000a0     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:ClockP_module)
                  20000330    00000098     (.common:gptimerCC26XXObjects)
                  200003c8    0000007c     (.common:pinHandleTable)
                  20000444    00000050     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinSemaphore)
                  20000494    00000034                      : PINCC26XX.oem4f (.bss:pinSwi)
                  200004c8    00000020     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_readyList)
                  200004e8    00000020     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioCallbackInfo)
                  20000508    00000020                      : PINCC26XX.oem4f (.bss:pinGpioConfigTable)
                  20000528    00000020     ti_drivers_config.obj (.bss:uartCC26XXRingBuffer0)
                  20000548    00000020     (.common:udmaCC26XXObject)
                  20000568    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_hwiStruct)
                  20000584    0000001c                     : TimerPCC26XX_nortos.oem4f (.bss:TimerP_hwiStruct)
                  200005a0    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinHwi)
                  200005bc    0000000d     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss)
                  200005c9    00000001     (.common:driverlib_release_0_59848)
                  200005ca    00000002     --HOLE--
                  200005cc    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioPinState)
                  200005d8    00000008                      : GPIOCC26XX.oem4f (.bss)
                  200005e0    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:timerFreq)
                  200005e8    00000004     drivers_cc13x2.a : PINCC26XX.oem4f (.bss)
                  200005ec    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss)
                  200005f0    00000004                     : PowerCC26X2_nortos.oem4f (.bss)
                  200005f4    00000004                     : TimerPCC26XX_nortos.oem4f (.bss)
                  200005f8    00000004     (.common:i)
                  200005fc    00000004     (.common:j)
                  20000600    00000004     (.common:uart)

.vtable_ram 
*          0    20000a00    000000d8     UNINITIALIZED
                  20000a00    000000d8     driverlib.lib : interrupt.obj (.vtable_ram)

.data      0    20000604    000003e6     UNINITIALIZED
                  20000604    00000170     drivers_cc13x2.a : PowerCC26X2.oem4f (.data:PowerCC26X2_module)
                  20000774    000000d8     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data:HwiP_dispatchTable)
                  2000084c    0000007c     SoundTX.obj (.data:ChirpDelay)
                  200008c8    0000007c     SoundTX.obj (.data:ChirpState)
                  20000944    00000024     driverlib.lib : osc.obj (.data:$O1$$)
                  20000968    00000010     ti_drivers_config.obj (.data:gpioCallbackFunctions)
                  20000978    00000010     ti_drivers_config.obj (.data:gpioPinConfigs)
                  20000988    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data:ClockP_defaultParams)
                  20000994    0000000c                     : TimerPCC26XX_nortos.oem4f (.data:TimerP_defaultParams)
                  200009a0    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.data:$O1$$)
                  200009a8    00000008                                   : memory.c.obj (.data:$O1$$)
                  200009b0    00000008     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data:SemaphoreP_defaultParams)
                  200009b8    00000008     SoundTX.obj (.data)
                  200009c0    00000008     drivers_cc13x2.a : PINCC26XX.oem4f (.data)
                  200009c8    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data)
                  200009d0    00000006     ti_drivers_config.obj (.data:adcBufCC26XXChannelLut0)
                  200009d6    00000001     drivers_cc13x2.a : ADCBuf.oem4f (.data)
                  200009d7    00000001                      : UART.oem4f (.data)
                  200009d8    00000005                      : GPIOCC26XX.oem4f (.data)
                  200009dd    00000003     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data)
                  200009e0    00000004     drivers_cc13x2.a : GPIOCC26XX.oem4f (.data:gpioPinTable)
                  200009e4    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data)
                  200009e8    00000001                     : SwiP_nortos.oem4f (.data)
                  200009e9    00000001                     : TimerPCC26XX_nortos.oem4f (.data)

.sysmem    0    20001a80    00004000     UNINITIALIZED
                  20001a80    00000010     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.sysmem)
                  20001a90    00003ff0     --HOLE--

.stack     0    20013c00    00000400     UNINITIALIZED
                  20013c00    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.stack)
                  20013c04    000003fc     --HOLE--

.log_data 
*          0    90000000    00000000     COPY SECTION

__llvm_prf_cnts 
*          0    20000000    00000000     UNINITIALIZED

.TI.bound:dmaADCPriControlTableEntry 
*          0    20001870    00000010     UNINITIALIZED
                  20001870    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCPriControlTableEntry)

.TI.bound:dmaADCAltControlTableEntry 
*          0    20001a70    00000010     UNINITIALIZED
                  20001a70    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCAltControlTableEntry)

.args      0    00004df8    00000008     
                  00004df8    00000008     --HOLE-- [fill = 0]

MODULE SUMMARY

       Module                             code    ro data   rw data
       ------                             ----    -------   -------
    .\
       SoundTX.obj                        706     0         256    
       mainNew.obj                        172     0         12     
       ADC.obj                            8       0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             886     0         268    
                                                                   
    .\syscfg\
       ti_drivers_config.obj              306     274       910    
       ti_devices_config.obj              0       88        0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             306     362       910    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos/lib/ccs/m4f/nortos_cc13x2.a
       ClockPTimer_nortos.oem4f           922     0         192    
       SwiP_nortos.oem4f                  724     16        74     
       TimerPCC26XX_nortos.oem4f          642     0         45     
       HwiPCC26XX_nortos.oem4f            294     0         220    
       SemaphoreP_nortos.oem4f            410     0         11     
       PowerCC26X2_nortos.oem4f           256     0         4      
       startup_cc13x2_cc26x2_ccs.oem4f    26      216       0      
       QueueP_nortos.oem4f                100     0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             3374    232       546    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/cc13x2_cc26x2/driverlib/bin/ccs/driverlib.lib
       sys_ctrl.obj                       420     0         0      
       osc.obj                            340     0         36     
       setup.obj                          324     0         0      
       interrupt.obj                      74      0         216    
       chipinfo.obj                       250     0         0      
       prcm.obj                           160     0         0      
       timer.obj                          112     0         0      
       aux_sysif.obj                      52      8         0      
       aux_adc.obj                        32      0         0      
       cpu.obj                            30      0         0      
       driverlib_release.obj              0       0         1      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1794    8         253    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/lib/ccs/m4f/drivers_cc13x2.a
       UARTCC26XX.oem4f                   2760    108       0      
       PowerCC26X2.oem4f                  2096    84        368    
       PINCC26XX.oem4f                    1554    0         328    
       ADCBufCC26X2.oem4f                 1542    36        32     
       PowerCC26X2_calibrateRCOSC.oem4f   1400    0         0      
       GPIOCC26XX.oem4f                   660     64        61     
       GPTimerCC26XX.oem4f                670     36        0      
       UART.oem4f                         156     36        1      
       UDMACC26XX.oem4f                   168     0         0      
       RingBuf.oem4f                      152     0         0      
       ADCBuf.oem4f                       78      0         1      
       List.oem4f                         76      0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             11312   364       791    
                                                                   
    C:\ti\ccs1011\ccs\tools\compiler\ti-cgt-arm_20.2.4.LTS\lib\rtsv7M4_T_le_v4SPD16_eabi.lib
       memory.c.obj                       672     0         8      
       memcpy_t2.asm.obj                  156     0         0      
       ull_div_t2.asm.obj                 150     0         0      
       memset_t2.asm.obj                  122     0         0      
       copy_decompress_lzss.c.obj         104     0         0      
       autoinit.c.obj                     68      0         0      
       boot_cortex_m.c.obj                48      0         0      
       args_main.c.obj                    24      0         0      
       ll_mul_t2.asm.obj                  24      0         0      
       copy_decompress_none.c.obj         14      0         0      
       copy_zero_init.c.obj               12      0         0      
       _lock.c.obj                        2       0         8      
       exit.c.obj                         4       0         0      
       pre_init.c.obj                     4       0         0      
       div0.asm.obj                       2       0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1406    0         16     
                                                                   
       Heap:                              0       0         16384  
       Stack:                             0       0         1024   
       Linker Generated:                  0       220       0      
    +--+----------------------------------+-------+---------+---------+
       Grand Total:                       19078   1186      20192  


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004eb8 records: 5, size/record: 8, table size: 40
	.data: load addr=00004e00, load size=00000088 bytes, run addr=20000604, run size=000003e6 bytes, compression=lzss
	.TI.bound:dmaADCAltControlTableEntry: load addr=00004e98, load size=00000008 bytes, run addr=20001a70, run size=00000010 bytes, compression=zero_init
	.TI.bound:dmaADCPriControlTableEntry: load addr=00004ea0, load size=00000008 bytes, run addr=20001870, run size=00000010 bytes, compression=zero_init
	.bss: load addr=00004ea8, load size=00000008 bytes, run addr=20000000, run size=00000604 bytes, compression=zero_init
	.vtable_ram: load addr=00004eb0, load size=00000008 bytes, run addr=20000a00, run size=000000d8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004e88 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_lzss
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                                               
-------   ----                                                               
00004d6b  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004d68  ADCBUF_CONST                                                       
00004d69  ADCBUF_SOUND_CONST                                                 
00004d6a  ADCBUF_TEMPERATURE_CONST                                           
00003255  ADCBufCC26X2_adjustRawValues                                       
0000334d  ADCBufCC26X2_close                                                 
000035b1  ADCBufCC26X2_control                                               
0000118d  ADCBufCC26X2_convert                                               
000035fd  ADCBufCC26X2_convertAdjustedToMicroVolts                           
00003a59  ADCBufCC26X2_convertCancel                                         
00004c2c  ADCBufCC26X2_fxnTable                                              
00004b2f  ADCBufCC26X2_getResolution                                         
00004ad3  ADCBufCC26X2_init                                                  
0000076d  ADCBufCC26X2_open                                                  
00004d78  ADCBuf_config                                                      
00004aad  ADCBuf_convertCancel                                               
00004d6c  ADCBuf_count                                                       
000037f5  ADCBuf_init                                                        
00004bb4  BoardGpioInitTable                                                 
00004425  Board_init                                                         
00000e47  Board_initHook                                                     
00002735  Board_sendExtFlashByte                                             
00003a99  Board_shutDownExtFlash                                             
000033e9  Board_wakeUpExtFlash                                               
00004b53  C$$EXIT                                                            
00004d73  CONFIG_GPTIMER_0_CONST                                             
00004d74  CONFIG_GPTIMER_1_CONST                                             
2000084c  ChirpDelay                                                         
200009bc  ChirpIndex                                                         
200009b8  ChirpSize                                                          
200008c8  ChirpState                                                         
00004931  ClockP_Params_init                                                 
00004445  ClockP_add                                                         
00003645  ClockP_construct                                                   
00004727  ClockP_destruct                                                    
00004a1d  ClockP_doTick                                                      
00004941  ClockP_getCpuFreq                                                  
00004a29  ClockP_getSystemTickPeriod                                         
0000434d  ClockP_getTicks                                                    
00003e09  ClockP_getTicksUntilInterrupt                                      
00004b37  ClockP_isActive                                                    
00004371  ClockP_scheduleNextTick                                            
00004b3b  ClockP_setTimeout                                                  
000028bd  ClockP_start                                                       
00001c69  ClockP_startup                                                     
00004b0b  ClockP_stop                                                        
200009cc  ClockP_tickPeriod                                                  
00002ab9  ClockP_walkQueueDynamic                                            
00001d1d  ClockP_workFuncDynamic                                             
00004cec  GPIOCC26XX_config                                                  
00003fdd  GPIO_hwiIntFxn                                                     
000017f9  GPIO_init                                                          
0000400d  GPIO_setCallback                                                   
0000107d  GPIO_setConfig                                                     
00003035  GPIO_write                                                         
000048e9  GPTimerCC26XX_Params_init                                          
0000403d  GPTimerCC26XX_close                                                
00004c90  GPTimerCC26XX_config                                               
00003e71  GPTimerCC26XX_configureDebugStall                                  
0000248d  GPTimerCC26XX_open                                                 
00004adb  GPTimerCC26XX_setLoadValue                                         
00003481  GPTimerCC26XX_start                                                
000031a5  GPTimerCC26XX_stop                                                 
00004d75  GPTimer_count                                                      
000048fb  HwiP_Params_init                                                   
00004951  HwiP_clearInterrupt                                                
0000293d  HwiP_construct                                                     
000045f9  HwiP_destruct                                                      
00004961  HwiP_disable                                                       
00004b3f  HwiP_enable                                                        
00004741  HwiP_inISR                                                         
00004971  HwiP_post                                                          
00004ae3  HwiP_restore                                                       
200009e4  HwiP_swiPIntNum                                                    
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
00004aeb  InitADC                                                            
0000219d  InitTimer2                                                         
00003839  InitUart                                                           
00004301  List_put                                                           
00004327  List_remove                                                        
00004465  NOROM_AUXADCEnableSync                                             
00003ea5  NOROM_AUXSYSIFOpModeChange                                         
00004a35  NOROM_CPUcpsid                                                     
00004a41  NOROM_CPUcpsie                                                     
00004b11  NOROM_CPUdelay                                                     
00004615  NOROM_ChipInfo_GetChipFamily                                       
00002cf9  NOROM_ChipInfo_GetChipType                                         
0000387d  NOROM_ChipInfo_GetHwRevision                                       
00004759  NOROM_ChipInfo_GetPackageType                                      
00003ed9  NOROM_IntRegister                                                  
00004871  NOROM_IntUnregister                                                
00002515  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002d65  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
000043b9  NOROM_OSCHF_TurnOnXosc                                             
00003ad9  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
000032a9  NOROM_PRCMPowerDomainsAllOff                                       
000034cd  NOROM_PRCMPowerDomainsAllOn                                        
00002235  NOROM_SetupTrimDevice                                              
00003f0d  NOROM_SysCtrlIdle                                                  
000029bd  NOROM_SysCtrlSetRechargeBeforePowerDown                            
0000138d  NOROM_SysCtrlStandby                                               
00004819  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00003b19  NOROM_TimerIntRegister                                             
00004b3f  NoRTOS_start                                                       
00004981  PINCC26XX_getPinCount                                              
00004dc0  PINCC26XX_hwAttrs                                                  
0000406d  PINCC26XX_setMux                                                   
00004d6f  PIN_DRIVE_SPEAKER_A_CONST                                          
00004d70  PIN_DRIVE_SPEAKER_B_CONST                                          
00004d6d  PIN_TEST1_CONST                                                    
00004d6e  PIN_TEST2_CONST                                                    
00002c19  PIN_add                                                            
00004545  PIN_close                                                          
000000d9  PIN_init                                                           
00001dc9  PIN_open                                                           
00004a4d  PIN_registerIntCb                                                  
00003095  PIN_remove                                                         
00003c4d  PIN_setConfig                                                      
0000368d  PIN_setOutputEnable                                                
00003bd9  PIN_setOutputValue                                                 
000036d5  PowerCC26X2_RCOSC_clockFunc                                        
00002ba5  PowerCC26X2_auxISR                                                 
00004885  PowerCC26X2_calibrate                                              
00004d00  PowerCC26X2_config                                                 
000031fd  PowerCC26X2_initiateCalibration                                    
20000604  PowerCC26X2_module                                                 
000046d9  PowerCC26XX_calibrate                                              
00004991  PowerCC26XX_schedulerDisable                                       
00004a65  PowerCC26XX_schedulerRestore                                       
00001561  PowerCC26XX_standbyPolicy                                          
00004899  Power_disablePolicy                                                
000049a1  Power_enablePolicy                                                 
00004a71  Power_getConstraintMask                                            
00004a7d  Power_getDependencyCount                                           
00004563  Power_getTransitionLatency                                         
00004771  Power_idleFunc                                                     
000008e1  Power_init                                                         
00004289  Power_registerNotify                                               
0000409d  Power_releaseConstraint                                            
000018bd  Power_releaseDependency                                            
000040cd  Power_setConstraint                                                
00001af9  Power_setDependency                                                
000002b9  Power_sleep                                                        
000044a5  Power_unregisterNotify                                             
000049f1  QueueP_empty                                                       
000046f3  QueueP_get                                                         
00004b43  QueueP_head                                                        
00004b17  QueueP_init                                                        
00004b47  QueueP_next                                                        
000044c5  QueueP_put                                                         
000049ff  QueueP_remove                                                      
0000490d  RingBuf_construct                                                  
00003b59  RingBuf_get                                                        
000037ad  RingBuf_put                                                        
000049b1  SemaphoreP_Params_init                                             
000032fd  SemaphoreP_construct                                               
00004581  SemaphoreP_constructBinary                                         
0000482f  SemaphoreP_create                                                  
0000470d  SemaphoreP_createBinary                                            
200009b0  SemaphoreP_defaultParams                                           
00004b4b  SemaphoreP_delete                                                  
00003033  SemaphoreP_destruct                                                
00001fc5  SemaphoreP_pend                                                    
00003905  SemaphoreP_post                                                    
00003f41  SoundTransmit                                                      
000049c1  SwiP_Params_init                                                   
00001721  SwiP_construct                                                     
00003f75  SwiP_destruct                                                      
00004669  SwiP_disable                                                       
00002065  SwiP_dispatch                                                      
00004a89  SwiP_getTrigger                                                    
0000459f  SwiP_or                                                            
00002dcd  SwiP_post                                                          
00003949  SwiP_restore                                                       
00001f21  Timer2AInterruptHandler                                            
000049d1  TimerP_Params_init                                                 
00001a3d  TimerP_construct                                                   
000044e5  TimerP_dynamicStub                                                 
000048c1  TimerP_getCount64                                                  
00003c85  TimerP_getCurrentTick                                              
00004a95  TimerP_getFreq                                                     
000043dd  TimerP_getMaxTicks                                                 
00003cbd  TimerP_initDevice                                                  
00003d9d  TimerP_setNextTick                                                 
000041b5  TimerP_setThreshold                                                
00002e35  TimerP_start                                                       
000047a1  TimerP_startup                                                     
000027b9  UARTCC26XX_close                                                   
00002f6d  UARTCC26XX_control                                                 
00004bdc  UARTCC26XX_fxnTable                                                
00000bb9  UARTCC26XX_hwiIntFxn                                               
00004af3  UARTCC26XX_init                                                    
00000a55  UARTCC26XX_open                                                    
00000d0d  UARTCC26XX_read                                                    
00003519  UARTCC26XX_readCancel                                              
00004b1d  UARTCC26XX_readPolling                                             
00002fd1  UARTCC26XX_swiIntFxn                                               
00001641  UARTCC26XX_write                                                   
000023fb  UARTCC26XX_writeCancel                                             
00004b23  UARTCC26XX_writePolling                                            
00004d71  UART_0_CONST                                                       
000047b9  UART_Params_init                                                   
00004d84  UART_config                                                        
00004d72  UART_count                                                         
00004c50  UART_defaultParams                                                 
0000398d  UART_init                                                          
00003b99  UART_open                                                          
00003dd3  UDMACC26XX_close                                                   
00004dc8  UDMACC26XX_config                                                  
00004ab7  UDMACC26XX_hwiIntFxn                                               
00003565  UDMACC26XX_open                                                    
20014000  __STACK_END                                                        
00000400  __STACK_SIZE                                                       
00004000  __SYSMEM_SIZE                                                      
00004eb8  __TI_CINIT_Base                                                    
00004ee0  __TI_CINIT_Limit                                                   
00004e88  __TI_Handler_Table_Base                                            
00004e94  __TI_Handler_Table_Limit                                           
000039d1  __TI_auto_init_nobinit_nopinit                                     
00002e9d  __TI_decompress_lzss                                               
00004a0d  __TI_decompress_none                                               
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
00000000  __TI_static_base__                                                 
00004aa1  __TI_zero_init                                                     
00004a1b  __aeabi_idiv0                                                      
00004a1b  __aeabi_ldiv0                                                      
000047e9  __aeabi_lmul                                                       
00002a3d  __aeabi_memclr                                                     
00002a3d  __aeabi_memclr4                                                    
00002a3d  __aeabi_memclr8                                                    
00002101  __aeabi_memcpy                                                     
00002101  __aeabi_memcpy4                                                    
00002101  __aeabi_memcpy8                                                    
00002a3f  __aeabi_memset                                                     
00002a3f  __aeabi_memset4                                                    
00002a3f  __aeabi_memset8                                                    
00002365  __aeabi_uldivmod                                                   
ffffffff  __binit__                                                          
00004df8  __c_args__                                                         
00057fa8  __ccfg                                                             
UNDEFED   __mpu_init                                                         
20013c00  __stack                                                            
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
000047d1  _args_main                                                         
0000412d  _c_int00                                                           
20000944  _hposcCoeffs                                                       
200009a0  _lock                                                              
0000339b  _nop                                                               
20001a80  _sys_memory                                                        
UNDEFED   _system_post_cinit                                                 
00004b4f  _system_pre_init                                                   
200009a4  _unlock                                                            
00004b53  abort                                                              
200009d0  adcBufCC26XXChannelLut0                                            
00004d38  adcbufCC26XXHWAttrs                                                
200001b4  adcbufCC26XXbjects                                                 
0000128d  aligned_alloc                                                      
ffffffff  binit                                                              
000035fb  clkFxn                                                             
200005c9  driverlib_release_0_59848                                          
0000147d  free                                                               
20000a00  g_pfnRAMVectors                                                    
00000000  g_pfnVectors                                                       
20000968  gpioCallbackFunctions                                              
20000978  gpioPinConfigs                                                     
00004ca8  gptimerCC26XXHWAttrs                                               
20000330  gptimerCC26XXObjects                                               
200005f8  i                                                                  
00004d90  inPinTypes                                                         
200005fc  j                                                                  
00002f05  main                                                               
00004afb  malloc                                                             
0000128d  memalign                                                           
00002101  memcpy                                                             
00002a45  memset                                                             
00004d9c  outPinStrengths                                                    
00004d58  outPinTypes                                                        
200003c8  pinHandleTable                                                     
200009c4  pinLowerBound                                                      
200009c0  pinUpperBound                                                      
000048d5  resetISR                                                           
00004b60  resourceDB                                                         
20000600  uart                                                               
20000000  uartCC26XXObjects                                                  
00004dd8  udmaCC26XXHWAttrs                                                  
20000548  udmaCC26XXObject                                                   


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                                               
-------   ----                                                               
00000000  __TI_static_base__                                                 
00000000  g_pfnVectors                                                       
000000d9  PIN_init                                                           
000002b9  Power_sleep                                                        
00000400  __STACK_SIZE                                                       
0000076d  ADCBufCC26X2_open                                                  
000008e1  Power_init                                                         
00000a55  UARTCC26XX_open                                                    
00000bb9  UARTCC26XX_hwiIntFxn                                               
00000d0d  UARTCC26XX_read                                                    
00000e47  Board_initHook                                                     
0000107d  GPIO_setConfig                                                     
0000118d  ADCBufCC26X2_convert                                               
0000128d  aligned_alloc                                                      
0000128d  memalign                                                           
0000138d  NOROM_SysCtrlStandby                                               
0000147d  free                                                               
00001561  PowerCC26XX_standbyPolicy                                          
00001641  UARTCC26XX_write                                                   
00001721  SwiP_construct                                                     
000017f9  GPIO_init                                                          
000018bd  Power_releaseDependency                                            
00001a3d  TimerP_construct                                                   
00001af9  Power_setDependency                                                
00001c69  ClockP_startup                                                     
00001d1d  ClockP_workFuncDynamic                                             
00001dc9  PIN_open                                                           
00001f21  Timer2AInterruptHandler                                            
00001fc5  SemaphoreP_pend                                                    
00002065  SwiP_dispatch                                                      
00002101  __aeabi_memcpy                                                     
00002101  __aeabi_memcpy4                                                    
00002101  __aeabi_memcpy8                                                    
00002101  memcpy                                                             
0000219d  InitTimer2                                                         
00002235  NOROM_SetupTrimDevice                                              
00002365  __aeabi_uldivmod                                                   
000023fb  UARTCC26XX_writeCancel                                             
0000248d  GPTimerCC26XX_open                                                 
00002515  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002735  Board_sendExtFlashByte                                             
000027b9  UARTCC26XX_close                                                   
000028bd  ClockP_start                                                       
0000293d  HwiP_construct                                                     
000029bd  NOROM_SysCtrlSetRechargeBeforePowerDown                            
00002a3d  __aeabi_memclr                                                     
00002a3d  __aeabi_memclr4                                                    
00002a3d  __aeabi_memclr8                                                    
00002a3f  __aeabi_memset                                                     
00002a3f  __aeabi_memset4                                                    
00002a3f  __aeabi_memset8                                                    
00002a45  memset                                                             
00002ab9  ClockP_walkQueueDynamic                                            
00002ba5  PowerCC26X2_auxISR                                                 
00002c19  PIN_add                                                            
00002cf9  NOROM_ChipInfo_GetChipType                                         
00002d65  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
00002dcd  SwiP_post                                                          
00002e35  TimerP_start                                                       
00002e9d  __TI_decompress_lzss                                               
00002f05  main                                                               
00002f6d  UARTCC26XX_control                                                 
00002fd1  UARTCC26XX_swiIntFxn                                               
00003033  SemaphoreP_destruct                                                
00003035  GPIO_write                                                         
00003095  PIN_remove                                                         
000031a5  GPTimerCC26XX_stop                                                 
000031fd  PowerCC26X2_initiateCalibration                                    
00003255  ADCBufCC26X2_adjustRawValues                                       
000032a9  NOROM_PRCMPowerDomainsAllOff                                       
000032fd  SemaphoreP_construct                                               
0000334d  ADCBufCC26X2_close                                                 
0000339b  _nop                                                               
000033e9  Board_wakeUpExtFlash                                               
00003481  GPTimerCC26XX_start                                                
000034cd  NOROM_PRCMPowerDomainsAllOn                                        
00003519  UARTCC26XX_readCancel                                              
00003565  UDMACC26XX_open                                                    
000035b1  ADCBufCC26X2_control                                               
000035fb  clkFxn                                                             
000035fd  ADCBufCC26X2_convertAdjustedToMicroVolts                           
00003645  ClockP_construct                                                   
0000368d  PIN_setOutputEnable                                                
000036d5  PowerCC26X2_RCOSC_clockFunc                                        
000037ad  RingBuf_put                                                        
000037f5  ADCBuf_init                                                        
00003839  InitUart                                                           
0000387d  NOROM_ChipInfo_GetHwRevision                                       
00003905  SemaphoreP_post                                                    
00003949  SwiP_restore                                                       
0000398d  UART_init                                                          
000039d1  __TI_auto_init_nobinit_nopinit                                     
00003a59  ADCBufCC26X2_convertCancel                                         
00003a99  Board_shutDownExtFlash                                             
00003ad9  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00003b19  NOROM_TimerIntRegister                                             
00003b59  RingBuf_get                                                        
00003b99  UART_open                                                          
00003bd9  PIN_setOutputValue                                                 
00003c4d  PIN_setConfig                                                      
00003c85  TimerP_getCurrentTick                                              
00003cbd  TimerP_initDevice                                                  
00003d9d  TimerP_setNextTick                                                 
00003dd3  UDMACC26XX_close                                                   
00003e09  ClockP_getTicksUntilInterrupt                                      
00003e71  GPTimerCC26XX_configureDebugStall                                  
00003ea5  NOROM_AUXSYSIFOpModeChange                                         
00003ed9  NOROM_IntRegister                                                  
00003f0d  NOROM_SysCtrlIdle                                                  
00003f41  SoundTransmit                                                      
00003f75  SwiP_destruct                                                      
00003fdd  GPIO_hwiIntFxn                                                     
00004000  __SYSMEM_SIZE                                                      
0000400d  GPIO_setCallback                                                   
0000403d  GPTimerCC26XX_close                                                
0000406d  PINCC26XX_setMux                                                   
0000409d  Power_releaseConstraint                                            
000040cd  Power_setConstraint                                                
0000412d  _c_int00                                                           
000041b5  TimerP_setThreshold                                                
00004289  Power_registerNotify                                               
00004301  List_put                                                           
00004327  List_remove                                                        
0000434d  ClockP_getTicks                                                    
00004371  ClockP_scheduleNextTick                                            
000043b9  NOROM_OSCHF_TurnOnXosc                                             
000043dd  TimerP_getMaxTicks                                                 
00004425  Board_init                                                         
00004445  ClockP_add                                                         
00004465  NOROM_AUXADCEnableSync                                             
000044a5  Power_unregisterNotify                                             
000044c5  QueueP_put                                                         
000044e5  TimerP_dynamicStub                                                 
00004545  PIN_close                                                          
00004563  Power_getTransitionLatency                                         
00004581  SemaphoreP_constructBinary                                         
0000459f  SwiP_or                                                            
000045f9  HwiP_destruct                                                      
00004615  NOROM_ChipInfo_GetChipFamily                                       
00004669  SwiP_disable                                                       
000046d9  PowerCC26XX_calibrate                                              
000046f3  QueueP_get                                                         
0000470d  SemaphoreP_createBinary                                            
00004727  ClockP_destruct                                                    
00004741  HwiP_inISR                                                         
00004759  NOROM_ChipInfo_GetPackageType                                      
00004771  Power_idleFunc                                                     
000047a1  TimerP_startup                                                     
000047b9  UART_Params_init                                                   
000047d1  _args_main                                                         
000047e9  __aeabi_lmul                                                       
00004819  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
0000482f  SemaphoreP_create                                                  
00004871  NOROM_IntUnregister                                                
00004885  PowerCC26X2_calibrate                                              
00004899  Power_disablePolicy                                                
000048c1  TimerP_getCount64                                                  
000048d5  resetISR                                                           
000048e9  GPTimerCC26XX_Params_init                                          
000048fb  HwiP_Params_init                                                   
0000490d  RingBuf_construct                                                  
00004931  ClockP_Params_init                                                 
00004941  ClockP_getCpuFreq                                                  
00004951  HwiP_clearInterrupt                                                
00004961  HwiP_disable                                                       
00004971  HwiP_post                                                          
00004981  PINCC26XX_getPinCount                                              
00004991  PowerCC26XX_schedulerDisable                                       
000049a1  Power_enablePolicy                                                 
000049b1  SemaphoreP_Params_init                                             
000049c1  SwiP_Params_init                                                   
000049d1  TimerP_Params_init                                                 
000049f1  QueueP_empty                                                       
000049ff  QueueP_remove                                                      
00004a0d  __TI_decompress_none                                               
00004a1b  __aeabi_idiv0                                                      
00004a1b  __aeabi_ldiv0                                                      
00004a1d  ClockP_doTick                                                      
00004a29  ClockP_getSystemTickPeriod                                         
00004a35  NOROM_CPUcpsid                                                     
00004a41  NOROM_CPUcpsie                                                     
00004a4d  PIN_registerIntCb                                                  
00004a65  PowerCC26XX_schedulerRestore                                       
00004a71  Power_getConstraintMask                                            
00004a7d  Power_getDependencyCount                                           
00004a89  SwiP_getTrigger                                                    
00004a95  TimerP_getFreq                                                     
00004aa1  __TI_zero_init                                                     
00004aad  ADCBuf_convertCancel                                               
00004ab7  UDMACC26XX_hwiIntFxn                                               
00004ad3  ADCBufCC26X2_init                                                  
00004adb  GPTimerCC26XX_setLoadValue                                         
00004ae3  HwiP_restore                                                       
00004aeb  InitADC                                                            
00004af3  UARTCC26XX_init                                                    
00004afb  malloc                                                             
00004b0b  ClockP_stop                                                        
00004b11  NOROM_CPUdelay                                                     
00004b17  QueueP_init                                                        
00004b1d  UARTCC26XX_readPolling                                             
00004b23  UARTCC26XX_writePolling                                            
00004b2f  ADCBufCC26X2_getResolution                                         
00004b37  ClockP_isActive                                                    
00004b3b  ClockP_setTimeout                                                  
00004b3f  HwiP_enable                                                        
00004b3f  NoRTOS_start                                                       
00004b43  QueueP_head                                                        
00004b47  QueueP_next                                                        
00004b4b  SemaphoreP_delete                                                  
00004b4f  _system_pre_init                                                   
00004b53  C$$EXIT                                                            
00004b53  abort                                                              
00004b60  resourceDB                                                         
00004bb4  BoardGpioInitTable                                                 
00004bdc  UARTCC26XX_fxnTable                                                
00004c2c  ADCBufCC26X2_fxnTable                                              
00004c50  UART_defaultParams                                                 
00004c90  GPTimerCC26XX_config                                               
00004ca8  gptimerCC26XXHWAttrs                                               
00004cec  GPIOCC26XX_config                                                  
00004d00  PowerCC26X2_config                                                 
00004d38  adcbufCC26XXHWAttrs                                                
00004d58  outPinTypes                                                        
00004d68  ADCBUF_CONST                                                       
00004d69  ADCBUF_SOUND_CONST                                                 
00004d6a  ADCBUF_TEMPERATURE_CONST                                           
00004d6b  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004d6c  ADCBuf_count                                                       
00004d6d  PIN_TEST1_CONST                                                    
00004d6e  PIN_TEST2_CONST                                                    
00004d6f  PIN_DRIVE_SPEAKER_A_CONST                                          
00004d70  PIN_DRIVE_SPEAKER_B_CONST                                          
00004d71  UART_0_CONST                                                       
00004d72  UART_count                                                         
00004d73  CONFIG_GPTIMER_0_CONST                                             
00004d74  CONFIG_GPTIMER_1_CONST                                             
00004d75  GPTimer_count                                                      
00004d78  ADCBuf_config                                                      
00004d84  UART_config                                                        
00004d90  inPinTypes                                                         
00004d9c  outPinStrengths                                                    
00004dc0  PINCC26XX_hwAttrs                                                  
00004dc8  UDMACC26XX_config                                                  
00004dd8  udmaCC26XXHWAttrs                                                  
00004df8  __c_args__                                                         
00004e88  __TI_Handler_Table_Base                                            
00004e94  __TI_Handler_Table_Limit                                           
00004eb8  __TI_CINIT_Base                                                    
00004ee0  __TI_CINIT_Limit                                                   
00057fa8  __ccfg                                                             
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
20000000  uartCC26XXObjects                                                  
200001b4  adcbufCC26XXbjects                                                 
20000330  gptimerCC26XXObjects                                               
200003c8  pinHandleTable                                                     
20000548  udmaCC26XXObject                                                   
200005c9  driverlib_release_0_59848                                          
200005f8  i                                                                  
200005fc  j                                                                  
20000600  uart                                                               
20000604  PowerCC26X2_module                                                 
2000084c  ChirpDelay                                                         
200008c8  ChirpState                                                         
20000944  _hposcCoeffs                                                       
20000968  gpioCallbackFunctions                                              
20000978  gpioPinConfigs                                                     
200009a0  _lock                                                              
200009a4  _unlock                                                            
200009b0  SemaphoreP_defaultParams                                           
200009b8  ChirpSize                                                          
200009bc  ChirpIndex                                                         
200009c0  pinUpperBound                                                      
200009c4  pinLowerBound                                                      
200009cc  ClockP_tickPeriod                                                  
200009d0  adcBufCC26XXChannelLut0                                            
200009e4  HwiP_swiPIntNum                                                    
20000a00  g_pfnRAMVectors                                                    
20001a80  _sys_memory                                                        
20013c00  __stack                                                            
20014000  __STACK_END                                                        
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
ffffffff  __binit__                                                          
ffffffff  binit                                                              
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
UNDEFED   __mpu_init                                                         
UNDEFED   _system_post_cinit                                                 

[290 symbols]
