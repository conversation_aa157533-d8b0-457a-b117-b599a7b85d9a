<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: docs/src/external_lists.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_49e56c817e5e54854c35e136979f97ca.html">docs</a></li><li class="navelem"><a class="el" href="dir_248832ff8517b5b7d5da1a6bc8750d99.html">src</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">external_lists.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno"><a class="line" href="group__a2dp.html#gab981cf845089ef19df871466126b6f14">    1</a></span>&#160; </div>
<div class="line"><a name="l00005"></a><span class="lineno"><a class="line" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">    5</a></span>&#160;<span class="keyword">enum</span> <a class="code" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> {</div>
<div class="line"><a name="l00006"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a4f131f965f55142fa9fb3557f88b8793">    6</a></span>&#160;    <a class="code" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a4f131f965f55142fa9fb3557f88b8793">ESP_A2D_AUDIO_STATE_SUSPEND</a> = 0,           </div>
<div class="line"><a name="l00007"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a1fb2443ca89d24271931c19e1554b088">    7</a></span>&#160;    <a class="code" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a1fb2443ca89d24271931c19e1554b088">ESP_A2D_AUDIO_STATE_STARTED</a>,               </div>
<div class="line"><a name="l00008"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a2cac45c7b95065ed02420c5a632ab538">    8</a></span>&#160;    <a class="code" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a2cac45c7b95065ed02420c5a632ab538">ESP_A2D_AUDIO_STATE_STOPPED</a> = <a class="code" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a4f131f965f55142fa9fb3557f88b8793">ESP_A2D_AUDIO_STATE_SUSPEND</a>,          </div>
<div class="line"><a name="l00009"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a2144df52646c47f697ec1f0e4b59686f">    9</a></span>&#160;    <a class="code" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a2144df52646c47f697ec1f0e4b59686f">ESP_A2D_AUDIO_STATE_REMOTE_SUSPEND</a> = <a class="code" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a4f131f965f55142fa9fb3557f88b8793">ESP_A2D_AUDIO_STATE_SUSPEND</a>,   </div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;};</div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160; </div>
<div class="line"><a name="l00016"></a><span class="lineno"><a class="line" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">   16</a></span>&#160;<span class="keyword">enum</span> <a class="code" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> {</div>
<div class="line"><a name="l00017"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410">   17</a></span>&#160;    <a class="code" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410">ESP_A2D_CONNECTION_STATE_DISCONNECTED</a> = 0, </div>
<div class="line"><a name="l00018"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590a3661beedfd8bd0804def35a94a948543">   18</a></span>&#160;    <a class="code" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590a3661beedfd8bd0804def35a94a948543">ESP_A2D_CONNECTION_STATE_CONNECTING</a>,       </div>
<div class="line"><a name="l00019"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590a0248c5f6ba7c85f2044a9a2c79bc57d4">   19</a></span>&#160;    <a class="code" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590a0248c5f6ba7c85f2044a9a2c79bc57d4">ESP_A2D_CONNECTION_STATE_CONNECTED</a>,        </div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;    <a class="code" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590aca30f95d06a691676a9d85a0998ea1e0">ESP_A2D_CONNECTION_STATE_DISCONNECTING</a>     </div>
<div class="line"><a name="l00021"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590aca30f95d06a691676a9d85a0998ea1e0">   21</a></span>&#160;} ;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160; </div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="keyword">enum</span> <a class="code" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;    <a class="code" href="group__a2dp.html#gab981cf845089ef19df871466126b6f14">ESP_AVRC_RN_PLAY_STATUS_CHANGE</a> = 0x01,        </div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;    ESP_AVRC_RN_TRACK_CHANGE = 0x02,              </div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;    ESP_AVRC_RN_TRACK_REACHED_END = 0x03,         </div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;    ESP_AVRC_RN_TRACK_REACHED_START = 0x04,       </div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;    ESP_AVRC_RN_PLAY_POS_CHANGED = 0x05,          </div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;    ESP_AVRC_RN_BATTERY_STATUS_CHANGE = 0x06,     </div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;    ESP_AVRC_RN_SYSTEM_STATUS_CHANGE = 0x07,      </div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;    ESP_AVRC_RN_APP_SETTING_CHANGE = 0x08,        </div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;    ESP_AVRC_RN_NOW_PLAYING_CHANGE = 0x09,        </div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;    ESP_AVRC_RN_AVAILABLE_PLAYERS_CHANGE = 0x0a,  </div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;    ESP_AVRC_RN_ADDRESSED_PLAYER_CHANGE = 0x0b,   </div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;    ESP_AVRC_RN_UIDS_CHANGE = 0x0c,               </div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;    ESP_AVRC_RN_VOLUME_CHANGE = 0x0d,             </div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;    ESP_AVRC_RN_MAX_EVT</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;};</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160; </div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160; </div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160; </div>
<div class="line"><a name="l00050"></a><span class="lineno"><a class="line" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">   50</a></span>&#160;<span class="keyword">enum</span> <a class="code" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a>{</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;    <a class="code" href="group__a2dp.html#gab981cf845089ef19df871466126b6f14">ESP_AVRC_RN_PLAY_STATUS_CHANGE</a> = 0x01,        </div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;    ESP_AVRC_RN_TRACK_CHANGE = 0x02,              </div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;    ESP_AVRC_RN_TRACK_REACHED_END = 0x03,         </div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;    ESP_AVRC_RN_TRACK_REACHED_START = 0x04,       </div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;    ESP_AVRC_RN_PLAY_POS_CHANGED = 0x05,          </div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;    ESP_AVRC_RN_BATTERY_STATUS_CHANGE = 0x06,     </div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;    ESP_AVRC_RN_SYSTEM_STATUS_CHANGE = 0x07,      </div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;    ESP_AVRC_RN_APP_SETTING_CHANGE = 0x08,        </div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;    ESP_AVRC_RN_NOW_PLAYING_CHANGE = 0x09,        </div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    ESP_AVRC_RN_AVAILABLE_PLAYERS_CHANGE = 0x0a,  </div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;    ESP_AVRC_RN_ADDRESSED_PLAYER_CHANGE = 0x0b,   </div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;    ESP_AVRC_RN_UIDS_CHANGE = 0x0c,               </div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;    ESP_AVRC_RN_VOLUME_CHANGE = 0x0d,             </div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;    ESP_AVRC_RN_MAX_EVT</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;};</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160; </div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160; </div>
<div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">   72</a></span>&#160;<span class="keyword">enum</span>  <a class="code" href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a>{</div>
<div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba91a5ffbfe55aba08e1d323848e719fd7">   73</a></span>&#160;    <a class="code" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba91a5ffbfe55aba08e1d323848e719fd7">ESP_AVRC_PLAYBACK_STOPPED</a> = 0,                </div>
<div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba080ee68b71dd63f15c1f4f28ad9c7fec">   74</a></span>&#160;    <a class="code" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba080ee68b71dd63f15c1f4f28ad9c7fec">ESP_AVRC_PLAYBACK_PLAYING</a> = 1,                </div>
<div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fbaad9f30b7d23101c762b775f083e76a80">   75</a></span>&#160;    <a class="code" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fbaad9f30b7d23101c762b775f083e76a80">ESP_AVRC_PLAYBACK_PAUSED</a> = 2,                 </div>
<div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba4b13603deb02c5bc0a9f60bdbe3f55d9">   76</a></span>&#160;    <a class="code" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba4b13603deb02c5bc0a9f60bdbe3f55d9">ESP_AVRC_PLAYBACK_FWD_SEEK</a> = 3,               </div>
<div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba98c8358017f0694e2af18dcecf87eb6e">   77</a></span>&#160;    <a class="code" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba98c8358017f0694e2af18dcecf87eb6e">ESP_AVRC_PLAYBACK_REV_SEEK</a> = 4,               </div>
<div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fbab141fa00060af447da20081cb396f02d">   78</a></span>&#160;    <a class="code" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fbab141fa00060af447da20081cb396f02d">ESP_AVRC_PLAYBACK_ERROR</a> = 0xFF,               </div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;} ;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160; </div>
<div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">   85</a></span>&#160;<span class="keyword">enum</span> <a class="code" href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">esp_bt_discovery_mode_t</a>{</div>
<div class="line"><a name="l00086"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da41f6ad4d6283fe67dfeaf9cb3e14da65">   86</a></span>&#160;    <a class="code" href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da41f6ad4d6283fe67dfeaf9cb3e14da65">ESP_BT_NON_DISCOVERABLE</a>,            </div>
<div class="line"><a name="l00087"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da286efd17a5bec03100355acf2dd198de">   87</a></span>&#160;    <a class="code" href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da286efd17a5bec03100355acf2dd198de">ESP_BT_LIMITED_DISCOVERABLE</a>,        </div>
<div class="line"><a name="l00088"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da4fa5e4d5a80a86974515a8a362989711">   88</a></span>&#160;    <a class="code" href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da4fa5e4d5a80a86974515a8a362989711">ESP_BT_GENERAL_DISCOVERABLE</a>,        </div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;} ;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160; </div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160; </div>
<div class="line"><a name="l00096"></a><span class="lineno"><a class="line" href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">   96</a></span>&#160;<span class="keyword">enum</span> <a class="code" href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">esp_bt_mode_t</a> {</div>
<div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a479cab8a16f9567cb00a7940564d3943">   97</a></span>&#160;    <a class="code" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a479cab8a16f9567cb00a7940564d3943">ESP_BT_MODE_IDLE</a>       = 0x00,   </div>
<div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4aedb9bdfab09a30a92faa66165dc35c36">   98</a></span>&#160;    <a class="code" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4aedb9bdfab09a30a92faa66165dc35c36">ESP_BT_MODE_BLE</a>        = 0x01,   </div>
<div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a9fab3664192991a2bd656ba090c1da85">   99</a></span>&#160;    <a class="code" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a9fab3664192991a2bd656ba090c1da85">ESP_BT_MODE_CLASSIC_BT</a> = 0x02,   </div>
<div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a3d0aa499ec6f785eeaa44765e8585cb3">  100</a></span>&#160;    <a class="code" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a3d0aa499ec6f785eeaa44765e8585cb3">ESP_BT_MODE_BTDM</a>       = 0x03,   </div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;} ;</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160; </div>
<div class="line"><a name="l00107"></a><span class="lineno"><a class="line" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">  107</a></span>&#160;<span class="keyword">typedef</span> uint8_t <a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a>[ESP_BD_ADDR_LEN];</div>
<div class="ttc" id="agroup__a2dp_html_ga0af05e9d744ec14ee33e345d678e8ade"><div class="ttname"><a href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a></div><div class="ttdeci">esp_avrc_rn_event_ids_t</div><div class="ttdoc">AVRC event notification ids.</div><div class="ttdef"><b>Definition:</b> external_lists.h:50</div></div>
<div class="ttc" id="agroup__a2dp_html_ga49adfa87b1ad7420b0075a0ac03cc194"><div class="ttname"><a href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a></div><div class="ttdeci">esp_a2d_audio_state_t</div><div class="ttdoc">Buetooth A2DP datapath states.</div><div class="ttdef"><b>Definition:</b> external_lists.h:5</div></div>
<div class="ttc" id="agroup__a2dp_html_ga52caa2d1e1c9d880c9651d52ff78a590"><div class="ttname"><a href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a></div><div class="ttdeci">esp_a2d_connection_state_t</div><div class="ttdoc">Buetooth A2DP connection states.</div><div class="ttdef"><b>Definition:</b> external_lists.h:16</div></div>
<div class="ttc" id="agroup__a2dp_html_ga6562796046744d7333ad2c64d2c8557d"><div class="ttname"><a href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">esp_bt_discovery_mode_t</a></div><div class="ttdeci">esp_bt_discovery_mode_t</div><div class="ttdoc">AVRCP discovery mode.</div><div class="ttdef"><b>Definition:</b> external_lists.h:85</div></div>
<div class="ttc" id="agroup__a2dp_html_ga89fdf5fb26b1ea6f33d36cc0eebca4fb"><div class="ttname"><a href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a></div><div class="ttdeci">esp_avrc_playback_stat_t</div><div class="ttdoc">AVRCP current status of playback.</div><div class="ttdef"><b>Definition:</b> external_lists.h:72</div></div>
<div class="ttc" id="agroup__a2dp_html_ga9861ef3ac455a4b2875219d457073de4"><div class="ttname"><a href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">esp_bt_mode_t</a></div><div class="ttdeci">esp_bt_mode_t</div><div class="ttdoc">Bluetooth Controller mode.</div><div class="ttdef"><b>Definition:</b> external_lists.h:96</div></div>
<div class="ttc" id="agroup__a2dp_html_gab981cf845089ef19df871466126b6f14"><div class="ttname"><a href="group__a2dp.html#gab981cf845089ef19df871466126b6f14">ESP_AVRC_RN_PLAY_STATUS_CHANGE</a></div><div class="ttdeci">enum esp_avrc_rn_event_ids_t ESP_AVRC_RN_PLAY_STATUS_CHANGE</div><div class="ttdoc">AVRC event notification ids.</div><div class="ttdef"><b>Definition:</b> external_lists.h:28</div></div>
<div class="ttc" id="agroup__a2dp_html_gae1f72542f04666cd97c26732366bf109"><div class="ttname"><a href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a></div><div class="ttdeci">uint8_t esp_bd_addr_t[ESP_BD_ADDR_LEN]</div><div class="ttdoc">Bluetooth address.</div><div class="ttdef"><b>Definition:</b> external_lists.h:107</div></div>
<div class="ttc" id="agroup__a2dp_html_gga49adfa87b1ad7420b0075a0ac03cc194a1fb2443ca89d24271931c19e1554b088"><div class="ttname"><a href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a1fb2443ca89d24271931c19e1554b088">ESP_A2D_AUDIO_STATE_STARTED</a></div><div class="ttdeci">@ ESP_A2D_AUDIO_STATE_STARTED</div><div class="ttdef"><b>Definition:</b> external_lists.h:7</div></div>
<div class="ttc" id="agroup__a2dp_html_gga49adfa87b1ad7420b0075a0ac03cc194a2144df52646c47f697ec1f0e4b59686f"><div class="ttname"><a href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a2144df52646c47f697ec1f0e4b59686f">ESP_A2D_AUDIO_STATE_REMOTE_SUSPEND</a></div><div class="ttdeci">@ ESP_A2D_AUDIO_STATE_REMOTE_SUSPEND</div><div class="ttdef"><b>Definition:</b> external_lists.h:9</div></div>
<div class="ttc" id="agroup__a2dp_html_gga49adfa87b1ad7420b0075a0ac03cc194a2cac45c7b95065ed02420c5a632ab538"><div class="ttname"><a href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a2cac45c7b95065ed02420c5a632ab538">ESP_A2D_AUDIO_STATE_STOPPED</a></div><div class="ttdeci">@ ESP_A2D_AUDIO_STATE_STOPPED</div><div class="ttdef"><b>Definition:</b> external_lists.h:8</div></div>
<div class="ttc" id="agroup__a2dp_html_gga49adfa87b1ad7420b0075a0ac03cc194a4f131f965f55142fa9fb3557f88b8793"><div class="ttname"><a href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a4f131f965f55142fa9fb3557f88b8793">ESP_A2D_AUDIO_STATE_SUSPEND</a></div><div class="ttdeci">@ ESP_A2D_AUDIO_STATE_SUSPEND</div><div class="ttdef"><b>Definition:</b> external_lists.h:6</div></div>
<div class="ttc" id="agroup__a2dp_html_gga52caa2d1e1c9d880c9651d52ff78a590a0248c5f6ba7c85f2044a9a2c79bc57d4"><div class="ttname"><a href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590a0248c5f6ba7c85f2044a9a2c79bc57d4">ESP_A2D_CONNECTION_STATE_CONNECTED</a></div><div class="ttdeci">@ ESP_A2D_CONNECTION_STATE_CONNECTED</div><div class="ttdef"><b>Definition:</b> external_lists.h:19</div></div>
<div class="ttc" id="agroup__a2dp_html_gga52caa2d1e1c9d880c9651d52ff78a590a3661beedfd8bd0804def35a94a948543"><div class="ttname"><a href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590a3661beedfd8bd0804def35a94a948543">ESP_A2D_CONNECTION_STATE_CONNECTING</a></div><div class="ttdeci">@ ESP_A2D_CONNECTION_STATE_CONNECTING</div><div class="ttdef"><b>Definition:</b> external_lists.h:18</div></div>
<div class="ttc" id="agroup__a2dp_html_gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410"><div class="ttname"><a href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410">ESP_A2D_CONNECTION_STATE_DISCONNECTED</a></div><div class="ttdeci">@ ESP_A2D_CONNECTION_STATE_DISCONNECTED</div><div class="ttdef"><b>Definition:</b> external_lists.h:17</div></div>
<div class="ttc" id="agroup__a2dp_html_gga52caa2d1e1c9d880c9651d52ff78a590aca30f95d06a691676a9d85a0998ea1e0"><div class="ttname"><a href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590aca30f95d06a691676a9d85a0998ea1e0">ESP_A2D_CONNECTION_STATE_DISCONNECTING</a></div><div class="ttdeci">@ ESP_A2D_CONNECTION_STATE_DISCONNECTING</div><div class="ttdef"><b>Definition:</b> external_lists.h:20</div></div>
<div class="ttc" id="agroup__a2dp_html_gga6562796046744d7333ad2c64d2c8557da286efd17a5bec03100355acf2dd198de"><div class="ttname"><a href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da286efd17a5bec03100355acf2dd198de">ESP_BT_LIMITED_DISCOVERABLE</a></div><div class="ttdeci">@ ESP_BT_LIMITED_DISCOVERABLE</div><div class="ttdef"><b>Definition:</b> external_lists.h:87</div></div>
<div class="ttc" id="agroup__a2dp_html_gga6562796046744d7333ad2c64d2c8557da41f6ad4d6283fe67dfeaf9cb3e14da65"><div class="ttname"><a href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da41f6ad4d6283fe67dfeaf9cb3e14da65">ESP_BT_NON_DISCOVERABLE</a></div><div class="ttdeci">@ ESP_BT_NON_DISCOVERABLE</div><div class="ttdef"><b>Definition:</b> external_lists.h:86</div></div>
<div class="ttc" id="agroup__a2dp_html_gga6562796046744d7333ad2c64d2c8557da4fa5e4d5a80a86974515a8a362989711"><div class="ttname"><a href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da4fa5e4d5a80a86974515a8a362989711">ESP_BT_GENERAL_DISCOVERABLE</a></div><div class="ttdeci">@ ESP_BT_GENERAL_DISCOVERABLE</div><div class="ttdef"><b>Definition:</b> external_lists.h:88</div></div>
<div class="ttc" id="agroup__a2dp_html_gga89fdf5fb26b1ea6f33d36cc0eebca4fba080ee68b71dd63f15c1f4f28ad9c7fec"><div class="ttname"><a href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba080ee68b71dd63f15c1f4f28ad9c7fec">ESP_AVRC_PLAYBACK_PLAYING</a></div><div class="ttdeci">@ ESP_AVRC_PLAYBACK_PLAYING</div><div class="ttdef"><b>Definition:</b> external_lists.h:74</div></div>
<div class="ttc" id="agroup__a2dp_html_gga89fdf5fb26b1ea6f33d36cc0eebca4fba4b13603deb02c5bc0a9f60bdbe3f55d9"><div class="ttname"><a href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba4b13603deb02c5bc0a9f60bdbe3f55d9">ESP_AVRC_PLAYBACK_FWD_SEEK</a></div><div class="ttdeci">@ ESP_AVRC_PLAYBACK_FWD_SEEK</div><div class="ttdef"><b>Definition:</b> external_lists.h:76</div></div>
<div class="ttc" id="agroup__a2dp_html_gga89fdf5fb26b1ea6f33d36cc0eebca4fba91a5ffbfe55aba08e1d323848e719fd7"><div class="ttname"><a href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba91a5ffbfe55aba08e1d323848e719fd7">ESP_AVRC_PLAYBACK_STOPPED</a></div><div class="ttdeci">@ ESP_AVRC_PLAYBACK_STOPPED</div><div class="ttdef"><b>Definition:</b> external_lists.h:73</div></div>
<div class="ttc" id="agroup__a2dp_html_gga89fdf5fb26b1ea6f33d36cc0eebca4fba98c8358017f0694e2af18dcecf87eb6e"><div class="ttname"><a href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba98c8358017f0694e2af18dcecf87eb6e">ESP_AVRC_PLAYBACK_REV_SEEK</a></div><div class="ttdeci">@ ESP_AVRC_PLAYBACK_REV_SEEK</div><div class="ttdef"><b>Definition:</b> external_lists.h:77</div></div>
<div class="ttc" id="agroup__a2dp_html_gga89fdf5fb26b1ea6f33d36cc0eebca4fbaad9f30b7d23101c762b775f083e76a80"><div class="ttname"><a href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fbaad9f30b7d23101c762b775f083e76a80">ESP_AVRC_PLAYBACK_PAUSED</a></div><div class="ttdeci">@ ESP_AVRC_PLAYBACK_PAUSED</div><div class="ttdef"><b>Definition:</b> external_lists.h:75</div></div>
<div class="ttc" id="agroup__a2dp_html_gga89fdf5fb26b1ea6f33d36cc0eebca4fbab141fa00060af447da20081cb396f02d"><div class="ttname"><a href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fbab141fa00060af447da20081cb396f02d">ESP_AVRC_PLAYBACK_ERROR</a></div><div class="ttdeci">@ ESP_AVRC_PLAYBACK_ERROR</div><div class="ttdef"><b>Definition:</b> external_lists.h:78</div></div>
<div class="ttc" id="agroup__a2dp_html_gga9861ef3ac455a4b2875219d457073de4a3d0aa499ec6f785eeaa44765e8585cb3"><div class="ttname"><a href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a3d0aa499ec6f785eeaa44765e8585cb3">ESP_BT_MODE_BTDM</a></div><div class="ttdeci">@ ESP_BT_MODE_BTDM</div><div class="ttdef"><b>Definition:</b> external_lists.h:100</div></div>
<div class="ttc" id="agroup__a2dp_html_gga9861ef3ac455a4b2875219d457073de4a479cab8a16f9567cb00a7940564d3943"><div class="ttname"><a href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a479cab8a16f9567cb00a7940564d3943">ESP_BT_MODE_IDLE</a></div><div class="ttdeci">@ ESP_BT_MODE_IDLE</div><div class="ttdef"><b>Definition:</b> external_lists.h:97</div></div>
<div class="ttc" id="agroup__a2dp_html_gga9861ef3ac455a4b2875219d457073de4a9fab3664192991a2bd656ba090c1da85"><div class="ttname"><a href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a9fab3664192991a2bd656ba090c1da85">ESP_BT_MODE_CLASSIC_BT</a></div><div class="ttdeci">@ ESP_BT_MODE_CLASSIC_BT</div><div class="ttdef"><b>Definition:</b> external_lists.h:99</div></div>
<div class="ttc" id="agroup__a2dp_html_gga9861ef3ac455a4b2875219d457073de4aedb9bdfab09a30a92faa66165dc35c36"><div class="ttname"><a href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4aedb9bdfab09a30a92faa66165dc35c36">ESP_BT_MODE_BLE</a></div><div class="ttdeci">@ ESP_BT_MODE_BLE</div><div class="ttdef"><b>Definition:</b> external_lists.h:98</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
