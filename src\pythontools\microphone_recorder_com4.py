import serial
import time
import numpy as np
import soundfile as sf
from datetime import datetime
import os

def record_from_microphone():
    """Record audio data from ESP32 microphone via COM4 and create WAV file"""
    
    print("🎤 ESP32 Microphone Recorder (COM4)")
    print("=" * 50)
    
    # Connect to ESP32 on COM4 with higher baudrate
    print("📡 Connecting to ESP32 on COM4 (921600 baud)...")
    try:
        ser = serial.Serial('COM4', 921600, timeout=10)
        time.sleep(2)
        print("✅ Connected successfully!")
    except Exception as e:
        print(f"❌ Failed to connect to COM4: {e}")
        print("💡 Make sure ESP32 is using 921600 baudrate")
        return False
    
    # Send buffered recording command for 10 seconds
    print("📤 Sending 'b' command to start 10-second buffered recording...")
    ser.write(b'b\n')
    ser.flush()
    
    # Wait for recording to start
    print("🎯 Waiting for audio data...")
    samples = []
    capturing_data = False
    start_time = time.time()
    timeout_start = time.time()
    max_timeout = 30  # Maximum 30 seconds total timeout

    try:
        while True:
            # Check for overall timeout
            if time.time() - timeout_start > max_timeout:
                print(f"⏰ Timeout after {max_timeout} seconds!")
                break

            line = ser.readline().decode(errors="ignore").strip()

            if line:
                print(f"📥 {line}")  # Show ESP32 output

                # Check if audio data starts
                if "--- AUDIO DATA START ---" in line:
                    print("✅ Audio data capture started...")
                    capturing_data = True
                    start_time = time.time()
                    continue

                # Check if audio data ends
                if "--- AUDIO DATA END ---" in line:
                    print(f"✅ Audio capture complete! Got {len(samples)} samples")
                    break

                # Capture sample data (only numeric lines)
                if capturing_data:
                    # Try to parse as integer (handle negative numbers)
                    try:
                        # Check if line is a valid integer (including negative)
                        if line.strip() and (line.strip().lstrip('-').isdigit() or line.strip() == '0'):
                            sample_value = int(line.strip())
                            samples.append(sample_value)

                            # Progress indicator every 48000 samples (1 second)
                            if len(samples) % 48000 == 0:
                                elapsed = time.time() - start_time
                                seconds = len(samples) // 48000
                                print(f"📊 Captured {len(samples)} samples ({seconds}s / 10s)")

                            # Safety check: Stop if we have enough samples (10 seconds = 480000 samples)
                            if len(samples) >= 480000:
                                print(f"🛑 Reached maximum samples (480000), stopping capture...")
                                break
                    except ValueError:
                        # Skip non-numeric lines during capture
                        if not any(keyword in line for keyword in ["Progress:", "📊", "Total samples"]):
                            print(f"⚠️  Skipping non-numeric line: {line[:50]}...")
                        pass

            # If capturing and no data for 5 seconds, assume it's done
            if capturing_data and (time.time() - start_time) > 15:  # 15 second timeout during capture
                print(f"⏰ No data received for 15 seconds, assuming recording complete...")
                break
    
    except KeyboardInterrupt:
        print("\n⏹️  Recording interrupted by user")
        return False
    except Exception as e:
        print(f"❌ Error during capture: {e}")
        return False
    finally:
        ser.close()
        print("🔌 Serial connection closed")
    
    # Check if we got data
    if not samples:
        print("❌ No audio data captured!")
        return False

    # Diagnose captured data
    print(f"🔍 Captured {len(samples)} samples")
    print(f"📊 Expected: 480000 samples (10 seconds at 48kHz)")
    print(f"⏱️  Actual duration: {len(samples)/48000:.3f} seconds")

    if len(samples) < 48000:
        print("⚠️  WARNING: Very short recording! Less than 1 second of audio")
    elif len(samples) < 240000:
        print("⚠️  WARNING: Short recording! Less than 5 seconds of audio")

    # Convert to numpy array
    print("🔄 Converting to audio format...")
    signal = np.array(samples, dtype=np.int16)
    
    # Create WAV file in the same directory as this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    wav_filename = os.path.join(script_dir, "microphone_recording.wav")

    print(f"💾 Creating WAV file: {wav_filename}")
    
    # WAV parameters
    sample_rate = 48000  # 48 kHz
    
    try:
        sf.write(wav_filename, signal, samplerate=sample_rate, subtype='PCM_16')
        print(f"✅ WAV file created successfully!")
        print(f"📁 File: {wav_filename}")
        print(f"📊 Samples: {len(samples)}")
        print(f"⏱️  Duration: {len(samples)/sample_rate:.3f} seconds")
        print(f"🎵 Sample Rate: {sample_rate} Hz")
        print(f"🎤 Format: Mono, 16-bit")
        
        # Statistics
        print(f"📈 Signal Range: {np.min(signal)} to {np.max(signal)}")
        print(f"📊 RMS Level: {np.sqrt(np.mean(signal**2)):.1f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating WAV file: {e}")
        return False

def main():
    """Main function - automatically starts recording"""
    print("🎤 ESP32 Microphone Recorder")
    print("📡 Connects to COM4 and records 10-second audio")
    print("⚙️  Config: 48kHz, 16-bit, Mono")
    print("-" * 50)

    # Automatically start recording (no user interaction needed)
    print("🚀 Starting automatic recording...")
    success = record_from_microphone()

    if success:
        print("\n🎉 Recording completed successfully!")
        print("📁 WAV file created: microphone_recording.wav")
    else:
        print("\n❌ Recording failed!")
        print("� Try running the script again or check ESP32 connection")

    print("\n⏸️  Press Enter to exit...")
    input()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Program interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
    
    print("🏁 Exiting...")
