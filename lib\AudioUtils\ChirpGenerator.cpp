#include "ChirpGenerator.h"
#include <cmath>
#include <Arduino.h>

ChirpGenerator::ChirpGenerator() {
    if(!generated) {
        generateSineChirp();
        generated = true;
    }
}

void ChirpGenerator::generateSineChirp() {
    // Generate mathematical sine chirp (frequency sweep)
    for(uint32_t n = 0; n < NUM_SAMPLES; n++) {
        // Calculate instantaneous frequency: f(t) = F_START + k*t
        // where k = (F_END - F_START) / CHIRP_TOTAL
        float instantaneous_freq = F_START + CHIRP_K * n;

        // Calculate phase: φ(t) = 2π * (F_START * t + k * t²/2)
        // For discrete samples: φ[n] = 2π * (F_START * n/fs + k * n²/(2*fs²))
        float time_sample = static_cast<float>(n) / SAMPLE_RATE;
        float phase = 2.0f * M_PI * (F_START * time_sample + CHIRP_K * n * time_sample / 2.0f);

        // Generate sine wave sample
        float sine_value = sin(phase);

        // Apply fade-in/fade-out ramp to reduce clicks
        float ramp = 1.0f;
        if (n < FADE_SAMPLES) {
            // Fade-in: linear ramp from 0 to 1 over first 2ms
            ramp = static_cast<float>(n) / FADE_SAMPLES;
        } else if (n > NUM_SAMPLES - FADE_SAMPLES) {
            // Fade-out: linear ramp from 1 to 0 over last 2ms
            ramp = static_cast<float>(NUM_SAMPLES - n) / FADE_SAMPLES;
        }

        // Convert to 16-bit integer with amplitude scaling and ramp
        chirp[n] = static_cast<int16_t>(sine_value * CHIRP_AMPLITUDE * ramp);
    }
}

const int16_t* ChirpGenerator::generate() {
    return chirp;
}

std::size_t ChirpGenerator::size() const {
    return NUM_SAMPLES;
}

// Real-time transmission method - now outputs the pre-generated sine chirp
void ChirpGenerator::transmitChirp(void (*outputFunction)(int16_t)) {
    // Calculate delay between samples in microseconds
    uint32_t sample_delay_us = 1000000 / SAMPLE_RATE;  // ~20.83 µs at 48kHz

    for(uint32_t i = 0; i < NUM_SAMPLES; i++) {
        // Output the current sample
        outputFunction(chirp[i]);

        // Wait for the sample period
        delayMicroseconds(sample_delay_us);
    }

    // End with zero output
    outputFunction(0);
}
