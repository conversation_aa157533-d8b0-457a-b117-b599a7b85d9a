import serial
import time

# Very simple version - exactly like your original but sends 'd' command
ser = serial.Serial('COM3', 115200)

# Wait for connection
time.sleep(2)

# Send dump command
print("Sending 'dump' command...")
ser.write(b'd\n')
ser.flush()

# Capture data to file
print("Capturing to chirp.txt...")
with open("chirp.txt", "w") as f:
    try:
        while True:
            line = ser.readline().decode(errors="ignore")
            print(line, end="")
            f.write(line)
    except KeyboardInterrupt:
        print("\nStopped.")
    finally:
        ser.close()
