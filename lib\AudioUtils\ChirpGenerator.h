#pragma once
#include "AudioUtils.h"
#include <cstdint>
#include <cstddef>
#include <cmath>

class ChirpGenerator {
public:
    ChirpGenerator();
    const int16_t* generate();
    std::size_t size() const;
    void transmitChirp(void (*outputFunction)(int16_t));

private:
    static constexpr uint32_t NUM_SAMPLES = CHIRP_TOTAL;  // Use CHIRP_TOTAL (1440 samples)
    int16_t chirp[NUM_SAMPLES];
    bool generated = false;

    // Sine chirp parameters
    static constexpr int16_t CHIRP_AMPLITUDE = 30000;

    void generateSineChirp();
};
