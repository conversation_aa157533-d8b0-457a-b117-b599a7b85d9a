#include "gpio.h"

//*********************************************************************************************************
// Initialize IO pins
// The intention was to use sys config to configure IO pins and use GPIO_write to drive output, but this functions is extremely slow !
// test : while(1) -> on/off output + optimization level3  : with GPIO_write 226Khz , with GPIO_clearDio/GPIO_setDio : 9MHz
//*********************************************************************************************************
void Gpio_Init(void)
{
    IOCIOInputSet(DetectBoardType_ID0, IOC_INPUT_ENABLE);
    IOCIOInputSet(DetectBoardType_ID1, IOC_INPUT_ENABLE);
    IOCIOPortPullSet(DetectBoardType_ID0, IOC_IOPULL_UP);
    IOCIOPortPullSet(DetectBoardType_ID1, IOC_IOPULL_UP);
    GPIO_setOutputEnableDio(SpeakerOutput1,GPIO_OUTPUT_ENABLE);
    GPIO_setOutputEnableDio(SpeakerOutput2,GPIO_OUTPUT_ENABLE);
}
