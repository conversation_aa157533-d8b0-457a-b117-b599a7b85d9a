/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "CC1312R1F3RGZ" --package "RGZ" --part "Default" --product "simplelink_cc13x2_26x2_sdk@**********"
 * @versions {"data":"2021010520","timestamp":"2021010520","tool":"1.7.0+1746","templates":null}
 */

/**
 * Import the modules used in this configuration.
 */
const CCFG    = scripting.addModule("/ti/devices/CCFG");
const custom  = scripting.addModule("/ti/devices/radioconfig/custom");
const ADCBuf  = scripting.addModule("/ti/drivers/ADCBuf", {}, false);
const ADCBuf1 = ADCBuf.addInstance();
const RTOS    = scripting.addModule("/ti/drivers/RTOS");
const UART    = scripting.addModule("/ti/drivers/UART", {}, false);
const UART1   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
CCFG.forceVddr          = true;
CCFG.ccfgTemplate.$name = "ti_devices_CCFGTemplate0";

custom.prop8                                              = ["custom868"];
custom.radioConfigcustom868.$name                         = "ti_devices_radioconfig_settings_prop0";
custom.radioConfigcustom868.phyType868                    = "2gfsk50kbps";
custom.radioConfigcustom868.codeExportConfig.$name        = "ti_devices_radioconfig_code_export_param0";
custom.radioConfigcustom868.codeExportConfig.symGenMethod = "Legacy";

ADCBuf1.$name                                = "ADCBUF";
ADCBuf1.channels                             = 3;
ADCBuf1.adc.$assign                          = "ADC0";
ADCBuf1.timerInstance.$name                  = "CONFIG_GPTIMER_0";
ADCBuf1.timerInstance.timer.$assign          = "GPTM0";
ADCBuf1.adcBufChannel0.$name                 = "ADCBUF_SOUND";
ADCBuf1.adcBufChannel0.adc.adcPin.$assign    = "40";
ADCBuf1.adcBufChannel0.adcPinInstance0.$name = "CONFIG_PIN_0";
ADCBuf1.adcBufChannel1.$name                 = "ADCBUF_TEMPERATURE";
ADCBuf1.adcBufChannel1.adc.adcPin.$assign    = "42";
ADCBuf1.adcBufChannel1.adcPinInstance1.$name = "CONFIG_PIN_1";
ADCBuf1.adcBufChannel2.$name                 = "ADCBUF_BATTERY_VOLTAGE";
ADCBuf1.adcBufChannel2.adc.adcPin.$assign    = "43";
ADCBuf1.adcBufChannel2.adcPinInstance2.$name = "CONFIG_PIN_2";

RTOS.name = "NoRTOS";

UART1.$name               = "UART_0";
UART1.uart.$assign        = "UART0";
UART1.uart.txPin.$assign  = "8";
UART1.uart.rxPin.$assign  = "7";
UART1.txPinInstance.$name = "CONFIG_PIN_5";
UART1.rxPinInstance.$name = "CONFIG_PIN_6";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
ADCBuf1.adc.dmaADCChannel.$suggestSolution  = "DMA_CH7";
ADCBuf1.adcBufChannel0.adc.$suggestSolution = "ADC0";
ADCBuf1.adcBufChannel1.adc.$suggestSolution = "ADC0";
ADCBuf1.adcBufChannel2.adc.$suggestSolution = "ADC0";
