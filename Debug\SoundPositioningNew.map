******************************************************************************
                  TI ARM Linker PC v20.2.4                     
******************************************************************************
>> Linked Sun Jul 11 22:03:48 2021

OUTPUT FILE NAME:   <SoundPositioningNew.out>
ENTRY POINT SYMBOL: "resetISR"  address: 00008451


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00058000  00008d8a  0004f276  R  X
  GPRAM                 11000000   00002000  00000000  00002000  RW X
  SRAM                  20000000   00014000  000089af  0000b651  RW X
  LOG_DATA              90000000   00040000  00000000  00040000  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00008bc4   00008bc4    r-x
  00000000    00000000    000000d8   000000d8    r-- .intvecs
  000000d8    000000d8    0000868a   0000868a    r-x .text
  00008764    00008764    00000460   00000460    r-- .const
00008bc4    00008bc4    00000008   00000008    rw-
  00008bc4    00008bc4    00000008   00000008    rw- .args
00008bd0    00008bd0    00000168   00000168    r--
  00008bd0    00008bd0    00000168   00000168    r-- .cinit
00057fa8    00057fa8    00000058   00000058    r--
  00057fa8    00057fa8    00000058   00000058    r-- .ccfg
20000000    20000000    00000d4d   00000000    rw-
  20000000    20000000    00000d4d   00000000    rw- .data
20000e00    20000e00    000000d8   00000000    rw-
  20000e00    20000e00    000000d8   00000000    rw- .vtable_ram
20001870    20001870    00000010   00000000    rw-
  20001870    20001870    00000010   00000000    rw- .TI.bound:dmaADCPriControlTableEntry
20001a70    20001a70    0000777a   00000000    rw-
  20001a70    20001a70    00000010   00000000    rw- .TI.bound:dmaADCAltControlTableEntry
  20001a80    20001a80    00004000   00000000    rw- .sysmem
  20005a80    20005a80    0000376a   00000000    rw- .bss
20013c00    20013c00    00000400   00000000    rw-
  20013c00    20013c00    00000400   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000d8     
                  00000000    000000d8     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.intvecs)

.text      0    000000d8    0000868a     
                  000000d8    000004b0     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_fsmActiveState)
                  00000588    00000370     ControlMobile.obj (.text:Mobile_execute_MeasureAll)
                  000008f8    00000312     ControlMobile.obj (.text:ControlMobile_Run)
                  00000c0a    00000006     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_stop)
                  00000c10    000002a0     ControlMobile.obj (.text:CalculatePosition)
                  00000eb0    00000200     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_open)
                  000010b0    000001e0     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_init)
                  00001290    000001d4     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_dispatchNextCmd)
                  00001464    000001b6     rtsv7M4_T_le_v4SPD16_eabi.lib : fd_add_t2.asm.obj (.text)
                  0000161a    00000002     driverlib.lib : interrupt.obj (.text:IntDefaultHandler)
                  0000161c    00000198     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_sleep)
                  000017b4    00000190                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf1)
                  00001944    0000018c                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:runCalibrateFsm)
                  00001ad0    00000174                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_open)
                  00001c44    00000174                      : PowerCC26X2.oem4f (.text:Power_init)
                  00001db8    00000164                      : UARTCC26XX.oem4f (.text:UARTCC26XX_open)
                  00001f1c    00000158     rtsv7M4_T_le_v4SPD16_eabi.lib : e_sqrt.c.obj (.text:sqrt)
                  00002074    00000154     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_radioOpDoneCb)
                  000021c8    00000154     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_hwiIntFxn)
                  0000231c    00000148     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_fsmSetupState)
                  00002464    0000013a     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_read)
                  0000259e    00000002     rf_multiMode_cc13x2.aem4f : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCCpePatchReset)
                  000025a0    00000138                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_abortCmd)
                  000026d8    00000136     rtsv7M4_T_le_v4SPD16_eabi.lib : fd_div_t2.asm.obj (.text)
                  0000280e    00000002     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_defaultCallback)
                  00002810    00000120     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureXOSCHF)
                  00002930    00000114                      : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfg)
                  00002a44    00000112     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_decodeOverridePointers)
                  00002b56    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_destruct)
                  00002b58    00000104     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_hwiCpe0Active)
                  00002c5c    00000004     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_getResolution)
                  00002c60    00000102     ControlX0.obj (.text:ControlX0_RunX)
                  00002d62    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:clkFxn)
                  00002d64    00000100     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convert)
                  00002e64    000000fc     rtsv7M4_T_le_v4SPD16_eabi.lib : fd_mul_t2.asm.obj (.text)
                  00002f60    000000f0     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlStandby)
                  00003050    000000f0     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_fsmPowerUpState)
                  00003140    000000ea     ControlX0.obj (.text:ControlX0_Run0)
                  0000322a    00000006     driverlib.lib : cpu.obj (.text:NOROM_CPUdelay)
                  00003230    000000e8     RFCommunication.obj (.text:RFCommunication_TXReadSamples)
                  00003318    000000e8     RFCommunication.obj (.text:RFCommunication_TXReturnResults)
                  00003400    000000e8     RFQueue.obj (.text:RFQueue_defineQueue)
                  000034e8    000000e4     RFCommunication.obj (.text:RFCommunication_init)
                  000035cc    000000e4     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_postCmd)
                  000036b0    000000e0     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_standbyPolicy)
                  00003790    000000e0     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_write)
                  00003870    000000de     ADC.obj (.text:ADC_ReadArrayADCraw)
                  0000394e    00000002     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:emptyClockFunc)
                  00003950    000000d8     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_pendCmd)
                  00003a28    000000d8     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_construct)
                  00003b00    000000d2     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_updatePaConfiguration)
                  00003bd2    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:faultISR)
                  00003bd4    000000d0     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_swiHw)
                  00003ca4    00000004     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:CPUwfi)
                  00003ca8    000000cc     RFCommunication.obj (.text:RFCommunication_TXArraySection)
                  00003d74    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_isActive)
                  00003d78    000000c8     RFCommunication.obj (.text:RFCommunication_TXListenToSound)
                  00003e40    000000c0     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_releaseDependency)
                  00003f00    000000c0                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initIO)
                  00003fc0    000000bc     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_initRadioSetup)
                  0000407c    000000bc     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_construct)
                  00004138    000000b8     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_setDependency)
                  000041f0    000000b8                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initHw)
                  000042a8    000000b6     time.obj (.text:Time_Init)
                  0000435e    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:intDefaultHandler)
                  00004360    000000b4                     : ClockPTimer_nortos.oem4f (.text:ClockP_startup)
                  00004414    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_setTimeout)
                  00004418    000000b0     Serial.obj (.text:Serial_CopyArraySectionToSerial)
                  000044c8    000000af     Serial.obj (.text:Serial_PrintMeasurements)
                  00004577    00000001     --HOLE-- [fill = 0]
                  00004578    000000ac     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_workFuncDynamic)
                  00004624    000000ac     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_open)
                  000046d0    000000ac     driverlib.lib : setup.obj (.text:TrimAfterColdResetWakeupFromShutDown)
                  0000477c    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_enable)
                  00004780    000000a6     Serial.obj (.text:Serial_CheckCommand)
                  00004826    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:nmiISR)
                  00004828    000000a4     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_ratRestartChannels)
                  000048cc    00000004                               : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCOverrideUpdate)
                  000048d0    000000a4     Temperature.obj (.text:Temperature_GetValueDeciDegrees)
                  00004974    00000004     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_head)
                  00004978    000000a0     RFCommunication.obj (.text:RFCommunication_TXMeasurements)
                  00004a18    000000a0     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_pend)
                  00004ab8    0000009e     RFCommunication.obj (.text:RFCommunication_CheckReceivedPacket)
                  00004b56    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : div0.asm.obj (.text)
                  00004b58    0000009c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_dispatch)
                  00004bf4    0000009c     rtsv7M4_T_le_v4SPD16_eabi.lib : memcpy_t2.asm.obj (.text)
                  00004c90    00000098     driverlib.lib : setup.obj (.text:NOROM_SetupTrimDevice)
                  00004d28    00000098     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_calculateDispatchTime)
                  00004dc0    00000098     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeTxFifoFlush)
                  00004e58    00000096     rtsv7M4_T_le_v4SPD16_eabi.lib : ull_div_t2.asm.obj (.text)
                  00004eee    00000002     --HOLE-- [fill = 0]
                  00004ef0    00000092     ti_drivers_config.obj (.text:Board_shutDownExtFlash)
                  00004f82    00000092     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_writeCancel)
                  00005014    00000090     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_discardPendCmd)
                  000050a4    00000004     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_next)
                  000050a8    0000008e     SoundTransmit.obj (.text:TimerMatchCallback)
                  00005136    00000002     --HOLE-- [fill = 0]
                  00005138    00000088     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_open)
                  000051c0    00000088     driverlib.lib : osc.obj (.text:NOROM_OSCHF_AttemptToSwitchToXosc)
                  00005248    00000088     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_cmdDispatchTime)
                  000052d0    00000088     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf2)
                  00005358    00000088                      : PowerCC26X2.oem4f (.text:disableLFClockQualifiers)
                  000053e0    00000088                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:updateSubSecInc)
                  00005468    00000084     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_issueRadioFreeCb)
                  000054ec    00000084                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_verifyGap)
                  00005570    00000084     SoundReceive.obj (.text:SoundReceive_CalculateDistance)
                  000055f4    00000084     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_close)
                  00005678    00000080                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_hwiFxn)
                  000056f8    00000080     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_start)
                  00005778    00000080                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_construct)
                  000057f8    00000080     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlSetRechargeBeforePowerDown)
                  00005878    0000007c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_ratFreeChannel)
                  000058f4    00000004                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_defaultExecutionPolicy)
                  000058f8    0000007b     Serial.obj (.text:Serial_CopyReceivedArraySectionToSerial)
                  00005973    00000001     --HOLE-- [fill = 0]
                  00005974    0000007a     rtsv7M4_T_le_v4SPD16_eabi.lib : memset_t2.asm.obj (.text)
                  000059ee    00000002     --HOLE-- [fill = 0]
                  000059f0    00000078     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_walkQueueDynamic)
                  00005a68    00000078     main.obj (.text:main)
                  00005ae0    00000074     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_resetHw)
                  00005b54    00000074                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_auxISR)
                  00005bc8    00000072     RFCommunication.obj (.text:RFCommunication_TXAskXToMeasureXto0)
                  00005c3a    00000072     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_detachOverrides)
                  00005cac    00000070                               : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCAnaDivTxOverride)
                  00005d1c    00000070                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_getCurrentTime)
                  00005d8c    0000006e                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_attachOverrides)
                  00005dfa    0000006e                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_howToSchedule)
                  00005e68    0000006e     rtsv7M4_T_le_v4SPD16_eabi.lib : fd_tos_t2.asm.obj (.text)
                  00005ed6    00000002     --HOLE-- [fill = 0]
                  00005ed8    0000006c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipType)
                  00005f44    0000006c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_dispatchNextEvent)
                  00005fb0    0000006a     SoundTransmit.obj (.text:SoundTransmit_GenerateSound)
                  0000601a    00000002     --HOLE-- [fill = 0]
                  0000601c    00000068     driverlib.lib : osc.obj (.text:NOROM_OSCHF_SwitchToRcOscTurnOffXosc)
                  00006084    00000068     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_hwiHw)
                  000060ec    00000068     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_post)
                  00006154    00000068                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_start)
                  000061bc    00000068     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_lzss.c.obj (.text:decompress:lzss:__TI_decompress_lzss)
                  00006224    00000064     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_control)
                  00006288    00000062     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_syncCb)
                  000062ea    00000062     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_swiIntFxn)
                  0000634c    00000060     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_applyRfCorePatch)
                  000063ac    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : pre_init.c.obj (.text:_system_pre_init)
                  000063b0    0000005e     DeviceType.obj (.text:DeviceType_ReadFromBoard)
                  0000640e    00000002     --HOLE-- [fill = 0]
                  00006410    0000005c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_remove)
                  0000646c    0000005c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_hwiCpe0PowerFsm)
                  000064c8    0000005c     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:startTxFifoEmptyClk)
                  00006524    00000058                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_loadDMAControlTableEntry)
                  0000657c    00000058                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_stop)
                  000065d4    00000058                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_initiateCalibration)
                  0000662c    00000056     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_defaultSubmitPolicy)
                  00006682    00000006     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_init)
                  00006688    00000056     Serial.obj (.text:Serial_Init)
                  000066de    00000054     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_adjustRawValues)
                  00006732    00000002     --HOLE-- [fill = 0]
                  00006734    00000054     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOff)
                  00006788    00000054     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_fsmXOSCState)
                  000067dc    00000050     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_construct)
                  0000682c    0000004e     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_close)
                  0000687a    00000006     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_clkReqAccess)
                  00006880    0000004e     gpio.obj (.text:Gpio_Init)
                  000068ce    00000002     --HOLE-- [fill = 0]
                  000068d0    0000004c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_cleanADC)
                  0000691c    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_initHw)
                  00006968    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_start)
                  000069b4    0000004c     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOn)
                  00006a00    0000004c     RFCommunication.obj (.text:RFCommunication_GetDataReceivedArraySectionPacket)
                  00006a4c    0000004c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_wakeupNotification)
                  00006a98    0000004c     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readCancel)
                  00006ae4    0000004c                      : UDMACC26XX.oem4f (.text:UDMACC26XX_open)
                  00006b30    0000004a                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_control)
                  00006b7a    00000006                      : UARTCC26XX.oem4f (.text:UARTCC26XX_readPolling)
                  00006b80    0000004a     ti_drivers_config.obj (.text:Board_wakeUpExtFlash)
                  00006bca    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_writePolling)
                  00006bd0    0000004a     Serial.obj (.text:Uart_ReadCallback)
                  00006c1a    00000002     --HOLE-- [fill = 0]
                  00006c1c    00000048     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertAdjustedToMicroVolts)
                  00006c64    00000048     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_construct)
                  00006cac    00000048     rf_multiMode_cc13x2.aem4f : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCSynthPowerDown)
                  00006cf4    00000048     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputEnable)
                  00006d3c    00000048                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_RCOSC_clockFunc)
                  00006d84    00000048                      : PowerCC26X2.oem4f (.text:PowerCC26XX_switchXOSC_HF)
                  00006dcc    00000048                      : PowerCC26X2.oem4f (.text:switchXOSCHF)
                  00006e14    00000048                      : UARTCC26XX.oem4f (.text:writeFinishedDoCallback)
                  00006e5c    00000046                      : RingBuf.oem4f (.text:RingBuf_put)
                  00006ea2    00000002     --HOLE-- [fill = 0]
                  00006ea4    00000044                      : ADCBuf.oem4f (.text:ADCBuf_init)
                  00006ee8    00000044     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetHwRevision)
                  00006f2c    00000044     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_swi)
                  00006f70    00000044     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_post)
                  00006fb4    00000044                     : SwiP_nortos.oem4f (.text:SwiP_restore)
                  00006ff8    00000044     drivers_cc13x2.a : UART.oem4f (.text:UART_init)
                  0000703c    00000044     rtsv7M4_T_le_v4SPD16_eabi.lib : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit:__TI_auto_init_nobinit_nopinit)
                  00007080    00000042     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_swiFxn)
                  000070c2    00000002     --HOLE-- [fill = 0]
                  000070c4    00000040                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertCancel)
                  00007104    00000040                      : ADCBuf.oem4f (.text:ADCBuf_open)
                  00007144    00000040     driverlib.lib : osc.obj (.text:NOROM_OSC_HPOSCRelativeFrequencyOffsetGet)
                  00007184    00000040                   : timer.obj (.text:NOROM_TimerIntRegister)
                  000071c4    00000040     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:PowerCC26XX_isStableXOSC_HF)
                  00007204    00000040                      : RingBuf.oem4f (.text:RingBuf_get)
                  00007244    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : exit.c.obj (.text:abort:abort)
                  00007248    00000040     time.obj (.text:Timer2AInterruptHandler)
                  00007288    00000040     drivers_cc13x2.a : UART.oem4f (.text:UART_open)
                  000072c8    00000040     rf_multiMode_cc13x2.aem4f : rf_patch_cpe_prop.c.cc13x2.ccs.o (.text:configurePropPatch)
                  00007308    0000003e     ADC.obj (.text:ADCBufCallback)
                  00007346    00000002     --HOLE-- [fill = 0]
                  00007348    0000003e     RFCommunication.obj (.text:RXcallback)
                  00007386    00000002     --HOLE-- [fill = 0]
                  00007388    0000003c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputValue)
                  000073c4    0000003c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_cmdGet)
                  00007400    0000003c                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_cmdStoreEvents)
                  0000743c    00000038     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_configDMA)
                  00007474    00000038                      : PINCC26XX.oem4f (.text:PIN_setConfig)
                  000074ac    00000038     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_checkCmdFsError)
                  000074e4    00000038                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_powerConstraintRelease)
                  0000751c    00000038     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCurrentTick)
                  00007554    00000038                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_initDevice)
                  0000758c    00000038     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:notify)
                  000075c4    00000038                      : UARTCC26XX.oem4f (.text:readData)
                  000075fc    00000038     rtsv7M4_T_le_v4SPD16_eabi.lib : fs_tod_t2.asm.obj (.text)
                  00007634    00000038     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeData)
                  0000766c    00000004                      : PowerCC26X2.oem4f (.text:configureRFCoreClocks)
                  00007670    00000036     ADC.obj (.text:ADC_ReadSingleValueADCmV)
                  000076a6    00000002     --HOLE-- [fill = 0]
                  000076a8    00000036     time.obj (.text:Time_IsSoftTimerExpired)
                  000076de    00000036     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setNextTick)
                  00007714    00000036     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_close)
                  0000774a    00000002     --HOLE-- [fill = 0]
                  0000774c    00000034     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicksUntilInterrupt)
                  00007780    00000034     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XXSetLoadMatch)
                  000077b4    00000034                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_configureDebugStall)
                  000077e8    00000034     driverlib.lib : aux_sysif.obj (.text:NOROM_AUXSYSIFOpModeChange)
                  0000781c    00000034                   : interrupt.obj (.text:NOROM_IntRegister)
                  00007850    00000034                   : sys_ctrl.obj (.text:NOROM_SysCtrlIdle)
                  00007884    00000034     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_destruct)
                  000078b8    00000034     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:readData2RingBuf)
                  000078ec    00000030                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_close)
                  0000791c    00000030                      : PINCC26XX.oem4f (.text:PINCC26XX_setMux)
                  0000794c    00000030                      : PowerCC26X2.oem4f (.text:Power_releaseConstraint)
                  0000797c    00000030                      : PowerCC26X2.oem4f (.text:Power_setConstraint)
                  000079ac    00000030     driverlib.lib : timer.obj (.text:TimerIntNumberGet)
                  000079dc    00000030     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.text:_c_int00:_c_int00)
                  00007a0c    0000002c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_acquireADCSemaphore)
                  00007a38    0000002c                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_releaseADCSemaphore)
                  00007a64    0000002c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_executeDirectImmediateCmd)
                  00007a90    0000002c     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setThreshold)
                  00007abc    0000002c     rf_multiMode_cc13x2.aem4f : rf_patch_cpe_prop.c.cc13x2.ccs.o (.text:applyPropPatch)
                  00007ae8    0000002a     drivers_cc13x2.a : List.oem4f (.text:List_insert)
                  00007b12    0000002a     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_searchAndReplacePAOverride)
                  00007b3c    00000028                               : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCOverrideSearch)
                  00007b64    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_registerNotify)
                  00007b8c    00000028     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_calculateDeltaTimeUs)
                  00007bb4    00000028                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_runCmd)
                  00007bdc    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:oscillatorISR)
                  00007c04    00000026                      : List.oem4f (.text:List_put)
                  00007c2a    00000026                      : List.oem4f (.text:List_putHead)
                  00007c50    00000026                      : List.oem4f (.text:List_remove)
                  00007c76    00000002     --HOLE-- [fill = 0]
                  00007c78    00000024     BatteryVoltage.obj (.text:BatteryVoltage_GetStoredValue)
                  00007c9c    00000024     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicks)
                  00007cc0    00000024                     : ClockPTimer_nortos.oem4f (.text:ClockP_scheduleNextTick)
                  00007ce4    00000024                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_dispatch)
                  00007d08    00000024     driverlib.lib : osc.obj (.text:NOROM_OSCHF_TurnOnXosc)
                  00007d2c    00000024     rf_multiMode_cc13x2.aem4f : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCDoorbellSendTo)
                  00007d50    00000024                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_cmdAlloc)
                  00007d74    00000024                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_invokeGlobalCallback)
                  00007d98    00000024     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getMaxTicks)
                  00007dbc    00000024     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:getTdcSemaphore)
                  00007de0    00000022     ti_drivers_config.obj (.text:Board_init)
                  00007e02    00000022     drivers_cc13x2.a : List.oem4f (.text:List_get)
                  00007e24    00000020     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_add)
                  00007e44    00000020     driverlib.lib : aux_adc.obj (.text:NOROM_AUXADCEnableSync)
                  00007e64    00000020     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfgMux)
                  00007e84    00000020                      : PowerCC26X2.oem4f (.text:Power_unregisterNotify)
                  00007ea4    00000020     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_put)
                  00007ec4    00000020     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_clkPowerUp)
                  00007ee4    00000020                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_hwiCpe0ChangePhy)
                  00007f04    00000020                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_isStateTransitionAllowed)
                  00007f24    00000020                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_ratIsRunning)
                  00007f44    00000020     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_dynamicStub)
                  00007f64    00000020     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:threadSafeStdbyDisRelease)
                  00007f84    00000020                      : UARTCC26XX.oem4f (.text:threadSafeStdbyDisSet)
                  00007fa4    00000004                      : PowerCC26X2.oem4f (.text:nopResourceHandler)
                  00007fa8    0000001e     BatteryVoltage.obj (.text:BatteryVoltage_Store)
                  00007fc6    0000001e     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_close)
                  00007fe4    0000001e                      : PowerCC26X2.oem4f (.text:Power_getTransitionLatency)
                  00008002    0000001e     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_constructBinary)
                  00008020    0000001e                     : SwiP_nortos.oem4f (.text:SwiP_or)
                  0000803e    00000002     --HOLE-- [fill = 0]
                  00008040    0000001c                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_destruct)
                  0000805c    0000001c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipFamily)
                  00008078    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_hwi)
                  00008094    0000001c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_clkInactivityCallback)
                  000080b0    0000001c                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_getPAOverrideOffsetAndValue)
                  000080cc    0000001c                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_powerConstraintSet)
                  000080e8    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_disable)
                  00008104    0000001c     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_initHw)
                  00008120    0000001a     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_setFunc)
                  0000813a    0000001a     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:PowerCC26XX_calibrate)
                  00008154    0000001a     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_get)
                  0000816e    00000018                     : ClockPTimer_nortos.oem4f (.text:ClockP_destruct)
                  00008186    00000018                     : ClockPTimer_nortos.oem4f (.text:ClockP_getTimeout)
                  0000819e    00000002     --HOLE-- [fill = 0]
                  000081a0    00000018                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_inISR)
                  000081b8    00000018     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetPackageType)
                  000081d0    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_idleFunc)
                  000081e8    00000018     RFCommunication.obj (.text:RFCommunication_GetBatteryFromReceivedMeasurementPacket)
                  00008200    00000018     RFCommunication.obj (.text:RFCommunication_GetDistanceFromReceivedMeasurementPacket)
                  00008218    00000018     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_swiFsm)
                  00008230    00000018     time.obj (.text:Time_EnableMatch)
                  00008248    00000018     time.obj (.text:Time_StartSoftTimer)
                  00008260    00000018     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_startup)
                  00008278    00000018     drivers_cc13x2.a : UART.oem4f (.text:UART_Params_init)
                  00008290    00000018     rtsv7M4_T_le_v4SPD16_eabi.lib : args_main.c.obj (.text:_args_main)
                  000082a8    00000018                                   : ll_mul_t2.asm.obj (.text)
                  000082c0    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:switchToTCXO)
                  000082d8    00000016     driverlib.lib : chipinfo.obj (.text:NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated)
                  000082ee    00000002     --HOLE-- [fill = 0]
                  000082f0    00000016     RFQueue.obj (.text:RFQueue_nextEntry)
                  00008306    00000016     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_restartClockTimeout)
                  0000831c    00000004     --HOLE-- [fill = 0]
                  00008320    00000016     SoundReceive.obj (.text:SoundReceive_SampleSound)
                  00008336    00000016     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:maxbit)
                  0000834c    00000014     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_freqToCounts)
                  00008360    00000014                      : ADCBuf.oem4f (.text:ADCBuf_Params_init)
                  00008374    00000014                      : List.oem4f (.text:List_clearList)
                  00008388    00000014     driverlib.lib : interrupt.obj (.text:NOROM_IntUnregister)
                  0000839c    00000014     rf_multiMode_cc13x2.aem4f : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCCpeIntGetAndClear)
                  000083b0    00000014     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_calibrate)
                  000083c4    00000014                      : PowerCC26X2.oem4f (.text:Power_disablePolicy)
                  000083d8    00000014     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_Params_init)
                  000083ec    00000014                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_dbellSyncOnAck)
                  00008400    00000014                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_flushCmd)
                  00008414    00000014                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_ratGetChannel)
                  00008428    00000014     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_handleHwi)
                  0000843c    00000014                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCount64)
                  00008450    00000014                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:resetISR)
                  00008464    00000012     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_Params_init)
                  00008476    00000012     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_Params_init)
                  00008488    00000012     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_construct)
                  0000849a    00000006                      : PowerCC26X2.oem4f (.text:delayUs)
                  000084a0    00000012     time.obj (.text:Time_DisableMatch)
                  000084b2    00000006     --HOLE-- [fill = 0]
                  000084b8    00000010     BatteryVoltage.obj (.text:BatteryVoltage_GetMyVoltage)
                  000084c8    00000010     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_Params_init)
                  000084d8    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_getCpuFreq)
                  000084e8    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_clearInterrupt)
                  000084f8    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_disable)
                  00008508    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_enableInterrupt)
                  00008518    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_post)
                  00008528    00000010     rf_multiMode_cc13x2.aem4f : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCHwIntGetAndClear)
                  00008538    00000010     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_getPinCount)
                  00008548    00000010     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerDisable)
                  00008558    00000010     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_enablePolicy)
                  00008568    00000010     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_dbellSubmitCmdAsync)
                  00008578    00000010                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_isClientOwner)
                  00008588    00000010     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_Params_init)
                  00008598    00000010                     : SwiP_nortos.oem4f (.text:SwiP_Params_init)
                  000085a8    00000010     time.obj (.text:Time_SetNextMatch)
                  000085b8    00000010     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_Params_init)
                  000085c8    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:uartPostNotify)
                  000085d8    0000000e     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_empty)
                  000085e6    0000000e                     : QueueP_nortos.oem4f (.text:QueueP_remove)
                  000085f4    0000000e     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_getSwitchingTimeInUs)
                  00008602    0000000e     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_none.c.obj (.text:decompress:none:__TI_decompress_none)
                  00008610    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_doTick)
                  0000861c    0000000c                     : ClockPTimer_nortos.oem4f (.text:ClockP_getSystemTickPeriod)
                  00008628    0000000c     driverlib.lib : cpu.obj (.text:NOROM_CPUcpsid)
                  00008634    0000000c                   : cpu.obj (.text:NOROM_CPUcpsie)
                  00008640    0000000c     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerRestore)
                  0000864c    0000000c     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_getConstraintMask)
                  00008658    0000000c                      : PowerCC26X2.oem4f (.text:Power_getDependencyCount)
                  00008664    00000004     --HOLE-- [fill = 0]
                  00008668    0000000c     RFCommunication.obj (.text:RFCommunication_GetSectionReceivedArraySectionPacket)
                  00008674    00000004     --HOLE-- [fill = 0]
                  00008678    0000000c     RFQueue.obj (.text:RFQueue_getDataEntry)
                  00008684    0000000c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_powerConstraintGet)
                  00008690    0000000c     SoundTransmit.obj (.text:SoundTransmit_Init)
                  0000869c    0000000c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_getTrigger)
                  000086a8    0000000c     time.obj (.text:Time_SetCallbackFunctionOnMatch)
                  000086b4    0000000c     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getFreq)
                  000086c0    0000000c     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init)
                  000086cc    0000000a     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_close)
                  000086d6    0000000a                      : ADCBuf.oem4f (.text:ADCBuf_convert)
                  000086e0    0000000a                      : ADCBuf.oem4f (.text:ADCBuf_convertCancel)
                  000086ea    00000006     --HOLE-- [fill = 0]
                  000086f0    0000000a     SoundReceive.obj (.text:SoundReceive_GetSoundSampleArray)
                  000086fa    0000000a     drivers_cc13x2.a : UART.oem4f (.text:UART_read)
                  00008704    0000000a                      : UART.oem4f (.text:UART_write)
                  0000870e    0000000a                      : UDMACC26XX.oem4f (.text:UDMACC26XX_hwiIntFxn)
                  00008718    0000000a                      : UARTCC26XX.oem4f (.text:readSemCallback)
                  00008722    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_conversionCallback)
                  0000872a    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_init)
                  00008732    00000008                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_setLoadValue)
                  0000873a    00000008     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_restore)
                  00008742    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_init)
                  0000874a    00000002     --HOLE-- [fill = 0]
                  0000874c    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : errno.c.obj (.text:__aeabi_errno_addr)
                  00008754    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeSemCallback)
                  0000875c    00000004     --HOLE-- [fill = 0]
                  00008760    00000002     ti_drivers_config.obj (.text:Board_initHook)

.const     0    00008764    00000460     
                  00008764    000001f0     rf_multiMode_cc13x2.aem4f : rf_patch_cpe_prop.c.cc13x2.ccs.o (.const:patchImageProp)
                  00008954    00000054     drivers_cc13x2.a : PowerCC26X2.oem4f (.const:resourceDB)
                  000089a8    00000034     ti_drivers_config.obj (.const:$O1$$)
                  000089dc    00000028     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:UARTCC26XX_fxnTable)
                  00008a04    00000028     ti_drivers_config.obj (.const:uartCC26XXHWAttrs)
                  00008a2c    00000024     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.const:ADCBufCC26X2_fxnTable)
                  00008a50    00000024                      : UART.oem4f (.const:UART_defaultParams)
                  00008a74    00000020     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.const:RF_defaultParams)
                  00008a94    0000001c     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.const:GPT_LUT)
                  00008ab0    00000018                      : ADCBuf.oem4f (.const:ADCBuf_defaultParams)
                  00008ac8    00000014     ADC.obj (.const:$P$T0$1)
                  00008adc    00000014     ti_drivers_config.obj (.const:PowerCC26X2_config)
                  00008af0    00000014     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:parityType)
                  00008b04    00000010     nortos_cc13x2.a : SwiP_nortos.oem4f (.const:SwiP_defaultParams)
                  00008b14    00000010     ti_drivers_config.obj (.const:adcbufCC26XXHWAttrs)
                  00008b24    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:dataLength)
                  00008b34    0000000c     ti_drivers_config.obj (.const:ADCBuf_config)
                  00008b40    0000000c     ti_drivers_config.obj (.const:GPTimerCC26XX_config)
                  00008b4c    0000000c     ti_drivers_config.obj (.const:RFCC26XX_hwAttrs)
                  00008b58    0000000c     ti_drivers_config.obj (.const:UART_config)
                  00008b64    0000000c     ti_drivers_config.obj (.const:gptimerCC26XXHWAttrs)
                  00008b70    00000009     ti_drivers_config.obj (.const)
                  00008b79    00000003     --HOLE-- [fill = 0]
                  00008b7c    00000008     driverlib.lib : aux_sysif.obj (.const:$O1$$)
                  00008b84    00000008     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.const:GPT_DefaultParams)
                  00008b8c    00000008     ti_drivers_config.obj (.const:PINCC26XX_hwAttrs)
                  00008b94    00000008     ti_drivers_config.obj (.const:UDMACC26XX_config)
                  00008b9c    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:stopBits)
                  00008ba4    00000008     ti_drivers_config.obj (.const:udmaCC26XXHWAttrs)
                  00008bac    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:.string:rxFifoBytes)
                  00008bb2    00000006                      : UARTCC26XX.oem4f (.const:.string:rxFifoThreshold)
                  00008bb8    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoBytes)
                  00008bbe    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoThreshold)

.binit     0    00000000    00000000     

.cinit     0    00008bd0    00000168     
                  00008bd0    0000010f     (.cinit..data.load) [load image, compression = lzss]
                  00008cdf    00000001     --HOLE-- [fill = 0]
                  00008ce0    0000000c     (__TI_handler_table)
                  00008cec    00000004     --HOLE-- [fill = 0]
                  00008cf0    00000008     (.cinit..TI.bound:dmaADCAltControlTableEntry.load) [load image, compression = zero_init]
                  00008cf8    00000008     (.cinit..TI.bound:dmaADCPriControlTableEntry.load) [load image, compression = zero_init]
                  00008d00    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00008d08    00000008     (.cinit..vtable_ram.load) [load image, compression = zero_init]
                  00008d10    00000028     (__TI_cinit_table)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.ccfg      0    00057fa8    00000058     
                  00057fa8    00000058     ti_devices_config.obj (.ccfg:retain)

.vtable_ram 
*          0    20000e00    000000d8     UNINITIALIZED
                  20000e00    000000d8     driverlib.lib : interrupt.obj (.vtable_ram)

.data      0    20000000    00000d4d     UNINITIALIZED
                  20000000    000004f8     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.data:$O1$$)
                  200004f8    00000260     RFCommunication.obj (.data:$O1$$)
                  20000758    00000170     drivers_cc13x2.a : PowerCC26X2.oem4f (.data:PowerCC26X2_module)
                  200008c8    00000128     SoundTransmit.obj (.data:$O1$$)
                  200009f0    00000108     SoundReceive.obj (.data:Signature)
                  20000af8    000000d8     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data:HwiP_dispatchTable)
                  20000bd0    00000028     ti_radio_config.obj (.data:RF_cmdPropRadioDivSetup)
                  20000bf8    00000028     ti_radio_config.obj (.data:pOverrides)
                  20000c20    00000024     driverlib.lib : osc.obj (.data:$O1$$)
                  20000c44    00000024     ti_radio_config.obj (.data:RF_cmdPropRx)
                  20000c68    00000018     Serial.obj (.data:$O1$$)
                  20000c80    00000018     time.obj (.data:$O1$$)
                  20000c98    00000018     ti_radio_config.obj (.data:RF_cmdFs)
                  20000cb0    00000018     ti_radio_config.obj (.data:RF_cmdPropTx)
                  20000cc8    00000010     ControlMobile.obj (.data:$O1$$)
                  20000cd8    00000010     ti_radio_config.obj (.data:RF_prop)
                  20000ce8    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data:ClockP_defaultParams)
                  20000cf4    0000000c                     : TimerPCC26XX_nortos.oem4f (.data:TimerP_defaultParams)
                  20000d00    00000008     ADC.obj (.data:$O1$$)
                  20000d08    00000008     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.data:RFCC26XX_schedulerPolicy)
                  20000d10    00000008     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data:SemaphoreP_defaultParams)
                  20000d18    00000008     drivers_cc13x2.a : PINCC26XX.oem4f (.data)
                  20000d20    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data)
                  20000d28    00000006     ti_drivers_config.obj (.data:adcBufCC26XXChannelLut0)
                  20000d2e    00000002     ControlX0.obj (.data)
                  20000d30    00000004     BatteryVoltage.obj (.data:$O1$$)
                  20000d34    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data)
                  20000d38    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : errno.c.obj (.data)
                  20000d3c    00000004     time.obj (.data)
                  20000d40    00000003     Serial.obj (.data)
                  20000d43    00000003     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data)
                  20000d46    00000002     SoundTransmit.obj (.data)
                  20000d48    00000001     drivers_cc13x2.a : ADCBuf.oem4f (.data)
                  20000d49    00000001                      : UART.oem4f (.data)
                  20000d4a    00000001     nortos_cc13x2.a : SwiP_nortos.oem4f (.data)
                  20000d4b    00000001                     : TimerPCC26XX_nortos.oem4f (.data)
                  20000d4c    00000001     rf_multiMode_cc13x2.aem4f : rf_patch_cpe_prop.c.cc13x2.ccs.o (.data)

.sysmem    0    20001a80    00004000     UNINITIALIZED
                  20001a80    00000010     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.sysmem)
                  20001a90    00003ff0     --HOLE--

.bss       0    20005a80    0000376a     UNINITIALIZED
                  20005a80    00002f00     SoundReceive.obj (.bss:SoundSamples)
                  20008980    000001b4     (.common:uartCC26XXObjects)
                  20008b34    00000148     RFCommunication.obj (.bss:rxDataEntryBuffer)
                  20008c7c    000000dc     (.common:adcbufCC26XXbjects)
                  20008d58    000000c8     (.common:InternalADCBuffer1)
                  20008e20    000000c8     (.common:InternalADCBuffer2)
                  20008ee8    000000a0     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:ClockP_module)
                  20008f88    0000007c     (.common:pinHandleTable)
                  20009004    00000050     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinSemaphore)
                  20009054    0000004c     (.common:gptimerCC26XXObjects)
                  200090a0    00000034                      : PINCC26XX.oem4f (.bss:pinSwi)
                  200090d4    00000020     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_readyList)
                  200090f4    00000020     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinGpioConfigTable)
                  20009114    00000020     ti_drivers_config.obj (.bss:uartCC26XXRingBuffer0)
                  20009134    00000020     (.common:udmaCC26XXObject)
                  20009154    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_hwiStruct)
                  20009170    0000001c                     : TimerPCC26XX_nortos.oem4f (.bss:TimerP_hwiStruct)
                  2000918c    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinHwi)
                  200091a8    00000014     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.bss:RF_ratSyncCmd)
                  200091bc    0000000d     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss)
                  200091c9    00000001     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.bss)
                  200091ca    00000002     (.common:DestinationArrayIndex)
                  200091cc    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:timerFreq)
                  200091d4    00000004     drivers_cc13x2.a : PINCC26XX.oem4f (.bss)
                  200091d8    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss)
                  200091dc    00000004                     : PowerCC26X2_nortos.oem4f (.bss)
                  200091e0    00000004                     : TimerPCC26XX_nortos.oem4f (.bss)
                  200091e4    00000004     (.common:readEntry)
                  200091e8    00000001     (.common:DeviceType)
                  200091e9    00000001     (.common:driverlib_release_0_59848)

.stack     0    20013c00    00000400     UNINITIALIZED
                  20013c00    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.stack)
                  20013c04    000003fc     --HOLE--

.log_data 
*          0    90000000    00000000     COPY SECTION

__llvm_prf_cnts 
*          0    20000000    00000000     UNINITIALIZED

.TI.bound:dmaADCPriControlTableEntry 
*          0    20001870    00000010     UNINITIALIZED
                  20001870    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCPriControlTableEntry)

.TI.bound:dmaADCAltControlTableEntry 
*          0    20001a70    00000010     UNINITIALIZED
                  20001a70    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCAltControlTableEntry)

.args      0    00008bc4    00000008     
                  00008bc4    00000008     --HOLE-- [fill = 0]

MODULE SUMMARY

       Module                              code    ro data   rw data
       ------                              ----    -------   -------
    .\
       SoundReceive.obj                    164     0         12296  
       RFCommunication.obj                 1726    0         936    
       ControlMobile.obj                   2338    0         16     
       Serial.obj                          800     0         27     
       ADC.obj                             338     20        410    
       SoundTransmit.obj                   260     0         298    
       ControlX0.obj                       492     0         2      
       time.obj                            394     0         28     
       RFQueue.obj                         266     0         4      
       Temperature.obj                     164     0         0      
       main.obj                            120     0         0      
       DeviceType.obj                      94      0         1      
       BatteryVoltage.obj                  82      0         4      
       gpio.obj                            78      0         0      
    +--+-----------------------------------+-------+---------+---------+
       Total:                              7316    20        14022  
                                                                    
    .\syscfg\
       ti_drivers_config.obj               256     221       802    
       ti_radio_config.obj                 0       0         180    
       ti_devices_config.obj               0       88        0      
    +--+-----------------------------------+-------+---------+---------+
       Total:                              256     309       982    
                                                                    
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos/lib/ccs/m4f/nortos_cc13x2.a
       ClockPTimer_nortos.oem4f            946     0         192    
       SwiP_nortos.oem4f                   724     16        74     
       TimerPCC26XX_nortos.oem4f           642     0         45     
       HwiPCC26XX_nortos.oem4f             336     0         220    
       SemaphoreP_nortos.oem4f             358     0         11     
       PowerCC26X2_nortos.oem4f            256     0         4      
       startup_cc13x2_cc26x2_ccs.oem4f     26      216       0      
       QueueP_nortos.oem4f                 100     0         0      
    +--+-----------------------------------+-------+---------+---------+
       Total:                              3388    232       546    
                                                                    
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/cc13x2_cc26x2/driverlib/bin/ccs/driverlib.lib
       sys_ctrl.obj                        420     0         0      
       osc.obj                             340     0         36     
       setup.obj                           324     0         0      
       interrupt.obj                       74      0         216    
       chipinfo.obj                        250     0         0      
       prcm.obj                            160     0         0      
       timer.obj                           112     0         0      
       aux_sysif.obj                       52      8         0      
       aux_adc.obj                         32      0         0      
       cpu.obj                             30      0         0      
       driverlib_release.obj               0       0         1      
    +--+-----------------------------------+-------+---------+---------+
       Total:                              1794    8         253    
                                                                    
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/lib/ccs/m4f/drivers_cc13x2.a
       UARTCC26XX.oem4f                    2760    108       0      
       PowerCC26X2.oem4f                   2232    84        368    
       PINCC26XX.oem4f                     1430    0         328    
       ADCBufCC26X2.oem4f                  1542    36        32     
       PowerCC26X2_calibrateRCOSC.oem4f    1400    0         0      
       GPTimerCC26XX.oem4f                 670     36        0      
       UART.oem4f                          176     36        1      
       List.oem4f                          210     0         0      
       ADCBuf.oem4f                        182     24        1      
       UDMACC26XX.oem4f                    168     0         0      
       RingBuf.oem4f                       152     0         0      
    +--+-----------------------------------+-------+---------+---------+
       Total:                              10922   324       730    
                                                                    
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/rf/lib/rf_multiMode_cc13x2.aem4f
       RFCC26X2_multiMode.c.cc13x2.ccs.o   8036    32        1301   
       rf_patch_cpe_prop.c.cc13x2.ccs.o    108     496       1      
       rfc.c.cc13x2.ccs.o                  302     0         0      
    +--+-----------------------------------+-------+---------+---------+
       Total:                              8446    528       1302   
                                                                    
    C:\ti\ccs1011\ccs\tools\compiler\ti-cgt-arm_20.2.4.LTS\lib\rtsv7M4_T_le_v4SPD16_eabi.lib
       fd_add_t2.asm.obj                   438     0         0      
       e_sqrt.c.obj                        344     0         0      
       fd_div_t2.asm.obj                   310     0         0      
       fd_mul_t2.asm.obj                   252     0         0      
       memcpy_t2.asm.obj                   156     0         0      
       ull_div_t2.asm.obj                  150     0         0      
       memset_t2.asm.obj                   122     0         0      
       fd_tos_t2.asm.obj                   110     0         0      
       copy_decompress_lzss.c.obj          104     0         0      
       autoinit.c.obj                      68      0         0      
       fs_tod_t2.asm.obj                   56      0         0      
       boot_cortex_m.c.obj                 48      0         0      
       args_main.c.obj                     24      0         0      
       ll_mul_t2.asm.obj                   24      0         0      
       copy_decompress_none.c.obj          14      0         0      
       copy_zero_init.c.obj                12      0         0      
       errno.c.obj                         8       0         4      
       exit.c.obj                          4       0         0      
       pre_init.c.obj                      4       0         0      
       div0.asm.obj                        2       0         0      
    +--+-----------------------------------+-------+---------+---------+
       Total:                              2250    0         4      
                                                                    
       Heap:                               0       0         16384  
       Stack:                              0       0         1024   
       Linker Generated:                   0       355       0      
    +--+-----------------------------------+-------+---------+---------+
       Grand Total:                        34372   1776      35247  


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00008d10 records: 5, size/record: 8, table size: 40
	.data: load addr=00008bd0, load size=0000010f bytes, run addr=20000000, run size=00000d4d bytes, compression=lzss
	.TI.bound:dmaADCAltControlTableEntry: load addr=00008cf0, load size=00000008 bytes, run addr=20001a70, run size=00000010 bytes, compression=zero_init
	.TI.bound:dmaADCPriControlTableEntry: load addr=00008cf8, load size=00000008 bytes, run addr=20001870, run size=00000010 bytes, compression=zero_init
	.bss: load addr=00008d00, load size=00000008 bytes, run addr=20005a80, run size=0000376a bytes, compression=zero_init
	.vtable_ram: load addr=00008d08, load size=00000008 bytes, run addr=20000e00, run size=000000d8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00008ce0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_lzss
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                                               
-------   ----                                                               
00008b73  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00008b70  ADCBUF_CONST                                                       
00008b71  ADCBUF_SOUND_CONST                                                 
00008b72  ADCBUF_TEMPERATURE_CONST                                           
000066df  ADCBufCC26X2_adjustRawValues                                       
0000682d  ADCBufCC26X2_close                                                 
00006b31  ADCBufCC26X2_control                                               
00002d65  ADCBufCC26X2_convert                                               
00006c1d  ADCBufCC26X2_convertAdjustedToMicroVolts                           
000070c5  ADCBufCC26X2_convertCancel                                         
00008a2c  ADCBufCC26X2_fxnTable                                              
00002c5d  ADCBufCC26X2_getResolution                                         
0000872b  ADCBufCC26X2_init                                                  
00001ad1  ADCBufCC26X2_open                                                  
00007309  ADCBufCallback                                                     
00008361  ADCBuf_Params_init                                                 
000086cd  ADCBuf_close                                                       
00008b34  ADCBuf_config                                                      
000086d7  ADCBuf_convert                                                     
000086e1  ADCBuf_convertCancel                                               
00008b74  ADCBuf_count                                                       
00008ab0  ADCBuf_defaultParams                                               
00006ea5  ADCBuf_init                                                        
00007105  ADCBuf_open                                                        
00006ea5  ADC_Init                                                           
00003871  ADC_ReadArrayADCraw                                                
00007671  ADC_ReadSingleValueADCmV                                           
200008c8  ArraySize                                                          
20000d30  Battery0                                                           
000084b9  BatteryVoltage_GetMyVoltage                                        
00007c79  BatteryVoltage_GetStoredValue                                      
00007fa9  BatteryVoltage_Store                                               
20000d32  BatteryX                                                           
20000950  BeepDelay                                                          
200009a0  BeepState                                                          
000089c4  BoardGpioInitTable                                                 
00007de1  Board_init                                                         
00008761  Board_initHook                                                     
00004ef1  Board_shutDownExtFlash                                             
00006b81  Board_wakeUpExtFlash                                               
00007245  C$$EXIT                                                            
00008b77  CONFIG_GPTIMER_0_CONST                                             
00000c11  CalculatePosition                                                  
20000c80  CallBackMatch                                                      
200008d4  ChirpDelay                                                         
20000d46  ChirpIndex                                                         
20000912  ChirpState                                                         
000084c9  ClockP_Params_init                                                 
00007e25  ClockP_add                                                         
00006c65  ClockP_construct                                                   
0000816f  ClockP_destruct                                                    
00008611  ClockP_doTick                                                      
000084d9  ClockP_getCpuFreq                                                  
0000861d  ClockP_getSystemTickPeriod                                         
00007c9d  ClockP_getTicks                                                    
0000774d  ClockP_getTicksUntilInterrupt                                      
00008187  ClockP_getTimeout                                                  
00003d75  ClockP_isActive                                                    
00007cc1  ClockP_scheduleNextTick                                            
00004415  ClockP_setTimeout                                                  
000056f9  ClockP_start                                                       
00004361  ClockP_startup                                                     
00000c0b  ClockP_stop                                                        
20000d24  ClockP_tickPeriod                                                  
000059f1  ClockP_walkQueueDynamic                                            
00004579  ClockP_workFuncDynamic                                             
000008f9  ControlMobile_Run                                                  
00003141  ControlX0_Run0                                                     
00002c61  ControlX0_RunX                                                     
200008cc  DelayArray                                                         
200091ca  DestinationArrayIndex                                              
20000d00  DestinationArraySize                                               
200091e8  DeviceType                                                         
000063b1  DeviceType_ReadFromBoard                                           
00008465  GPTimerCC26XX_Params_init                                          
000078ed  GPTimerCC26XX_close                                                
00008b40  GPTimerCC26XX_config                                               
000077b5  GPTimerCC26XX_configureDebugStall                                  
00005139  GPTimerCC26XX_open                                                 
00008733  GPTimerCC26XX_setLoadValue                                         
00006969  GPTimerCC26XX_start                                                
0000657d  GPTimerCC26XX_stop                                                 
00008b78  GPTimer_count                                                      
00006881  Gpio_Init                                                          
00008477  HwiP_Params_init                                                   
000084e9  HwiP_clearInterrupt                                                
00005779  HwiP_construct                                                     
00008041  HwiP_destruct                                                      
000084f9  HwiP_disable                                                       
0000477d  HwiP_enable                                                        
00008509  HwiP_enableInterrupt                                               
000081a1  HwiP_inISR                                                         
00008519  HwiP_post                                                          
0000873b  HwiP_restore                                                       
00008121  HwiP_setFunc                                                       
20000d34  HwiP_swiPIntNum                                                    
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
20008d58  InternalADCBuffer1                                                 
20008e20  InternalADCBuffer2                                                 
00008375  List_clearList                                                     
00007e03  List_get                                                           
00007ae9  List_insert                                                        
00007c05  List_put                                                           
00007c2b  List_putHead                                                       
00007c51  List_remove                                                        
00000589  Mobile_execute_MeasureAll                                          
00007e45  NOROM_AUXADCEnableSync                                             
000077e9  NOROM_AUXSYSIFOpModeChange                                         
00008629  NOROM_CPUcpsid                                                     
00008635  NOROM_CPUcpsie                                                     
0000322b  NOROM_CPUdelay                                                     
0000805d  NOROM_ChipInfo_GetChipFamily                                       
00005ed9  NOROM_ChipInfo_GetChipType                                         
00006ee9  NOROM_ChipInfo_GetHwRevision                                       
000081b9  NOROM_ChipInfo_GetPackageType                                      
0000781d  NOROM_IntRegister                                                  
00008389  NOROM_IntUnregister                                                
000051c1  NOROM_OSCHF_AttemptToSwitchToXosc                                  
0000601d  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
00007d09  NOROM_OSCHF_TurnOnXosc                                             
00007145  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00006735  NOROM_PRCMPowerDomainsAllOff                                       
000069b5  NOROM_PRCMPowerDomainsAllOn                                        
00005cad  NOROM_RFCAnaDivTxOverride                                          
0000839d  NOROM_RFCCpeIntGetAndClear                                         
0000259f  NOROM_RFCCpePatchReset                                             
00007d2d  NOROM_RFCDoorbellSendTo                                            
00008529  NOROM_RFCHwIntGetAndClear                                          
00007b3d  NOROM_RFCOverrideSearch                                            
000048cd  NOROM_RFCOverrideUpdate                                            
00006cad  NOROM_RFCSynthPowerDown                                            
00004c91  NOROM_SetupTrimDevice                                              
00007851  NOROM_SysCtrlIdle                                                  
000057f9  NOROM_SysCtrlSetRechargeBeforePowerDown                            
00002f61  NOROM_SysCtrlStandby                                               
000082d9  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00007185  NOROM_TimerIntRegister                                             
0000477d  NoRTOS_start                                                       
00008539  PINCC26XX_getPinCount                                              
00008b8c  PINCC26XX_hwAttrs                                                  
0000791d  PINCC26XX_setMux                                                   
00007fc7  PIN_close                                                          
000010b1  PIN_init                                                           
00004625  PIN_open                                                           
00006411  PIN_remove                                                         
00007475  PIN_setConfig                                                      
00006cf5  PIN_setOutputEnable                                                
00007389  PIN_setOutputValue                                                 
00006d3d  PowerCC26X2_RCOSC_clockFunc                                        
00005b55  PowerCC26X2_auxISR                                                 
000083b1  PowerCC26X2_calibrate                                              
00008adc  PowerCC26X2_config                                                 
000065d5  PowerCC26X2_initiateCalibration                                    
20000758  PowerCC26X2_module                                                 
0000813b  PowerCC26XX_calibrate                                              
000071c5  PowerCC26XX_isStableXOSC_HF                                        
00008549  PowerCC26XX_schedulerDisable                                       
00008641  PowerCC26XX_schedulerRestore                                       
000036b1  PowerCC26XX_standbyPolicy                                          
00006d85  PowerCC26XX_switchXOSC_HF                                          
000083c5  Power_disablePolicy                                                
00008559  Power_enablePolicy                                                 
0000864d  Power_getConstraintMask                                            
00008659  Power_getDependencyCount                                           
00007fe5  Power_getTransitionLatency                                         
000081d1  Power_idleFunc                                                     
00001c45  Power_init                                                         
00007b65  Power_registerNotify                                               
0000794d  Power_releaseConstraint                                            
00003e41  Power_releaseDependency                                            
0000797d  Power_setConstraint                                                
00004139  Power_setDependency                                                
0000161d  Power_sleep                                                        
00007e85  Power_unregisterNotify                                             
000085d9  QueueP_empty                                                       
00008155  QueueP_get                                                         
00004975  QueueP_head                                                        
00006683  QueueP_init                                                        
000050a5  QueueP_next                                                        
00007ea5  QueueP_put                                                         
000085e7  QueueP_remove                                                      
00008b4c  RFCC26XX_hwAttrs                                                   
20000d08  RFCC26XX_schedulerPolicy                                           
00004ab9  RFCommunication_CheckReceivedPacket                                
000081e9  RFCommunication_GetBatteryFromReceivedMeasurementPacket            
00006a01  RFCommunication_GetDataReceivedArraySectionPacket                  
00008201  RFCommunication_GetDistanceFromReceivedMeasurementPacket           
00008669  RFCommunication_GetSectionReceivedArraySectionPacket               
00003ca9  RFCommunication_TXArraySection                                     
00005bc9  RFCommunication_TXAskXToMeasureXto0                                
00003d79  RFCommunication_TXListenToSound                                    
00004979  RFCommunication_TXMeasurements                                     
00003231  RFCommunication_TXReadSamples                                      
00003319  RFCommunication_TXReturnResults                                    
000034e9  RFCommunication_init                                               
00003401  RFQueue_defineQueue                                                
00008679  RFQueue_getDataEntry                                               
000082f1  RFQueue_nextEntry                                                  
000083d9  RF_Params_init                                                     
20000c98  RF_cmdFs                                                           
20000bd0  RF_cmdPropRadioDivSetup                                            
20000c44  RF_cmdPropRx                                                       
20000cb0  RF_cmdPropTx                                                       
000058f5  RF_defaultExecutionPolicy                                          
0000662d  RF_defaultSubmitPolicy                                             
00008401  RF_flushCmd                                                        
00005d1d  RF_getCurrentTime                                                  
00000eb1  RF_open                                                            
00003951  RF_pendCmd                                                         
000035cd  RF_postCmd                                                         
00008685  RF_powerConstraintGet                                              
000074e5  RF_powerConstraintRelease                                          
000080cd  RF_powerConstraintSet                                              
20000cd8  RF_prop                                                            
00007bb5  RF_runCmd                                                          
200004f8  RXpacketLength                                                     
00008489  RingBuf_construct                                                  
00007205  RingBuf_get                                                        
00006e5d  RingBuf_put                                                        
20000d40  RxIndex                                                            
00008589  SemaphoreP_Params_init                                             
000067dd  SemaphoreP_construct                                               
00008003  SemaphoreP_constructBinary                                         
20000d10  SemaphoreP_defaultParams                                           
00002b57  SemaphoreP_destruct                                                
00004a19  SemaphoreP_pend                                                    
00006f71  SemaphoreP_post                                                    
20000d42  SerialLineReceived                                                 
00004781  Serial_CheckCommand                                                
00004419  Serial_CopyArraySectionToSerial                                    
000058f9  Serial_CopyReceivedArraySectionToSerial                            
00006689  Serial_Init                                                        
000044c9  Serial_PrintMeasurements                                           
200009f0  Signature                                                          
00005571  SoundReceive_CalculateDistance                                     
000086f1  SoundReceive_GetSoundSampleArray                                   
00008321  SoundReceive_SampleSound                                           
00005fb1  SoundTransmit_GenerateSound                                        
00008691  SoundTransmit_Init                                                 
200008d0  StateArray                                                         
00008599  SwiP_Params_init                                                   
00003a29  SwiP_construct                                                     
00007885  SwiP_destruct                                                      
000080e9  SwiP_disable                                                       
00004b59  SwiP_dispatch                                                      
0000869d  SwiP_getTrigger                                                    
00008021  SwiP_or                                                            
000060ed  SwiP_post                                                          
00006fb5  SwiP_restore                                                       
000048d1  Temperature_GetValueDeciDegrees                                    
000084a1  Time_DisableMatch                                                  
00008231  Time_EnableMatch                                                   
000042a9  Time_Init                                                          
000076a9  Time_IsSoftTimerExpired                                            
000086a9  Time_SetCallbackFunctionOnMatch                                    
000085a9  Time_SetNextMatch                                                  
00008249  Time_StartSoftTimer                                                
00007249  Timer2AInterruptHandler                                            
000050a9  TimerMatchCallback                                                 
20000d3c  TimerOverflows                                                     
000085b9  TimerP_Params_init                                                 
0000407d  TimerP_construct                                                   
00007f45  TimerP_dynamicStub                                                 
0000843d  TimerP_getCount64                                                  
0000751d  TimerP_getCurrentTick                                              
000086b5  TimerP_getFreq                                                     
00007d99  TimerP_getMaxTicks                                                 
00007555  TimerP_initDevice                                                  
000076df  TimerP_setNextTick                                                 
00007a91  TimerP_setThreshold                                                
00006155  TimerP_start                                                       
00008261  TimerP_startup                                                     
20000c84  TimerStartTime                                                     
000055f5  UARTCC26XX_close                                                   
00006225  UARTCC26XX_control                                                 
000089dc  UARTCC26XX_fxnTable                                                
000021c9  UARTCC26XX_hwiIntFxn                                               
00008743  UARTCC26XX_init                                                    
00001db9  UARTCC26XX_open                                                    
00002465  UARTCC26XX_read                                                    
00006a99  UARTCC26XX_readCancel                                              
00006b7b  UARTCC26XX_readPolling                                             
000062eb  UARTCC26XX_swiIntFxn                                               
00003791  UARTCC26XX_write                                                   
00004f83  UARTCC26XX_writeCancel                                             
00006bcb  UARTCC26XX_writePolling                                            
20000c70  UARTRXbuffer                                                       
00008b75  UART_0_CONST                                                       
00008279  UART_Params_init                                                   
00008b58  UART_config                                                        
00008b76  UART_count                                                         
00008a50  UART_defaultParams                                                 
00006ff9  UART_init                                                          
00007289  UART_open                                                          
000086fb  UART_read                                                          
00008705  UART_write                                                         
00007715  UDMACC26XX_close                                                   
00008b94  UDMACC26XX_config                                                  
0000870f  UDMACC26XX_hwiIntFxn                                               
00006ae5  UDMACC26XX_open                                                    
00006bd1  Uart_ReadCallback                                                  
20014000  __STACK_END                                                        
00000400  __STACK_SIZE                                                       
00004000  __SYSMEM_SIZE                                                      
00008d10  __TI_CINIT_Base                                                    
00008d38  __TI_CINIT_Limit                                                   
00008ce0  __TI_Handler_Table_Base                                            
00008cec  __TI_Handler_Table_Limit                                           
0000703d  __TI_auto_init_nobinit_nopinit                                     
000061bd  __TI_decompress_lzss                                               
00008603  __TI_decompress_none                                               
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
00000000  __TI_static_base__                                                 
000086c1  __TI_zero_init                                                     
00005e69  __aeabi_d2f                                                        
0000146f  __aeabi_dadd                                                       
000026d9  __aeabi_ddiv                                                       
00002e65  __aeabi_dmul                                                       
00001465  __aeabi_dsub                                                       
0000874d  __aeabi_errno_addr                                                 
000075fd  __aeabi_f2d                                                        
00004b57  __aeabi_idiv0                                                      
00004b57  __aeabi_ldiv0                                                      
000082a9  __aeabi_lmul                                                       
00005975  __aeabi_memclr                                                     
00005975  __aeabi_memclr4                                                    
00005975  __aeabi_memclr8                                                    
00004bf5  __aeabi_memcpy                                                     
00004bf5  __aeabi_memcpy4                                                    
00004bf5  __aeabi_memcpy8                                                    
00005977  __aeabi_memset                                                     
00005977  __aeabi_memset4                                                    
00005977  __aeabi_memset8                                                    
00004e59  __aeabi_uldivmod                                                   
ffffffff  __binit__                                                          
00008bc4  __c_args__                                                         
00057fa8  __ccfg                                                             
UNDEFED   __mpu_init                                                         
20013c00  __stack                                                            
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
00008291  _args_main                                                         
000079dd  _c_int00                                                           
20000c20  _hposcCoeffs                                                       
20001a80  _sys_memory                                                        
UNDEFED   _system_post_cinit                                                 
000063ad  _system_pre_init                                                   
00007245  abort                                                              
20000d28  adcBufCC26XXChannelLut0                                            
00008b14  adcbufCC26XXHWAttrs                                                
20008c7c  adcbufCC26XXbjects                                                 
ffffffff  binit                                                              
00002d63  clkFxn                                                             
200091e9  driverlib_release_0_59848                                          
20000d38  errno                                                              
20000e00  g_pfnRAMVectors                                                    
00000000  g_pfnVectors                                                       
00008b64  gptimerCC26XXHWAttrs                                               
20009054  gptimerCC26XXObjects                                               
00005a69  main                                                               
00004bf5  memcpy                                                             
0000597d  memset                                                             
20000d04  pDestinationArray                                                  
20000bf8  pOverrides                                                         
20008f88  pinHandleTable                                                     
20000d1c  pinLowerBound                                                      
20000d18  pinUpperBound                                                      
200091e4  readEntry                                                          
20000c68  receivedByte                                                       
00008451  resetISR                                                           
00008954  resourceDB                                                         
00007abd  rf_patch_cpe_prop                                                  
00001f1d  sqrt                                                               
00001f1d  sqrtl                                                              
20000c6c  uart                                                               
20008980  uartCC26XXObjects                                                  
00008ba4  udmaCC26XXHWAttrs                                                  
20009134  udmaCC26XXObject                                                   


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                                               
-------   ----                                                               
00000000  __TI_static_base__                                                 
00000000  g_pfnVectors                                                       
00000400  __STACK_SIZE                                                       
00000589  Mobile_execute_MeasureAll                                          
000008f9  ControlMobile_Run                                                  
00000c0b  ClockP_stop                                                        
00000c11  CalculatePosition                                                  
00000eb1  RF_open                                                            
000010b1  PIN_init                                                           
00001465  __aeabi_dsub                                                       
0000146f  __aeabi_dadd                                                       
0000161d  Power_sleep                                                        
00001ad1  ADCBufCC26X2_open                                                  
00001c45  Power_init                                                         
00001db9  UARTCC26XX_open                                                    
00001f1d  sqrt                                                               
00001f1d  sqrtl                                                              
000021c9  UARTCC26XX_hwiIntFxn                                               
00002465  UARTCC26XX_read                                                    
0000259f  NOROM_RFCCpePatchReset                                             
000026d9  __aeabi_ddiv                                                       
00002b57  SemaphoreP_destruct                                                
00002c5d  ADCBufCC26X2_getResolution                                         
00002c61  ControlX0_RunX                                                     
00002d63  clkFxn                                                             
00002d65  ADCBufCC26X2_convert                                               
00002e65  __aeabi_dmul                                                       
00002f61  NOROM_SysCtrlStandby                                               
00003141  ControlX0_Run0                                                     
0000322b  NOROM_CPUdelay                                                     
00003231  RFCommunication_TXReadSamples                                      
00003319  RFCommunication_TXReturnResults                                    
00003401  RFQueue_defineQueue                                                
000034e9  RFCommunication_init                                               
000035cd  RF_postCmd                                                         
000036b1  PowerCC26XX_standbyPolicy                                          
00003791  UARTCC26XX_write                                                   
00003871  ADC_ReadArrayADCraw                                                
00003951  RF_pendCmd                                                         
00003a29  SwiP_construct                                                     
00003ca9  RFCommunication_TXArraySection                                     
00003d75  ClockP_isActive                                                    
00003d79  RFCommunication_TXListenToSound                                    
00003e41  Power_releaseDependency                                            
00004000  __SYSMEM_SIZE                                                      
0000407d  TimerP_construct                                                   
00004139  Power_setDependency                                                
000042a9  Time_Init                                                          
00004361  ClockP_startup                                                     
00004415  ClockP_setTimeout                                                  
00004419  Serial_CopyArraySectionToSerial                                    
000044c9  Serial_PrintMeasurements                                           
00004579  ClockP_workFuncDynamic                                             
00004625  PIN_open                                                           
0000477d  HwiP_enable                                                        
0000477d  NoRTOS_start                                                       
00004781  Serial_CheckCommand                                                
000048cd  NOROM_RFCOverrideUpdate                                            
000048d1  Temperature_GetValueDeciDegrees                                    
00004975  QueueP_head                                                        
00004979  RFCommunication_TXMeasurements                                     
00004a19  SemaphoreP_pend                                                    
00004ab9  RFCommunication_CheckReceivedPacket                                
00004b57  __aeabi_idiv0                                                      
00004b57  __aeabi_ldiv0                                                      
00004b59  SwiP_dispatch                                                      
00004bf5  __aeabi_memcpy                                                     
00004bf5  __aeabi_memcpy4                                                    
00004bf5  __aeabi_memcpy8                                                    
00004bf5  memcpy                                                             
00004c91  NOROM_SetupTrimDevice                                              
00004e59  __aeabi_uldivmod                                                   
00004ef1  Board_shutDownExtFlash                                             
00004f83  UARTCC26XX_writeCancel                                             
000050a5  QueueP_next                                                        
000050a9  TimerMatchCallback                                                 
00005139  GPTimerCC26XX_open                                                 
000051c1  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00005571  SoundReceive_CalculateDistance                                     
000055f5  UARTCC26XX_close                                                   
000056f9  ClockP_start                                                       
00005779  HwiP_construct                                                     
000057f9  NOROM_SysCtrlSetRechargeBeforePowerDown                            
000058f5  RF_defaultExecutionPolicy                                          
000058f9  Serial_CopyReceivedArraySectionToSerial                            
00005975  __aeabi_memclr                                                     
00005975  __aeabi_memclr4                                                    
00005975  __aeabi_memclr8                                                    
00005977  __aeabi_memset                                                     
00005977  __aeabi_memset4                                                    
00005977  __aeabi_memset8                                                    
0000597d  memset                                                             
000059f1  ClockP_walkQueueDynamic                                            
00005a69  main                                                               
00005b55  PowerCC26X2_auxISR                                                 
00005bc9  RFCommunication_TXAskXToMeasureXto0                                
00005cad  NOROM_RFCAnaDivTxOverride                                          
00005d1d  RF_getCurrentTime                                                  
00005e69  __aeabi_d2f                                                        
00005ed9  NOROM_ChipInfo_GetChipType                                         
00005fb1  SoundTransmit_GenerateSound                                        
0000601d  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
000060ed  SwiP_post                                                          
00006155  TimerP_start                                                       
000061bd  __TI_decompress_lzss                                               
00006225  UARTCC26XX_control                                                 
000062eb  UARTCC26XX_swiIntFxn                                               
000063ad  _system_pre_init                                                   
000063b1  DeviceType_ReadFromBoard                                           
00006411  PIN_remove                                                         
0000657d  GPTimerCC26XX_stop                                                 
000065d5  PowerCC26X2_initiateCalibration                                    
0000662d  RF_defaultSubmitPolicy                                             
00006683  QueueP_init                                                        
00006689  Serial_Init                                                        
000066df  ADCBufCC26X2_adjustRawValues                                       
00006735  NOROM_PRCMPowerDomainsAllOff                                       
000067dd  SemaphoreP_construct                                               
0000682d  ADCBufCC26X2_close                                                 
00006881  Gpio_Init                                                          
00006969  GPTimerCC26XX_start                                                
000069b5  NOROM_PRCMPowerDomainsAllOn                                        
00006a01  RFCommunication_GetDataReceivedArraySectionPacket                  
00006a99  UARTCC26XX_readCancel                                              
00006ae5  UDMACC26XX_open                                                    
00006b31  ADCBufCC26X2_control                                               
00006b7b  UARTCC26XX_readPolling                                             
00006b81  Board_wakeUpExtFlash                                               
00006bcb  UARTCC26XX_writePolling                                            
00006bd1  Uart_ReadCallback                                                  
00006c1d  ADCBufCC26X2_convertAdjustedToMicroVolts                           
00006c65  ClockP_construct                                                   
00006cad  NOROM_RFCSynthPowerDown                                            
00006cf5  PIN_setOutputEnable                                                
00006d3d  PowerCC26X2_RCOSC_clockFunc                                        
00006d85  PowerCC26XX_switchXOSC_HF                                          
00006e5d  RingBuf_put                                                        
00006ea5  ADCBuf_init                                                        
00006ea5  ADC_Init                                                           
00006ee9  NOROM_ChipInfo_GetHwRevision                                       
00006f71  SemaphoreP_post                                                    
00006fb5  SwiP_restore                                                       
00006ff9  UART_init                                                          
0000703d  __TI_auto_init_nobinit_nopinit                                     
000070c5  ADCBufCC26X2_convertCancel                                         
00007105  ADCBuf_open                                                        
00007145  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00007185  NOROM_TimerIntRegister                                             
000071c5  PowerCC26XX_isStableXOSC_HF                                        
00007205  RingBuf_get                                                        
00007245  C$$EXIT                                                            
00007245  abort                                                              
00007249  Timer2AInterruptHandler                                            
00007289  UART_open                                                          
00007309  ADCBufCallback                                                     
00007389  PIN_setOutputValue                                                 
00007475  PIN_setConfig                                                      
000074e5  RF_powerConstraintRelease                                          
0000751d  TimerP_getCurrentTick                                              
00007555  TimerP_initDevice                                                  
000075fd  __aeabi_f2d                                                        
00007671  ADC_ReadSingleValueADCmV                                           
000076a9  Time_IsSoftTimerExpired                                            
000076df  TimerP_setNextTick                                                 
00007715  UDMACC26XX_close                                                   
0000774d  ClockP_getTicksUntilInterrupt                                      
000077b5  GPTimerCC26XX_configureDebugStall                                  
000077e9  NOROM_AUXSYSIFOpModeChange                                         
0000781d  NOROM_IntRegister                                                  
00007851  NOROM_SysCtrlIdle                                                  
00007885  SwiP_destruct                                                      
000078ed  GPTimerCC26XX_close                                                
0000791d  PINCC26XX_setMux                                                   
0000794d  Power_releaseConstraint                                            
0000797d  Power_setConstraint                                                
000079dd  _c_int00                                                           
00007a91  TimerP_setThreshold                                                
00007abd  rf_patch_cpe_prop                                                  
00007ae9  List_insert                                                        
00007b3d  NOROM_RFCOverrideSearch                                            
00007b65  Power_registerNotify                                               
00007bb5  RF_runCmd                                                          
00007c05  List_put                                                           
00007c2b  List_putHead                                                       
00007c51  List_remove                                                        
00007c79  BatteryVoltage_GetStoredValue                                      
00007c9d  ClockP_getTicks                                                    
00007cc1  ClockP_scheduleNextTick                                            
00007d09  NOROM_OSCHF_TurnOnXosc                                             
00007d2d  NOROM_RFCDoorbellSendTo                                            
00007d99  TimerP_getMaxTicks                                                 
00007de1  Board_init                                                         
00007e03  List_get                                                           
00007e25  ClockP_add                                                         
00007e45  NOROM_AUXADCEnableSync                                             
00007e85  Power_unregisterNotify                                             
00007ea5  QueueP_put                                                         
00007f45  TimerP_dynamicStub                                                 
00007fa9  BatteryVoltage_Store                                               
00007fc7  PIN_close                                                          
00007fe5  Power_getTransitionLatency                                         
00008003  SemaphoreP_constructBinary                                         
00008021  SwiP_or                                                            
00008041  HwiP_destruct                                                      
0000805d  NOROM_ChipInfo_GetChipFamily                                       
000080cd  RF_powerConstraintSet                                              
000080e9  SwiP_disable                                                       
00008121  HwiP_setFunc                                                       
0000813b  PowerCC26XX_calibrate                                              
00008155  QueueP_get                                                         
0000816f  ClockP_destruct                                                    
00008187  ClockP_getTimeout                                                  
000081a1  HwiP_inISR                                                         
000081b9  NOROM_ChipInfo_GetPackageType                                      
000081d1  Power_idleFunc                                                     
000081e9  RFCommunication_GetBatteryFromReceivedMeasurementPacket            
00008201  RFCommunication_GetDistanceFromReceivedMeasurementPacket           
00008231  Time_EnableMatch                                                   
00008249  Time_StartSoftTimer                                                
00008261  TimerP_startup                                                     
00008279  UART_Params_init                                                   
00008291  _args_main                                                         
000082a9  __aeabi_lmul                                                       
000082d9  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
000082f1  RFQueue_nextEntry                                                  
00008321  SoundReceive_SampleSound                                           
00008361  ADCBuf_Params_init                                                 
00008375  List_clearList                                                     
00008389  NOROM_IntUnregister                                                
0000839d  NOROM_RFCCpeIntGetAndClear                                         
000083b1  PowerCC26X2_calibrate                                              
000083c5  Power_disablePolicy                                                
000083d9  RF_Params_init                                                     
00008401  RF_flushCmd                                                        
0000843d  TimerP_getCount64                                                  
00008451  resetISR                                                           
00008465  GPTimerCC26XX_Params_init                                          
00008477  HwiP_Params_init                                                   
00008489  RingBuf_construct                                                  
000084a1  Time_DisableMatch                                                  
000084b9  BatteryVoltage_GetMyVoltage                                        
000084c9  ClockP_Params_init                                                 
000084d9  ClockP_getCpuFreq                                                  
000084e9  HwiP_clearInterrupt                                                
000084f9  HwiP_disable                                                       
00008509  HwiP_enableInterrupt                                               
00008519  HwiP_post                                                          
00008529  NOROM_RFCHwIntGetAndClear                                          
00008539  PINCC26XX_getPinCount                                              
00008549  PowerCC26XX_schedulerDisable                                       
00008559  Power_enablePolicy                                                 
00008589  SemaphoreP_Params_init                                             
00008599  SwiP_Params_init                                                   
000085a9  Time_SetNextMatch                                                  
000085b9  TimerP_Params_init                                                 
000085d9  QueueP_empty                                                       
000085e7  QueueP_remove                                                      
00008603  __TI_decompress_none                                               
00008611  ClockP_doTick                                                      
0000861d  ClockP_getSystemTickPeriod                                         
00008629  NOROM_CPUcpsid                                                     
00008635  NOROM_CPUcpsie                                                     
00008641  PowerCC26XX_schedulerRestore                                       
0000864d  Power_getConstraintMask                                            
00008659  Power_getDependencyCount                                           
00008669  RFCommunication_GetSectionReceivedArraySectionPacket               
00008679  RFQueue_getDataEntry                                               
00008685  RF_powerConstraintGet                                              
00008691  SoundTransmit_Init                                                 
0000869d  SwiP_getTrigger                                                    
000086a9  Time_SetCallbackFunctionOnMatch                                    
000086b5  TimerP_getFreq                                                     
000086c1  __TI_zero_init                                                     
000086cd  ADCBuf_close                                                       
000086d7  ADCBuf_convert                                                     
000086e1  ADCBuf_convertCancel                                               
000086f1  SoundReceive_GetSoundSampleArray                                   
000086fb  UART_read                                                          
00008705  UART_write                                                         
0000870f  UDMACC26XX_hwiIntFxn                                               
0000872b  ADCBufCC26X2_init                                                  
00008733  GPTimerCC26XX_setLoadValue                                         
0000873b  HwiP_restore                                                       
00008743  UARTCC26XX_init                                                    
0000874d  __aeabi_errno_addr                                                 
00008761  Board_initHook                                                     
00008954  resourceDB                                                         
000089c4  BoardGpioInitTable                                                 
000089dc  UARTCC26XX_fxnTable                                                
00008a2c  ADCBufCC26X2_fxnTable                                              
00008a50  UART_defaultParams                                                 
00008ab0  ADCBuf_defaultParams                                               
00008adc  PowerCC26X2_config                                                 
00008b14  adcbufCC26XXHWAttrs                                                
00008b34  ADCBuf_config                                                      
00008b40  GPTimerCC26XX_config                                               
00008b4c  RFCC26XX_hwAttrs                                                   
00008b58  UART_config                                                        
00008b64  gptimerCC26XXHWAttrs                                               
00008b70  ADCBUF_CONST                                                       
00008b71  ADCBUF_SOUND_CONST                                                 
00008b72  ADCBUF_TEMPERATURE_CONST                                           
00008b73  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00008b74  ADCBuf_count                                                       
00008b75  UART_0_CONST                                                       
00008b76  UART_count                                                         
00008b77  CONFIG_GPTIMER_0_CONST                                             
00008b78  GPTimer_count                                                      
00008b8c  PINCC26XX_hwAttrs                                                  
00008b94  UDMACC26XX_config                                                  
00008ba4  udmaCC26XXHWAttrs                                                  
00008bc4  __c_args__                                                         
00008ce0  __TI_Handler_Table_Base                                            
00008cec  __TI_Handler_Table_Limit                                           
00008d10  __TI_CINIT_Base                                                    
00008d38  __TI_CINIT_Limit                                                   
00057fa8  __ccfg                                                             
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
200004f8  RXpacketLength                                                     
20000758  PowerCC26X2_module                                                 
200008c8  ArraySize                                                          
200008cc  DelayArray                                                         
200008d0  StateArray                                                         
200008d4  ChirpDelay                                                         
20000912  ChirpState                                                         
20000950  BeepDelay                                                          
200009a0  BeepState                                                          
200009f0  Signature                                                          
20000bd0  RF_cmdPropRadioDivSetup                                            
20000bf8  pOverrides                                                         
20000c20  _hposcCoeffs                                                       
20000c44  RF_cmdPropRx                                                       
20000c68  receivedByte                                                       
20000c6c  uart                                                               
20000c70  UARTRXbuffer                                                       
20000c80  CallBackMatch                                                      
20000c84  TimerStartTime                                                     
20000c98  RF_cmdFs                                                           
20000cb0  RF_cmdPropTx                                                       
20000cd8  RF_prop                                                            
20000d00  DestinationArraySize                                               
20000d04  pDestinationArray                                                  
20000d08  RFCC26XX_schedulerPolicy                                           
20000d10  SemaphoreP_defaultParams                                           
20000d18  pinUpperBound                                                      
20000d1c  pinLowerBound                                                      
20000d24  ClockP_tickPeriod                                                  
20000d28  adcBufCC26XXChannelLut0                                            
20000d30  Battery0                                                           
20000d32  BatteryX                                                           
20000d34  HwiP_swiPIntNum                                                    
20000d38  errno                                                              
20000d3c  TimerOverflows                                                     
20000d40  RxIndex                                                            
20000d42  SerialLineReceived                                                 
20000d46  ChirpIndex                                                         
20000e00  g_pfnRAMVectors                                                    
20001a80  _sys_memory                                                        
20008980  uartCC26XXObjects                                                  
20008c7c  adcbufCC26XXbjects                                                 
20008d58  InternalADCBuffer1                                                 
20008e20  InternalADCBuffer2                                                 
20008f88  pinHandleTable                                                     
20009054  gptimerCC26XXObjects                                               
20009134  udmaCC26XXObject                                                   
200091ca  DestinationArrayIndex                                              
200091e4  readEntry                                                          
200091e8  DeviceType                                                         
200091e9  driverlib_release_0_59848                                          
20013c00  __stack                                                            
20014000  __STACK_END                                                        
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
ffffffff  __binit__                                                          
ffffffff  binit                                                              
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
UNDEFED   __mpu_init                                                         
UNDEFED   _system_post_cinit                                                 

[381 symbols]
