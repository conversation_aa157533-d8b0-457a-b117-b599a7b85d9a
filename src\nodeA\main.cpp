#include <Arduino.h>
#include "i2s_sender.h"
#include <ESPNowManager.h>
#include <ChirpGenerator.h>

const uint8_t RECEIVER_MAC[] = {0x50, 0x02, 0x91, 0x98, 0xFC, 0xBC};
const uint8_t SYNC_COMMAND = 0xAA;  // Sync command byte
I2SSender i2s;
ESPNowManager espNow;
ChirpGenerator chirpGen;

// Counter for debug output
uint32_t cycleCount = 0;

void setup() {
    Serial.begin(115200);
    while(!Serial);
    
    Serial.println("\n\n--- NodeA (Transmitter) Starting ---");
    
    // Print own MAC address
    uint8_t macAddr[6];
    WiFi.macAddress(macAddr);
    Serial.printf("My MAC: %02X:%02X:%02X:%02X:%02X:%02X\n", 
                 macAddr[0], macAddr[1], macAddr[2], macAddr[3], macAddr[4], macAddr[5]);
    
    Serial.printf("Target MAC: %02X:%02X:%02X:%02X:%02X:%02X\n", 
                 RECEIVER_MAC[0], RECEIVER_MAC[1], RECEIVER_MAC[2], 
                 RECEIVER_MAC[3], RECEIVER_MAC[4], RECEIVER_MAC[5]);
    
    Serial.println("Initializing ESP-NOW...");
    espNow.init(true);
    
    Serial.println("Adding peer...");
    bool peerAdded = espNow.addPeer(RECEIVER_MAC);
    Serial.printf("Peer added: %s\n", peerAdded ? "success" : "FAILED");
    
    Serial.println("Initializing I2S...");
    i2s.init();
    
    Serial.println("Initializing chirp generator...");
    const int16_t* chirp = chirpGen.generate();
    Serial.printf("Chirp size: %u samples\n", chirpGen.size());
    Serial.printf("First sample: %d, Last sample: %d\n", 
                 chirp[0], chirp[chirpGen.size()-1]);
    
    Serial.println("NodeA initialized and ready");
}

void loop() {
    cycleCount++;
    
    // Print debug info every 10 cycles
    if (cycleCount % 10 == 1) {
        Serial.printf("\n--- Cycle %u ---\n", cycleCount);
    }
    
    // Send sync pulse with 0xAA command
    Serial.println("Sending sync command...");
    bool sendResult = espNow.send(RECEIVER_MAC, &SYNC_COMMAND, 1);
    Serial.printf("Sync send result: %s\n", sendResult ? "success" : "FAILED");
    
    // Transmit chirp
    Serial.println("Sending chirp...");
    i2s.send(chirpGen.generate(), chirpGen.size());
    
    // Fade-out
    Serial.println("Sending fade-out...");
    int16_t fade[50];
    const int16_t* lastSample = chirpGen.generate() + chirpGen.size() - 1;
    for(int i = 0; i < 50; i++) {
        fade[i] = *lastSample * (50 - i) / 50;
    }
    i2s.send(fade, 50);
    
    Serial.println("Waiting for next cycle...");
    delay(980);
}
