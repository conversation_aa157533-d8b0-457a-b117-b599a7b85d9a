import numpy as np
import soundfile as sf

# === 1. Daten laden (jede <PERSON> = 1 Sam<PERSON>) ===
with open("chirp.txt", "r") as f:
    lines = f.readlines()

# Nur Zahlen extrahieren
samples = [int(line.strip()) for line in lines if line.strip().lstrip('-').isdigit()]
signal = np.array(samples, dtype=np.int16)

# === 2. WAV speichern ===
sample_rate = 48000  # muss zu deinem CHIRP passen!
sf.write("chirp_from_esp32.wav", signal, samplerate=sample_rate, subtype='PCM_16')

print("✅ WAV-Datei gespeichert: chirp_from_esp32.wav")

# === 3. Python Array Definition erstellen ===
def create_python_array_file(samples, filename="i2s_data_array.py"):
    """
    Erstellt eine Python-Datei mit den I2S-Daten als Array-Definition
    """
    with open(filename, "w") as f:
        f.write("# I2S Audio Data Array\n")
        f.write("# Generated from ESP32 I2S output\n")
        f.write("# Data format: signed 16-bit (int16_t)\n")
        f.write(f"# Sample count: {len(samples)}\n")
        f.write(f"# Sample rate: {sample_rate} Hz\n")
        f.write(f"# Duration: {len(samples)/sample_rate*1000:.2f} ms\n\n")

        f.write("import numpy as np\n\n")

        # Array als Python Liste
        f.write("# I2S data as Python list (signed 16-bit values)\n")
        f.write("i2s_data_list = [\n")

        # Schreibe Daten in 16er-Gruppen für bessere Lesbarkeit
        for i in range(0, len(samples), 16):
            chunk = samples[i:i+16]
            line = "    " + ", ".join(f"{sample:6d}" for sample in chunk)
            if i + 16 < len(samples):
                line += ","
            f.write(line + "\n")

        f.write("]\n\n")

        # Array als NumPy Array
        f.write("# I2S data as NumPy array (int16)\n")
        f.write("i2s_data_numpy = np.array(i2s_data_list, dtype=np.int16)\n\n")

        # Zusätzliche Informationen
        f.write("# Array properties\n")
        f.write(f"SAMPLE_COUNT = {len(samples)}\n")
        f.write(f"SAMPLE_RATE = {sample_rate}\n")
        f.write(f"DURATION_MS = {len(samples)/sample_rate*1000:.2f}\n")
        f.write(f"DATA_TYPE = 'int16'  # signed 16-bit\n")
        f.write(f"VALUE_RANGE = (-32768, 32767)  # signed 16-bit range\n\n")

        # Statistiken
        f.write("# Data statistics\n")
        f.write(f"MIN_VALUE = {min(samples)}\n")
        f.write(f"MAX_VALUE = {max(samples)}\n")
        f.write(f"MEAN_VALUE = {np.mean(samples):.2f}\n")
        f.write(f"RMS_VALUE = {np.sqrt(np.mean(np.array(samples)**2)):.2f}\n\n")

        # Verwendungsbeispiel
        f.write('# Usage example:\n')
        f.write('# from i2s_data_array import i2s_data_numpy, SAMPLE_RATE\n')
        f.write('# import soundfile as sf\n')
        f.write('# sf.write("reconstructed.wav", i2s_data_numpy, SAMPLE_RATE)\n')

# Python Array Datei erstellen
create_python_array_file(samples)
print("✅ Python Array-Datei gespeichert: i2s_data_array.py")
