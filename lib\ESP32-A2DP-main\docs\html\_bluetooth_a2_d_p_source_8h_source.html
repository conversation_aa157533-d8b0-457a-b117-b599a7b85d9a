<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: src/BluetoothA2DPSource.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">BluetoothA2DPSource.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">// C++ Class Implementation for a A2DP Source to be used as Arduino Library</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment">// The original ESP32 implementation can be found at</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment">// https://github.com/espressif/esp-idf/tree/master/examples/bluetooth/bluedroid/classic_bt/a2dp_source</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">// Licensed under the Apache License, Version 2.0 (the &quot;License&quot;);</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment">// you may not use this file except in compliance with the License.</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment">// You may obtain a copy of the License at</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment">//     http://www.apache.org/licenses/LICENSE-2.0</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment">// Unless required by applicable law or agreed to in writing, software</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment">// distributed under the License is distributed on an &quot;AS IS&quot; BASIS,</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment">// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment">// See the License for the specific language governing permissions and</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// limitations under the License.</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment">// Copyright 2020 Phil Schatzmann</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment">// Copyright 2015-2016 Espressif Systems (Shanghai) PTE LTD</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160; </div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160; </div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &lt;vector&gt;</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160; </div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="_bluetooth_a2_d_p_common_8h.html">BluetoothA2DPCommon.h</a>&quot;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160; </div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#include &quot;Stream.h&quot;</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160; </div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#if (ESP_IDF_VERSION &lt; ESP_IDF_VERSION_VAL(5, 0, 0))</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#define TIMER_ARG_TYPE void *</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#define TIMER_ARG_TYPE tmrTimerControl *</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160; </div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="keyword">typedef</span> int32_t (*music_data_cb_t)(uint8_t *data, int32_t len);</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="keyword">typedef</span> int32_t (*music_data_frames_cb_t)(Frame *data, int32_t len);</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">typedef</span> void (*bt_app_copy_cb_t)(<a class="code" href="structbt__app__msg__t.html">bt_app_msg_t</a> *msg, <span class="keywordtype">void</span> *p_dest, <span class="keywordtype">void</span> *p_src);</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="keyword">typedef</span> void (*bt_app_cb_t)(uint16_t event, <span class="keywordtype">void</span> *param);</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160; </div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> <span class="keywordtype">void</span> ccall_a2d_app_heart_beat(TIMER_ARG_TYPE arg);</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> <span class="keywordtype">void</span> ccall_bt_app_av_sm_hdlr(uint16_t event, <span class="keywordtype">void</span> *param);</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> <span class="keywordtype">void</span> ccall_bt_av_hdl_avrc_ct_evt(uint16_t event, <span class="keywordtype">void</span> *param);</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> int32_t ccall_bt_app_a2d_data_cb(uint8_t *data, int32_t len);</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160; </div>
<div class="line"><a name="l00050"></a><span class="lineno"><a class="line" href="group__a2dp.html#gabf9f46a0805b93eedaeccb8e512ef7fa">   50</a></span>&#160;<span class="keyword">enum</span> <a class="code" href="group__a2dp.html#gabf9f46a0805b93eedaeccb8e512ef7fa">APP_AV_STATE</a> {</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;  APP_AV_STATE_IDLE,</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;  APP_AV_STATE_DISCOVERING,</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;  APP_AV_STATE_DISCOVERED,</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;  APP_AV_STATE_UNCONNECTED,</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;  APP_AV_STATE_CONNECTING,</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;  APP_AV_STATE_CONNECTED,</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  APP_AV_STATE_DISCONNECTING,</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;};</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160; </div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="keyword">static</span> <span class="keyword">const</span> <span class="keywordtype">char</span> *APP_AV_STATE_STR[] = {</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;    <span class="stringliteral">&quot;IDLE&quot;</span>,       <span class="stringliteral">&quot;DISCOVERING&quot;</span>, <span class="stringliteral">&quot;DISCOVERED&quot;</span>,   <span class="stringliteral">&quot;UNCONNECTED&quot;</span>,</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;    <span class="stringliteral">&quot;CONNECTING&quot;</span>, <span class="stringliteral">&quot;CONNECTED&quot;</span>,   <span class="stringliteral">&quot;DISCONNECTING&quot;</span>};</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160; </div>
<div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html">   71</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a> : <span class="keyword">public</span> <a class="code" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a> {</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  <span class="keyword">friend</span> <span class="keywordtype">void</span> ccall_a2d_app_heart_beat(TIMER_ARG_TYPE arg);</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;  <span class="keyword">friend</span> <span class="keywordtype">void</span> ccall_bt_app_av_sm_hdlr(uint16_t event, <span class="keywordtype">void</span> *param);</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <span class="keyword">friend</span> <span class="keywordtype">void</span> ccall_bt_av_hdl_avrc_ct_evt(uint16_t event, <span class="keywordtype">void</span> *param);</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  <span class="keyword">friend</span> int32_t ccall_bt_app_a2d_data_cb(uint8_t *data, int32_t len);</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160; </div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_source.html#a545a8b10ab474d787744f85fe784d49a">BluetoothA2DPSource</a>();</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160; </div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_source.html#a417e7ef0049364c22c92a29e6c4b4ed1">~BluetoothA2DPSource</a>();</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160; </div>
<div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#a959ff7f30a6064f018dcdad5deb8e3d9">   85</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a959ff7f30a6064f018dcdad5deb8e3d9">set_ssp_enabled</a>(<span class="keywordtype">bool</span> active) { this-&gt;ssp_enabled = active; }</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160; </div>
<div class="line"><a name="l00089"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#a65ac6f2b0777c97874ee358119de3790">   89</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a65ac6f2b0777c97874ee358119de3790">set_auto_reconnect</a>(<span class="keywordtype">bool</span> active) {</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    this-&gt;reconnect_status = active ? AutoReconnect : NoReconnect;</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;  }</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160; </div>
<div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#ab94a0596fd595994f3ae4d2d6d2e5a5b">   94</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#ab94a0596fd595994f3ae4d2d6d2e5a5b">set_auto_reconnect</a>(<a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> addr) {</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#a65ac6f2b0777c97874ee358119de3790">set_auto_reconnect</a>(<span class="keyword">true</span>);</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;    memcpy(last_connection, addr, ESP_BD_ADDR_LEN);</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;  }</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160; </div>
<div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#acb9c5182525261c53b803719b3027014">  100</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#acb9c5182525261c53b803719b3027014">set_local_name</a>(<span class="keyword">const</span> <span class="keywordtype">char</span> *name) { dev_name = name; }</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160; </div>
<div class="line"><a name="l00103"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#a9eb67e480675059a96014f2c1b84b0c3">  103</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a9eb67e480675059a96014f2c1b84b0c3">set_data_callback</a>(int32_t(cb)(uint8_t *data, int32_t len)) {</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#ac77d0c29cc27815f703469aa0083439e">get_data_cb</a> = cb;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;  }</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160; </div>
<div class="line"><a name="l00108"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#ad42fbe4ec00846ab22157545b3885db9">  108</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#ad42fbe4ec00846ab22157545b3885db9">set_data_callback_in_frames</a>(int32_t(cb)(Frame *data,</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;                                                       int32_t len)) {</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;    get_data_in_frames_cb = cb;</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;  }</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160; </div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160; </div>
<div class="line"><a name="l00116"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#a75f389f93441fe0735c3bae5f68043ae">  116</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a75f389f93441fe0735c3bae5f68043ae">set_data_source</a>(Stream &amp;data) { p_stream = &amp;data; }</div>
<div class="line"><a name="l00118"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#a90f7e0445fffbc5ff4ac2f0d50f0b51e">  118</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a90f7e0445fffbc5ff4ac2f0d50f0b51e">set_data_source_callback</a>(Stream &amp;(*next_stream)()) {</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;    get_next_stream_cb = next_stream;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;  }</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160; </div>
<div class="line"><a name="l00125"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">  125</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">start</a>() {</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;    std::vector&lt;const char *&gt; names;  <span class="comment">// empty vector</span></div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">start</a>(names);</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;  }</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160; </div>
<div class="line"><a name="l00131"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#affb5e5c979a96e43a0cec7c4e0d0065d">  131</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#affb5e5c979a96e43a0cec7c4e0d0065d">start</a>(<span class="keyword">const</span> <span class="keywordtype">char</span> *name) {</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;    std::vector&lt;const char *&gt; names = {name};  <span class="comment">// empty vector</span></div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">start</a>(names);</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;  }</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160; </div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">start</a>(std::vector&lt;const char *&gt; names);</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160; </div>
<div class="line"><a name="l00140"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#a690ae791e0b9256dc2b6d460e0f9eed5">  140</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a690ae791e0b9256dc2b6d460e0f9eed5">start</a>(<span class="keyword">const</span> <span class="keywordtype">char</span> *name, music_data_frames_cb_t callback) {</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#ad42fbe4ec00846ab22157545b3885db9">set_data_callback_in_frames</a>(callback);</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;    std::vector&lt;const char *&gt; names = {name};</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">start</a>(names);</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;  }</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160; </div>
<div class="line"><a name="l00147"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#ace5835661dbf0ecb5f09600a6bf90304">  147</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#ace5835661dbf0ecb5f09600a6bf90304">start</a>(music_data_frames_cb_t callback) {</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#ad42fbe4ec00846ab22157545b3885db9">set_data_callback_in_frames</a>(callback);</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">start</a>();</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;  }</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160; </div>
<div class="line"><a name="l00153"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#a19f050d45d834b32f069608af38a46a1">  153</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a19f050d45d834b32f069608af38a46a1">start</a>(std::vector&lt;const char *&gt; names,</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;                     music_data_frames_cb_t callback) {</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#ad42fbe4ec00846ab22157545b3885db9">set_data_callback_in_frames</a>(callback);</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">start</a>(names);</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;  }</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160; </div>
<div class="line"><a name="l00160"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#ad6aac053cc521667ea15a87277009574">  160</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#ad6aac053cc521667ea15a87277009574">start_raw</a>(<span class="keyword">const</span> <span class="keywordtype">char</span> *name, music_data_cb_t callback = <span class="keyword">nullptr</span>) {</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#a9eb67e480675059a96014f2c1b84b0c3">set_data_callback</a>(callback);</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">start</a>(name);</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;  }</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160; </div>
<div class="line"><a name="l00166"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#a06fa3a9b19ea4450a9b24c4cade2ee61">  166</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a06fa3a9b19ea4450a9b24c4cade2ee61">start_raw</a>(std::vector&lt;const char *&gt; names,</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;                         music_data_cb_t callback = <span class="keyword">nullptr</span>) {</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#a9eb67e480675059a96014f2c1b84b0c3">set_data_callback</a>(callback);</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">start</a>(names);</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;  }</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160; </div>
<div class="line"><a name="l00173"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#a37eda6b257aadbc20c3f5a7556d595ca">  173</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a37eda6b257aadbc20c3f5a7556d595ca">start_raw</a>(music_data_cb_t callback = <span class="keyword">nullptr</span>) {</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#a9eb67e480675059a96014f2c1b84b0c3">set_data_callback</a>(callback);</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">start</a>();</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;  }</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160; </div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a098d9fdb6cfe044406025e89725c449d">set_pin_code</a>(<span class="keyword">const</span> <span class="keywordtype">char</span> *pin_code, esp_bt_pin_type_t pin_type);</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160; </div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a5efcc4c32c6ec8ce7eac2c1acb59f27c">set_reset_ble</a>(<span class="keywordtype">bool</span> doInit);</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160; </div>
<div class="line"><a name="l00185"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#a6ba5496831ff64bdd515fc2ad811d76d">  185</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a6ba5496831ff64bdd515fc2ad811d76d">set_ssid_callback</a>(<span class="keywordtype">bool</span> (*callback)(<span class="keyword">const</span> <span class="keywordtype">char</span> *ssid,</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;                                                  <a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> address,</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;                                                  <span class="keywordtype">int</span> rrsi)) {</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;    ssid_callback = callback;</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;  }</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160; </div>
<div class="line"><a name="l00192"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#a73eea280b254473cab7c3b1e528b030f">  192</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a73eea280b254473cab7c3b1e528b030f">set_discovery_mode_callback</a>(</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;      <span class="keywordtype">void</span> (*callback)(esp_bt_gap_discovery_state_t discoveryMode)) {</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;    discovery_mode_callback = callback;</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;  }</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160; </div>
<div class="line"><a name="l00199"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#af29d19e53e3585446fc294a3213a06af">  199</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#af29d19e53e3585446fc294a3213a06af">is_discovery_active</a>() { <span class="keywordflow">return</span> discovery_active; }</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160; </div>
<div class="line"><a name="l00204"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#a2923c8e2a689f21fc5acf7895ad2f7a7">  204</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a2923c8e2a689f21fc5acf7895ad2f7a7">set_valid_cod_service</a>(uint16_t filter) {</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;    this-&gt;valid_cod_services = filter;</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;  }</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160; </div>
<div class="line"><a name="l00209"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#a72999af828301ef39f1f388fd7356b3b">  209</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a72999af828301ef39f1f388fd7356b3b">set_avrc_passthru_command_callback</a>(<span class="keywordtype">void</span> (*cb)(uint8_t key,</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;                                                             <span class="keywordtype">bool</span> isReleased)) {</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;    passthru_command_callback = cb;</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;    is_passthru_active = (cb != <span class="keyword">nullptr</span>);</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;  }</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160; </div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#aef98c427f8e590be0a4dfaa28a5cb4fd">end</a>(<span class="keywordtype">bool</span> releaseMemory=<span class="keyword">false</span>) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160; </div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160; <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00220"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#ac77d0c29cc27815f703469aa0083439e">  220</a></span>&#160;  int32_t (*<a class="code" href="class_bluetooth_a2_d_p_source.html#ac77d0c29cc27815f703469aa0083439e">get_data_cb</a>)(uint8_t *data, int32_t len) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;  int32_t (*get_data_in_frames_cb)(Frame *data, int32_t len) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;  Stream *p_stream = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;  Stream &amp;(*get_next_stream_cb)() = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;  <span class="keyword">const</span> <span class="keywordtype">char</span> *dev_name = <span class="stringliteral">&quot;ESP32_A2DP_SRC&quot;</span>;</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;  <span class="keywordtype">bool</span> ssp_enabled = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;  std::vector&lt;const char *&gt; bt_names;</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;  esp_bt_pin_type_t pin_type;</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;  esp_bt_pin_code_t pin_code;</div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;  uint32_t pin_code_len;</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;  uint8_t s_peer_bdname[ESP_BT_GAP_MAX_BDNAME_LEN + 1];</div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;  <a class="code" href="group__a2dp.html#gabf9f46a0805b93eedaeccb8e512ef7fa">APP_AV_STATE</a> s_a2d_state = APP_AV_STATE_IDLE;  <span class="comment">// Next Target Connection State</span></div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;  <a class="code" href="group__a2dp.html#gabf9f46a0805b93eedaeccb8e512ef7fa">APP_AV_STATE</a> s_a2d_last_state =</div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;      APP_AV_STATE_IDLE;  <span class="comment">// Next Target Connection State</span></div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;  <span class="keywordtype">int</span> s_media_state = 0;</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;  <span class="keywordtype">int</span> s_intv_cnt = 0;</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;  <span class="keywordtype">int</span> s_connecting_heatbeat_count;</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;  uint32_t s_pkt_cnt;</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;  TimerHandle_t s_tmr;</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160; </div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;  <span class="comment">// initialization</span></div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;  <span class="keywordtype">bool</span> reset_ble = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;  <span class="keywordtype">bool</span> discovery_active = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;  uint16_t valid_cod_services = ESP_BT_COD_SRVC_RENDERING |</div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;                                ESP_BT_COD_SRVC_AUDIO |</div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;                                ESP_BT_COD_SRVC_TELEPHONY;</div>
<div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160; </div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;  bool (*ssid_callback)(<span class="keyword">const</span> <span class="keywordtype">char</span> *ssid, <a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> address,</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;                        <span class="keywordtype">int</span> rrsi) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;  void (*discovery_mode_callback)(esp_bt_gap_discovery_state_t discoveryMode) =</div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;      <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;  void (*passthru_command_callback)(uint8_t, bool) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;  <span class="keywordtype">bool</span> is_passthru_active = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160; </div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &gt;= ESP_IDF_VERSION_VAL(4, 0, 0)</span></div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;  esp_avrc_rn_evt_cap_mask_t s_avrc_peer_rn_cap;</div>
<div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160; </div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;  <span class="keywordtype">void</span> app_gap_callback(esp_bt_gap_cb_event_t event,</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;                        esp_bt_gap_cb_param_t *param) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a1e2a51edb571273cbd349fbed8874cc1">app_rc_ct_callback</a>(esp_avrc_ct_cb_event_t event,</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;                          esp_avrc_ct_cb_param_t *param) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#aa44bdbc77afd851d305a5412e3cc92e1">app_a2d_callback</a>(esp_a2d_cb_event_t event,</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;                        esp_a2d_cb_param_t *param) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;  <span class="keywordtype">void</span> av_hdl_stack_evt(uint16_t event, <span class="keywordtype">void</span> *p_param) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160; </div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;  <span class="keyword">virtual</span> int32_t <a class="code" href="class_bluetooth_a2_d_p_source.html#a2c3a7aa140cf42a1f324e7669a65e5cc">get_audio_data</a>(uint8_t *data, int32_t len);</div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;  <span class="keyword">virtual</span> int32_t <a class="code" href="class_bluetooth_a2_d_p_source.html#a03d1c23eb8a98bccdd15970f9d35db8c">get_audio_data_volume</a>(uint8_t *data, int32_t len);</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;  </div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160; </div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> process_user_state_callbacks(uint16_t event, <span class="keywordtype">void</span> *param);</div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160; </div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> bt_app_work_dispatch(bt_app_cb_t p_cback, uint16_t event,</div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;                                    <span class="keywordtype">void</span> *p_params, <span class="keywordtype">int</span> param_len,</div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;                                    bt_app_copy_cb_t p_copy_cback);</div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> bt_app_av_media_proc(uint16_t event, <span class="keywordtype">void</span> *param);</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160; </div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a025e54e7d0e8a4e07d5a125273fcb875">bt_app_av_state_unconnected_hdlr</a>(uint16_t event, <span class="keywordtype">void</span> *param);</div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#ac7131b626b43a516ae4ae9df6a7ec366">bt_app_av_state_connecting_hdlr</a>(uint16_t event, <span class="keywordtype">void</span> *param);</div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> bt_app_av_state_connected_hdlr(uint16_t event, <span class="keywordtype">void</span> *param);</div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> bt_app_av_state_disconnecting_hdlr(uint16_t event, <span class="keywordtype">void</span> *param);</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160; </div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> get_name_from_eir(uint8_t *eir, uint8_t *bdname,</div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;                                 uint8_t *bdname_len);</div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> filter_inquiry_scan_result(esp_bt_gap_cb_param_t *param);</div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160; </div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;  <span class="keyword">virtual</span> <span class="keyword">const</span> <span class="keywordtype">char</span> *last_bda_nvs_name() { <span class="keywordflow">return</span> <span class="stringliteral">&quot;src_bda&quot;</span>; }</div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160; </div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> a2d_app_heart_beat(<span class="keywordtype">void</span> *arg);</div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#ad3bb1aaddd9dcbd9da6a37c5aded8727">bt_app_av_sm_hdlr</a>(uint16_t event, <span class="keywordtype">void</span> *param);</div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#ae9f14078c1d5dd00c93049fa8b2e283e">bt_av_hdl_avrc_ct_evt</a>(uint16_t event, <span class="keywordtype">void</span> *p_param);</div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a190c59464f53e2d4c3f121afbb7a3c21">reset_last_connection</a>();</div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="class_bluetooth_a2_d_p_source.html#a51f93bebf73f8bf9b98fa3c5fc4fcb18">is_valid_cod_service</a>(uint32_t cod);</div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160; </div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;  esp_err_t esp_a2d_connect(<a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> peer)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;    ESP_LOGI(BT_AV_TAG, <span class="stringliteral">&quot;==&gt; a2dp connecting to: %s&quot;</span>, <a class="code" href="class_bluetooth_a2_d_p_common.html#a2b78346084e12feeea035d006e7cf07a">to_str</a>(peer));</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;    s_media_state = 0;</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;    s_a2d_state = APP_AV_STATE_CONNECTING;</div>
<div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;    <span class="keywordflow">return</span> esp_a2d_source_connect(peer);</div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;  }</div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160; </div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;  esp_err_t esp_a2d_disconnect(<a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> peer)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;    ESP_LOGI(BT_AV_TAG, <span class="stringliteral">&quot;==&gt; a2dp esp_a2d_source_disconnect from: %s&quot;</span>,</div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160;             <a class="code" href="class_bluetooth_a2_d_p_common.html#a2b78346084e12feeea035d006e7cf07a">to_str</a>(peer));</div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;    esp_a2d_media_ctrl(ESP_A2D_MEDIA_CTRL_STOP);</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;    <span class="keywordflow">return</span> esp_a2d_source_disconnect(peer);</div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;  }</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160; </div>
<div class="line"><a name="l00319"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_source.html#ac7cd3655a7e2cfbd7e6c3474a5f2bc34">  319</a></span>&#160;  <span class="keyword">const</span> <span class="keywordtype">char</span> *<a class="code" href="class_bluetooth_a2_d_p_source.html#ac7cd3655a7e2cfbd7e6c3474a5f2bc34">to_state_str</a>(<span class="keywordtype">int</span> state) { <span class="keywordflow">return</span> APP_AV_STATE_STR[state]; }</div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160; </div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;  <span class="keywordtype">void</span> set_scan_mode_connectable_default()<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_common.html#af1e2f14ddbe9266b61f5e721095c3685">set_scan_mode_connectable</a>(<span class="keyword">false</span>);</div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;  }</div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160; </div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &gt;= ESP_IDF_VERSION_VAL(4, 0, 0)</span></div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> bt_av_notify_evt_handler(uint8_t event, esp_avrc_rn_param_t *param);</div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> bt_av_volume_changed(<span class="keywordtype">void</span>);</div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> app_rc_tg_callback(esp_avrc_tg_cb_event_t event,</div>
<div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;                          esp_avrc_tg_cb_param_t *param) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> av_hdl_avrc_tg_evt(uint16_t event, <span class="keywordtype">void</span> *p_param) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> isSource() { <span class="keywordflow">return</span> <span class="keyword">true</span>;}</div>
<div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160; </div>
<div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;};</div>
<div class="ttc" id="a_bluetooth_a2_d_p_common_8h_html"><div class="ttname"><a href="_bluetooth_a2_d_p_common_8h.html">BluetoothA2DPCommon.h</a></div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></div><div class="ttdoc">Common Bluetooth A2DP functions.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:169</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a2b78346084e12feeea035d006e7cf07a"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a2b78346084e12feeea035d006e7cf07a">BluetoothA2DPCommon::to_str</a></div><div class="ttdeci">const char * to_str(esp_a2d_connection_state_t state)</div><div class="ttdoc">converts esp_a2d_connection_state_t to a string</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.cpp:364</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_af1e2f14ddbe9266b61f5e721095c3685"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#af1e2f14ddbe9266b61f5e721095c3685">BluetoothA2DPCommon::set_scan_mode_connectable</a></div><div class="ttdeci">virtual void set_scan_mode_connectable(bool connectable)</div><div class="ttdoc">Defines if the bluetooth is connectable.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.cpp:537</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></div><div class="ttdoc">A2DP Bluetooth Source.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:71</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a025e54e7d0e8a4e07d5a125273fcb875"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a025e54e7d0e8a4e07d5a125273fcb875">BluetoothA2DPSource::bt_app_av_state_unconnected_hdlr</a></div><div class="ttdeci">virtual void bt_app_av_state_unconnected_hdlr(uint16_t event, void *param)</div><div class="ttdoc">A2DP application state machine handler for each state.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:604</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a03d1c23eb8a98bccdd15970f9d35db8c"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a03d1c23eb8a98bccdd15970f9d35db8c">BluetoothA2DPSource::get_audio_data_volume</a></div><div class="ttdeci">virtual int32_t get_audio_data_volume(uint8_t *data, int32_t len)</div><div class="ttdoc">provides the audio after applying the volume</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:151</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a06fa3a9b19ea4450a9b24c4cade2ee61"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a06fa3a9b19ea4450a9b24c4cade2ee61">BluetoothA2DPSource::start_raw</a></div><div class="ttdeci">virtual void start_raw(std::vector&lt; const char * &gt; names, music_data_cb_t callback=nullptr)</div><div class="ttdoc">Obsolete: use the start w/o callback and set the callback separately.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:166</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a098d9fdb6cfe044406025e89725c449d"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a098d9fdb6cfe044406025e89725c449d">BluetoothA2DPSource::set_pin_code</a></div><div class="ttdeci">void set_pin_code(const char *pin_code, esp_bt_pin_type_t pin_type)</div><div class="ttdoc">Defines the pin code. If nothing is defined we use &quot;1234&quot;.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:80</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a190c59464f53e2d4c3f121afbb7a3c21"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a190c59464f53e2d4c3f121afbb7a3c21">BluetoothA2DPSource::reset_last_connection</a></div><div class="ttdeci">virtual void reset_last_connection()</div><div class="ttdoc">resets the last connectioin so that we can reconnect</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:183</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a19f050d45d834b32f069608af38a46a1"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a19f050d45d834b32f069608af38a46a1">BluetoothA2DPSource::start</a></div><div class="ttdeci">virtual void start(std::vector&lt; const char * &gt; names, music_data_frames_cb_t callback)</div><div class="ttdoc">Obsolete: use the start w/o callback and set the callback separately.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:153</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a1e2a51edb571273cbd349fbed8874cc1"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a1e2a51edb571273cbd349fbed8874cc1">BluetoothA2DPSource::app_rc_ct_callback</a></div><div class="ttdeci">void app_rc_ct_callback(esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param) override</div><div class="ttdoc">callback function for AVRCP controller</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:843</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a2923c8e2a689f21fc5acf7895ad2f7a7"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a2923c8e2a689f21fc5acf7895ad2f7a7">BluetoothA2DPSource::set_valid_cod_service</a></div><div class="ttdeci">virtual void set_valid_cod_service(uint16_t filter)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:204</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a2c3a7aa140cf42a1f324e7669a65e5cc"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a2c3a7aa140cf42a1f324e7669a65e5cc">BluetoothA2DPSource::get_audio_data</a></div><div class="ttdeci">virtual int32_t get_audio_data(uint8_t *data, int32_t len)</div><div class="ttdoc">provides the audio data to be sent</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:158</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a37eda6b257aadbc20c3f5a7556d595ca"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a37eda6b257aadbc20c3f5a7556d595ca">BluetoothA2DPSource::start_raw</a></div><div class="ttdeci">virtual void start_raw(music_data_cb_t callback=nullptr)</div><div class="ttdoc">Obsolete: use the start w/o callback and set the callback separately.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:173</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a417e7ef0049364c22c92a29e6c4b4ed1"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a417e7ef0049364c22c92a29e6c4b4ed1">BluetoothA2DPSource::~BluetoothA2DPSource</a></div><div class="ttdeci">~BluetoothA2DPSource()</div><div class="ttdoc">Destructor.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:78</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a51f93bebf73f8bf9b98fa3c5fc4fcb18"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a51f93bebf73f8bf9b98fa3c5fc4fcb18">BluetoothA2DPSource::is_valid_cod_service</a></div><div class="ttdeci">virtual bool is_valid_cod_service(uint32_t cod)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:259</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a545a8b10ab474d787744f85fe784d49a"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a545a8b10ab474d787744f85fe784d49a">BluetoothA2DPSource::BluetoothA2DPSource</a></div><div class="ttdeci">BluetoothA2DPSource()</div><div class="ttdoc">Constructor.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:59</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a5efcc4c32c6ec8ce7eac2c1acb59f27c"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a5efcc4c32c6ec8ce7eac2c1acb59f27c">BluetoothA2DPSource::set_reset_ble</a></div><div class="ttdeci">virtual void set_reset_ble(bool doInit)</div><div class="ttdoc">Defines if the BLE should be reset on start.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:1003</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a65ac6f2b0777c97874ee358119de3790"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a65ac6f2b0777c97874ee358119de3790">BluetoothA2DPSource::set_auto_reconnect</a></div><div class="ttdeci">virtual void set_auto_reconnect(bool active)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:89</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a690ae791e0b9256dc2b6d460e0f9eed5"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a690ae791e0b9256dc2b6d460e0f9eed5">BluetoothA2DPSource::start</a></div><div class="ttdeci">virtual void start(const char *name, music_data_frames_cb_t callback)</div><div class="ttdoc">Obsolete: use the start w/o callback and set the callback separately.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:140</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a6ba5496831ff64bdd515fc2ad811d76d"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a6ba5496831ff64bdd515fc2ad811d76d">BluetoothA2DPSource::set_ssid_callback</a></div><div class="ttdeci">virtual void set_ssid_callback(bool(*callback)(const char *ssid, esp_bd_addr_t address, int rrsi))</div><div class="ttdoc">Define callback to be notified about the found ssids.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:185</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a72999af828301ef39f1f388fd7356b3b"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a72999af828301ef39f1f388fd7356b3b">BluetoothA2DPSource::set_avrc_passthru_command_callback</a></div><div class="ttdeci">virtual void set_avrc_passthru_command_callback(void(*cb)(uint8_t key, bool isReleased))</div><div class="ttdoc">Define the handler fur button presses on the remote bluetooth speaker.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:209</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a73eea280b254473cab7c3b1e528b030f"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a73eea280b254473cab7c3b1e528b030f">BluetoothA2DPSource::set_discovery_mode_callback</a></div><div class="ttdeci">virtual void set_discovery_mode_callback(void(*callback)(esp_bt_gap_discovery_state_t discoveryMode))</div><div class="ttdoc">Define callback to be notified about bt discovery mode state changes.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:192</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a75f389f93441fe0735c3bae5f68043ae"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a75f389f93441fe0735c3bae5f68043ae">BluetoothA2DPSource::set_data_source</a></div><div class="ttdeci">virtual void set_data_source(Stream &amp;data)</div><div class="ttdoc">Defines a single Arduino Stream (e.g. File) as audio source.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:116</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a90f7e0445fffbc5ff4ac2f0d50f0b51e"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a90f7e0445fffbc5ff4ac2f0d50f0b51e">BluetoothA2DPSource::set_data_source_callback</a></div><div class="ttdeci">virtual void set_data_source_callback(Stream &amp;(*next_stream)())</div><div class="ttdoc">Provide a callback which provides streams.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:118</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a959ff7f30a6064f018dcdad5deb8e3d9"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a959ff7f30a6064f018dcdad5deb8e3d9">BluetoothA2DPSource::set_ssp_enabled</a></div><div class="ttdeci">virtual void set_ssp_enabled(bool active)</div><div class="ttdoc">activate Secure Simple Pairing</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:85</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a98c1eb3ad55af189fd3f1ddeac8f3636"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">BluetoothA2DPSource::start</a></div><div class="ttdeci">virtual void start()</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:125</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_a9eb67e480675059a96014f2c1b84b0c3"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#a9eb67e480675059a96014f2c1b84b0c3">BluetoothA2DPSource::set_data_callback</a></div><div class="ttdeci">virtual void set_data_callback(int32_t(cb)(uint8_t *data, int32_t len))</div><div class="ttdoc">Defines the data callback.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:103</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_aa44bdbc77afd851d305a5412e3cc92e1"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#aa44bdbc77afd851d305a5412e3cc92e1">BluetoothA2DPSource::app_a2d_callback</a></div><div class="ttdeci">void app_a2d_callback(esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param) override</div><div class="ttdoc">callback function for A2DP source</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:531</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_ab94a0596fd595994f3ae4d2d6d2e5a5b"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#ab94a0596fd595994f3ae4d2d6d2e5a5b">BluetoothA2DPSource::set_auto_reconnect</a></div><div class="ttdeci">virtual void set_auto_reconnect(esp_bd_addr_t addr)</div><div class="ttdoc">automatically tries to reconnect to the indicated address</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:94</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_ac7131b626b43a516ae4ae9df6a7ec366"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#ac7131b626b43a516ae4ae9df6a7ec366">BluetoothA2DPSource::bt_app_av_state_connecting_hdlr</a></div><div class="ttdeci">virtual void bt_app_av_state_connecting_hdlr(uint16_t event, void *param)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:644</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_ac77d0c29cc27815f703469aa0083439e"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#ac77d0c29cc27815f703469aa0083439e">BluetoothA2DPSource::get_data_cb</a></div><div class="ttdeci">int32_t(* get_data_cb)(uint8_t *data, int32_t len)</div><div class="ttdoc">callback for data</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:220</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_ac7cd3655a7e2cfbd7e6c3474a5f2bc34"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#ac7cd3655a7e2cfbd7e6c3474a5f2bc34">BluetoothA2DPSource::to_state_str</a></div><div class="ttdeci">const char * to_state_str(int state)</div><div class="ttdoc">converts a APP_AV_STATE_ENUM to a string</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:319</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_acb9c5182525261c53b803719b3027014"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#acb9c5182525261c53b803719b3027014">BluetoothA2DPSource::set_local_name</a></div><div class="ttdeci">virtual void set_local_name(const char *name)</div><div class="ttdoc">Defines the local name.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:100</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_ace5835661dbf0ecb5f09600a6bf90304"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#ace5835661dbf0ecb5f09600a6bf90304">BluetoothA2DPSource::start</a></div><div class="ttdeci">virtual void start(music_data_frames_cb_t callback)</div><div class="ttdoc">Obsolete: use the start w/o callback and set the callback separately.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:147</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_ad3bb1aaddd9dcbd9da6a37c5aded8727"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#ad3bb1aaddd9dcbd9da6a37c5aded8727">BluetoothA2DPSource::bt_app_av_sm_hdlr</a></div><div class="ttdeci">virtual void bt_app_av_sm_hdlr(uint16_t event, void *param)</div><div class="ttdoc">A2DP application state machine.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:576</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_ad42fbe4ec00846ab22157545b3885db9"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#ad42fbe4ec00846ab22157545b3885db9">BluetoothA2DPSource::set_data_callback_in_frames</a></div><div class="ttdeci">virtual void set_data_callback_in_frames(int32_t(cb)(Frame *data, int32_t len))</div><div class="ttdoc">Defines the data callback.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:108</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_ad6aac053cc521667ea15a87277009574"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#ad6aac053cc521667ea15a87277009574">BluetoothA2DPSource::start_raw</a></div><div class="ttdeci">virtual void start_raw(const char *name, music_data_cb_t callback=nullptr)</div><div class="ttdoc">Obsolete: use the start w/o callback and set the callback separately.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:160</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_ae9f14078c1d5dd00c93049fa8b2e283e"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#ae9f14078c1d5dd00c93049fa8b2e283e">BluetoothA2DPSource::bt_av_hdl_avrc_ct_evt</a></div><div class="ttdeci">virtual void bt_av_hdl_avrc_ct_evt(uint16_t event, void *p_param)</div><div class="ttdoc">avrc CT event handler</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:900</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_aef98c427f8e590be0a4dfaa28a5cb4fd"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#aef98c427f8e590be0a4dfaa28a5cb4fd">BluetoothA2DPSource::end</a></div><div class="ttdeci">void end(bool releaseMemory=false) override</div><div class="ttdoc">Ends the processing and releases the resources.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.cpp:141</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_af29d19e53e3585446fc294a3213a06af"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#af29d19e53e3585446fc294a3213a06af">BluetoothA2DPSource::is_discovery_active</a></div><div class="ttdeci">virtual bool is_discovery_active()</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:199</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_source_html_affb5e5c979a96e43a0cec7c4e0d0065d"><div class="ttname"><a href="class_bluetooth_a2_d_p_source.html#affb5e5c979a96e43a0cec7c4e0d0065d">BluetoothA2DPSource::start</a></div><div class="ttdeci">virtual void start(const char *name)</div><div class="ttdoc">Starts the A2DP source.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:131</div></div>
<div class="ttc" id="agroup__a2dp_html_gabf9f46a0805b93eedaeccb8e512ef7fa"><div class="ttname"><a href="group__a2dp.html#gabf9f46a0805b93eedaeccb8e512ef7fa">APP_AV_STATE</a></div><div class="ttdeci">APP_AV_STATE</div><div class="ttdoc">Buetooth A2DP global state.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSource.h:50</div></div>
<div class="ttc" id="agroup__a2dp_html_gae1f72542f04666cd97c26732366bf109"><div class="ttname"><a href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a></div><div class="ttdeci">uint8_t esp_bd_addr_t[ESP_BD_ADDR_LEN]</div><div class="ttdoc">Bluetooth address.</div><div class="ttdef"><b>Definition:</b> external_lists.h:107</div></div>
<div class="ttc" id="astructbt__app__msg__t_html"><div class="ttname"><a href="structbt__app__msg__t.html">bt_app_msg_t</a></div><div class="ttdoc">Internal message to be sent for BluetoothA2DPSink and BluetoothA2DPSource.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:124</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
