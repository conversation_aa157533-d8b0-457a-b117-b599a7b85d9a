/*
 *  ======== ti_drivers_config.h ========
 *  Configured TI-Drivers module declarations
 *
 *  The macros defines herein are intended for use by applications which
 *  directly include this header. These macros should NOT be hard coded or
 *  copied into library source code.
 *
 *  Symbols declared as const are intended for use with libraries.
 *  Library source code must extern the correct symbol--which is resolved
 *  when the application is linked.
 *
 *  DO NOT EDIT - This file is generated for the CC1312R1F3RGZ
 *  by the SysConfig tool.
 */
#ifndef ti_drivers_config_h
#define ti_drivers_config_h

#define CONFIG_SYSCONFIG_PREVIEW

#define CONFIG_CC1312R1F3RGZ
#ifndef DeviceFamily_CC13X2
#define DeviceFamily_CC13X2
#endif

#include <ti/devices/DeviceFamily.h>

#include <stdint.h>

/* support C++ sources */
#ifdef __cplusplus
extern "C" {
#endif


/*
 *  ======== CCFG ========
 */


/*
 *  ======== ADCBuf ========
 */

extern const uint_least8_t                  ADCBUF_CONST;
#define ADCBUF                              0
#define CONFIG_TI_DRIVERS_ADCBUF_COUNT      1

/*
 * ======== ADCBuf Channels ========
 */

/* DIO27 */
extern const uint_least8_t                          ADCBUF_SOUND_CONST;
#define ADCBUF_SOUND                                0
/* DIO29 */
extern const uint_least8_t                          ADCBUF_TEMPERATURE_CONST;
#define ADCBUF_TEMPERATURE                          1
/* DIO30 */
extern const uint_least8_t                          ADCBUF_BATTERY_VOLTAGE_CONST;
#define ADCBUF_BATTERY_VOLTAGE                      2
#define CONFIG_TI_DRIVERS_ADCBUF_CHANNEL_COUNT      3

/*
 *  ======== GPIO ========
 */

/* DIO18 */
extern const uint_least8_t              PIN_TEST1_CONST;
#define PIN_TEST1                       0
/* DIO19 */
extern const uint_least8_t              PIN_TEST2_CONST;
#define PIN_TEST2                       1
/* DIO5 */
extern const uint_least8_t              PIN_DRIVE_SPEAKER_A_CONST;
#define PIN_DRIVE_SPEAKER_A             2
/* DIO6 */
extern const uint_least8_t              PIN_DRIVE_SPEAKER_B_CONST;
#define PIN_DRIVE_SPEAKER_B             3
#define CONFIG_TI_DRIVERS_GPIO_COUNT    4

/* LEDs are active high */
#define CONFIG_GPIO_LED_ON  (1)
#define CONFIG_GPIO_LED_OFF (0)

#define CONFIG_LED_ON  (CONFIG_GPIO_LED_ON)
#define CONFIG_LED_OFF (CONFIG_GPIO_LED_OFF)


/*
 *  ======== PIN ========
 */
#include <ti/drivers/PIN.h>

extern const PIN_Config BoardGpioInitTable[];

/* Parent Signal: ADCBUF_SOUND ADCBuf Channel 0, (DIO27) */
#define CONFIG_PIN_4                   0x0000001b
/* Parent Signal: PIN_TEST1 GPIO Pin, (DIO18) */
#define CONFIG_PIN_0                   0x00000012
/* Parent Signal: PIN_TEST2 GPIO Pin, (DIO19) */
#define CONFIG_PIN_1                   0x00000013
/* Parent Signal: PIN_DRIVE_SPEAKER_A GPIO Pin, (DIO5) */
#define CONFIG_PIN_2                   0x00000005
/* Parent Signal: PIN_DRIVE_SPEAKER_B GPIO Pin, (DIO6) */
#define CONFIG_PIN_3                   0x00000006
/* Parent Signal: UART_0 TX, (DIO3) */
#define CONFIG_PIN_5                   0x00000003
/* Parent Signal: UART_0 RX, (DIO2) */
#define CONFIG_PIN_6                   0x00000002
/* Parent Signal: ADCBUF_TEMPERATURE ADCBuf Channel 1, (DIO29) */
#define CONFIG_PIN_7                   0x0000001d
/* Parent Signal: ADCBUF_BATTERY_VOLTAGE ADCBuf Channel 2, (DIO30) */
#define CONFIG_PIN_8                   0x0000001e
#define CONFIG_TI_DRIVERS_PIN_COUNT    9




/*
 *  ======== UART ========
 */

/*
 *  TX: DIO3
 *  RX: DIO2
 */
extern const uint_least8_t              UART_0_CONST;
#define UART_0                          0
#define CONFIG_TI_DRIVERS_UART_COUNT    1


/*
 *  ======== GPTimer ========
 */

extern const uint_least8_t                  CONFIG_GPTIMER_0_CONST;
#define CONFIG_GPTIMER_0                    0
extern const uint_least8_t                  CONFIG_GPTIMER_1_CONST;
#define CONFIG_GPTIMER_1                    1
#define CONFIG_TI_DRIVERS_GPTIMER_COUNT     2


/*
 *  ======== Board_init ========
 *  Perform all required TI-Drivers initialization
 *
 *  This function should be called once at a point before any use of
 *  TI-Drivers.
 */
extern void Board_init(void);

/*
 *  ======== Board_initGeneral ========
 *  (deprecated)
 *
 *  Board_initGeneral() is defined purely for backward compatibility.
 *
 *  All new code should use Board_init() to do any required TI-Drivers
 *  initialization _and_ use <Driver>_init() for only where specific drivers
 *  are explicitly referenced by the application.  <Driver>_init() functions
 *  are idempotent.
 */
#define Board_initGeneral Board_init

#ifdef __cplusplus
}
#endif

#endif /* include guard */
