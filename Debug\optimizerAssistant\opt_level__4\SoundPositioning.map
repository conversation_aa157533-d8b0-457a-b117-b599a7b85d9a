******************************************************************************
                  TI ARM Linker PC v20.2.4                     
******************************************************************************
>> Linked Mon Feb 22 19:52:18 2021

OUTPUT FILE NAME:   <SoundPositioning.out>
ENTRY POINT SYMBOL: "resetISR"  address: 0000463d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00058000  00004c80  00053380  R  X
  GPRAM                 11000000   00002000  00000000  00002000  RW X
  SRAM                  20000000   00014000  00004f46  0000f0ba  RW X
  LOG_DATA              90000000   00040000  00000000  00040000  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004b40   00004b40    r-x
  00000000    00000000    000000d8   000000d8    r-- .intvecs
  000000d8    000000d8    000047d0   000047d0    r-x .text
  000048a8    000048a8    00000298   00000298    r-- .const
00004b40    00004b40    00000008   00000008    rw-
  00004b40    00004b40    00000008   00000008    rw- .args
00004b48    00004b48    000000e0   000000e0    r--
  00004b48    00004b48    000000e0   000000e0    r-- .cinit
00057fa8    00057fa8    00000058   00000058    r--
  00057fa8    00057fa8    00000058   00000058    r-- .ccfg
20000000    20000000    00000a4e   00000000    rw-
  20000000    20000000    00000668   00000000    rw- .bss
  20000668    20000668    000003e6   00000000    rw- .data
20000b00    20000b00    000000d8   00000000    rw-
  20000b00    20000b00    000000d8   00000000    rw- .vtable_ram
20001870    20001870    00000010   00000000    rw-
  20001870    20001870    00000010   00000000    rw- .TI.bound:dmaADCPriControlTableEntry
20001a70    20001a70    00004010   00000000    rw-
  20001a70    20001a70    00000010   00000000    rw- .TI.bound:dmaADCAltControlTableEntry
  20001a80    20001a80    00004000   00000000    rw- .sysmem
20013c00    20013c00    00000400   00000000    rw-
  20013c00    20013c00    00000400   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000d8     
                  00000000    000000d8     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.intvecs)

.text      0    000000d8    000047d0     
                  000000d8    000001e0     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_init)
                  000002b8    00000198                      : PowerCC26X2.oem4f (.text:Power_sleep)
                  00000450    00000190                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf1)
                  000005e0    0000018c                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:runCalibrateFsm)
                  0000076c    00000174                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_open)
                  000008e0    00000174                      : PowerCC26X2.oem4f (.text:Power_init)
                  00000a54    00000164                      : UARTCC26XX.oem4f (.text:UARTCC26XX_open)
                  00000bb8    00000158     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlStandby)
                  00000d10    00000154     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_hwiIntFxn)
                  00000e64    0000013a                      : UARTCC26XX.oem4f (.text:UARTCC26XX_read)
                  00000f9e    00000002     ti_drivers_config.obj (.text:Board_initHook)
                  00000fa0    00000120     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureXOSCHF)
                  000010c0    00000114                      : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfg)
                  000011d4    00000110                      : GPIOCC26XX.oem4f (.text:GPIO_setConfig)
                  000012e4    00000100                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convert)
                  000013e4    000000fc     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:aligned_alloc)
                  000014e0    000000f8     mainNew.obj (.text:main)
                  000015d8    000000e0     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_standbyPolicy)
                  000016b8    000000e0     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_write)
                  00001798    000000e0     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free)
                  00001878    000000d8     driverlib.lib : setup.obj (.text:NOROM_SetupTrimDevice)
                  00001950    000000d8     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_construct)
                  00001a28    000000d8     driverlib.lib : setup.obj (.text:TrimAfterColdResetWakeupFromShutDown$9)
                  00001b00    000000c4     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_init)
                  00001bc4    000000c0                      : PowerCC26X2.oem4f (.text:Power_releaseDependency)
                  00001c84    000000c0                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initIO)
                  00001d44    000000bc     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_construct)
                  00001e00    000000b8     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_setDependency)
                  00001eb8    000000b8                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initHw)
                  00001f70    000000b4     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_startup)
                  00002024    000000ac                     : ClockPTimer_nortos.oem4f (.text:ClockP_workFuncDynamic)
                  000020d0    000000ac     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_open)
                  0000217c    000000a0     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_pend)
                  0000221c    0000009c                     : SwiP_nortos.oem4f (.text:SwiP_dispatch)
                  000022b8    0000009c     rtsv7M4_T_le_v4SPD16_eabi.lib : memcpy_t2.asm.obj (.text)
                  00002354    00000098     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeTxFifoFlush)
                  000023ec    00000096     rtsv7M4_T_le_v4SPD16_eabi.lib : ull_div_t2.asm.obj (.text)
                  00002482    00000092     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_writeCancel)
                  00002514    00000088                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_open)
                  0000259c    00000088     driverlib.lib : osc.obj (.text:NOROM_OSCHF_AttemptToSwitchToXosc)
                  00002624    00000088     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf2)
                  000026ac    00000088                      : PowerCC26X2.oem4f (.text:disableLFClockQualifiers)
                  00002734    00000088                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:updateSubSecInc)
                  000027bc    00000084                      : UARTCC26XX.oem4f (.text:UARTCC26XX_close)
                  00002840    00000080                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_hwiFxn)
                  000028c0    00000080     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_start)
                  00002940    00000080                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_construct)
                  000029c0    0000007a     rtsv7M4_T_le_v4SPD16_eabi.lib : memset_t2.asm.obj (.text)
                  00002a3a    00000002     driverlib.lib : interrupt.obj (.text:IntDefaultHandler$7)
                  00002a3c    00000078     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_walkQueueDynamic)
                  00002ab4    00000074     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_resetHw)
                  00002b28    00000074                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_auxISR)
                  00002b9c    00000070                      : PINCC26XX.oem4f (.text:PIN_add)
                  00002c0c    00000070     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:split$17)
                  00002c7c    0000006c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipType)
                  00002ce8    00000068                   : osc.obj (.text:NOROM_OSCHF_SwitchToRcOscTurnOffXosc)
                  00002d50    00000068     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_post)
                  00002db8    00000068     SoundTX.obj (.text:Timer2AInterruptHandler)
                  00002e20    00000068     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_start)
                  00002e88    00000068     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_lzss.c.obj (.text:decompress:lzss:__TI_decompress_lzss)
                  00002ef0    00000064     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_control)
                  00002f54    00000062     ti_drivers_config.obj (.text:Board_sendExtFlashByte)
                  00002fb6    00000062     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_swiIntFxn)
                  00003018    00000060                      : GPIOCC26XX.oem4f (.text:GPIO_write)
                  00003078    0000005c                      : PINCC26XX.oem4f (.text:PIN_remove)
                  000030d4    0000005c                      : UARTCC26XX.oem4f (.text:startTxFifoEmptyClk)
                  00003130    00000058                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_loadDMAControlTableEntry)
                  00003188    00000058                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_stop)
                  000031e0    00000058                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_initiateCalibration)
                  00003238    00000054                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_adjustRawValues)
                  0000328c    00000054     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOff)
                  000032e0    00000050     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_construct)
                  00003330    0000004e     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_close)
                  0000337e    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_destruct)
                  00003380    0000004c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_cleanADC)
                  000033cc    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_initHw)
                  00003418    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_start)
                  00003464    0000004c     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOn)
                  000034b0    0000004c     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readCancel)
                  000034fc    0000004c                      : UDMACC26XX.oem4f (.text:UDMACC26XX_open)
                  00003548    0000004a                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_control)
                  00003592    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.text:_nop)
                  00003594    00000048     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertAdjustedToMicroVolts)
                  000035dc    00000048     ti_drivers_config.obj (.text:Board_wakeUpExtFlash)
                  00003624    00000048     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_construct)
                  0000366c    00000048     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputEnable)
                  000036b4    00000048                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_RCOSC_clockFunc)
                  000036fc    00000048                      : PowerCC26X2.oem4f (.text:switchXOSCHF)
                  00003744    00000048                      : UARTCC26XX.oem4f (.text:writeFinishedDoCallback)
                  0000378c    00000046                      : RingBuf.oem4f (.text:RingBuf_put)
                  000037d2    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:clkFxn)
                  000037d4    00000044     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_init)
                  00003818    00000044                      : PINCC26XX.oem4f (.text:PIN_swi)
                  0000385c    00000044     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_post)
                  000038a0    00000044                     : SwiP_nortos.oem4f (.text:SwiP_restore)
                  000038e4    00000044     drivers_cc13x2.a : UART.oem4f (.text:UART_init)
                  00003928    00000044     rtsv7M4_T_le_v4SPD16_eabi.lib : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit:__TI_auto_init_nobinit_nopinit)
                  0000396c    00000042     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_swiFxn)
                  000039ae    00000002                      : PowerCC26X2.oem4f (.text:emptyClockFunc)
                  000039b0    00000040                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertCancel)
                  000039f0    00000040     driverlib.lib : osc.obj (.text:NOROM_OSC_HPOSCRelativeFrequencyOffsetGet)
                  00003a30    00000040     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_get)
                  00003a70    00000040                      : UART.oem4f (.text:UART_open)
                  00003ab0    0000003c     mainNew.obj (.text:InitUart)
                  00003aec    0000003c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputValue)
                  00003b28    00000038                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_configDMA)
                  00003b60    00000038     ti_drivers_config.obj (.text:Board_shutDownExtFlash)
                  00003b98    00000038     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setConfig)
                  00003bd0    00000038     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCurrentTick)
                  00003c08    00000038                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_initDevice)
                  00003c40    00000038     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:notify)
                  00003c78    00000038                      : UARTCC26XX.oem4f (.text:readData)
                  00003cb0    00000038                      : UARTCC26XX.oem4f (.text:writeData)
                  00003ce8    00000036     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setNextTick)
                  00003d1e    00000036     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_close)
                  00003d54    00000034     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicksUntilInterrupt)
                  00003d88    00000034     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XXSetLoadMatch)
                  00003dbc    00000034                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_configureDebugStall)
                  00003df0    00000034     driverlib.lib : interrupt.obj (.text:NOROM_IntRegister)
                  00003e24    00000034                   : sys_ctrl.obj (.text:NOROM_SysCtrlIdle)
                  00003e58    00000034     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_destruct)
                  00003e8c    00000034     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:readData2RingBuf)
                  00003ec0    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_hwiIntFxn)
                  00003ef0    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_setCallback)
                  00003f20    00000030                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_close)
                  00003f50    00000030                      : PINCC26XX.oem4f (.text:PINCC26XX_setMux)
                  00003f80    00000030                      : PowerCC26X2.oem4f (.text:Power_releaseConstraint)
                  00003fb0    00000030                      : PowerCC26X2.oem4f (.text:Power_setConstraint)
                  00003fe0    00000030     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.text:_c_int00:_c_int00)
                  00004010    0000002c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_acquireADCSemaphore)
                  0000403c    0000002c                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_releaseADCSemaphore)
                  00004068    0000002c     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setThreshold)
                  00004094    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_registerNotify)
                  000040bc    00000028     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_insert$17)
                  000040e4    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:oscillatorISR)
                  0000410c    00000026                      : List.oem4f (.text:List_put)
                  00004132    00000026                      : List.oem4f (.text:List_remove)
                  00004158    00000024     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicks)
                  0000417c    00000024                     : ClockPTimer_nortos.oem4f (.text:ClockP_scheduleNextTick)
                  000041a0    00000024                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_dispatch)
                  000041c4    00000024     driverlib.lib : osc.obj (.text:NOROM_OSCHF_TurnOnXosc)
                  000041e8    00000024     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getMaxTicks)
                  0000420c    00000024     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:getTdcSemaphore)
                  00004230    00000020     ti_drivers_config.obj (.text:Board_init)
                  00004250    00000020     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_add)
                  00004270    00000020     driverlib.lib : aux_adc.obj (.text:NOROM_AUXADCEnableSync)
                  00004290    00000020     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfgMux)
                  000042b0    00000020                      : PowerCC26X2.oem4f (.text:Power_unregisterNotify)
                  000042d0    00000020     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_put)
                  000042f0    00000020                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_dynamicStub)
                  00004310    00000020     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:threadSafeStdbyDisRelease)
                  00004330    00000020                      : UARTCC26XX.oem4f (.text:threadSafeStdbyDisSet)
                  00004350    0000001e                      : PINCC26XX.oem4f (.text:PIN_close)
                  0000436e    0000001e                      : PowerCC26X2.oem4f (.text:Power_getTransitionLatency)
                  0000438c    0000001e     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_constructBinary)
                  000043aa    0000001e                     : SwiP_nortos.oem4f (.text:SwiP_or)
                  000043c8    0000001c                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_destruct)
                  000043e4    0000001c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipFamily)
                  00004400    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_hwi)
                  0000441c    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_disable)
                  00004438    0000001c     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_initHw)
                  00004454    0000001c     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_remove$17)
                  00004470    0000001a     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:PowerCC26XX_calibrate)
                  0000448a    0000001a     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_get)
                  000044a4    0000001a                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_createBinary)
                  000044be    00000018                     : ClockPTimer_nortos.oem4f (.text:ClockP_destruct)
                  000044d6    00000002                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:faultISR)
                  000044d8    00000018                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_inISR)
                  000044f0    00000018     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetPackageType)
                  00004508    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_idleFunc)
                  00004520    00000018     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_startup)
                  00004538    00000018     drivers_cc13x2.a : UART.oem4f (.text:UART_Params_init)
                  00004550    00000018     rtsv7M4_T_le_v4SPD16_eabi.lib : args_main.c.obj (.text:_args_main)
                  00004568    00000018                                   : ll_mul_t2.asm.obj (.text)
                  00004580    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:switchToTCXO)
                  00004598    00000016     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_create)
                  000045ae    00000016                     : SwiP_nortos.oem4f (.text:maxbit)
                  000045c4    00000014     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_freqToCounts)
                  000045d8    00000014     driverlib.lib : interrupt.obj (.text:NOROM_IntUnregister)
                  000045ec    00000014     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_calibrate)
                  00004600    00000014                      : PowerCC26X2.oem4f (.text:Power_disablePolicy)
                  00004614    00000014     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_handleHwi)
                  00004628    00000014                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCount64)
                  0000463c    00000014                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:resetISR)
                  00004650    00000012     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_Params_init)
                  00004662    00000012     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_Params_init)
                  00004674    00000012     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_construct)
                  00004686    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:intDefaultHandler)
                  00004688    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_Params_init)
                  00004698    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_getCpuFreq)
                  000046a8    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_clearInterrupt)
                  000046b8    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_disable)
                  000046c8    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_post)
                  000046d8    00000010     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_getPinCount)
                  000046e8    00000010     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerDisable)
                  000046f8    00000010     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_enablePolicy)
                  00004708    00000010     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_Params_init)
                  00004718    00000010                     : SwiP_nortos.oem4f (.text:SwiP_Params_init)
                  00004728    00000010                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_Params_init)
                  00004738    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:uartPostNotify)
                  00004748    0000000e     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_empty)
                  00004756    0000000e                     : QueueP_nortos.oem4f (.text:QueueP_remove)
                  00004764    0000000e     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_none.c.obj (.text:decompress:none:__TI_decompress_none)
                  00004772    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:nmiISR)
                  00004774    0000000c                     : ClockPTimer_nortos.oem4f (.text:ClockP_doTick)
                  00004780    0000000c                     : ClockPTimer_nortos.oem4f (.text:ClockP_getSystemTickPeriod)
                  0000478c    0000000c     driverlib.lib : cpu.obj (.text:NOROM_CPUcpsid)
                  00004798    0000000c                   : cpu.obj (.text:NOROM_CPUcpsie)
                  000047a4    0000000c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_registerIntCb)
                  000047b0    0000000c     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerRestore)
                  000047bc    0000000c     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_getConstraintMask)
                  000047c8    0000000c                      : PowerCC26X2.oem4f (.text:Power_getDependencyCount)
                  000047d4    0000000c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_getTrigger)
                  000047e0    0000000c                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getFreq)
                  000047ec    0000000c     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init)
                  000047f8    0000000a     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_convertCancel)
                  00004802    0000000a                      : UDMACC26XX.oem4f (.text:UDMACC26XX_hwiIntFxn)
                  0000480c    0000000a                      : UARTCC26XX.oem4f (.text:readSemCallback)
                  00004816    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_conversionCallback)
                  0000481e    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_init)
                  00004826    00000008                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_setLoadValue)
                  0000482e    00000008     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_restore)
                  00004836    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_init)
                  0000483e    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:malloc)
                  00004846    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeSemCallback)
                  0000484e    00000006     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_stop)
                  00004854    00000006     driverlib.lib : cpu.obj (.text:NOROM_CPUdelay)
                  0000485a    00000006     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_init)
                  00004860    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readPolling)
                  00004866    00000006                      : UARTCC26XX.oem4f (.text:UARTCC26XX_writePolling)
                  0000486c    00000006                      : PowerCC26X2.oem4f (.text:delayUs)
                  00004872    00000004                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_getResolution)
                  00004876    00000004     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:CPUwfi)
                  0000487a    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_isActive)
                  0000487e    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_setTimeout)
                  00004882    00000004                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_enable)
                  00004886    00000004                     : QueueP_nortos.oem4f (.text:QueueP_head)
                  0000488a    00000004                     : QueueP_nortos.oem4f (.text:QueueP_next)
                  0000488e    00000004                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_delete)
                  00004892    00000004     driverlib.lib : timer.obj (.text:TimerIntNumberGet$11)
                  00004896    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : pre_init.c.obj (.text:_system_pre_init)
                  0000489a    00000004                                   : exit.c.obj (.text:abort:abort)
                  0000489e    00000004     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureRFCoreClocks)
                  000048a2    00000004                      : PowerCC26X2.oem4f (.text:nopResourceHandler)
                  000048a6    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : div0.asm.obj (.text)

.const     0    000048a8    00000298     
                  000048a8    00000054     drivers_cc13x2.a : PowerCC26X2.oem4f (.const:resourceDB)
                  000048fc    00000044     ti_drivers_config.obj (.const:$O1$$)
                  00004940    00000028     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:UARTCC26XX_fxnTable)
                  00004968    00000028     ti_drivers_config.obj (.const:uartCC26XXHWAttrs)
                  00004990    00000024     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.const:ADCBufCC26X2_fxnTable)
                  000049b4    00000024                      : UART.oem4f (.const:UART_defaultParams)
                  000049d8    0000001c                      : GPTimerCC26XX.oem4f (.const:GPT_LUT)
                  000049f4    00000018     ti_drivers_config.obj (.const:GPTimerCC26XX_config)
                  00004a0c    00000018     ti_drivers_config.obj (.const:gptimerCC26XXHWAttrs)
                  00004a24    00000018     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:interruptType)
                  00004a3c    00000014     ti_drivers_config.obj (.const:GPIOCC26XX_config)
                  00004a50    00000014     ti_drivers_config.obj (.const:PowerCC26X2_config)
                  00004a64    00000014     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:parityType)
                  00004a78    00000010     nortos_cc13x2.a : SwiP_nortos.oem4f (.const:SwiP_defaultParams)
                  00004a88    00000010     ti_drivers_config.obj (.const:adcbufCC26XXHWAttrs)
                  00004a98    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:dataLength)
                  00004aa8    00000010                      : GPIOCC26XX.oem4f (.const:outPinTypes)
                  00004ab8    0000000e     ti_drivers_config.obj (.const)
                  00004ac6    00000002     --HOLE-- [fill = 0]
                  00004ac8    0000000c     ti_drivers_config.obj (.const:ADCBuf_config)
                  00004ad4    0000000c     ti_drivers_config.obj (.const:UART_config)
                  00004ae0    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:inPinTypes)
                  00004aec    0000000c                      : GPIOCC26XX.oem4f (.const:outPinStrengths)
                  00004af8    00000008     driverlib.lib : aux_sysif.obj (.const:$O7$$)
                  00004b00    00000008     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.const:GPT_DefaultParams)
                  00004b08    00000008     ti_drivers_config.obj (.const:PINCC26XX_hwAttrs)
                  00004b10    00000008     ti_drivers_config.obj (.const:UDMACC26XX_config)
                  00004b18    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:stopBits)
                  00004b20    00000008     ti_drivers_config.obj (.const:udmaCC26XXHWAttrs)
                  00004b28    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:.string:rxFifoBytes)
                  00004b2e    00000006                      : UARTCC26XX.oem4f (.const:.string:rxFifoThreshold)
                  00004b34    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoBytes)
                  00004b3a    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoThreshold)

.binit     0    00000000    00000000     

.cinit     0    00004b48    000000e0     
                  00004b48    00000085     (.cinit..data.load) [load image, compression = lzss]
                  00004bcd    00000003     --HOLE-- [fill = 0]
                  00004bd0    0000000c     (__TI_handler_table)
                  00004bdc    00000004     --HOLE-- [fill = 0]
                  00004be0    00000008     (.cinit..TI.bound:dmaADCAltControlTableEntry.load) [load image, compression = zero_init]
                  00004be8    00000008     (.cinit..TI.bound:dmaADCPriControlTableEntry.load) [load image, compression = zero_init]
                  00004bf0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004bf8    00000008     (.cinit..vtable_ram.load) [load image, compression = zero_init]
                  00004c00    00000028     (__TI_cinit_table)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.ccfg      0    00057fa8    00000058     
                  00057fa8    00000058     ti_devices_config.obj (.ccfg:retain)

.bss       0    20000000    00000668     UNINITIALIZED
                  20000000    000001b4     (.common:uartCC26XXObjects)
                  200001b4    000000dc     (.common:adcbufCC26XXbjects)
                  20000290    000000a0     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:ClockP_module)
                  20000330    00000098     (.common:gptimerCC26XXObjects)
                  200003c8    0000007c     (.common:pinHandleTable)
                  20000444    00000070     mainNew.obj (.bss:$O3$$)
                  200004b4    00000050     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinSemaphore)
                  20000504    00000034                      : PINCC26XX.oem4f (.bss:pinSwi)
                  20000538    00000020     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_readyList)
                  20000558    00000020     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioCallbackInfo)
                  20000578    00000020                      : PINCC26XX.oem4f (.bss:pinGpioConfigTable)
                  20000598    00000020     ti_drivers_config.obj (.bss:uartCC26XXRingBuffer0)
                  200005b8    00000020     (.common:udmaCC26XXObject)
                  200005d8    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_hwiStruct)
                  200005f4    0000001c                     : TimerPCC26XX_nortos.oem4f (.bss:TimerP_hwiStruct)
                  20000610    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinHwi)
                  2000062c    0000000d     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss)
                  20000639    00000001     (.common:driverlib_release_0_59848)
                  2000063a    00000002     --HOLE--
                  2000063c    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioPinState)
                  20000648    00000008                      : GPIOCC26XX.oem4f (.bss)
                  20000650    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:timerFreq)
                  20000658    00000004     drivers_cc13x2.a : PINCC26XX.oem4f (.bss)
                  2000065c    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss)
                  20000660    00000004                     : PowerCC26X2_nortos.oem4f (.bss)
                  20000664    00000004                     : TimerPCC26XX_nortos.oem4f (.bss)

.vtable_ram 
*          0    20000b00    000000d8     UNINITIALIZED
                  20000b00    000000d8     driverlib.lib : interrupt.obj (.vtable_ram)

.data      0    20000668    000003e6     UNINITIALIZED
                  20000668    00000170     drivers_cc13x2.a : PowerCC26X2.oem4f (.data:PowerCC26X2_module)
                  200007d8    00000100     SoundTX.obj (.data:$O2$$)
                  200008d8    000000d8     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data:HwiP_dispatchTable)
                  200009b0    00000024     driverlib.lib : osc.obj (.data:$O5$$)
                  200009d4    00000010     ti_drivers_config.obj (.data:gpioCallbackFunctions)
                  200009e4    00000010     ti_drivers_config.obj (.data:gpioPinConfigs)
                  200009f4    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data:ClockP_defaultParams)
                  20000a00    0000000c                     : TimerPCC26XX_nortos.oem4f (.data:TimerP_defaultParams)
                  20000a0c    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.data:$O8$$)
                  20000a14    00000008                                   : _lock.c.obj (.data:$O9$$)
                  20000a1c    00000008     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data:SemaphoreP_defaultParams)
                  20000a24    00000008     drivers_cc13x2.a : PINCC26XX.oem4f (.data)
                  20000a2c    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data)
                  20000a34    00000006     ti_drivers_config.obj (.data:adcBufCC26XXChannelLut0)
                  20000a3a    00000001     drivers_cc13x2.a : ADCBuf.oem4f (.data)
                  20000a3b    00000001                      : UART.oem4f (.data)
                  20000a3c    00000005                      : GPIOCC26XX.oem4f (.data)
                  20000a41    00000003     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data)
                  20000a44    00000004     drivers_cc13x2.a : GPIOCC26XX.oem4f (.data:gpioPinTable)
                  20000a48    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data)
                  20000a4c    00000001                     : SwiP_nortos.oem4f (.data)
                  20000a4d    00000001                     : TimerPCC26XX_nortos.oem4f (.data)

.sysmem    0    20001a80    00004000     UNINITIALIZED
                  20001a80    00000010     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.sysmem)
                  20001a90    00003ff0     --HOLE--

.stack     0    20013c00    00000400     UNINITIALIZED
                  20013c00    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.stack)
                  20013c04    000003fc     --HOLE--

.log_data 
*          0    90000000    00000000     COPY SECTION

__llvm_prf_cnts 
*          0    20000000    00000000     UNINITIALIZED

.TI.bound:dmaADCPriControlTableEntry 
*          0    20001870    00000010     UNINITIALIZED
                  20001870    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCPriControlTableEntry)

.TI.bound:dmaADCAltControlTableEntry 
*          0    20001a70    00000010     UNINITIALIZED
                  20001a70    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCAltControlTableEntry)

.args      0    00004b40    00000008     
                  00004b40    00000008     --HOLE-- [fill = 0]

MODULE SUMMARY

       Module                                   code    ro data   rw data
       ------                                   ----    -------   -------
    .\
       mainNew.obj                              308     0         112    
       SoundTX.obj                              104     0         256    
    +--+----------------------------------------+-------+---------+---------+
       Total:                                   412     0         368    
                                                                         
    .\syscfg\
       ti_drivers_config.obj                    260     274       910    
       ti_devices_config.obj                    0       88        0      
    +--+----------------------------------------+-------+---------+---------+
       Total:                                   260     362       910    
                                                                         
    C:\Users\<USER>\AppData\Local\Temp\
       {90DC24A6-EE3A-4667-8BA1-B363B24449C6}   0       0         1      
    +--+----------------------------------------+-------+---------+---------+
       Total:                                   0       0         1      
                                                                         
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos/lib/ccs/m4f/nortos_cc13x2.a
       ClockPTimer_nortos.oem4f                 922     0         192    
       SwiP_nortos.oem4f                        724     16        74     
       TimerPCC26XX_nortos.oem4f                642     0         45     
       HwiPCC26XX_nortos.oem4f                  294     0         220    
       SemaphoreP_nortos.oem4f                  410     0         11     
       PowerCC26X2_nortos.oem4f                 256     0         4      
       startup_cc13x2_cc26x2_ccs.oem4f          26      216       0      
       QueueP_nortos.oem4f                      100     0         0      
    +--+----------------------------------------+-------+---------+---------+
       Total:                                   3374    232       546    
                                                                         
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/cc13x2_cc26x2/driverlib/bin/ccs/driverlib.lib
       setup.obj                                432     0         0      
       sys_ctrl.obj                             396     0         0      
       osc.obj                                  340     0         36     
       interrupt.obj                            74      0         216    
       chipinfo.obj                             160     0         0      
       prcm.obj                                 160     0         0      
       aux_adc.obj                              32      0         0      
       cpu.obj                                  30      0         0      
       aux_sysif.obj                            0       8         0      
       timer.obj                                4       0         0      
    +--+----------------------------------------+-------+---------+---------+
       Total:                                   1628    8         252    
                                                                         
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/lib/ccs/m4f/drivers_cc13x2.a
       UARTCC26XX.oem4f                         2760    108       0      
       PowerCC26X2.oem4f                        2096    84        368    
       PINCC26XX.oem4f                          1554    0         328    
       ADCBufCC26X2.oem4f                       1542    36        32     
       PowerCC26X2_calibrateRCOSC.oem4f         1400    0         0      
       GPIOCC26XX.oem4f                         660     64        61     
       GPTimerCC26XX.oem4f                      670     36        0      
       UART.oem4f                               156     36        1      
       UDMACC26XX.oem4f                         168     0         0      
       RingBuf.oem4f                            152     0         0      
       ADCBuf.oem4f                             78      0         1      
       List.oem4f                               76      0         0      
    +--+----------------------------------------+-------+---------+---------+
       Total:                                   11312   364       791    
                                                                         
    C:\ti\ccs1011\ccs\tools\compiler\ti-cgt-arm_20.2.4.LTS\lib\rtsv7M4_T_le_v4SPD16_eabi.lib
       memory.c.obj                             664     0         8      
       memcpy_t2.asm.obj                        156     0         0      
       ull_div_t2.asm.obj                       150     0         0      
       memset_t2.asm.obj                        122     0         0      
       copy_decompress_lzss.c.obj               104     0         0      
       autoinit.c.obj                           68      0         0      
       boot_cortex_m.c.obj                      48      0         0      
       args_main.c.obj                          24      0         0      
       ll_mul_t2.asm.obj                        24      0         0      
       copy_decompress_none.c.obj               14      0         0      
       copy_zero_init.c.obj                     12      0         0      
       _lock.c.obj                              2       0         8      
       exit.c.obj                               4       0         0      
       pre_init.c.obj                           4       0         0      
       div0.asm.obj                             2       0         0      
    +--+----------------------------------------+-------+---------+---------+
       Total:                                   1398    0         16     
                                                                         
       Heap:                                    0       0         16384  
       Stack:                                   0       0         1024   
       Linker Generated:                        0       217       0      
    +--+----------------------------------------+-------+---------+---------+
       Grand Total:                             18384   1183      20292  


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004c00 records: 5, size/record: 8, table size: 40
	.data: load addr=00004b48, load size=00000085 bytes, run addr=20000668, run size=000003e6 bytes, compression=lzss
	.TI.bound:dmaADCAltControlTableEntry: load addr=00004be0, load size=00000008 bytes, run addr=20001a70, run size=00000010 bytes, compression=zero_init
	.TI.bound:dmaADCPriControlTableEntry: load addr=00004be8, load size=00000008 bytes, run addr=20001870, run size=00000010 bytes, compression=zero_init
	.bss: load addr=00004bf0, load size=00000008 bytes, run addr=20000000, run size=00000668 bytes, compression=zero_init
	.vtable_ram: load addr=00004bf8, load size=00000008 bytes, run addr=20000b00, run size=000000d8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004bd0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_lzss
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                     
-------   ----                                     
00004abb  ADCBUF_BATTERY_VOLTAGE_CONST             
00004ab8  ADCBUF_CONST                             
00004ab9  ADCBUF_SOUND_CONST                       
00004aba  ADCBUF_TEMPERATURE_CONST                 
00003239  ADCBufCC26X2_adjustRawValues             
00003331  ADCBufCC26X2_close                       
00003549  ADCBufCC26X2_control                     
000012e5  ADCBufCC26X2_convert                     
00003595  ADCBufCC26X2_convertAdjustedToMicroVolts 
000039b1  ADCBufCC26X2_convertCancel               
00004990  ADCBufCC26X2_fxnTable                    
00004873  ADCBufCC26X2_getResolution               
0000481f  ADCBufCC26X2_init                        
0000076d  ADCBufCC26X2_open                        
00004ac8  ADCBuf_config                            
000047f9  ADCBuf_convertCancel                     
00004abc  ADCBuf_count                             
000037d5  ADCBuf_init                              
00004918  BoardGpioInitTable                       
00004231  Board_init                               
00000f9f  Board_initHook                           
00002f55  Board_sendExtFlashByte                   
00003b61  Board_shutDownExtFlash                   
000035dd  Board_wakeUpExtFlash                     
0000489b  C$$EXIT                                  
00004ac3  CONFIG_GPTIMER_0_CONST                   
00004ac4  CONFIG_GPTIMER_1_CONST                   
200007e0  ChirpDelay                               
200007dc  ChirpIndex                               
200007d8  ChirpSize                                
2000085c  ChirpState                               
00004689  ClockP_Params_init                       
00004251  ClockP_add                               
00003625  ClockP_construct                         
000044bf  ClockP_destruct                          
00004775  ClockP_doTick                            
00004699  ClockP_getCpuFreq                        
00004781  ClockP_getSystemTickPeriod               
00004159  ClockP_getTicks                          
00003d55  ClockP_getTicksUntilInterrupt            
0000487b  ClockP_isActive                          
0000417d  ClockP_scheduleNextTick                  
0000487f  ClockP_setTimeout                        
000028c1  ClockP_start                             
00001f71  ClockP_startup                           
0000484f  ClockP_stop                              
20000a30  ClockP_tickPeriod                        
00002a3d  ClockP_walkQueueDynamic                  
00002025  ClockP_workFuncDynamic                   
00004a3c  GPIOCC26XX_config                        
00003ec1  GPIO_hwiIntFxn                           
00001b01  GPIO_init                                
00003ef1  GPIO_setCallback                         
000011d5  GPIO_setConfig                           
00003019  GPIO_write                               
00004651  GPTimerCC26XX_Params_init                
00003f21  GPTimerCC26XX_close                      
000049f4  GPTimerCC26XX_config                     
00003dbd  GPTimerCC26XX_configureDebugStall        
00002515  GPTimerCC26XX_open                       
00004827  GPTimerCC26XX_setLoadValue               
00003419  GPTimerCC26XX_start                      
00003189  GPTimerCC26XX_stop                       
00004ac5  GPTimer_count                            
00004663  HwiP_Params_init                         
000046a9  HwiP_clearInterrupt                      
00002941  HwiP_construct                           
000043c9  HwiP_destruct                            
000046b9  HwiP_disable                             
00004883  HwiP_enable                              
000044d9  HwiP_inISR                               
000046c9  HwiP_post                                
0000482f  HwiP_restore                             
20000a48  HwiP_swiPIntNum                          
UNDEFED   ITM_flush                                
UNDEFED   ITM_restore                              
00003ab1  InitUart                                 
0000410d  List_put                                 
00004133  List_remove                              
00004271  NOROM_AUXADCEnableSync                   
0000478d  NOROM_CPUcpsid                           
00004799  NOROM_CPUcpsie                           
00004855  NOROM_CPUdelay                           
000043e5  NOROM_ChipInfo_GetChipFamily             
00002c7d  NOROM_ChipInfo_GetChipType               
000044f1  NOROM_ChipInfo_GetPackageType            
00003df1  NOROM_IntRegister                        
000045d9  NOROM_IntUnregister                      
0000259d  NOROM_OSCHF_AttemptToSwitchToXosc        
00002ce9  NOROM_OSCHF_SwitchToRcOscTurnOffXosc     
000041c5  NOROM_OSCHF_TurnOnXosc                   
000039f1  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet
0000328d  NOROM_PRCMPowerDomainsAllOff             
00003465  NOROM_PRCMPowerDomainsAllOn              
00001879  NOROM_SetupTrimDevice                    
00003e25  NOROM_SysCtrlIdle                        
00000bb9  NOROM_SysCtrlStandby                     
00004883  NoRTOS_start                             
000046d9  PINCC26XX_getPinCount                    
00004b08  PINCC26XX_hwAttrs                        
00003f51  PINCC26XX_setMux                         
00004abf  PIN_DRIVE_SPEAKER_A_CONST                
00004ac0  PIN_DRIVE_SPEAKER_B_CONST                
00004abd  PIN_TEST1_CONST                          
00004abe  PIN_TEST2_CONST                          
00002b9d  PIN_add                                  
00004351  PIN_close                                
000000d9  PIN_init                                 
000020d1  PIN_open                                 
000047a5  PIN_registerIntCb                        
00003079  PIN_remove                               
00003b99  PIN_setConfig                            
0000366d  PIN_setOutputEnable                      
00003aed  PIN_setOutputValue                       
000036b5  PowerCC26X2_RCOSC_clockFunc              
00002b29  PowerCC26X2_auxISR                       
000045ed  PowerCC26X2_calibrate                    
00004a50  PowerCC26X2_config                       
000031e1  PowerCC26X2_initiateCalibration          
20000668  PowerCC26X2_module                       
00004471  PowerCC26XX_calibrate                    
000046e9  PowerCC26XX_schedulerDisable             
000047b1  PowerCC26XX_schedulerRestore             
000015d9  PowerCC26XX_standbyPolicy                
00004601  Power_disablePolicy                      
000046f9  Power_enablePolicy                       
000047bd  Power_getConstraintMask                  
000047c9  Power_getDependencyCount                 
0000436f  Power_getTransitionLatency               
00004509  Power_idleFunc                           
000008e1  Power_init                               
00004095  Power_registerNotify                     
00003f81  Power_releaseConstraint                  
00001bc5  Power_releaseDependency                  
00003fb1  Power_setConstraint                      
00001e01  Power_setDependency                      
000002b9  Power_sleep                              
000042b1  Power_unregisterNotify                   
00004749  QueueP_empty                             
0000448b  QueueP_get                               
00004887  QueueP_head                              
0000485b  QueueP_init                              
0000488b  QueueP_next                              
000042d1  QueueP_put                               
00004757  QueueP_remove                            
00004675  RingBuf_construct                        
00003a31  RingBuf_get                              
0000378d  RingBuf_put                              
00004709  SemaphoreP_Params_init                   
000032e1  SemaphoreP_construct                     
0000438d  SemaphoreP_constructBinary               
00004599  SemaphoreP_create                        
000044a5  SemaphoreP_createBinary                  
20000a1c  SemaphoreP_defaultParams                 
0000488f  SemaphoreP_delete                        
0000337f  SemaphoreP_destruct                      
0000217d  SemaphoreP_pend                          
0000385d  SemaphoreP_post                          
00004719  SwiP_Params_init                         
00001951  SwiP_construct                           
00003e59  SwiP_destruct                            
0000441d  SwiP_disable                             
0000221d  SwiP_dispatch                            
000047d5  SwiP_getTrigger                          
000043ab  SwiP_or                                  
00002d51  SwiP_post                                
000038a1  SwiP_restore                             
00002db9  Timer2AInterruptHandler                  
00004729  TimerP_Params_init                       
00001d45  TimerP_construct                         
000042f1  TimerP_dynamicStub                       
00004629  TimerP_getCount64                        
00003bd1  TimerP_getCurrentTick                    
000047e1  TimerP_getFreq                           
000041e9  TimerP_getMaxTicks                       
00003c09  TimerP_initDevice                        
00003ce9  TimerP_setNextTick                       
00004069  TimerP_setThreshold                      
00002e21  TimerP_start                             
00004521  TimerP_startup                           
000027bd  UARTCC26XX_close                         
00002ef1  UARTCC26XX_control                       
00004940  UARTCC26XX_fxnTable                      
00000d11  UARTCC26XX_hwiIntFxn                     
00004837  UARTCC26XX_init                          
00000a55  UARTCC26XX_open                          
00000e65  UARTCC26XX_read                          
000034b1  UARTCC26XX_readCancel                    
00004861  UARTCC26XX_readPolling                   
00002fb7  UARTCC26XX_swiIntFxn                     
000016b9  UARTCC26XX_write                         
00002483  UARTCC26XX_writeCancel                   
00004867  UARTCC26XX_writePolling                  
00004ac1  UART_0_CONST                             
00004539  UART_Params_init                         
00004ad4  UART_config                              
00004ac2  UART_count                               
000049b4  UART_defaultParams                       
000038e5  UART_init                                
00003a71  UART_open                                
20000450  UARTbuffer                               
00003d1f  UDMACC26XX_close                         
00004b10  UDMACC26XX_config                        
00004803  UDMACC26XX_hwiIntFxn                     
000034fd  UDMACC26XX_open                          
20014000  __STACK_END                              
00000400  __STACK_SIZE                             
00004000  __SYSMEM_SIZE                            
00004c00  __TI_CINIT_Base                          
00004c28  __TI_CINIT_Limit                         
00004bd0  __TI_Handler_Table_Base                  
00004bdc  __TI_Handler_Table_Limit                 
00003929  __TI_auto_init_nobinit_nopinit           
00002e89  __TI_decompress_lzss                     
00004765  __TI_decompress_none                     
ffffffff  __TI_pprof_out_hndl                      
ffffffff  __TI_prof_data_size                      
ffffffff  __TI_prof_data_start                     
00000000  __TI_static_base__                       
000047ed  __TI_zero_init                           
000048a7  __aeabi_idiv0                            
000048a7  __aeabi_ldiv0                            
00004569  __aeabi_lmul                             
000029c1  __aeabi_memclr                           
000029c1  __aeabi_memclr4                          
000029c1  __aeabi_memclr8                          
000022b9  __aeabi_memcpy                           
000022b9  __aeabi_memcpy4                          
000022b9  __aeabi_memcpy8                          
000029c3  __aeabi_memset                           
000029c3  __aeabi_memset4                          
000029c3  __aeabi_memset8                          
000023ed  __aeabi_uldivmod                         
ffffffff  __binit__                                
00004b40  __c_args__                               
00057fa8  __ccfg                                   
UNDEFED   __mpu_init                               
20013c00  __stack                                  
20000000  __start___llvm_prf_cnts                  
20000000  __stop___llvm_prf_cnts                   
00004551  _args_main                               
00003fe1  _c_int00                                 
200009b0  _hposcCoeffs                             
20000a14  _lock                                    
00003593  _nop                                     
20001a80  _sys_memory                              
UNDEFED   _system_post_cinit                       
00004897  _system_pre_init                         
20000a18  _unlock                                  
0000489b  abort                                    
20000a34  adcBufCC26XXChannelLut0                  
00004a88  adcbufCC26XXHWAttrs                      
200001b4  adcbufCC26XXbjects                       
000013e5  aligned_alloc                            
ffffffff  binit                                    
000037d3  clkFxn                                   
20000639  driverlib_release_0_59848                
00001799  free                                     
20000b00  g_pfnRAMVectors                          
00000000  g_pfnVectors                             
200009d4  gpioCallbackFunctions                    
200009e4  gpioPinConfigs                           
00004a0c  gptimerCC26XXHWAttrs                     
20000330  gptimerCC26XXObjects                     
20000444  i                                        
00004ae0  inPinTypes                               
20000448  j                                        
000014e1  main                                     
0000483f  malloc                                   
000013e5  memalign                                 
000022b9  memcpy                                   
000029c9  memset                                   
00004aec  outPinStrengths                          
00004aa8  outPinTypes                              
200003c8  pinHandleTable                           
20000a28  pinLowerBound                            
20000a24  pinUpperBound                            
0000463d  resetISR                                 
000048a8  resourceDB                               
2000044c  uart                                     
20000000  uartCC26XXObjects                        
00004b20  udmaCC26XXHWAttrs                        
200005b8  udmaCC26XXObject                         


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                     
-------   ----                                     
00000000  __TI_static_base__                       
00000000  g_pfnVectors                             
000000d9  PIN_init                                 
000002b9  Power_sleep                              
00000400  __STACK_SIZE                             
0000076d  ADCBufCC26X2_open                        
000008e1  Power_init                               
00000a55  UARTCC26XX_open                          
00000bb9  NOROM_SysCtrlStandby                     
00000d11  UARTCC26XX_hwiIntFxn                     
00000e65  UARTCC26XX_read                          
00000f9f  Board_initHook                           
000011d5  GPIO_setConfig                           
000012e5  ADCBufCC26X2_convert                     
000013e5  aligned_alloc                            
000013e5  memalign                                 
000014e1  main                                     
000015d9  PowerCC26XX_standbyPolicy                
000016b9  UARTCC26XX_write                         
00001799  free                                     
00001879  NOROM_SetupTrimDevice                    
00001951  SwiP_construct                           
00001b01  GPIO_init                                
00001bc5  Power_releaseDependency                  
00001d45  TimerP_construct                         
00001e01  Power_setDependency                      
00001f71  ClockP_startup                           
00002025  ClockP_workFuncDynamic                   
000020d1  PIN_open                                 
0000217d  SemaphoreP_pend                          
0000221d  SwiP_dispatch                            
000022b9  __aeabi_memcpy                           
000022b9  __aeabi_memcpy4                          
000022b9  __aeabi_memcpy8                          
000022b9  memcpy                                   
000023ed  __aeabi_uldivmod                         
00002483  UARTCC26XX_writeCancel                   
00002515  GPTimerCC26XX_open                       
0000259d  NOROM_OSCHF_AttemptToSwitchToXosc        
000027bd  UARTCC26XX_close                         
000028c1  ClockP_start                             
00002941  HwiP_construct                           
000029c1  __aeabi_memclr                           
000029c1  __aeabi_memclr4                          
000029c1  __aeabi_memclr8                          
000029c3  __aeabi_memset                           
000029c3  __aeabi_memset4                          
000029c3  __aeabi_memset8                          
000029c9  memset                                   
00002a3d  ClockP_walkQueueDynamic                  
00002b29  PowerCC26X2_auxISR                       
00002b9d  PIN_add                                  
00002c7d  NOROM_ChipInfo_GetChipType               
00002ce9  NOROM_OSCHF_SwitchToRcOscTurnOffXosc     
00002d51  SwiP_post                                
00002db9  Timer2AInterruptHandler                  
00002e21  TimerP_start                             
00002e89  __TI_decompress_lzss                     
00002ef1  UARTCC26XX_control                       
00002f55  Board_sendExtFlashByte                   
00002fb7  UARTCC26XX_swiIntFxn                     
00003019  GPIO_write                               
00003079  PIN_remove                               
00003189  GPTimerCC26XX_stop                       
000031e1  PowerCC26X2_initiateCalibration          
00003239  ADCBufCC26X2_adjustRawValues             
0000328d  NOROM_PRCMPowerDomainsAllOff             
000032e1  SemaphoreP_construct                     
00003331  ADCBufCC26X2_close                       
0000337f  SemaphoreP_destruct                      
00003419  GPTimerCC26XX_start                      
00003465  NOROM_PRCMPowerDomainsAllOn              
000034b1  UARTCC26XX_readCancel                    
000034fd  UDMACC26XX_open                          
00003549  ADCBufCC26X2_control                     
00003593  _nop                                     
00003595  ADCBufCC26X2_convertAdjustedToMicroVolts 
000035dd  Board_wakeUpExtFlash                     
00003625  ClockP_construct                         
0000366d  PIN_setOutputEnable                      
000036b5  PowerCC26X2_RCOSC_clockFunc              
0000378d  RingBuf_put                              
000037d3  clkFxn                                   
000037d5  ADCBuf_init                              
0000385d  SemaphoreP_post                          
000038a1  SwiP_restore                             
000038e5  UART_init                                
00003929  __TI_auto_init_nobinit_nopinit           
000039b1  ADCBufCC26X2_convertCancel               
000039f1  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet
00003a31  RingBuf_get                              
00003a71  UART_open                                
00003ab1  InitUart                                 
00003aed  PIN_setOutputValue                       
00003b61  Board_shutDownExtFlash                   
00003b99  PIN_setConfig                            
00003bd1  TimerP_getCurrentTick                    
00003c09  TimerP_initDevice                        
00003ce9  TimerP_setNextTick                       
00003d1f  UDMACC26XX_close                         
00003d55  ClockP_getTicksUntilInterrupt            
00003dbd  GPTimerCC26XX_configureDebugStall        
00003df1  NOROM_IntRegister                        
00003e25  NOROM_SysCtrlIdle                        
00003e59  SwiP_destruct                            
00003ec1  GPIO_hwiIntFxn                           
00003ef1  GPIO_setCallback                         
00003f21  GPTimerCC26XX_close                      
00003f51  PINCC26XX_setMux                         
00003f81  Power_releaseConstraint                  
00003fb1  Power_setConstraint                      
00003fe1  _c_int00                                 
00004000  __SYSMEM_SIZE                            
00004069  TimerP_setThreshold                      
00004095  Power_registerNotify                     
0000410d  List_put                                 
00004133  List_remove                              
00004159  ClockP_getTicks                          
0000417d  ClockP_scheduleNextTick                  
000041c5  NOROM_OSCHF_TurnOnXosc                   
000041e9  TimerP_getMaxTicks                       
00004231  Board_init                               
00004251  ClockP_add                               
00004271  NOROM_AUXADCEnableSync                   
000042b1  Power_unregisterNotify                   
000042d1  QueueP_put                               
000042f1  TimerP_dynamicStub                       
00004351  PIN_close                                
0000436f  Power_getTransitionLatency               
0000438d  SemaphoreP_constructBinary               
000043ab  SwiP_or                                  
000043c9  HwiP_destruct                            
000043e5  NOROM_ChipInfo_GetChipFamily             
0000441d  SwiP_disable                             
00004471  PowerCC26XX_calibrate                    
0000448b  QueueP_get                               
000044a5  SemaphoreP_createBinary                  
000044bf  ClockP_destruct                          
000044d9  HwiP_inISR                               
000044f1  NOROM_ChipInfo_GetPackageType            
00004509  Power_idleFunc                           
00004521  TimerP_startup                           
00004539  UART_Params_init                         
00004551  _args_main                               
00004569  __aeabi_lmul                             
00004599  SemaphoreP_create                        
000045d9  NOROM_IntUnregister                      
000045ed  PowerCC26X2_calibrate                    
00004601  Power_disablePolicy                      
00004629  TimerP_getCount64                        
0000463d  resetISR                                 
00004651  GPTimerCC26XX_Params_init                
00004663  HwiP_Params_init                         
00004675  RingBuf_construct                        
00004689  ClockP_Params_init                       
00004699  ClockP_getCpuFreq                        
000046a9  HwiP_clearInterrupt                      
000046b9  HwiP_disable                             
000046c9  HwiP_post                                
000046d9  PINCC26XX_getPinCount                    
000046e9  PowerCC26XX_schedulerDisable             
000046f9  Power_enablePolicy                       
00004709  SemaphoreP_Params_init                   
00004719  SwiP_Params_init                         
00004729  TimerP_Params_init                       
00004749  QueueP_empty                             
00004757  QueueP_remove                            
00004765  __TI_decompress_none                     
00004775  ClockP_doTick                            
00004781  ClockP_getSystemTickPeriod               
0000478d  NOROM_CPUcpsid                           
00004799  NOROM_CPUcpsie                           
000047a5  PIN_registerIntCb                        
000047b1  PowerCC26XX_schedulerRestore             
000047bd  Power_getConstraintMask                  
000047c9  Power_getDependencyCount                 
000047d5  SwiP_getTrigger                          
000047e1  TimerP_getFreq                           
000047ed  __TI_zero_init                           
000047f9  ADCBuf_convertCancel                     
00004803  UDMACC26XX_hwiIntFxn                     
0000481f  ADCBufCC26X2_init                        
00004827  GPTimerCC26XX_setLoadValue               
0000482f  HwiP_restore                             
00004837  UARTCC26XX_init                          
0000483f  malloc                                   
0000484f  ClockP_stop                              
00004855  NOROM_CPUdelay                           
0000485b  QueueP_init                              
00004861  UARTCC26XX_readPolling                   
00004867  UARTCC26XX_writePolling                  
00004873  ADCBufCC26X2_getResolution               
0000487b  ClockP_isActive                          
0000487f  ClockP_setTimeout                        
00004883  HwiP_enable                              
00004883  NoRTOS_start                             
00004887  QueueP_head                              
0000488b  QueueP_next                              
0000488f  SemaphoreP_delete                        
00004897  _system_pre_init                         
0000489b  C$$EXIT                                  
0000489b  abort                                    
000048a7  __aeabi_idiv0                            
000048a7  __aeabi_ldiv0                            
000048a8  resourceDB                               
00004918  BoardGpioInitTable                       
00004940  UARTCC26XX_fxnTable                      
00004990  ADCBufCC26X2_fxnTable                    
000049b4  UART_defaultParams                       
000049f4  GPTimerCC26XX_config                     
00004a0c  gptimerCC26XXHWAttrs                     
00004a3c  GPIOCC26XX_config                        
00004a50  PowerCC26X2_config                       
00004a88  adcbufCC26XXHWAttrs                      
00004aa8  outPinTypes                              
00004ab8  ADCBUF_CONST                             
00004ab9  ADCBUF_SOUND_CONST                       
00004aba  ADCBUF_TEMPERATURE_CONST                 
00004abb  ADCBUF_BATTERY_VOLTAGE_CONST             
00004abc  ADCBuf_count                             
00004abd  PIN_TEST1_CONST                          
00004abe  PIN_TEST2_CONST                          
00004abf  PIN_DRIVE_SPEAKER_A_CONST                
00004ac0  PIN_DRIVE_SPEAKER_B_CONST                
00004ac1  UART_0_CONST                             
00004ac2  UART_count                               
00004ac3  CONFIG_GPTIMER_0_CONST                   
00004ac4  CONFIG_GPTIMER_1_CONST                   
00004ac5  GPTimer_count                            
00004ac8  ADCBuf_config                            
00004ad4  UART_config                              
00004ae0  inPinTypes                               
00004aec  outPinStrengths                          
00004b08  PINCC26XX_hwAttrs                        
00004b10  UDMACC26XX_config                        
00004b20  udmaCC26XXHWAttrs                        
00004b40  __c_args__                               
00004bd0  __TI_Handler_Table_Base                  
00004bdc  __TI_Handler_Table_Limit                 
00004c00  __TI_CINIT_Base                          
00004c28  __TI_CINIT_Limit                         
00057fa8  __ccfg                                   
20000000  __start___llvm_prf_cnts                  
20000000  __stop___llvm_prf_cnts                   
20000000  uartCC26XXObjects                        
200001b4  adcbufCC26XXbjects                       
20000330  gptimerCC26XXObjects                     
200003c8  pinHandleTable                           
20000444  i                                        
20000448  j                                        
2000044c  uart                                     
20000450  UARTbuffer                               
200005b8  udmaCC26XXObject                         
20000639  driverlib_release_0_59848                
20000668  PowerCC26X2_module                       
200007d8  ChirpSize                                
200007dc  ChirpIndex                               
200007e0  ChirpDelay                               
2000085c  ChirpState                               
200009b0  _hposcCoeffs                             
200009d4  gpioCallbackFunctions                    
200009e4  gpioPinConfigs                           
20000a14  _lock                                    
20000a18  _unlock                                  
20000a1c  SemaphoreP_defaultParams                 
20000a24  pinUpperBound                            
20000a28  pinLowerBound                            
20000a30  ClockP_tickPeriod                        
20000a34  adcBufCC26XXChannelLut0                  
20000a48  HwiP_swiPIntNum                          
20000b00  g_pfnRAMVectors                          
20001a80  _sys_memory                              
20013c00  __stack                                  
20014000  __STACK_END                              
ffffffff  __TI_pprof_out_hndl                      
ffffffff  __TI_prof_data_size                      
ffffffff  __TI_prof_data_start                     
ffffffff  __binit__                                
ffffffff  binit                                    
UNDEFED   ITM_flush                                
UNDEFED   ITM_restore                              
UNDEFED   __mpu_init                               
UNDEFED   _system_post_cinit                       

[283 symbols]
