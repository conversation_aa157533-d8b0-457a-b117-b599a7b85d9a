#include <stdint.h>
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <ti/devices/cc13x2_cc26x2/driverlib/timer.h>
#include <ti/devices/cc13x2_cc26x2/driverlib/prcm.h>
#include <ti/drivers/Board.h>
#include "ti_drivers_config.h"
#include <stddef.h>
#include "time.h"
#include <stdbool.h>

#define MaxNumberOfTimers  5
void (*CallBackMatch)(void) =  NULL;
uint32_t TimerStartTime[MaxNumberOfTimers] = { 0 };
volatile uint32_t TimerOverflows=0; // using a long will result in an overflow error after 203 days, not relevant in this application.

void DisableTimer2A();
void EnableTimer2A();
void Timer2AInterruptHandler(void);

//*********************************************************************************************************
// Initialize general purpose timer2 for periodic counting @ 16MHz, configure output compare interrupt
//*********************************************************************************************************
void Time_Init(void)
{
    PRCMPowerDomainOn( PRCM_DOMAIN_PERIPH);
    PRCMPeripheralRunEnable(PRCM_PERIPH_TIMER2);
    PRCMLoadSet();
    while(!PRCMLoadGet());
    DisableTimer2A();
    TimerConfigure(GPT2_BASE, TIMER_CFG_A_PERIODIC | TIMER_CFG_SPLIT_PAIR);  // 16 bit up counter
    TimerPrescaleSet(GPT2_BASE, TIMER_A,2 );  // 3 prescaler  ->  16MHz clock
    TimerMatchUpdateMode(GPT2_BASE, TIMER_A, TIMER_MATCHUPDATE_NEXTCYCLE);
    TimerIntervalLoadMode(GPT2_BASE, TIMER_A ,TIMER_INTERVALLOAD_NEXTCYCLE);
    TimerPrescaleMatchSet(GPT2_BASE, TIMER_A, 0);
    TimerIntRegister(GPT2_BASE, TIMER_A,  Timer2AInterruptHandler);
    TimerIntEnable(GPT2_BASE, TIMER_TIMA_TIMEOUT);
    EnableTimer2A();
}

void DisableTimer2A()
{
  TimerDisable(GPT2_BASE,TIMER_A);
}

void EnableTimer2A()
{
  TimerEnable(GPT2_BASE,TIMER_A);
}

//*********************************************************************************************************
// functions that expose timer2 channel A match
//*********************************************************************************************************
void Time_EnableMatch()
{
    TimerMatchSet(GPT2_BASE,TIMER_A, TimerValueGet(GPT2_BASE,TIMER_A)-0x00FF); // set first output compare to FF cycles in the future
    TimerIntEnable(GPT2_BASE, TIMER_TIMA_MATCH);
}

void Time_DisableMatch()
{
 TimerIntDisable(GPT2_BASE, TIMER_TIMA_MATCH);
}

// Next match is set to current match + ticks16MHz.
void Time_SetNextMatch(uint16_t Ticks16MHz)
{
 TimerMatchSet(GPT2_BASE,TIMER_A, TimerMatchGet(GPT2_BASE,TIMER_A)-Ticks16MHz) ;
}

void Timer2AInterruptHandler(void)
{
 if ( TimerIntStatus(GPT2_BASE, true) & TIMER_TIMA_MATCH)
  {  // handle the output compare interrupt
     if (CallBackMatch!=NULL) CallBackMatch();
     TimerIntClear(GPT2_BASE, TIMER_TIMA_MATCH);
  } else
  {
     // only timeout interrupt is enabled, handle this
     TimerOverflows++;
     TimerIntClear(GPT2_BASE,  TIMER_TIMA_TIMEOUT );
  }
}

void Time_SetCallbackFunctionOnMatch(void (*Callback)(void)) // call back executed on match if match is enabled.
{
  CallBackMatch=Callback;
}

//*********************************************************************************************************
// functions that expose a set of soft timers
//*********************************************************************************************************

// (re)start a given soft timer
void Time_StartSoftTimer(SoftTimers timer)
{
    TimerStartTime[timer] =  TimerOverflows;
}



// returns true if given timer > ( time when timer started + delay in milliseconds).
bool Time_IsSoftTimerExpired(SoftTimers timer,uint32_t delayms)
{
    uint32_t d = TimerOverflows-TimerStartTime[timer] ; // time difference start and now in timer overflows
    d =    (d * 4096 )/ 1000 ;  // convert to milliseconds  . 1 timer tick = 16MHz/2^16  -> 4,096 ms / tick
    return ( d>=delayms);
}






