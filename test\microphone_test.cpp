#include <Arduino.h>
#include <driver/i2s.h>

// I2S pin definitions (updated as requested)
#define I2S_WS 15    // Left/Right clock
#define I2S_SD 13    // Serial Data (microphone output)
#define I2S_SCK 2    // Serial Clock
#define I2S_PORT I2S_NUM_0

// Recording parameters
#define SAMPLE_RATE 48000
#define RECORD_DURATION_SEC 10
#define SAMPLES_PER_SECOND SAMPLE_RATE
#define TOTAL_SAMPLES (SAMPLES_PER_SECOND * RECORD_DURATION_SEC)

void i2s_install();
void i2s_setpin();
void record_audio();

void setup() {
  Serial.begin(115200);
  Serial.println("🎤 INMP441 Microphone Recording Test");
  Serial.println("📡 Send 'r' to start 10-second recording");
  Serial.println("⚙️  Config: 48kHz, 16-bit, Mono");
  delay(1000);

  i2s_install();
  i2s_setpin();
  i2s_start(I2S_PORT);

  Serial.println("✅ I2S initialized successfully");
  Serial.println("💡 Ready for commands...");
  delay(500);
}

void loop() {
  // Check for UART commands
  if (Serial.available() > 0) {
    char command = Serial.read();

    if (command == 'r' || command == 'R') {
      Serial.println("🎵 Starting 10-second recording...");
      record_audio();
      Serial.println("✅ Recording complete!");
      Serial.println("💡 Send 'r' for another recording");
    } else {
      Serial.println("❓ Unknown command. Send 'r' to record.");
    }
  }

  delay(100); // Small delay to prevent excessive polling
}

// Record 10 seconds of audio and send via UART
void record_audio() {
  Serial.printf("🎤 Starting %d-second recording...\n", RECORD_DURATION_SEC);
  Serial.printf("🎯 Target: %d samples at %d Hz\n", TOTAL_SAMPLES, SAMPLE_RATE);
  Serial.println("--- AUDIO DATA START ---");

  const size_t buffer_size = 512; // Smaller chunks for more responsive output
  int16_t buffer[buffer_size];
  size_t samples_recorded = 0;
  unsigned long start_time = millis();

  while (samples_recorded < TOTAL_SAMPLES) {
    size_t bytes_read = 0;
    size_t samples_to_read = min(buffer_size, TOTAL_SAMPLES - samples_recorded);
    size_t bytes_to_read = samples_to_read * sizeof(int16_t);

    esp_err_t result = i2s_read(
      I2S_PORT,
      (void*)buffer,
      bytes_to_read,
      &bytes_read,
      portMAX_DELAY
    );

    if (result == ESP_OK && bytes_read > 0) {
      size_t samples_read = bytes_read / sizeof(int16_t);

      // Send samples via UART
      for (size_t i = 0; i < samples_read; i++) {
        Serial.println(buffer[i]);
      }

      samples_recorded += samples_read;

      // Progress indicator every second
      if (samples_recorded % SAMPLE_RATE == 0) {
        size_t seconds = samples_recorded / SAMPLE_RATE;
        unsigned long elapsed_ms = millis() - start_time;
        Serial.printf("📊 Progress: %d/%d seconds (actual: %d ms)\n",
                     seconds, RECORD_DURATION_SEC, elapsed_ms);
      }
    } else {
      Serial.printf("❌ I2S read error: %s\n", esp_err_to_name(result));
      delay(1); // Small delay on error
    }
  }

  unsigned long total_time = millis() - start_time;
  Serial.println("--- AUDIO DATA END ---");
  Serial.printf("📈 Total samples recorded: %d\n", samples_recorded);
  Serial.printf("⏱️  Total recording time: %d ms\n", total_time);
  Serial.printf("📊 Actual sample rate: %.1f Hz\n", (float)samples_recorded * 1000.0 / total_time);
}

// I2S configuration and setup
void i2s_install() {
  const i2s_config_t i2s_config = {
    .mode = i2s_mode_t(I2S_MODE_MASTER | I2S_MODE_RX),
    .sample_rate = SAMPLE_RATE,  // 48000 Hz
    .bits_per_sample = I2S_BITS_PER_SAMPLE_16BIT,
    .channel_format = I2S_CHANNEL_FMT_ONLY_LEFT,  // Mono
    .communication_format = i2s_comm_format_t(I2S_COMM_FORMAT_STAND_I2S),
    .intr_alloc_flags = ESP_INTR_FLAG_LEVEL1,
    .dma_buf_count = 8,
    .dma_buf_len = 128,  // Larger buffer for better performance
    .use_apll = false,
    .tx_desc_auto_clear = false,
    .fixed_mclk = 0
  };
  i2s_driver_install(I2S_PORT, &i2s_config, 0, NULL);
}

void i2s_setpin() {
  const i2s_pin_config_t pin_config = {
    .bck_io_num = I2S_SCK,
    .ws_io_num = I2S_WS,
    .data_out_num = I2S_PIN_NO_CHANGE,
    .data_in_num = I2S_SD
  };
  i2s_set_pin(I2S_PORT, &pin_config);
}
