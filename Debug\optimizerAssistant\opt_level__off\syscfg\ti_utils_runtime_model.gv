digraph Model
{
    graph[rankdir=LR]

    // mod.$name=/ti/common/multi_stack_validate

    // mod.$name=/ti/devices/CCFG
    ti_devices_CCFG -> ti_devices_CCFGTemplate0

    // mod.$name=/ti/devices/CCFGTemplate

    // mod.$name=/ti/devices/DriverLib

    // mod.$name=/ti/devices/radioconfig/custom
    ti_devices_radioconfig_custom -> ti_common_multi_stack_validate

    // mod.$name=/ti/drivers/ADCBuf
    ADCBUF -> ti_drivers_DMA
    ADCBUF -> ti_drivers_Power
    ADCBUF -> ti_drivers_Board
    ADCBUF -> CONFIG_GPTIMER_0
    ADCBUF -> ADCBUF_SOUND
    ADCBUF -> ADCBUF_TEMPERATURE
    ADCBUF -> ADCBUF_BATTERY_VOLTAGE

    // mod.$name=/ti/drivers/Board
    ti_drivers_Board -> ti_devices_DriverLib

    // mod.$name=/ti/drivers/DMA
    ti_drivers_DMA -> ti_drivers_Board

    // mod.$name=/ti/drivers/GPIO
    ti_drivers_GPIO -> ti_drivers_Power
    ti_drivers_GPIO -> ti_drivers_Board
    PIN_TEST1 -> CONFIG_PIN_0
    PIN_TEST2 -> CONFIG_PIN_1
    PIN_DRIVE_SPEAKER_A -> CONFIG_PIN_2
    PIN_DRIVE_SPEAKER_B -> CONFIG_PIN_3

    // mod.$name=/ti/drivers/PIN
    ti_drivers_PIN -> ti_drivers_Power
    ti_drivers_PIN -> ti_drivers_Board

    // mod.$name=/ti/drivers/Power
    ti_drivers_Power -> ti_devices_CCFG
    ti_drivers_Power -> ti_drivers_Board

    // mod.$name=/ti/drivers/RTOS

    // mod.$name=/ti/drivers/Timer
    CONFIG_TIMER_1 -> ti_drivers_Power
    CONFIG_TIMER_1 -> ti_drivers_Board
    CONFIG_TIMER_1 -> CONFIG_GPTIMER_1

    // mod.$name=/ti/drivers/UART
    UART_0 -> ti_drivers_Power
    UART_0 -> ti_drivers_Board
    UART_0 -> CONFIG_PIN_5
    UART_0 -> CONFIG_PIN_6

    // mod.$name=/ti/drivers/adcbuf/ADCBufChanCC26XX
    ADCBUF_SOUND -> CONFIG_PIN_4
    ADCBUF_TEMPERATURE -> CONFIG_PIN_7
    ADCBUF_BATTERY_VOLTAGE -> CONFIG_PIN_8

    // mod.$name=/ti/drivers/timer/GPTimerCC26XX
    CONFIG_GPTIMER_0 -> ti_drivers_Power
    CONFIG_GPTIMER_0 -> ti_drivers_Board
    CONFIG_GPTIMER_1 -> ti_drivers_Power
    CONFIG_GPTIMER_1 -> ti_drivers_Board
}
