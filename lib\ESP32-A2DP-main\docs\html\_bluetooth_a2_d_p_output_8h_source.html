<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: src/BluetoothA2DPOutput.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">BluetoothA2DPOutput.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="_bluetooth_a2_d_p_common_8h.html">BluetoothA2DPCommon.h</a>&quot;</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160; </div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="preprocessor">#include &quot;Print.h&quot;</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160; </div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#if A2DP_LEGACY_I2S_SUPPORT</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &quot;driver/i2s.h&quot;</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160; </div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#if A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#include &quot;AudioTools.h&quot;</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160; </div>
<div class="line"><a name="l00022"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output.html">   22</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a> {</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> begin() = 0;</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">size_t</span> write(<span class="keyword">const</span> uint8_t *data, <span class="keywordtype">size_t</span> len) = 0;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> end() = 0;</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_sample_rate(<span class="keywordtype">int</span> rate) = 0;</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_output_active(<span class="keywordtype">bool</span> active) = 0;</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160; </div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#if A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00032"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output.html#a67fc2cf760ae2a48ac6bfa6ed235f8b8">   32</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_output.html#a67fc2cf760ae2a48ac6bfa6ed235f8b8">set_output</a>(audio_tools::AudioOutput &amp;output) {}</div>
<div class="line"><a name="l00034"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output.html#a9cdbeeafa4778b4efcf7108ec8b9afae">   34</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_output.html#a9cdbeeafa4778b4efcf7108ec8b9afae">set_output</a>(audio_tools::AudioStream &amp;output) {}</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160; </div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00039"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output.html#add4a1457f42f2e6ef8b905bcf4be10dc">   39</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_output.html#add4a1457f42f2e6ef8b905bcf4be10dc">set_output</a>(Print &amp;output) {}</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160; </div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#if A2DP_LEGACY_I2S_SUPPORT</span></div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_pin_config(i2s_pin_config_t pin_config) {}</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_port(i2s_port_t i2s_num) {}</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160; </div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_config(i2s_config_t i2s_config) {}</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160; </div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_bits_per_sample(<span class="keywordtype">int</span> bps) {}</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160; </div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &lt; ESP_IDF_VERSION_VAL(5, 1, 1)</span></div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  <span class="keyword">virtual</span> esp_err_t i2s_mclk_pin_select(<span class="keyword">const</span> uint8_t pin) { <span class="keywordflow">return</span> ESP_FAIL; };</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160; </div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;};</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160; </div>
<div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output_audio_tools.html">   69</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_bluetooth_a2_d_p_output_audio_tools.html">BluetoothA2DPOutputAudioTools</a> : <span class="keyword">public</span> <a class="code" href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a> {</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_output_audio_tools.html">BluetoothA2DPOutputAudioTools</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  <span class="keywordtype">bool</span> begin() <span class="keyword">override</span>;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;  <span class="keywordtype">size_t</span> write(<span class="keyword">const</span> uint8_t *data, <span class="keywordtype">size_t</span> len) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <span class="keywordtype">void</span> end() <span class="keyword">override</span>;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  <span class="keywordtype">void</span> set_sample_rate(<span class="keywordtype">int</span> rate) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  <span class="keywordtype">void</span> set_output_active(<span class="keywordtype">bool</span> active) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160; </div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <span class="keyword">operator</span> bool() { </div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;<span class="preprocessor">#if A2DP_I2S_AUDIOTOOLS || defined(ARDUINO)</span></div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;    <span class="keywordflow">return</span> p_print != <span class="keyword">nullptr</span>; </div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">false</span>;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  }</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160; </div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160; </div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;<span class="preprocessor">#if A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00089"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output_audio_tools.html#a7304b027857f382bfb1838b4b36df7f3">   89</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_output_audio_tools.html#a7304b027857f382bfb1838b4b36df7f3">set_output</a>(audio_tools::AudioOutput &amp;output) {</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    p_print = &amp;output;</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;    p_audio_print = &amp;output;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;  }</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160; </div>
<div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output_audio_tools.html#adf9ef0cf6b3e6ef45b03109605c66ccd">   95</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_output_audio_tools.html#adf9ef0cf6b3e6ef45b03109605c66ccd">set_output</a>(audio_tools::AudioStream &amp;output) {</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;    <span class="keyword">static</span> audio_tools::AdapterAudioStreamToAudioOutput adapter(output);</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;    adapter.setStream(output);</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;    p_print = &amp;output;</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;    p_audio_print = &amp;adapter;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;  }</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160; </div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00105"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output_audio_tools.html#a3c360cc7bf000d4e8bc241007e34f7d9">  105</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_output_audio_tools.html#a3c360cc7bf000d4e8bc241007e34f7d9">set_output</a>(Print &amp;output) { p_print = &amp;output; }</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160; </div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160; <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;<span class="preprocessor">#if defined(ARDUINO) || A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  Print *p_print = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160; </div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;<span class="preprocessor">#if A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;  audio_tools::AudioOutput *p_audio_print = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;};</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160; </div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160; </div>
<div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output_print.html">  126</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_bluetooth_a2_d_p_output_print.html">BluetoothA2DPOutputPrint</a> : <span class="keyword">public</span> <a class="code" href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a> {</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_output_print.html">BluetoothA2DPOutputPrint</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  <span class="keywordtype">bool</span> begin() { <span class="keywordflow">return</span> <span class="keyword">true</span>;};</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;  <span class="keywordtype">size_t</span> write(<span class="keyword">const</span> uint8_t *data, <span class="keywordtype">size_t</span> len)<span class="keyword"> override </span>{ </div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;    <span class="keywordflow">if</span> (p_print==<span class="keyword">nullptr</span>) <span class="keywordflow">return</span> 0;</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;    <span class="keywordflow">return</span> p_print-&gt;write(data, len);</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;  }</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;  <span class="keywordtype">void</span> end()<span class="keyword"> override </span>{}</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  <span class="keywordtype">void</span> set_sample_rate(<span class="keywordtype">int</span> rate)<span class="keyword"> override </span>{};</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  <span class="keywordtype">void</span> set_output_active(<span class="keywordtype">bool</span> active)<span class="keyword"> override </span>{};</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160; </div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;  <span class="keyword">operator</span> bool() { </div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;    <span class="keywordflow">return</span> p_print != <span class="keyword">nullptr</span>; </div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;  }</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160; </div>
<div class="line"><a name="l00143"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output_print.html#aa9e40b46faaba5afdbbf6dbf403c627e">  143</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_output_print.html#aa9e40b46faaba5afdbbf6dbf403c627e">set_output</a>(Print &amp;output) { p_print = &amp;output; }</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160; </div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160; <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;  Print *p_print = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160; </div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;};</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160; </div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160; </div>
<div class="line"><a name="l00157"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output_legacy.html">  157</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_bluetooth_a2_d_p_output_legacy.html">BluetoothA2DPOutputLegacy</a> : <span class="keyword">public</span> <a class="code" href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a> {</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_output_legacy.html">BluetoothA2DPOutputLegacy</a>();</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;  <span class="keywordtype">bool</span> begin() <span class="keyword">override</span>;</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;  <span class="keywordtype">size_t</span> write(<span class="keyword">const</span> uint8_t *data, <span class="keywordtype">size_t</span> len) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;  <span class="keywordtype">void</span> end() <span class="keyword">override</span>;</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;  <span class="keywordtype">void</span> set_sample_rate(<span class="keywordtype">int</span> rate) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;  <span class="keywordtype">void</span> set_output_active(<span class="keywordtype">bool</span> active) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160; </div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;<span class="preprocessor">#if A2DP_LEGACY_I2S_SUPPORT</span></div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_pin_config(i2s_pin_config_t pin_config) {</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;    this-&gt;pin_config = pin_config;</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;  }</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_port(i2s_port_t i2s_num) { i2s_port = i2s_num; }</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160; </div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_config(i2s_config_t i2s_config) {</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;    this-&gt;i2s_config = i2s_config;</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;  }</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160; </div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_bits_per_sample(<span class="keywordtype">int</span> bps) {</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;    i2s_config.bits_per_sample = (i2s_bits_per_sample_t)bps;</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;  }</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160; </div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &lt; ESP_IDF_VERSION_VAL(5, 1, 1)</span></div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;  <span class="keyword">virtual</span> esp_err_t i2s_mclk_pin_select(<span class="keyword">const</span> uint8_t pin);</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160; </div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160; <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;  i2s_config_t i2s_config;</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;  i2s_pin_config_t pin_config;</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;  i2s_channel_t i2s_channels = I2S_CHANNEL_STEREO;</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;  i2s_port_t i2s_port = I2S_NUM_0;</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;};</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160; </div>
<div class="line"><a name="l00203"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output_default.html">  203</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_bluetooth_a2_d_p_output_default.html">BluetoothA2DPOutputDefault</a> : <span class="keyword">public</span> <a class="code" href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a> {</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_output_default.html">BluetoothA2DPOutputDefault</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;  <span class="keywordtype">bool</span> begin() {</div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;    <span class="keywordtype">bool</span> rc = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;    <span class="keywordflow">if</span> (out_tools)</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;      rc = out_tools.begin();</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;    <span class="keywordflow">else</span></div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;      rc = out_legacy.begin();</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;    <span class="keywordflow">return</span> rc;</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;  }</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;  </div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;  <span class="keywordtype">size_t</span> write(<span class="keyword">const</span> uint8_t *data, <span class="keywordtype">size_t</span> len) {</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;    <span class="keywordtype">size_t</span> result = 0;</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;    <span class="keywordflow">if</span> (out_tools)</div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;      result = out_tools.write(data, len);</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;    <span class="keywordflow">else</span></div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;      result = out_legacy.write(data, len);</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;    <span class="keywordflow">return</span> result;</div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;  }</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;  </div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;  <span class="keywordtype">void</span> end()<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;    <span class="keywordflow">if</span> (out_tools)</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;      out_tools.end();</div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;    <span class="keywordflow">else</span></div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;      out_legacy.end();</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;  }</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;  </div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;  <span class="keywordtype">void</span> set_sample_rate(<span class="keywordtype">int</span> rate)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;    <span class="keywordflow">if</span> (out_tools)</div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;      out_tools.set_sample_rate(rate);</div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;    <span class="keywordflow">else</span></div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;      out_legacy.set_sample_rate(rate);</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;  }</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160; </div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;  <span class="keywordtype">void</span> set_output_active(<span class="keywordtype">bool</span> active)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;    <span class="keywordflow">if</span> (out_tools)</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;      out_tools.set_output_active(active);</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;    <span class="keywordflow">else</span></div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;      out_legacy.set_output_active(active);</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;  }</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160; </div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;<span class="preprocessor">#if A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00247"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output_default.html#a69efd35a5b96a8c4ea45286ec02cb550">  247</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_output_default.html#a69efd35a5b96a8c4ea45286ec02cb550">set_output</a>(audio_tools::AudioOutput &amp;output)<span class="keyword"> override  </span>{ out_tools.<a class="code" href="class_bluetooth_a2_d_p_output_audio_tools.html#a7304b027857f382bfb1838b4b36df7f3">set_output</a>(output); }</div>
<div class="line"><a name="l00249"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output_default.html#a74df73b612e2c4b99956e367d9f69656">  249</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_output_default.html#a74df73b612e2c4b99956e367d9f69656">set_output</a>(audio_tools::AudioStream &amp;output)<span class="keyword"> override </span>{ out_tools.<a class="code" href="class_bluetooth_a2_d_p_output_audio_tools.html#a7304b027857f382bfb1838b4b36df7f3">set_output</a>(output); }</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160; </div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00254"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_output_default.html#ad03aa92e57182e2a24eb3596fe5461ee">  254</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_output_default.html#ad03aa92e57182e2a24eb3596fe5461ee">set_output</a>(Print &amp;output)<span class="keyword"> override </span>{ out_tools.<a class="code" href="class_bluetooth_a2_d_p_output_audio_tools.html#a7304b027857f382bfb1838b4b36df7f3">set_output</a>(output); }</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160; </div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;<span class="preprocessor">#if A2DP_LEGACY_I2S_SUPPORT</span></div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_pin_config(i2s_pin_config_t pin_config)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;    out_legacy.set_pin_config(pin_config);</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;  }</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_port(i2s_port_t i2s_num)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;    out_legacy.set_i2s_port(i2s_num);</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;  }</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160; </div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_config(i2s_config_t i2s_config)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;    out_legacy.set_i2s_config(i2s_config);</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;  }</div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160; </div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_bits_per_sample(<span class="keywordtype">int</span> bps)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;    out_legacy.set_bits_per_sample(bps);</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;  }</div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160; </div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &lt; ESP_IDF_VERSION_VAL(5, 1, 1)</span></div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;  <span class="keyword">virtual</span> esp_err_t i2s_mclk_pin_select(<span class="keyword">const</span> uint8_t pin)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;    <span class="keywordflow">return</span> out_legacy.i2s_mclk_pin_select(pin);</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;  }</div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00283"></a><span class="lineno">  283</span>&#160;<span class="preprocessor">#endif  </span><span class="comment">// A2DP_LEGACY_I2S_SUPPORT</span></div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160; <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_output_audio_tools.html">BluetoothA2DPOutputAudioTools</a> out_tools;</div>
<div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_output_legacy.html">BluetoothA2DPOutputLegacy</a> out_legacy;</div>
<div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;};</div>
<div class="ttc" id="a_bluetooth_a2_d_p_common_8h_html"><div class="ttname"><a href="_bluetooth_a2_d_p_common_8h.html">BluetoothA2DPCommon.h</a></div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_audio_tools_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_output_audio_tools.html">BluetoothA2DPOutputAudioTools</a></div><div class="ttdoc">Output Class using AudioTools library: https://github.com/pschatzmann/arduino-audio-tools.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:69</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_audio_tools_html_a3c360cc7bf000d4e8bc241007e34f7d9"><div class="ttname"><a href="class_bluetooth_a2_d_p_output_audio_tools.html#a3c360cc7bf000d4e8bc241007e34f7d9">BluetoothA2DPOutputAudioTools::set_output</a></div><div class="ttdeci">void set_output(Print &amp;output)</div><div class="ttdoc">Output to Arduino Print.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:105</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_audio_tools_html_a7304b027857f382bfb1838b4b36df7f3"><div class="ttname"><a href="class_bluetooth_a2_d_p_output_audio_tools.html#a7304b027857f382bfb1838b4b36df7f3">BluetoothA2DPOutputAudioTools::set_output</a></div><div class="ttdeci">void set_output(audio_tools::AudioOutput &amp;output)</div><div class="ttdoc">Output AudioStream using AudioTools library.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:89</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_audio_tools_html_adf9ef0cf6b3e6ef45b03109605c66ccd"><div class="ttname"><a href="class_bluetooth_a2_d_p_output_audio_tools.html#adf9ef0cf6b3e6ef45b03109605c66ccd">BluetoothA2DPOutputAudioTools::set_output</a></div><div class="ttdeci">void set_output(audio_tools::AudioStream &amp;output)</div><div class="ttdoc">Output AudioStream using AudioTools library.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:95</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_default_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_output_default.html">BluetoothA2DPOutputDefault</a></div><div class="ttdoc">Default Output Class providing both the Legacy I2S and the AudioTools I2S functionality.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:203</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_default_html_a69efd35a5b96a8c4ea45286ec02cb550"><div class="ttname"><a href="class_bluetooth_a2_d_p_output_default.html#a69efd35a5b96a8c4ea45286ec02cb550">BluetoothA2DPOutputDefault::set_output</a></div><div class="ttdeci">void set_output(audio_tools::AudioOutput &amp;output) override</div><div class="ttdoc">Output AudioStream using AudioTools library.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:247</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_default_html_a74df73b612e2c4b99956e367d9f69656"><div class="ttname"><a href="class_bluetooth_a2_d_p_output_default.html#a74df73b612e2c4b99956e367d9f69656">BluetoothA2DPOutputDefault::set_output</a></div><div class="ttdeci">void set_output(audio_tools::AudioStream &amp;output) override</div><div class="ttdoc">Output AudioStream using AudioTools library.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:249</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_default_html_ad03aa92e57182e2a24eb3596fe5461ee"><div class="ttname"><a href="class_bluetooth_a2_d_p_output_default.html#ad03aa92e57182e2a24eb3596fe5461ee">BluetoothA2DPOutputDefault::set_output</a></div><div class="ttdeci">void set_output(Print &amp;output) override</div><div class="ttdoc">Output to Arduino Print.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:254</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a></div><div class="ttdoc">Abstract Output Class.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:22</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_html_a67fc2cf760ae2a48ac6bfa6ed235f8b8"><div class="ttname"><a href="class_bluetooth_a2_d_p_output.html#a67fc2cf760ae2a48ac6bfa6ed235f8b8">BluetoothA2DPOutput::set_output</a></div><div class="ttdeci">virtual void set_output(audio_tools::AudioOutput &amp;output)</div><div class="ttdoc">Not implemented.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:32</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_html_a9cdbeeafa4778b4efcf7108ec8b9afae"><div class="ttname"><a href="class_bluetooth_a2_d_p_output.html#a9cdbeeafa4778b4efcf7108ec8b9afae">BluetoothA2DPOutput::set_output</a></div><div class="ttdeci">virtual void set_output(audio_tools::AudioStream &amp;output)</div><div class="ttdoc">Not implemented.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:34</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_html_add4a1457f42f2e6ef8b905bcf4be10dc"><div class="ttname"><a href="class_bluetooth_a2_d_p_output.html#add4a1457f42f2e6ef8b905bcf4be10dc">BluetoothA2DPOutput::set_output</a></div><div class="ttdeci">virtual void set_output(Print &amp;output)</div><div class="ttdoc">Not implemented.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:39</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_legacy_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_output_legacy.html">BluetoothA2DPOutputLegacy</a></div><div class="ttdoc">Legacy I2S Output Class.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:157</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_print_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_output_print.html">BluetoothA2DPOutputPrint</a></div><div class="ttdoc">Output Class using Print API:</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:126</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_print_html_aa9e40b46faaba5afdbbf6dbf403c627e"><div class="ttname"><a href="class_bluetooth_a2_d_p_output_print.html#aa9e40b46faaba5afdbbf6dbf403c627e">BluetoothA2DPOutputPrint::set_output</a></div><div class="ttdeci">void set_output(Print &amp;output)</div><div class="ttdoc">Output to Arduino Print.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:143</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
