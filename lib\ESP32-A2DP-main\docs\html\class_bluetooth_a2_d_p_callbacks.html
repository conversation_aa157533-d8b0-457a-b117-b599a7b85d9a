<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: BluetoothA2DPCallbacks Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="class_bluetooth_a2_d_p_callbacks-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">BluetoothA2DPCallbacks Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Public callback methods. We use a separate class so that we can protect the called methods in <a class="el" href="class_bluetooth_a2_d_p_sink.html" title="A2DP Bluethooth Sink - We iniitialize and start the Bluetooth A2DP Sink. The example https://github....">BluetoothA2DPSink</a>.  
 <a href="class_bluetooth_a2_d_p_callbacks.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_bluetooth_a2_d_p_sink_8h_source.html">BluetoothA2DPSink.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:adbdbad5d8bf3d18fda77c3150fd0232e"><td class="memItemLeft" align="right" valign="top"><a id="adbdbad5d8bf3d18fda77c3150fd0232e"></a>
static void&#160;</td><td class="memItemRight" valign="bottom"><b>app_a2d_callback</b> (esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param)</td></tr>
<tr class="separator:adbdbad5d8bf3d18fda77c3150fd0232e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a69387f28dde83bd3862dfdf4af2be1b3"><td class="memItemLeft" align="right" valign="top"><a id="a69387f28dde83bd3862dfdf4af2be1b3"></a>
static void&#160;</td><td class="memItemRight" valign="bottom"><b>app_rc_ct_callback</b> (esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param)</td></tr>
<tr class="separator:a69387f28dde83bd3862dfdf4af2be1b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4827be80866be312e17d3bc3e029deb7"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_callbacks.html#a4827be80866be312e17d3bc3e029deb7">app_task_handler</a> ()</td></tr>
<tr class="separator:a4827be80866be312e17d3bc3e029deb7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c5b5bb151e0288dd716e8b6faf3d3b1"><td class="memItemLeft" align="right" valign="top"><a id="a1c5b5bb151e0288dd716e8b6faf3d3b1"></a>
static void&#160;</td><td class="memItemRight" valign="bottom"><b>audio_data_callback</b> (const uint8_t *data, uint32_t len)</td></tr>
<tr class="separator:a1c5b5bb151e0288dd716e8b6faf3d3b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a0c4debfe3d7a3a4594ed122904ebac"><td class="memItemLeft" align="right" valign="top"><a id="a0a0c4debfe3d7a3a4594ed122904ebac"></a>
static void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_stack_evt</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:a0a0c4debfe3d7a3a4594ed122904ebac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6e9bfbfc71992b2973e870b2816db8e"><td class="memItemLeft" align="right" valign="top"><a id="ac6e9bfbfc71992b2973e870b2816db8e"></a>
static void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_a2d_evt</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:ac6e9bfbfc71992b2973e870b2816db8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a250d311da12820d6fede1ab570733f71"><td class="memItemLeft" align="right" valign="top"><a id="a250d311da12820d6fede1ab570733f71"></a>
static void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_avrc_evt</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:a250d311da12820d6fede1ab570733f71"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Public callback methods. We use a separate class so that we can protect the called methods in <a class="el" href="class_bluetooth_a2_d_p_sink.html" title="A2DP Bluethooth Sink - We iniitialize and start the Bluetooth A2DP Sink. The example https://github....">BluetoothA2DPSink</a>. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a4827be80866be312e17d3bc3e029deb7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4827be80866be312e17d3bc3e029deb7">&#9670;&nbsp;</a></span>app_task_handler()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPCallbacks::app_task_handler </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>public Callbacks </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>src/<a class="el" href="_bluetooth_a2_d_p_sink_8h_source.html">BluetoothA2DPSink.h</a></li>
<li>src/BluetoothA2DPSink.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
