#include <stdint.h>
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include "adc.h"
#include <ti/drivers/Board.h>
#include "ti_drivers_config.h"
#include "SoundReceive.h"

#define  SignatureSize  132
int16_t Signature[SignatureSize]= { 1, 1, 1, -1, -1, 1, 1, 1, -1, -1, 1, 1, 1, -1, -1, -1, 1, 1, 1, -1, -1, -1, 1, 1, 1, -1, -1, -1, -1, 1, 1, 1,
                                    -1, -1, -1, -1, 1, 1, 1, 1, -1, -1, -1, -1, 1, 1, 1, 1, -1, -1, -1, -1, -1, 1, 1, 1, 1, -1, -1, -1, -1, -1, 1,
                                    1, 1, 1, -1, -1, -1, -1, -1, 1, 1, 1, 1, 1, -1, -1, -1, -1, -1, 1, 1, 1, 1, 1, -1, -1, -1, -1, -1, -1, 1, 1, 1,
                                    1, 1, -1, -1, -1, -1, -1, -1, 1, 1, 1, 1, 1, -1, -1, -1, -1, -1, -1, 1, 1, 1, 1, 1, 1, -1, -1, -1, -1, -1, -1,
                                    1, 1, 1, 1, 1, 1 };

static volatile uint16_t SoundSamples[NumberOfSoundSamples];

//*********************************************************************************************************
// Sample the microphone
//*********************************************************************************************************
void SoundReceive_SampleSound()
{
ADC_ReadArrayADCraw(0, SoundSamples, NumberOfSoundSamples,SoundSampleFrequency );
}

//*********************************************************************************************************
// Correlate the received signal with the signature and return the position in the array with highest correlation.
//*********************************************************************************************************
uint16_t SoundReceive_CalculateDistance()
{
  int16_t i,j;
  int32_t A;
  int32_t MaxA=0;
  int16_t MaxAIndex=0;
  for (i=0;i<(NumberOfSoundSamples-SignatureSize);i++)
   {
    A=0;
    for (j=0;j<SignatureSize;j++) A+=(int32_t)(Signature[j]*SoundSamples[i+j]);
    if (A>MaxA) { MaxA=A; MaxAIndex=i; }
   }
  return(MaxAIndex);
}

//*********************************************************************************************************
// return a pointer to where the sound samples are stored
//*********************************************************************************************************
uint16_t* SoundReceive_GetSoundSampleArray(void)
{
  return ((uint16_t*)SoundSamples);
}

