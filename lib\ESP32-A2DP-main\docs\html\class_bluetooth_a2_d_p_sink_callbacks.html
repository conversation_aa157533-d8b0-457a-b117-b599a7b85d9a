<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: BluetoothA2DPSinkCallbacks Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="class_bluetooth_a2_d_p_sink_callbacks-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">BluetoothA2DPSinkCallbacks Class Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Public callback methods. We use a separate class so that we can protect the called methods in <a class="el" href="class_bluetooth_a2_d_p_sink.html" title="A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example https://github....">BluetoothA2DPSink</a>.  
 <a href="class_bluetooth_a2_d_p_sink_callbacks.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_bluetooth_a2_d_p_sink_8h_source.html">BluetoothA2DPSink.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-static-methods" name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a6044c5dc42ff6440445d10f124d11e2c"><td class="memItemLeft" align="right" valign="top"><a id="a6044c5dc42ff6440445d10f124d11e2c" name="a6044c5dc42ff6440445d10f124d11e2c"></a>
static void&#160;</td><td class="memItemRight" valign="bottom"><b>app_a2d_callback</b> (esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param)</td></tr>
<tr class="memdesc:a6044c5dc42ff6440445d10f124d11e2c"><td class="mdescLeft">&#160;</td><td class="mdescRight">handle esp_a2d_cb_event_t <br /></td></tr>
<tr class="separator:a6044c5dc42ff6440445d10f124d11e2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a85efda64ad1405edb8382d24770654a8"><td class="memItemLeft" align="right" valign="top"><a id="a85efda64ad1405edb8382d24770654a8" name="a85efda64ad1405edb8382d24770654a8"></a>
static void&#160;</td><td class="memItemRight" valign="bottom"><b>app_rc_ct_callback</b> (esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param)</td></tr>
<tr class="memdesc:a85efda64ad1405edb8382d24770654a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">handle esp_avrc_ct_cb_event_t <br /></td></tr>
<tr class="separator:a85efda64ad1405edb8382d24770654a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac414ff3449b90390432d7ecc0f798308"><td class="memItemLeft" align="right" valign="top"><a id="ac414ff3449b90390432d7ecc0f798308" name="ac414ff3449b90390432d7ecc0f798308"></a>
static void&#160;</td><td class="memItemRight" valign="bottom"><b>app_gap_callback</b> (esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t *param)</td></tr>
<tr class="memdesc:ac414ff3449b90390432d7ecc0f798308"><td class="mdescLeft">&#160;</td><td class="mdescRight">GAP callback. <br /></td></tr>
<tr class="separator:ac414ff3449b90390432d7ecc0f798308"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e82c8cd94b5fe41e557d3c611fd12ec"><td class="memItemLeft" align="right" valign="top">static void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html#a6e82c8cd94b5fe41e557d3c611fd12ec">app_task_handler</a> (void *arg)</td></tr>
<tr class="memdesc:a6e82c8cd94b5fe41e557d3c611fd12ec"><td class="mdescLeft">&#160;</td><td class="mdescRight">task handler  <a href="class_bluetooth_a2_d_p_sink_callbacks.html#a6e82c8cd94b5fe41e557d3c611fd12ec">More...</a><br /></td></tr>
<tr class="separator:a6e82c8cd94b5fe41e557d3c611fd12ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9e2c4b56d94c1198b2e5650739b9be1a"><td class="memItemLeft" align="right" valign="top"><a id="a9e2c4b56d94c1198b2e5650739b9be1a" name="a9e2c4b56d94c1198b2e5650739b9be1a"></a>
static void&#160;</td><td class="memItemRight" valign="bottom"><b>audio_data_callback</b> (const uint8_t *data, uint32_t len)</td></tr>
<tr class="memdesc:a9e2c4b56d94c1198b2e5650739b9be1a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Callback for music stream. <br /></td></tr>
<tr class="separator:a9e2c4b56d94c1198b2e5650739b9be1a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a642fd44abbac9a50832db426f553ed"><td class="memItemLeft" align="right" valign="top"><a id="a1a642fd44abbac9a50832db426f553ed" name="a1a642fd44abbac9a50832db426f553ed"></a>
static void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_stack_evt</b> (uint16_t event, void *p_param)</td></tr>
<tr class="memdesc:a1a642fd44abbac9a50832db426f553ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">av event handler <br /></td></tr>
<tr class="separator:a1a642fd44abbac9a50832db426f553ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a616986984ae0d0deb7fc94f1604e17ef"><td class="memItemLeft" align="right" valign="top"><a id="a616986984ae0d0deb7fc94f1604e17ef" name="a616986984ae0d0deb7fc94f1604e17ef"></a>
static void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_a2d_evt</b> (uint16_t event, void *p_param)</td></tr>
<tr class="memdesc:a616986984ae0d0deb7fc94f1604e17ef"><td class="mdescLeft">&#160;</td><td class="mdescRight">a2dp event handler <br /></td></tr>
<tr class="separator:a616986984ae0d0deb7fc94f1604e17ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5562ce0b233c59b91d06bba2d894a9e2"><td class="memItemLeft" align="right" valign="top"><a id="a5562ce0b233c59b91d06bba2d894a9e2" name="a5562ce0b233c59b91d06bba2d894a9e2"></a>
static void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_avrc_evt</b> (uint16_t event, void *p_param)</td></tr>
<tr class="memdesc:a5562ce0b233c59b91d06bba2d894a9e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">avrc event handler <br /></td></tr>
<tr class="separator:a5562ce0b233c59b91d06bba2d894a9e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p >Public callback methods. We use a separate class so that we can protect the called methods in <a class="el" href="class_bluetooth_a2_d_p_sink.html" title="A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example https://github....">BluetoothA2DPSink</a>. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a6e82c8cd94b5fe41e557d3c611fd12ec" name="a6e82c8cd94b5fe41e557d3c611fd12ec"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6e82c8cd94b5fe41e557d3c611fd12ec">&#9670;&nbsp;</a></span>app_task_handler()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPSinkCallbacks::app_task_handler </td>
          <td>(</td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>arg</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>task handler </p>
<p >public Callbacks </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>src/<a class="el" href="_bluetooth_a2_d_p_sink_8h_source.html">BluetoothA2DPSink.h</a></li>
<li>src/BluetoothA2DPSink.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.2
</small></address>
</body>
</html>
