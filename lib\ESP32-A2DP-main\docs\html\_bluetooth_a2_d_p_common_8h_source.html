<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: src/BluetoothA2DPCommon.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">BluetoothA2DPCommon.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="_bluetooth_a2_d_p_common_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">// Licensed under the Apache License, Version 2.0 (the &quot;License&quot;);</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment">// you may not use this file except in compliance with the License.</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment">// You may obtain a copy of the License at</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160; </div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">//     http://www.apache.org/licenses/LICENSE-2.0</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment">// Unless required by applicable law or agreed to in writing, software</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment">// distributed under the License is distributed on an &quot;AS IS&quot; BASIS,</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment">// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment">// See the License for the specific language governing permissions and</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment">// limitations under the License.</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment">// Copyright 2020 Phil Schatzmann</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160; </div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#include &quot;config.h&quot;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="comment">// If you use #include &quot;I2S.h&quot; the i2s functionality is hidden in a namespace</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="comment">// this hack prevents any error messages</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#ifdef _I2S_H_INCLUDED</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="keyword">using namespace </span>esp_i2s;</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="comment">// Compile only for ESP32</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#if defined(CONFIG_IDF_TARGET_ESP32C2) || \</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">    defined(CONFIG_IDF_TARGET_ESP32C3) || \</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">    defined(CONFIG_IDF_TARGET_ESP32C5) || \</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">    defined(CONFIG_IDF_TARGET_ESP32C6) || \</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">    defined(CONFIG_IDF_TARGET_ESP32S2) || \</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">    defined(CONFIG_IDF_TARGET_ESP32S3) || \</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">    defined(CONFIG_IDF_TARGET_ESP32H2) || \</span></div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">    defined(CONFIG_IDF_TARGET_ESP32P4) </span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#error &quot;ESP32C3, ESP32S2, ESP32S3... do not support A2DP&quot;</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160; </div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#include &lt;math.h&gt;</span></div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="preprocessor">#include &lt;stdbool.h&gt;</span></div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="preprocessor">#include &lt;stdint.h&gt;</span></div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="preprocessor">#include &lt;stdio.h&gt;</span></div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="preprocessor">#include &lt;stdlib.h&gt;</span></div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="preprocessor">#include &lt;string.h&gt;</span></div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="preprocessor">#include &lt;unistd.h&gt;</span></div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160; </div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="preprocessor">#include &lt;vector&gt;</span></div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160; </div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="preprocessor">#include &quot;esp_idf_version.h&quot;</span></div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="preprocessor">#include &quot;freertos/FreeRTOS.h&quot;</span>  <span class="comment">// needed for ESP Arduino &lt; 2.0</span></div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="preprocessor">#include &quot;freertos/FreeRTOSConfig.h&quot;</span></div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="preprocessor">#include &quot;freertos/queue.h&quot;</span></div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="preprocessor">#include &quot;freertos/task.h&quot;</span></div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<span class="preprocessor">#include &quot;freertos/timers.h&quot;</span></div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &lt; ESP_IDF_VERSION_VAL(5, 2, 0)</span></div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="preprocessor">#include &quot;freertos/xtensa_api.h&quot;</span></div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;<span class="preprocessor">#include &quot;xtensa_api.h&quot;</span></div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;<span class="preprocessor">#include &quot;A2DPVolumeControl.h&quot;</span></div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;<span class="preprocessor">#include &quot;esp_a2dp_api.h&quot;</span></div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;<span class="preprocessor">#include &quot;esp_avrc_api.h&quot;</span></div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;<span class="preprocessor">#include &quot;esp_bt.h&quot;</span></div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;<span class="preprocessor">#include &quot;esp_bt_device.h&quot;</span></div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;<span class="preprocessor">#include &quot;esp_bt_main.h&quot;</span></div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;<span class="preprocessor">#include &quot;esp_gap_bt_api.h&quot;</span></div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;<span class="preprocessor">#include &quot;esp_spp_api.h&quot;</span></div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;<span class="preprocessor">#include &quot;esp_task_wdt.h&quot;</span></div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;<span class="preprocessor">#include &quot;esp_timer.h&quot;</span></div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;<span class="preprocessor">#include &quot;nvs.h&quot;</span></div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;<span class="preprocessor">#include &quot;nvs_flash.h&quot;</span></div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160; </div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;<span class="preprocessor">#ifdef ARDUINO_ARCH_ESP32</span></div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;<span class="preprocessor">#include &quot;esp32-hal-bt.h&quot;</span></div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;<span class="preprocessor">#include &quot;esp32-hal-log.h&quot;</span></div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;<span class="preprocessor">#include &quot;esp_log.h&quot;</span></div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160; </div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;<span class="preprocessor">#if !defined(ESP_IDF_VERSION)</span></div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;<span class="preprocessor">#error Unsupported ESP32 Version: Upgrade the ESP32 version in the Board Manager</span></div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160; </div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;<span class="comment">// Support for old and new IDF version</span></div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &lt; ESP_IDF_VERSION_VAL(4, 0, 0) &amp;&amp; \</span></div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;<span class="preprocessor">    !defined(I2S_COMM_FORMAT_STAND_I2S)</span></div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;<span class="comment">// support for old idf releases</span></div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;<span class="preprocessor">#define I2S_COMM_FORMAT_STAND_I2S \</span></div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;<span class="preprocessor">  (I2S_COMM_FORMAT_I2S | I2S_COMM_FORMAT_I2S_MSB)</span></div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;<span class="preprocessor">#define I2S_COMM_FORMAT_STAND_MSB \</span></div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;<span class="preprocessor">  (I2S_COMM_FORMAT_I2S | I2S_COMM_FORMAT_I2S_LSB)</span></div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;<span class="preprocessor">#define I2S_COMM_FORMAT_STAND_PCM_LONG \</span></div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;<span class="preprocessor">  (I2S_COMM_FORMAT_PCM | I2S_COMM_FORMAT_PCM_LONG)</span></div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;<span class="preprocessor">#define I2S_COMM_FORMAT_STAND_PCM_SHORT \</span></div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;<span class="preprocessor">  (I2S_COMM_FORMAT_PCM | I2S_COMM_FORMAT_PCM_SHORT)</span></div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160; </div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;<span class="comment">// Prior IDF 5 support</span></div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &lt; ESP_IDF_VERSION_VAL(5, 0, 0)</span></div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;<span class="preprocessor">#define TaskHandle_t xTaskHandle</span></div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;<span class="preprocessor">#define QueueHandle_t xQueueHandle</span></div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="preprocessor">#define TickType_t portTickType</span></div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160; </div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &lt; ESP_IDF_VERSION_VAL(5, 3, 0)</span></div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;<span class="preprocessor">#define esp_bt_gap_set_device_name esp_bt_dev_set_device_name</span></div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160; </div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;<span class="preprocessor">#define A2DP_DEPRECATED __attribute__((deprecated))</span></div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160; </div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;<span class="preprocessor">#define BT_APP_SIG_WORK_DISPATCH (0x01)</span></div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160; </div>
<div class="line"><a name="l00120"></a><span class="lineno"><a class="line" href="_bluetooth_a2_d_p_common_8h.html#a9bee258e477be3c0e70d6029ed86a019">  120</a></span>&#160;<span class="keyword">typedef</span> void (*<a class="code" href="_bluetooth_a2_d_p_common_8h.html#a9bee258e477be3c0e70d6029ed86a019">app_callback_t</a>)(uint16_t event, <span class="keywordtype">void</span> *param);</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160; </div>
<div class="line"><a name="l00124"></a><span class="lineno"><a class="line" href="structbt__app__msg__t.html">  124</a></span>&#160;<span class="keyword">typedef</span> <span class="keyword">struct </span>{</div>
<div class="line"><a name="l00125"></a><span class="lineno"><a class="line" href="structbt__app__msg__t.html#a3d8f18211a5ce29fecbebc5546e7a17e">  125</a></span>&#160;  uint16_t <a class="code" href="structbt__app__msg__t.html#a3d8f18211a5ce29fecbebc5546e7a17e">sig</a>;      </div>
<div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="structbt__app__msg__t.html#a74f4561abb15d9ae98c3eefdd68de7c4">  126</a></span>&#160;  uint16_t <a class="code" href="structbt__app__msg__t.html#a74f4561abb15d9ae98c3eefdd68de7c4">event</a>;    </div>
<div class="line"><a name="l00127"></a><span class="lineno"><a class="line" href="structbt__app__msg__t.html#a64ce3190ec86ce6a75f6b421319ed8f7">  127</a></span>&#160;  <a class="code" href="_bluetooth_a2_d_p_common_8h.html#a9bee258e477be3c0e70d6029ed86a019">app_callback_t</a> <a class="code" href="structbt__app__msg__t.html#a64ce3190ec86ce6a75f6b421319ed8f7">cb</a>; </div>
<div class="line"><a name="l00128"></a><span class="lineno"><a class="line" href="structbt__app__msg__t.html#aae06d9a8a215b9ae4b5a3827f5e5e7a7">  128</a></span>&#160;  <span class="keywordtype">void</span> *<a class="code" href="structbt__app__msg__t.html#aae06d9a8a215b9ae4b5a3827f5e5e7a7">param</a>;       </div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;} <a class="code" href="structbt__app__msg__t.html">bt_app_msg_t</a>;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160; </div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;<span class="preprocessor">#define BT_AV_TAG &quot;BT_AV&quot;</span></div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;<span class="preprocessor">#define BT_RC_CT_TAG &quot;RCCT&quot;</span></div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;<span class="preprocessor">#define BT_APP_TAG &quot;BT_API&quot;</span></div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160; </div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;<span class="comment">// AVRCP used transaction labels </span></div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;<span class="preprocessor">#define APP_RC_CT_TL_GET_CAPS (0)</span></div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;<span class="preprocessor">#define APP_RC_CT_TL_GET_META_DATA (1)</span></div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;<span class="preprocessor">#define APP_RC_CT_TL_RN_TRACK_CHANGE (2)</span></div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;<span class="preprocessor">#define APP_RC_CT_TL_RN_PLAYBACK_CHANGE (3)</span></div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;<span class="preprocessor">#define APP_RC_CT_TL_RN_PLAY_POS_CHANGE (4)</span></div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160; </div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;<span class="comment">// common a2dp callbacks</span></div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> <span class="keywordtype">void</span> ccall_bt_app_task_handler(<span class="keywordtype">void</span> *arg);</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> <span class="keywordtype">void</span> ccall_app_gap_callback(esp_bt_gap_cb_event_t event,</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;                                       esp_bt_gap_cb_param_t *param);</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> <span class="keywordtype">void</span> ccall_app_rc_ct_callback(esp_avrc_ct_cb_event_t event,</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;                                         esp_avrc_ct_cb_param_t *param);</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> <span class="keywordtype">void</span> ccall_app_a2d_callback(esp_a2d_cb_event_t event,</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;                                       esp_a2d_cb_param_t *param);</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> <span class="keywordtype">void</span> ccall_av_hdl_stack_evt(uint16_t event, <span class="keywordtype">void</span> *p_param);</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160; </div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &gt;= ESP_IDF_VERSION_VAL(4, 0, 0)</span></div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> <span class="keywordtype">void</span> ccall_app_rc_tg_callback(esp_avrc_tg_cb_event_t event,</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;                                         esp_avrc_tg_cb_param_t *param);</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> <span class="keywordtype">void</span> ccall_av_hdl_avrc_tg_evt(uint16_t event, <span class="keywordtype">void</span> *p_param);</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160; </div>
<div class="line"><a name="l00162"></a><span class="lineno"><a class="line" href="group__a2dp.html#ga28a6ac1cbaf47c9d341da5391e2e72b3">  162</a></span>&#160;<span class="keyword">enum</span> <a class="code" href="group__a2dp.html#ga28a6ac1cbaf47c9d341da5391e2e72b3">ReconnectStatus</a> { NoReconnect, AutoReconnect, IsReconnecting };</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160; </div>
<div class="line"><a name="l00169"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html">  169</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a> {</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;  <span class="keyword">friend</span> <span class="keywordtype">void</span> ccall_bt_app_task_handler(<span class="keywordtype">void</span> *arg);</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;  <span class="keyword">friend</span> <span class="keywordtype">void</span> ccall_app_gap_callback(esp_bt_gap_cb_event_t event,</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;                                     esp_bt_gap_cb_param_t *param);</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;  <span class="keyword">friend</span> <span class="keywordtype">void</span> ccall_app_rc_ct_callback(esp_avrc_ct_cb_event_t event,</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;                                       esp_avrc_ct_cb_param_t *param);</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;  <span class="keyword">friend</span> <span class="keywordtype">void</span> ccall_app_a2d_callback(esp_a2d_cb_event_t event,</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;                                     esp_a2d_cb_param_t *param);</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;  <span class="keyword">friend</span> <span class="keywordtype">void</span> ccall_av_hdl_stack_evt(uint16_t event, <span class="keywordtype">void</span> *p_param);</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160; </div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &gt;= ESP_IDF_VERSION_VAL(4, 0, 0)</span></div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;  <span class="keyword">friend</span> <span class="keywordtype">void</span> ccall_app_rc_tg_callback(esp_avrc_tg_cb_event_t event,</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;                                       esp_avrc_tg_cb_param_t *param);</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;  <span class="comment">/* avrc TG event handler */</span></div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;  <span class="keyword">friend</span> <span class="keywordtype">void</span> ccall_av_hdl_avrc_tg_evt(uint16_t event, <span class="keywordtype">void</span> *p_param);</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160; </div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>();</div>
<div class="line"><a name="l00192"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a4bbbd1a2c9c85004afaa7c6dbad45322">  192</a></span>&#160;  <span class="keyword">virtual</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#a4bbbd1a2c9c85004afaa7c6dbad45322">~BluetoothA2DPCommon</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160; </div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;  <span class="keywordtype">void</span> set_auto_reconnect(<span class="keywordtype">bool</span> active);</div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160; </div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> disconnect();</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160; </div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> reconnect();</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160; </div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> connect_to(<a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> peer);</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160; </div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_connected(<span class="keywordtype">bool</span> active);</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160; </div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> end(<span class="keywordtype">bool</span> releaseMemory = <span class="keyword">false</span>);</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160; </div>
<div class="line"><a name="l00214"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a5e76412770515732e3f54275decf02f0">  214</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#a5e76412770515732e3f54275decf02f0">is_connected</a>() {</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;    <span class="keywordflow">return</span> connection_state == <a class="code" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590a0248c5f6ba7c85f2044a9a2c79bc57d4">ESP_A2D_CONNECTION_STATE_CONNECTED</a>;</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;  }</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160; </div>
<div class="line"><a name="l00219"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a0bab92d9317837ecaeacbfe26814e28c">  219</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#a0bab92d9317837ecaeacbfe26814e28c">set_volume</a>(uint8_t volume) {</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;    volume_value = std::min((<span class="keywordtype">int</span>)volume, 0x7F);</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;    ESP_LOGI(BT_AV_TAG, <span class="stringliteral">&quot;set_volume: %d&quot;</span>, volume_value);</div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;    volume_control()-&gt;set_volume(volume_value);</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;    volume_control()-&gt;set_enabled(<span class="keyword">true</span>);</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;    is_volume_used = <span class="keyword">true</span>;</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;  }</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160; </div>
<div class="line"><a name="l00228"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a0e570c2c2f9db40873286e0571f0d93a">  228</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">int</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#a0e570c2c2f9db40873286e0571f0d93a">get_volume</a>() { <span class="keywordflow">return</span> is_volume_used ? volume_value : 0; }</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160; </div>
<div class="line"><a name="l00231"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a7757ddbf424aeb909dc952d7c40fc241">  231</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#a7757ddbf424aeb909dc952d7c40fc241">set_volume_control</a>(<a class="code" href="class_a2_d_p_volume_control.html">A2DPVolumeControl</a> *ptr) {</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;    volume_control_ptr = ptr;</div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;  }</div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160; </div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;  <span class="keyword">virtual</span> <a class="code" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> get_audio_state();</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160; </div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;  <span class="keyword">virtual</span> <a class="code" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> get_connection_state();</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160; </div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_on_connection_state_changed(</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;      <span class="keywordtype">void</span> (*callBack)(<a class="code" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> state, <span class="keywordtype">void</span> *),</div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;      <span class="keywordtype">void</span> *obj = <span class="keyword">nullptr</span>);</div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160; </div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_on_audio_state_changed_post(</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;      <span class="keywordtype">void</span> (*callBack)(<a class="code" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state, <span class="keywordtype">void</span> *),</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;      <span class="keywordtype">void</span> *obj = <span class="keyword">nullptr</span>);</div>
<div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160; </div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_on_audio_state_changed(</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;      <span class="keywordtype">void</span> (*callBack)(<a class="code" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state, <span class="keywordtype">void</span> *),</div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;      <span class="keywordtype">void</span> *obj = <span class="keyword">nullptr</span>);</div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160; </div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> debounce(<span class="keywordtype">void</span> (*cb)(<span class="keywordtype">void</span>), <span class="keywordtype">int</span> ms);</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160; </div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;  <span class="keywordtype">void</span> log_free_heap();</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160; </div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;  <span class="keyword">const</span> <span class="keywordtype">char</span> *to_str(<a class="code" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> state);</div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160; </div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;  <span class="keyword">const</span> <span class="keywordtype">char</span> *to_str(<a class="code" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state);</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160; </div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;  <span class="keyword">const</span> <span class="keywordtype">char</span> *to_str(<a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> bda);</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160; </div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &gt;= ESP_IDF_VERSION_VAL(4, 0, 0)</span></div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;  <span class="keyword">const</span> <span class="keywordtype">char</span> *to_str(<a class="code" href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a> state);</div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160; </div>
<div class="line"><a name="l00280"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a68f9e168839f0faeb72705ccabbb6b7a">  280</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#a68f9e168839f0faeb72705ccabbb6b7a">set_task_priority</a>(UBaseType_t priority) { task_priority = priority; }</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160; </div>
<div class="line"><a name="l00284"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a694940fad2a2d498875cfbdf52eea58b">  284</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#a694940fad2a2d498875cfbdf52eea58b">set_task_core</a>(BaseType_t core) { task_core = core; }</div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160; </div>
<div class="line"><a name="l00287"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#ae5e96c34428c50873a0ca7423a6b5402">  287</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#ae5e96c34428c50873a0ca7423a6b5402">set_event_queue_size</a>(<span class="keywordtype">int</span> size) { event_queue_size = size; }</div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160; </div>
<div class="line"><a name="l00290"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a04bc52a4279a503203084492fe20c32e">  290</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#a04bc52a4279a503203084492fe20c32e">set_event_stack_size</a>(<span class="keywordtype">int</span> size) { event_stack_size = size; }</div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160; </div>
<div class="line"><a name="l00293"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#ac21e1dbd2f5f475da871a7e778ba1a40">  293</a></span>&#160;  <span class="keyword">virtual</span> <a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> *<a class="code" href="class_bluetooth_a2_d_p_common.html#ac21e1dbd2f5f475da871a7e778ba1a40">get_last_peer_address</a>() { <span class="keywordflow">return</span> &amp;last_connection; }</div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160; </div>
<div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &gt;= ESP_IDF_VERSION_VAL(4, 0, 0)</span></div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_discoverability(<a class="code" href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">esp_bt_discovery_mode_t</a> d);</div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160; </div>
<div class="line"><a name="l00301"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a8b37f48a6b0bca33fb21b2a9ae9dab7c">  301</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#a8b37f48a6b0bca33fb21b2a9ae9dab7c">set_connectable</a>(<span class="keywordtype">bool</span> connectable) {</div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;    set_scan_mode_connectable(connectable);</div>
<div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;  }</div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160; </div>
<div class="line"><a name="l00306"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a726f45e4d405f5c5f5b259f11aaf8246">  306</a></span>&#160;  <span class="keyword">virtual</span> <span class="keyword">const</span> <span class="keywordtype">char</span> *<a class="code" href="class_bluetooth_a2_d_p_common.html#a726f45e4d405f5c5f5b259f11aaf8246">get_name</a>() { <span class="keywordflow">return</span> bt_name; }</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160; </div>
<div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> clean_last_connection();</div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160; </div>
<div class="line"><a name="l00313"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a41ab8453d4f7f88d68d6cdb1a866532b">  313</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#a41ab8453d4f7f88d68d6cdb1a866532b">set_default_bt_mode</a>(<a class="code" href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">esp_bt_mode_t</a> mode) { bt_mode = mode; }</div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160; </div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &gt;= ESP_IDF_VERSION_VAL(5, 2, 1)</span></div>
<div class="line"><a name="l00317"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a145c20271b53d4329e0e2c7fa36692b0">  317</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#a145c20271b53d4329e0e2c7fa36692b0">set_bluedroid_config_t</a>(esp_bluedroid_config_t cfg) {</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;    bluedroid_config = cfg;</div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;  }</div>
<div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;  <span class="keywordtype">void</span> delay_ms(uint32_t millis);</div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;  <span class="keywordtype">unsigned</span> <span class="keywordtype">long</span> get_millis();</div>
<div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160; </div>
<div class="line"><a name="l00334"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a17013c6f40042c68821548cab9ddb5eb">  334</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#a17013c6f40042c68821548cab9ddb5eb">set_avrc_rn_events</a>(std::vector&lt;esp_avrc_rn_event_ids_t&gt; events) {</div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;    avrc_rn_events = events;</div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;  }</div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160; </div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160; <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;  <span class="keyword">const</span> <span class="keywordtype">char</span> *bt_name = {0};</div>
<div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;  <a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> peer_bd_addr;</div>
<div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;  <a class="code" href="group__a2dp.html#ga28a6ac1cbaf47c9d341da5391e2e72b3">ReconnectStatus</a> reconnect_status = NoReconnect;</div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;  <span class="keywordtype">unsigned</span> <span class="keywordtype">long</span> reconnect_timout = 0;</div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;  <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> default_reconnect_timout = 10000;</div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;  <span class="keywordtype">bool</span> is_autoreconnect_allowed = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;  uint32_t debounce_ms = 0;</div>
<div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;  <a class="code" href="class_a2_d_p_default_volume_control.html">A2DPDefaultVolumeControl</a> default_volume_control;</div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;  <a class="code" href="class_a2_d_p_volume_control.html">A2DPVolumeControl</a> *volume_control_ptr = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;  <a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> last_connection = {0, 0, 0, 0, 0, 0};</div>
<div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;  <span class="keywordtype">bool</span> is_start_disabled = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;  <span class="keywordtype">bool</span> is_target_status_active = <span class="keyword">true</span>;</div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;  void (*connection_state_callback)(<a class="code" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> state,</div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;                                    <span class="keywordtype">void</span> *obj) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;  void (*audio_state_callback)(<a class="code" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state,</div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;                               <span class="keywordtype">void</span> *obj) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;  void (*audio_state_callback_post)(<a class="code" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state,</div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;                                    <span class="keywordtype">void</span> *obj) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;  <span class="keywordtype">void</span> *connection_state_obj = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;  <span class="keywordtype">void</span> *audio_state_obj = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;  <span class="keywordtype">void</span> *audio_state_obj_post = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;  <span class="keyword">const</span> <span class="keywordtype">char</span> *m_a2d_conn_state_str[4] = {<span class="stringliteral">&quot;Disconnected&quot;</span>, <span class="stringliteral">&quot;Connecting&quot;</span>,</div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;                                         <span class="stringliteral">&quot;Connected&quot;</span>, <span class="stringliteral">&quot;Disconnecting&quot;</span>};</div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;  <span class="keyword">const</span> <span class="keywordtype">char</span> *m_a2d_audio_state_str[4] = {<span class="stringliteral">&quot;Suspended&quot;</span>, <span class="stringliteral">&quot;Started&quot;</span>,  <span class="stringliteral">&quot;Suspended&quot;</span>, <span class="stringliteral">&quot;Suspended&quot;</span>};</div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;  <span class="keyword">const</span> <span class="keywordtype">char</span> *m_avrc_playback_state_str[5] = {<span class="stringliteral">&quot;stopped&quot;</span>, <span class="stringliteral">&quot;playing&quot;</span>, <span class="stringliteral">&quot;paused&quot;</span>,</div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;                                              <span class="stringliteral">&quot;forward seek&quot;</span>, <span class="stringliteral">&quot;reverse seek&quot;</span>};</div>
<div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;  <a class="code" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> audio_state = <a class="code" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a2cac45c7b95065ed02420c5a632ab538">ESP_A2D_AUDIO_STATE_STOPPED</a>;</div>
<div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;  <a class="code" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> connection_state =</div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;      <a class="code" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410">ESP_A2D_CONNECTION_STATE_DISCONNECTED</a>;</div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;  UBaseType_t task_priority = configMAX_PRIORITIES - 10;</div>
<div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;  <span class="comment">// volume</span></div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;  uint8_t volume_value = 0;</div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;  <span class="keywordtype">bool</span> is_volume_used = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;  BaseType_t task_core = 1;</div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160; </div>
<div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;  <span class="keywordtype">int</span> event_queue_size = 20;</div>
<div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;  <span class="keywordtype">int</span> event_stack_size = 3072;</div>
<div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;  <a class="code" href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">esp_bt_mode_t</a> bt_mode = <a class="code" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a9fab3664192991a2bd656ba090c1da85">ESP_BT_MODE_CLASSIC_BT</a>;</div>
<div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;  std::vector&lt;esp_avrc_rn_event_ids_t&gt; avrc_rn_events = {</div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;      ESP_AVRC_RN_VOLUME_CHANGE};</div>
<div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160; </div>
<div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;  QueueHandle_t app_task_queue = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;  TaskHandle_t app_task_handle = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160; </div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &gt;= ESP_IDF_VERSION_VAL(5, 2, 1)</span></div>
<div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;  esp_bluedroid_config_t bluedroid_config{.ssp_en = <span class="keyword">true</span>};</div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160; </div>
<div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> init_nvs();</div>
<div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;  <span class="keyword">virtual</span> esp_err_t esp_a2d_connect(<a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> peer) = 0;</div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;  <span class="keyword">virtual</span> <span class="keyword">const</span> <span class="keywordtype">char</span> *last_bda_nvs_name() = 0;</div>
<div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> get_last_connection();</div>
<div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_last_connection(<a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> bda);</div>
<div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> has_last_connection();</div>
<div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> read_address(<span class="keyword">const</span> <span class="keywordtype">char</span> *name, <a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> &amp;bda);</div>
<div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> write_address(<span class="keyword">const</span> <span class="keywordtype">char</span> *name, <a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> bda);</div>
<div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160; </div>
<div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;  <span class="comment">// change the scan mode</span></div>
<div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_scan_mode_connectable(<span class="keywordtype">bool</span> connectable);</div>
<div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_scan_mode_connectable_default() = 0;</div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160; </div>
<div class="line"><a name="l00401"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a6fec0cfd3d0d9017b7ffcf82630ab89a">  401</a></span>&#160;  <span class="keyword">virtual</span> <a class="code" href="class_a2_d_p_volume_control.html">A2DPVolumeControl</a> *<a class="code" href="class_bluetooth_a2_d_p_common.html#a6fec0cfd3d0d9017b7ffcf82630ab89a">volume_control</a>() {</div>
<div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;    <span class="keywordflow">return</span> volume_control_ptr != <span class="keyword">nullptr</span> ? volume_control_ptr</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;                                         : &amp;default_volume_control;</div>
<div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;  }</div>
<div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160; </div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> bt_start();</div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;  <span class="keyword">virtual</span> esp_err_t bluedroid_init();</div>
<div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;  <span class="keyword">virtual</span> esp_err_t esp_a2d_disconnect(<a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> remote_bda) = 0;</div>
<div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> app_task_start_up();</div>
<div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> app_task_shut_down();</div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> app_send_msg(<a class="code" href="structbt__app__msg__t.html">bt_app_msg_t</a> *msg);</div>
<div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> app_task_handler(<span class="keywordtype">void</span> *arg);</div>
<div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> app_work_dispatched(<a class="code" href="structbt__app__msg__t.html">bt_app_msg_t</a> *msg);</div>
<div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> isSource() = 0;</div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;  <span class="comment">// GAP callback</span></div>
<div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> app_gap_callback(esp_bt_gap_cb_event_t event,</div>
<div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;                                esp_bt_gap_cb_param_t *param) = 0;</div>
<div class="line"><a name="l00419"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#ac5b64dc4ea62522eee0694454a393b2d">  419</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#ac5b64dc4ea62522eee0694454a393b2d">app_rc_ct_callback</a>(esp_avrc_ct_cb_event_t event,</div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;                                  esp_avrc_ct_cb_param_t *param) = 0;</div>
<div class="line"><a name="l00422"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_common.html#a6aaac4480b57cbdbef5e07ca619eb330">  422</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#a6aaac4480b57cbdbef5e07ca619eb330">app_a2d_callback</a>(esp_a2d_cb_event_t event,</div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;                                esp_a2d_cb_param_t *param) = 0;</div>
<div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;  <span class="comment">// handler for bluetooth stack enabled events</span></div>
<div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> av_hdl_stack_evt(uint16_t event, <span class="keywordtype">void</span> *p_param) = 0;</div>
<div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160; </div>
<div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &gt;= ESP_IDF_VERSION_VAL(4, 0, 0)</span></div>
<div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;  <a class="code" href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">esp_bt_discovery_mode_t</a> discoverability = <a class="code" href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da4fa5e4d5a80a86974515a8a362989711">ESP_BT_GENERAL_DISCOVERABLE</a>;</div>
<div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> app_rc_tg_callback(esp_avrc_tg_cb_event_t event,</div>
<div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;                                  esp_avrc_tg_cb_param_t *param) = 0;</div>
<div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> av_hdl_avrc_tg_evt(uint16_t event, <span class="keywordtype">void</span> *p_param) = 0;</div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160; </div>
<div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;};</div>
<div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160; </div>
<div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;<span class="keyword">extern</span> <a class="code" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a> *actual_bluetooth_a2dp_common;</div>
<div class="ttc" id="a_bluetooth_a2_d_p_common_8h_html_a9bee258e477be3c0e70d6029ed86a019"><div class="ttname"><a href="_bluetooth_a2_d_p_common_8h.html#a9bee258e477be3c0e70d6029ed86a019">app_callback_t</a></div><div class="ttdeci">void(* app_callback_t)(uint16_t event, void *param)</div><div class="ttdoc">handler for the dispatched work</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:120</div></div>
<div class="ttc" id="aclass_a2_d_p_default_volume_control_html"><div class="ttname"><a href="class_a2_d_p_default_volume_control.html">A2DPDefaultVolumeControl</a></div><div class="ttdoc">Default implementation for handling of the volume of the audio data.</div><div class="ttdef"><b>Definition:</b> A2DPVolumeControl.h:105</div></div>
<div class="ttc" id="aclass_a2_d_p_volume_control_html"><div class="ttname"><a href="class_a2_d_p_volume_control.html">A2DPVolumeControl</a></div><div class="ttdoc">Abstract class for handling of the volume of the audio data.</div><div class="ttdef"><b>Definition:</b> A2DPVolumeControl.h:45</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></div><div class="ttdoc">Common Bluetooth A2DP functions.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:169</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a04bc52a4279a503203084492fe20c32e"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a04bc52a4279a503203084492fe20c32e">BluetoothA2DPCommon::set_event_stack_size</a></div><div class="ttdeci">void set_event_stack_size(int size)</div><div class="ttdoc">Defines the stack size of the event task (in bytes)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:290</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a0bab92d9317837ecaeacbfe26814e28c"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a0bab92d9317837ecaeacbfe26814e28c">BluetoothA2DPCommon::set_volume</a></div><div class="ttdeci">virtual void set_volume(uint8_t volume)</div><div class="ttdoc">Sets the volume (range 0 - 127)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:219</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a0e570c2c2f9db40873286e0571f0d93a"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a0e570c2c2f9db40873286e0571f0d93a">BluetoothA2DPCommon::get_volume</a></div><div class="ttdeci">virtual int get_volume()</div><div class="ttdoc">Determines the actual volume.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:228</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a145c20271b53d4329e0e2c7fa36692b0"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a145c20271b53d4329e0e2c7fa36692b0">BluetoothA2DPCommon::set_bluedroid_config_t</a></div><div class="ttdeci">void set_bluedroid_config_t(esp_bluedroid_config_t cfg)</div><div class="ttdoc">Defines the esp_bluedroid_config_t: Available from IDF 5.2.1.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:317</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a17013c6f40042c68821548cab9ddb5eb"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a17013c6f40042c68821548cab9ddb5eb">BluetoothA2DPCommon::set_avrc_rn_events</a></div><div class="ttdeci">virtual void set_avrc_rn_events(std::vector&lt; esp_avrc_rn_event_ids_t &gt; events)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:334</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a41ab8453d4f7f88d68d6cdb1a866532b"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a41ab8453d4f7f88d68d6cdb1a866532b">BluetoothA2DPCommon::set_default_bt_mode</a></div><div class="ttdeci">virtual void set_default_bt_mode(esp_bt_mode_t mode)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:313</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a4bbbd1a2c9c85004afaa7c6dbad45322"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a4bbbd1a2c9c85004afaa7c6dbad45322">BluetoothA2DPCommon::~BluetoothA2DPCommon</a></div><div class="ttdeci">virtual ~BluetoothA2DPCommon()=default</div><div class="ttdoc">Destructor.</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a5e76412770515732e3f54275decf02f0"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a5e76412770515732e3f54275decf02f0">BluetoothA2DPCommon::is_connected</a></div><div class="ttdeci">virtual bool is_connected()</div><div class="ttdoc">Checks if A2DP is connected.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:214</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a68f9e168839f0faeb72705ccabbb6b7a"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a68f9e168839f0faeb72705ccabbb6b7a">BluetoothA2DPCommon::set_task_priority</a></div><div class="ttdeci">void set_task_priority(UBaseType_t priority)</div><div class="ttdoc">defines the task priority (the default value is configMAX_PRIORITIES - 10)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:280</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a694940fad2a2d498875cfbdf52eea58b"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a694940fad2a2d498875cfbdf52eea58b">BluetoothA2DPCommon::set_task_core</a></div><div class="ttdeci">void set_task_core(BaseType_t core)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:284</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a6aaac4480b57cbdbef5e07ca619eb330"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a6aaac4480b57cbdbef5e07ca619eb330">BluetoothA2DPCommon::app_a2d_callback</a></div><div class="ttdeci">virtual void app_a2d_callback(esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param)=0</div><div class="ttdoc">callback function for A2DP source</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a6fec0cfd3d0d9017b7ffcf82630ab89a"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a6fec0cfd3d0d9017b7ffcf82630ab89a">BluetoothA2DPCommon::volume_control</a></div><div class="ttdeci">virtual A2DPVolumeControl * volume_control()</div><div class="ttdoc">provides access to the VolumeControl object</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:401</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a726f45e4d405f5c5f5b259f11aaf8246"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a726f45e4d405f5c5f5b259f11aaf8246">BluetoothA2DPCommon::get_name</a></div><div class="ttdeci">virtual const char * get_name()</div><div class="ttdoc">Provides the actual SSID name.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:306</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a7757ddbf424aeb909dc952d7c40fc241"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a7757ddbf424aeb909dc952d7c40fc241">BluetoothA2DPCommon::set_volume_control</a></div><div class="ttdeci">virtual void set_volume_control(A2DPVolumeControl *ptr)</div><div class="ttdoc">you can define a custom VolumeControl implementation</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:231</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a8b37f48a6b0bca33fb21b2a9ae9dab7c"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a8b37f48a6b0bca33fb21b2a9ae9dab7c">BluetoothA2DPCommon::set_connectable</a></div><div class="ttdeci">virtual void set_connectable(bool connectable)</div><div class="ttdoc">Bluetooth connectable.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:301</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_ac21e1dbd2f5f475da871a7e778ba1a40"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#ac21e1dbd2f5f475da871a7e778ba1a40">BluetoothA2DPCommon::get_last_peer_address</a></div><div class="ttdeci">virtual esp_bd_addr_t * get_last_peer_address()</div><div class="ttdoc">Provides the address of the last device.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:293</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_ac5b64dc4ea62522eee0694454a393b2d"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#ac5b64dc4ea62522eee0694454a393b2d">BluetoothA2DPCommon::app_rc_ct_callback</a></div><div class="ttdeci">virtual void app_rc_ct_callback(esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param)=0</div><div class="ttdoc">callback function for AVRCP controller</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_ae5e96c34428c50873a0ca7423a6b5402"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#ae5e96c34428c50873a0ca7423a6b5402">BluetoothA2DPCommon::set_event_queue_size</a></div><div class="ttdeci">void set_event_queue_size(int size)</div><div class="ttdoc">Defines the queue size of the event task.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:287</div></div>
<div class="ttc" id="agroup__a2dp_html_ga28a6ac1cbaf47c9d341da5391e2e72b3"><div class="ttname"><a href="group__a2dp.html#ga28a6ac1cbaf47c9d341da5391e2e72b3">ReconnectStatus</a></div><div class="ttdeci">ReconnectStatus</div><div class="ttdoc">Buetooth A2DP Reconnect Status.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:162</div></div>
<div class="ttc" id="agroup__a2dp_html_ga49adfa87b1ad7420b0075a0ac03cc194"><div class="ttname"><a href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a></div><div class="ttdeci">esp_a2d_audio_state_t</div><div class="ttdoc">Buetooth A2DP datapath states.</div><div class="ttdef"><b>Definition:</b> external_lists.h:5</div></div>
<div class="ttc" id="agroup__a2dp_html_ga52caa2d1e1c9d880c9651d52ff78a590"><div class="ttname"><a href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a></div><div class="ttdeci">esp_a2d_connection_state_t</div><div class="ttdoc">Buetooth A2DP connection states.</div><div class="ttdef"><b>Definition:</b> external_lists.h:16</div></div>
<div class="ttc" id="agroup__a2dp_html_ga6562796046744d7333ad2c64d2c8557d"><div class="ttname"><a href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">esp_bt_discovery_mode_t</a></div><div class="ttdeci">esp_bt_discovery_mode_t</div><div class="ttdoc">AVRCP discovery mode.</div><div class="ttdef"><b>Definition:</b> external_lists.h:85</div></div>
<div class="ttc" id="agroup__a2dp_html_ga89fdf5fb26b1ea6f33d36cc0eebca4fb"><div class="ttname"><a href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a></div><div class="ttdeci">esp_avrc_playback_stat_t</div><div class="ttdoc">AVRCP current status of playback.</div><div class="ttdef"><b>Definition:</b> external_lists.h:72</div></div>
<div class="ttc" id="agroup__a2dp_html_ga9861ef3ac455a4b2875219d457073de4"><div class="ttname"><a href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">esp_bt_mode_t</a></div><div class="ttdeci">esp_bt_mode_t</div><div class="ttdoc">Bluetooth Controller mode.</div><div class="ttdef"><b>Definition:</b> external_lists.h:96</div></div>
<div class="ttc" id="agroup__a2dp_html_gae1f72542f04666cd97c26732366bf109"><div class="ttname"><a href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a></div><div class="ttdeci">uint8_t esp_bd_addr_t[ESP_BD_ADDR_LEN]</div><div class="ttdoc">Bluetooth address.</div><div class="ttdef"><b>Definition:</b> external_lists.h:107</div></div>
<div class="ttc" id="agroup__a2dp_html_gga49adfa87b1ad7420b0075a0ac03cc194a2cac45c7b95065ed02420c5a632ab538"><div class="ttname"><a href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a2cac45c7b95065ed02420c5a632ab538">ESP_A2D_AUDIO_STATE_STOPPED</a></div><div class="ttdeci">@ ESP_A2D_AUDIO_STATE_STOPPED</div><div class="ttdef"><b>Definition:</b> external_lists.h:8</div></div>
<div class="ttc" id="agroup__a2dp_html_gga52caa2d1e1c9d880c9651d52ff78a590a0248c5f6ba7c85f2044a9a2c79bc57d4"><div class="ttname"><a href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590a0248c5f6ba7c85f2044a9a2c79bc57d4">ESP_A2D_CONNECTION_STATE_CONNECTED</a></div><div class="ttdeci">@ ESP_A2D_CONNECTION_STATE_CONNECTED</div><div class="ttdef"><b>Definition:</b> external_lists.h:19</div></div>
<div class="ttc" id="agroup__a2dp_html_gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410"><div class="ttname"><a href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410">ESP_A2D_CONNECTION_STATE_DISCONNECTED</a></div><div class="ttdeci">@ ESP_A2D_CONNECTION_STATE_DISCONNECTED</div><div class="ttdef"><b>Definition:</b> external_lists.h:17</div></div>
<div class="ttc" id="agroup__a2dp_html_gga6562796046744d7333ad2c64d2c8557da4fa5e4d5a80a86974515a8a362989711"><div class="ttname"><a href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da4fa5e4d5a80a86974515a8a362989711">ESP_BT_GENERAL_DISCOVERABLE</a></div><div class="ttdeci">@ ESP_BT_GENERAL_DISCOVERABLE</div><div class="ttdef"><b>Definition:</b> external_lists.h:88</div></div>
<div class="ttc" id="agroup__a2dp_html_gga9861ef3ac455a4b2875219d457073de4a9fab3664192991a2bd656ba090c1da85"><div class="ttname"><a href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a9fab3664192991a2bd656ba090c1da85">ESP_BT_MODE_CLASSIC_BT</a></div><div class="ttdeci">@ ESP_BT_MODE_CLASSIC_BT</div><div class="ttdef"><b>Definition:</b> external_lists.h:99</div></div>
<div class="ttc" id="astructbt__app__msg__t_html"><div class="ttname"><a href="structbt__app__msg__t.html">bt_app_msg_t</a></div><div class="ttdoc">Internal message to be sent for BluetoothA2DPSink and BluetoothA2DPSource.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:124</div></div>
<div class="ttc" id="astructbt__app__msg__t_html_a3d8f18211a5ce29fecbebc5546e7a17e"><div class="ttname"><a href="structbt__app__msg__t.html#a3d8f18211a5ce29fecbebc5546e7a17e">bt_app_msg_t::sig</a></div><div class="ttdeci">uint16_t sig</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:125</div></div>
<div class="ttc" id="astructbt__app__msg__t_html_a64ce3190ec86ce6a75f6b421319ed8f7"><div class="ttname"><a href="structbt__app__msg__t.html#a64ce3190ec86ce6a75f6b421319ed8f7">bt_app_msg_t::cb</a></div><div class="ttdeci">app_callback_t cb</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:127</div></div>
<div class="ttc" id="astructbt__app__msg__t_html_a74f4561abb15d9ae98c3eefdd68de7c4"><div class="ttname"><a href="structbt__app__msg__t.html#a74f4561abb15d9ae98c3eefdd68de7c4">bt_app_msg_t::event</a></div><div class="ttdeci">uint16_t event</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:126</div></div>
<div class="ttc" id="astructbt__app__msg__t_html_aae06d9a8a215b9ae4b5a3827f5e5e7a7"><div class="ttname"><a href="structbt__app__msg__t.html#aae06d9a8a215b9ae4b5a3827f5e5e7a7">bt_app_msg_t::param</a></div><div class="ttdeci">void * param</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:128</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
