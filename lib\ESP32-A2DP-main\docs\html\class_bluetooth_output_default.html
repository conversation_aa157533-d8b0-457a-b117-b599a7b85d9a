<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: BluetoothOutputDefault Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-attribs">Protected Attributes</a> &#124;
<a href="class_bluetooth_output_default-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">BluetoothOutputDefault Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Default Output Class providing both the Legacy I2S and the AudioTools I2S functionality.  
 <a href="class_bluetooth_output_default.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_bluetooth_output_8h_source.html">BluetoothOutput.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for BluetoothOutputDefault:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_bluetooth_output_default.png" usemap="#BluetoothOutputDefault_map" alt=""/>
  <map id="BluetoothOutputDefault_map" name="BluetoothOutputDefault_map">
<area href="class_bluetooth_output.html" title="Abstract Output Class." alt="BluetoothOutput" shape="rect" coords="0,0,141,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a9bc50801dbb28cdd89d13ac665f611c0"><td class="memItemLeft" align="right" valign="top"><a id="a9bc50801dbb28cdd89d13ac665f611c0"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>begin</b> ()</td></tr>
<tr class="separator:a9bc50801dbb28cdd89d13ac665f611c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1fe26991de8281bcf7bddf4cf743c045"><td class="memItemLeft" align="right" valign="top"><a id="a1fe26991de8281bcf7bddf4cf743c045"></a>
size_t&#160;</td><td class="memItemRight" valign="bottom"><b>write</b> (const uint8_t *data, size_t len)</td></tr>
<tr class="separator:a1fe26991de8281bcf7bddf4cf743c045"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77e9c95e9b7d839a2f01b5bd6745a93f"><td class="memItemLeft" align="right" valign="top"><a id="a77e9c95e9b7d839a2f01b5bd6745a93f"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>end</b> () override</td></tr>
<tr class="separator:a77e9c95e9b7d839a2f01b5bd6745a93f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e89371cfbbfeb6b5df534775c851b98"><td class="memItemLeft" align="right" valign="top"><a id="a1e89371cfbbfeb6b5df534775c851b98"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_sample_rate</b> (int rate) override</td></tr>
<tr class="separator:a1e89371cfbbfeb6b5df534775c851b98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe6e8a6eb8cf796004950e5027e43df5"><td class="memItemLeft" align="right" valign="top"><a id="abe6e8a6eb8cf796004950e5027e43df5"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_output_active</b> (bool active) override</td></tr>
<tr class="separator:abe6e8a6eb8cf796004950e5027e43df5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0442a097f1f125d0cdc4c478023bb2b"><td class="memItemLeft" align="right" valign="top"><a id="ab0442a097f1f125d0cdc4c478023bb2b"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_output_default.html#ab0442a097f1f125d0cdc4c478023bb2b">set_output</a> (AudioOutput &amp;output)</td></tr>
<tr class="memdesc:ab0442a097f1f125d0cdc4c478023bb2b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output AudioStream using AudioTools library. <br /></td></tr>
<tr class="separator:ab0442a097f1f125d0cdc4c478023bb2b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b2b09e4de6284433bccd92594a35b55"><td class="memItemLeft" align="right" valign="top"><a id="a0b2b09e4de6284433bccd92594a35b55"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_output_default.html#a0b2b09e4de6284433bccd92594a35b55">set_output</a> (AudioStream &amp;output)</td></tr>
<tr class="memdesc:a0b2b09e4de6284433bccd92594a35b55"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output AudioStream using AudioTools library. <br /></td></tr>
<tr class="separator:a0b2b09e4de6284433bccd92594a35b55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abfe6bfeac419b8a8d1033954d23dcb89"><td class="memItemLeft" align="right" valign="top"><a id="abfe6bfeac419b8a8d1033954d23dcb89"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_output_default.html#abfe6bfeac419b8a8d1033954d23dcb89">set_output</a> (Print &amp;output)</td></tr>
<tr class="memdesc:abfe6bfeac419b8a8d1033954d23dcb89"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output to Arduino Print. <br /></td></tr>
<tr class="separator:abfe6bfeac419b8a8d1033954d23dcb89"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-attribs"></a>
Protected Attributes</h2></td></tr>
<tr class="memitem:aa0b4ad6a887a658d795941249e3e83e0"><td class="memItemLeft" align="right" valign="top"><a id="aa0b4ad6a887a658d795941249e3e83e0"></a>
<a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a>&#160;</td><td class="memItemRight" valign="bottom"><b>out_tools</b></td></tr>
<tr class="separator:aa0b4ad6a887a658d795941249e3e83e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad4aa861fe01a222ac30a07a15173f6bb"><td class="memItemLeft" align="right" valign="top"><a id="ad4aa861fe01a222ac30a07a15173f6bb"></a>
<a class="el" href="class_bluetooth_output_legacy.html">BluetoothOutputLegacy</a>&#160;</td><td class="memItemRight" valign="bottom"><b>out_legacy</b></td></tr>
<tr class="separator:ad4aa861fe01a222ac30a07a15173f6bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Default Output Class providing both the Legacy I2S and the AudioTools I2S functionality. </p>
<dl class="section author"><dt>Author</dt><dd>Phil Schatzmann </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Apache License Version 2 </dd></dl>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li>src/<a class="el" href="_bluetooth_output_8h_source.html">BluetoothOutput.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
