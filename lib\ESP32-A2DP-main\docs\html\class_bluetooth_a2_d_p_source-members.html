<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">BluetoothA2DPSource Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>, including all inherited members.</p>
<table class="directory">
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>a2d_app_heart_beat</b>(void *arg) (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#aa44bdbc77afd851d305a5412e3cc92e1">app_a2d_callback</a>(esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param) override</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_gap_callback</b>(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t *param) override (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a1e2a51edb571273cbd349fbed8874cc1">app_rc_ct_callback</a>(esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param) override</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_rc_tg_callback</b>(esp_avrc_tg_cb_event_t event, esp_avrc_tg_cb_param_t *param) override (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_send_msg</b>(bt_app_msg_t *msg) (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_task_handle</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_task_handler</b>(void *arg) (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_task_queue</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_task_shut_down</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_task_start_up</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_work_dispatched</b>(bt_app_msg_t *msg) (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>audio_state</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>audio_state_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>audio_state_callback_post</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>audio_state_obj</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>audio_state_obj_post</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>av_hdl_avrc_tg_evt</b>(uint16_t event, void *p_param) override (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>av_hdl_stack_evt</b>(uint16_t event, void *p_param) override (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>avrc_rn_events</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bluedroid_config</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bluedroid_init</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a7cbfe59ac018d6886622c24139742ebe">BluetoothA2DPCommon</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a545a8b10ab474d787744f85fe784d49a">BluetoothA2DPSource</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bt_app_av_media_proc</b>(uint16_t event, void *param) (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#ad3bb1aaddd9dcbd9da6a37c5aded8727">bt_app_av_sm_hdlr</a>(uint16_t event, void *param)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bt_app_av_state_connected_hdlr</b>(uint16_t event, void *param) (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#ac7131b626b43a516ae4ae9df6a7ec366">bt_app_av_state_connecting_hdlr</a>(uint16_t event, void *param)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bt_app_av_state_disconnecting_hdlr</b>(uint16_t event, void *param) (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a025e54e7d0e8a4e07d5a125273fcb875">bt_app_av_state_unconnected_hdlr</a>(uint16_t event, void *param)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bt_app_work_dispatch</b>(bt_app_cb_t p_cback, uint16_t event, void *p_params, int param_len, bt_app_copy_cb_t p_copy_cback) (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#ae9f14078c1d5dd00c93049fa8b2e283e">bt_av_hdl_avrc_ct_evt</a>(uint16_t event, void *p_param)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bt_av_notify_evt_handler</b>(uint8_t event, esp_avrc_rn_param_t *param) (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bt_av_volume_changed</b>(void) (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bt_mode</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bt_name</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bt_names</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a8be3cf8679b236293658c06cd1ed010b">bt_start</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>ccall_a2d_app_heart_beat</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">friend</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>ccall_bt_app_a2d_data_cb</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">friend</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>ccall_bt_app_av_sm_hdlr</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">friend</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>ccall_bt_av_hdl_avrc_ct_evt</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">friend</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a962cc9aef396b06c7eb6f56462a743ac">clean_last_connection</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a788d81fe538021f912d737de72ed6be6">connect_to</a>(esp_bd_addr_t peer)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>connection_state</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>connection_state_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>connection_state_obj</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#aa6601d3c57e37f77bfdd03a3ef6231e2">debounce</a>(void(*cb)(void), int ms)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>debounce_ms</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>default_reconnect_timout</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>default_volume_control</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a2302fff324e703c3906835f759e87307">delay_ms</a>(uint32_t millis)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>dev_name</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#ab63e627832d6377be32dd700130bf0d8">disconnect</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>discoverability</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>discovery_active</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>discovery_mode_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#aef98c427f8e590be0a4dfaa28a5cb4fd">end</a>(bool releaseMemory=false) override</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>esp_a2d_connect</b>(esp_bd_addr_t peer) override (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>esp_a2d_disconnect</b>(esp_bd_addr_t peer) override (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>event_queue_size</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>event_stack_size</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>filter_inquiry_scan_result</b>(esp_bt_gap_cb_param_t *param) (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a2c3a7aa140cf42a1f324e7669a65e5cc">get_audio_data</a>(uint8_t *data, int32_t len)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a03d1c23eb8a98bccdd15970f9d35db8c">get_audio_data_volume</a>(uint8_t *data, int32_t len)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a74eadbd69b5c7adf1b190c7e41b75b10">get_audio_state</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a513b32676d8fc248bb481180f832ef97">get_connection_state</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#ac77d0c29cc27815f703469aa0083439e">get_data_cb</a></td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>get_data_in_frames_cb</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>get_last_connection</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#ac21e1dbd2f5f475da871a7e778ba1a40">get_last_peer_address</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a688bfd727bfed94f255b63c16a6b1b3c">get_millis</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a726f45e4d405f5c5f5b259f11aaf8246">get_name</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>get_name_from_eir</b>(uint8_t *eir, uint8_t *bdname, uint8_t *bdname_len) (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>get_next_stream_cb</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a0e570c2c2f9db40873286e0571f0d93a">get_volume</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>has_last_connection</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>init_nvs</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>is_autoreconnect_allowed</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a5e76412770515732e3f54275decf02f0">is_connected</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#af29d19e53e3585446fc294a3213a06af">is_discovery_active</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>is_passthru_active</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>is_start_disabled</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>is_target_status_active</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a51f93bebf73f8bf9b98fa3c5fc4fcb18">is_valid_cod_service</a>(uint32_t cod)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>is_volume_used</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>isSource</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>last_bda_nvs_name</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>last_connection</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a791432e5c800e75fb11b858071cff651">log_free_heap</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>m_a2d_audio_state_str</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>m_a2d_conn_state_str</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>m_avrc_playback_state_str</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>p_stream</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>passthru_command_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>peer_bd_addr</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>pin_code</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>pin_code_len</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>pin_type</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>process_user_state_callbacks</b>(uint16_t event, void *param) (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>read_address</b>(const char *name, esp_bd_addr_t &amp;bda) (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#ac795a023f85438355a1b00644f2b040f">reconnect</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>reconnect_status</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>reconnect_timout</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>reset_ble</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a190c59464f53e2d4c3f121afbb7a3c21">reset_last_connection</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>s_a2d_last_state</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>s_a2d_state</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>s_avrc_peer_rn_cap</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>s_connecting_heatbeat_count</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>s_intv_cnt</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>s_media_state</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>s_peer_bdname</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>s_pkt_cnt</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>s_tmr</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a65ac6f2b0777c97874ee358119de3790">set_auto_reconnect</a>(bool active)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#ab94a0596fd595994f3ae4d2d6d2e5a5b">set_auto_reconnect</a>(esp_bd_addr_t addr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a72999af828301ef39f1f388fd7356b3b">set_avrc_passthru_command_callback</a>(void(*cb)(uint8_t key, bool isReleased))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a17013c6f40042c68821548cab9ddb5eb">set_avrc_rn_events</a>(std::vector&lt; esp_avrc_rn_event_ids_t &gt; events)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a145c20271b53d4329e0e2c7fa36692b0">set_bluedroid_config_t</a>(esp_bluedroid_config_t cfg)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a8b37f48a6b0bca33fb21b2a9ae9dab7c">set_connectable</a>(bool connectable)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a0a8a860a325348cdf210637e8d1159e6">set_connected</a>(bool active)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a9eb67e480675059a96014f2c1b84b0c3">set_data_callback</a>(int32_t(cb)(uint8_t *data, int32_t len))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#ad42fbe4ec00846ab22157545b3885db9">set_data_callback_in_frames</a>(int32_t(cb)(Frame *data, int32_t len))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a75f389f93441fe0735c3bae5f68043ae">set_data_source</a>(Stream &amp;data)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a90f7e0445fffbc5ff4ac2f0d50f0b51e">set_data_source_callback</a>(Stream &amp;(*next_stream)())</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a41ab8453d4f7f88d68d6cdb1a866532b">set_default_bt_mode</a>(esp_bt_mode_t mode)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a8e53adc58f665113c9ac6a5521e58814">set_discoverability</a>(esp_bt_discovery_mode_t d)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a73eea280b254473cab7c3b1e528b030f">set_discovery_mode_callback</a>(void(*callback)(esp_bt_gap_discovery_state_t discoveryMode))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#ae5e96c34428c50873a0ca7423a6b5402">set_event_queue_size</a>(int size)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a04bc52a4279a503203084492fe20c32e">set_event_stack_size</a>(int size)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>set_last_connection</b>(esp_bd_addr_t bda) (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#acb9c5182525261c53b803719b3027014">set_local_name</a>(const char *name)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a5f13ecf541393c21a5a489235bad27fb">set_on_audio_state_changed</a>(void(*callBack)(esp_a2d_audio_state_t state, void *), void *obj=nullptr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a169e9b94cbbfb7311a8722cc6d436e95">set_on_audio_state_changed_post</a>(void(*callBack)(esp_a2d_audio_state_t state, void *), void *obj=nullptr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#aa79cff78c075c9273ea2b5c03f052fcd">set_on_connection_state_changed</a>(void(*callBack)(esp_a2d_connection_state_t state, void *), void *obj=nullptr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a098d9fdb6cfe044406025e89725c449d">set_pin_code</a>(const char *pin_code, esp_bt_pin_type_t pin_type)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a5efcc4c32c6ec8ce7eac2c1acb59f27c">set_reset_ble</a>(bool doInit)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#af1e2f14ddbe9266b61f5e721095c3685">set_scan_mode_connectable</a>(bool connectable)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>set_scan_mode_connectable_default</b>() override (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a6ba5496831ff64bdd515fc2ad811d76d">set_ssid_callback</a>(bool(*callback)(const char *ssid, esp_bd_addr_t address, int rrsi))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a959ff7f30a6064f018dcdad5deb8e3d9">set_ssp_enabled</a>(bool active)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a694940fad2a2d498875cfbdf52eea58b">set_task_core</a>(BaseType_t core)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a68f9e168839f0faeb72705ccabbb6b7a">set_task_priority</a>(UBaseType_t priority)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a2923c8e2a689f21fc5acf7895ad2f7a7">set_valid_cod_service</a>(uint16_t filter)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a0bab92d9317837ecaeacbfe26814e28c">set_volume</a>(uint8_t volume)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a7757ddbf424aeb909dc952d7c40fc241">set_volume_control</a>(A2DPVolumeControl *ptr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>ssid_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>ssp_enabled</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">start</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#affb5e5c979a96e43a0cec7c4e0d0065d">start</a>(const char *name)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a5983a6899889b57170ba27f2d4e93e1c">start</a>(std::vector&lt; const char * &gt; names)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a690ae791e0b9256dc2b6d460e0f9eed5">start</a>(const char *name, music_data_frames_cb_t callback)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#ace5835661dbf0ecb5f09600a6bf90304">start</a>(music_data_frames_cb_t callback)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a19f050d45d834b32f069608af38a46a1">start</a>(std::vector&lt; const char * &gt; names, music_data_frames_cb_t callback)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#ad6aac053cc521667ea15a87277009574">start_raw</a>(const char *name, music_data_cb_t callback=nullptr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a06fa3a9b19ea4450a9b24c4cade2ee61">start_raw</a>(std::vector&lt; const char * &gt; names, music_data_cb_t callback=nullptr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a37eda6b257aadbc20c3f5a7556d595ca">start_raw</a>(music_data_cb_t callback=nullptr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>task_core</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>task_priority</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#ac7cd3655a7e2cfbd7e6c3474a5f2bc34">to_state_str</a>(int state)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a2b78346084e12feeea035d006e7cf07a">to_str</a>(esp_a2d_connection_state_t state)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a38d70707790dec91d63da2006f2ff17a">to_str</a>(esp_a2d_audio_state_t state)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#afa76a15aa8922301e72a745b540b040c">to_str</a>(esp_bd_addr_t bda)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a7896335b8f2cc324da86e16efb1544c9">to_str</a>(esp_avrc_playback_stat_t state)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>valid_cod_services</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a6fec0cfd3d0d9017b7ffcf82630ab89a">volume_control</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>volume_control_ptr</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>volume_value</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>write_address</b>(const char *name, esp_bd_addr_t bda) (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a4bbbd1a2c9c85004afaa7c6dbad45322">~BluetoothA2DPCommon</a>()=default</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html#a417e7ef0049364c22c92a29e6c4b4ed1">~BluetoothA2DPSource</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
