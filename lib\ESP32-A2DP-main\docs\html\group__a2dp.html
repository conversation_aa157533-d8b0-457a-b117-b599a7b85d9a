<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: ESP32 A2DP</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#enum-members">Enumerations</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">ESP32 A2DP</div>  </div>
</div><!--header-->
<div class="contents">

<p>A Simple ESP32 Bluetooth A2DP Library (to implement a Music Receiver or Sender) that supports Arduino, PlatformIO and Espressif IDF.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_a2_d_p_volume_control.html">A2DPVolumeControl</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Abstract class for handling of the volume of the audio data.  <a href="class_a2_d_p_volume_control.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Abstract Output Class.  <a href="class_bluetooth_a2_d_p_output.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example <a href="https://github.com/espressif/esp-idf/tree/master/examples/bluetooth/bluedroid/classic_bt/a2dp_sink">https://github.com/espressif/esp-idf/tree/master/examples/bluetooth/bluedroid/classic_bt/a2dp_sink</a> was refactered into a C++ class.  <a href="class_bluetooth_a2_d_p_sink.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">The <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html" title="The BluetoothA2DPSinkQueued is using a separate Task with an additinal Queue to write the I2S data....">BluetoothA2DPSinkQueued</a> is using a separate Task with an additinal Queue to write the I2S data. application.  <a href="class_bluetooth_a2_d_p_sink_queued.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html">BluetoothA2DPSource</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">A2DP Bluetooth Source.  <a href="class_bluetooth_a2_d_p_source.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:gae1f72542f04666cd97c26732366bf109"><td class="memItemLeft" align="right" valign="top"><a id="gae1f72542f04666cd97c26732366bf109"></a>
typedef uint8_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a>[ESP_BD_ADDR_LEN]</td></tr>
<tr class="memdesc:gae1f72542f04666cd97c26732366bf109"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bluetooth address. <br /></td></tr>
<tr class="separator:gae1f72542f04666cd97c26732366bf109"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:gabf9f46a0805b93eedaeccb8e512ef7fa"><td class="memItemLeft" align="right" valign="top"><a id="gabf9f46a0805b93eedaeccb8e512ef7fa"></a>enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__a2dp.html#gabf9f46a0805b93eedaeccb8e512ef7fa">APP_AV_STATE</a> { <br />
&#160;&#160;<b>APP_AV_STATE_IDLE</b>
, <b>APP_AV_STATE_DISCOVERING</b>
, <b>APP_AV_STATE_DISCOVERED</b>
, <b>APP_AV_STATE_UNCONNECTED</b>
, <br />
&#160;&#160;<b>APP_AV_STATE_CONNECTING</b>
, <b>APP_AV_STATE_CONNECTED</b>
, <b>APP_AV_STATE_DISCONNECTING</b>
<br />
 }</td></tr>
<tr class="memdesc:gabf9f46a0805b93eedaeccb8e512ef7fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Buetooth A2DP global state. <br /></td></tr>
<tr class="separator:gabf9f46a0805b93eedaeccb8e512ef7fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga49adfa87b1ad7420b0075a0ac03cc194"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> { <a class="el" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a4f131f965f55142fa9fb3557f88b8793">ESP_A2D_AUDIO_STATE_SUSPEND</a> = 0
, <a class="el" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a1fb2443ca89d24271931c19e1554b088">ESP_A2D_AUDIO_STATE_STARTED</a>
, <a class="el" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a2cac45c7b95065ed02420c5a632ab538">ESP_A2D_AUDIO_STATE_STOPPED</a> = ESP_A2D_AUDIO_STATE_SUSPEND
, <a class="el" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a2144df52646c47f697ec1f0e4b59686f">ESP_A2D_AUDIO_STATE_REMOTE_SUSPEND</a> = ESP_A2D_AUDIO_STATE_SUSPEND
 }</td></tr>
<tr class="memdesc:ga49adfa87b1ad7420b0075a0ac03cc194"><td class="mdescLeft">&#160;</td><td class="mdescRight">Buetooth A2DP datapath states.  <a href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">More...</a><br /></td></tr>
<tr class="separator:ga49adfa87b1ad7420b0075a0ac03cc194"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga52caa2d1e1c9d880c9651d52ff78a590"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> { <a class="el" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410">ESP_A2D_CONNECTION_STATE_DISCONNECTED</a> = 0
, <a class="el" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590a3661beedfd8bd0804def35a94a948543">ESP_A2D_CONNECTION_STATE_CONNECTING</a>
, <a class="el" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590a0248c5f6ba7c85f2044a9a2c79bc57d4">ESP_A2D_CONNECTION_STATE_CONNECTED</a>
, <a class="el" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590aca30f95d06a691676a9d85a0998ea1e0">ESP_A2D_CONNECTION_STATE_DISCONNECTING</a>
 }</td></tr>
<tr class="memdesc:ga52caa2d1e1c9d880c9651d52ff78a590"><td class="mdescLeft">&#160;</td><td class="mdescRight">Buetooth A2DP connection states.  <a href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">More...</a><br /></td></tr>
<tr class="separator:ga52caa2d1e1c9d880c9651d52ff78a590"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga89fdf5fb26b1ea6f33d36cc0eebca4fb"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a> { <br />
&#160;&#160;<a class="el" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba91a5ffbfe55aba08e1d323848e719fd7">ESP_AVRC_PLAYBACK_STOPPED</a> = 0
, <a class="el" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba080ee68b71dd63f15c1f4f28ad9c7fec">ESP_AVRC_PLAYBACK_PLAYING</a> = 1
, <a class="el" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fbaad9f30b7d23101c762b775f083e76a80">ESP_AVRC_PLAYBACK_PAUSED</a> = 2
, <a class="el" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba4b13603deb02c5bc0a9f60bdbe3f55d9">ESP_AVRC_PLAYBACK_FWD_SEEK</a> = 3
, <br />
&#160;&#160;<a class="el" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fba98c8358017f0694e2af18dcecf87eb6e">ESP_AVRC_PLAYBACK_REV_SEEK</a> = 4
, <a class="el" href="group__a2dp.html#gga89fdf5fb26b1ea6f33d36cc0eebca4fbab141fa00060af447da20081cb396f02d">ESP_AVRC_PLAYBACK_ERROR</a> = 0xFF
<br />
 }</td></tr>
<tr class="memdesc:ga89fdf5fb26b1ea6f33d36cc0eebca4fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRCP current status of playback.  <a href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">More...</a><br /></td></tr>
<tr class="separator:ga89fdf5fb26b1ea6f33d36cc0eebca4fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0af05e9d744ec14ee33e345d678e8ade"><td class="memItemLeft" align="right" valign="top"><a id="ga0af05e9d744ec14ee33e345d678e8ade"></a>enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a> </td></tr>
<tr class="memdesc:ga0af05e9d744ec14ee33e345d678e8ade"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC event notification ids. <br /></td></tr>
<tr class="separator:ga0af05e9d744ec14ee33e345d678e8ade"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6562796046744d7333ad2c64d2c8557d"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">esp_bt_discovery_mode_t</a> { <a class="el" href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da41f6ad4d6283fe67dfeaf9cb3e14da65">ESP_BT_NON_DISCOVERABLE</a>
, <a class="el" href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da286efd17a5bec03100355acf2dd198de">ESP_BT_LIMITED_DISCOVERABLE</a>
, <a class="el" href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da4fa5e4d5a80a86974515a8a362989711">ESP_BT_GENERAL_DISCOVERABLE</a>
 }</td></tr>
<tr class="memdesc:ga6562796046744d7333ad2c64d2c8557d"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRCP discovery mode.  <a href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">More...</a><br /></td></tr>
<tr class="separator:ga6562796046744d7333ad2c64d2c8557d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9861ef3ac455a4b2875219d457073de4"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">esp_bt_mode_t</a> { <a class="el" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a479cab8a16f9567cb00a7940564d3943">ESP_BT_MODE_IDLE</a> = 0x00
, <a class="el" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4aedb9bdfab09a30a92faa66165dc35c36">ESP_BT_MODE_BLE</a> = 0x01
, <a class="el" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a9fab3664192991a2bd656ba090c1da85">ESP_BT_MODE_CLASSIC_BT</a> = 0x02
, <a class="el" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a3d0aa499ec6f785eeaa44765e8585cb3">ESP_BT_MODE_BTDM</a> = 0x03
 }</td></tr>
<tr class="memdesc:ga9861ef3ac455a4b2875219d457073de4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bluetooth Controller mode.  <a href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">More...</a><br /></td></tr>
<tr class="separator:ga9861ef3ac455a4b2875219d457073de4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga28a6ac1cbaf47c9d341da5391e2e72b3"><td class="memItemLeft" align="right" valign="top"><a id="ga28a6ac1cbaf47c9d341da5391e2e72b3"></a>enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__a2dp.html#ga28a6ac1cbaf47c9d341da5391e2e72b3">ReconnectStatus</a> { <b>NoReconnect</b>
, <b>AutoReconnect</b>
, <b>IsReconnecting</b>
 }</td></tr>
<tr class="memdesc:ga28a6ac1cbaf47c9d341da5391e2e72b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Buetooth A2DP Reconnect Status. <br /></td></tr>
<tr class="separator:ga28a6ac1cbaf47c9d341da5391e2e72b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:gab981cf845089ef19df871466126b6f14"><td class="memItemLeft" align="right" valign="top">enum <a class="el" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__a2dp.html#gab981cf845089ef19df871466126b6f14">ESP_AVRC_RN_PLAY_STATUS_CHANGE</a> = 0x01</td></tr>
<tr class="memdesc:gab981cf845089ef19df871466126b6f14"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC event notification ids.  <a href="group__a2dp.html#gab981cf845089ef19df871466126b6f14">More...</a><br /></td></tr>
<tr class="separator:gab981cf845089ef19df871466126b6f14"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>A Simple ESP32 Bluetooth A2DP Library (to implement a Music Receiver or Sender) that supports Arduino, PlatformIO and Espressif IDF. </p>
<h2 class="groupheader">Enumeration Type Documentation</h2>
<a id="ga49adfa87b1ad7420b0075a0ac03cc194"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga49adfa87b1ad7420b0075a0ac03cc194">&#9670;&nbsp;</a></span>esp_a2d_audio_state_t</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Buetooth A2DP datapath states. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="gga49adfa87b1ad7420b0075a0ac03cc194a4f131f965f55142fa9fb3557f88b8793"></a>ESP_A2D_AUDIO_STATE_SUSPEND&#160;</td><td class="fielddoc"><p>audio stream datapath suspended by remote device </p>
</td></tr>
<tr><td class="fieldname"><a id="gga49adfa87b1ad7420b0075a0ac03cc194a1fb2443ca89d24271931c19e1554b088"></a>ESP_A2D_AUDIO_STATE_STARTED&#160;</td><td class="fielddoc"><p>audio stream datapath started </p>
</td></tr>
<tr><td class="fieldname"><a id="gga49adfa87b1ad7420b0075a0ac03cc194a2cac45c7b95065ed02420c5a632ab538"></a>ESP_A2D_AUDIO_STATE_STOPPED&#160;</td><td class="fielddoc"><dl class="section note"><dt>Note</dt><dd>Deprecated </dd></dl>
</td></tr>
<tr><td class="fieldname"><a id="gga49adfa87b1ad7420b0075a0ac03cc194a2144df52646c47f697ec1f0e4b59686f"></a>ESP_A2D_AUDIO_STATE_REMOTE_SUSPEND&#160;</td><td class="fielddoc"><dl class="section note"><dt>Note</dt><dd>Deprecated </dd></dl>
</td></tr>
</table>

</div>
</div>
<a id="ga52caa2d1e1c9d880c9651d52ff78a590"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga52caa2d1e1c9d880c9651d52ff78a590">&#9670;&nbsp;</a></span>esp_a2d_connection_state_t</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Buetooth A2DP connection states. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410"></a>ESP_A2D_CONNECTION_STATE_DISCONNECTED&#160;</td><td class="fielddoc"><p>connection released <br  />
 </p>
</td></tr>
<tr><td class="fieldname"><a id="gga52caa2d1e1c9d880c9651d52ff78a590a3661beedfd8bd0804def35a94a948543"></a>ESP_A2D_CONNECTION_STATE_CONNECTING&#160;</td><td class="fielddoc"><p>connecting remote device </p>
</td></tr>
<tr><td class="fieldname"><a id="gga52caa2d1e1c9d880c9651d52ff78a590a0248c5f6ba7c85f2044a9a2c79bc57d4"></a>ESP_A2D_CONNECTION_STATE_CONNECTED&#160;</td><td class="fielddoc"><p>connection established </p>
</td></tr>
<tr><td class="fieldname"><a id="gga52caa2d1e1c9d880c9651d52ff78a590aca30f95d06a691676a9d85a0998ea1e0"></a>ESP_A2D_CONNECTION_STATE_DISCONNECTING&#160;</td><td class="fielddoc"><p>disconnecting remote device </p>
</td></tr>
</table>

</div>
</div>
<a id="ga89fdf5fb26b1ea6f33d36cc0eebca4fb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">&#9670;&nbsp;</a></span>esp_avrc_playback_stat_t</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>AVRCP current status of playback. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="gga89fdf5fb26b1ea6f33d36cc0eebca4fba91a5ffbfe55aba08e1d323848e719fd7"></a>ESP_AVRC_PLAYBACK_STOPPED&#160;</td><td class="fielddoc"><p>stopped </p>
</td></tr>
<tr><td class="fieldname"><a id="gga89fdf5fb26b1ea6f33d36cc0eebca4fba080ee68b71dd63f15c1f4f28ad9c7fec"></a>ESP_AVRC_PLAYBACK_PLAYING&#160;</td><td class="fielddoc"><p>playing </p>
</td></tr>
<tr><td class="fieldname"><a id="gga89fdf5fb26b1ea6f33d36cc0eebca4fbaad9f30b7d23101c762b775f083e76a80"></a>ESP_AVRC_PLAYBACK_PAUSED&#160;</td><td class="fielddoc"><p>paused </p>
</td></tr>
<tr><td class="fieldname"><a id="gga89fdf5fb26b1ea6f33d36cc0eebca4fba4b13603deb02c5bc0a9f60bdbe3f55d9"></a>ESP_AVRC_PLAYBACK_FWD_SEEK&#160;</td><td class="fielddoc"><p>forward seek </p>
</td></tr>
<tr><td class="fieldname"><a id="gga89fdf5fb26b1ea6f33d36cc0eebca4fba98c8358017f0694e2af18dcecf87eb6e"></a>ESP_AVRC_PLAYBACK_REV_SEEK&#160;</td><td class="fielddoc"><p>reverse seek </p>
</td></tr>
<tr><td class="fieldname"><a id="gga89fdf5fb26b1ea6f33d36cc0eebca4fbab141fa00060af447da20081cb396f02d"></a>ESP_AVRC_PLAYBACK_ERROR&#160;</td><td class="fielddoc"><p>error </p>
</td></tr>
</table>

</div>
</div>
<a id="ga6562796046744d7333ad2c64d2c8557d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga6562796046744d7333ad2c64d2c8557d">&#9670;&nbsp;</a></span>esp_bt_discovery_mode_t</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">esp_bt_discovery_mode_t</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>AVRCP discovery mode. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="gga6562796046744d7333ad2c64d2c8557da41f6ad4d6283fe67dfeaf9cb3e14da65"></a>ESP_BT_NON_DISCOVERABLE&#160;</td><td class="fielddoc"><p>Non-discoverable </p>
</td></tr>
<tr><td class="fieldname"><a id="gga6562796046744d7333ad2c64d2c8557da286efd17a5bec03100355acf2dd198de"></a>ESP_BT_LIMITED_DISCOVERABLE&#160;</td><td class="fielddoc"><p>Limited Discoverable </p>
</td></tr>
<tr><td class="fieldname"><a id="gga6562796046744d7333ad2c64d2c8557da4fa5e4d5a80a86974515a8a362989711"></a>ESP_BT_GENERAL_DISCOVERABLE&#160;</td><td class="fielddoc"><p>General Discoverable </p>
</td></tr>
</table>

</div>
</div>
<a id="ga9861ef3ac455a4b2875219d457073de4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ga9861ef3ac455a4b2875219d457073de4">&#9670;&nbsp;</a></span>esp_bt_mode_t</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">esp_bt_mode_t</a></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Bluetooth Controller mode. </p>
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="gga9861ef3ac455a4b2875219d457073de4a479cab8a16f9567cb00a7940564d3943"></a>ESP_BT_MODE_IDLE&#160;</td><td class="fielddoc"><p>Bluetooth is not operating. </p>
</td></tr>
<tr><td class="fieldname"><a id="gga9861ef3ac455a4b2875219d457073de4aedb9bdfab09a30a92faa66165dc35c36"></a>ESP_BT_MODE_BLE&#160;</td><td class="fielddoc"><p>Bluetooth is operating in BLE mode. </p>
</td></tr>
<tr><td class="fieldname"><a id="gga9861ef3ac455a4b2875219d457073de4a9fab3664192991a2bd656ba090c1da85"></a>ESP_BT_MODE_CLASSIC_BT&#160;</td><td class="fielddoc"><p>Bluetooth is operating in Classic Bluetooth mode. </p>
</td></tr>
<tr><td class="fieldname"><a id="gga9861ef3ac455a4b2875219d457073de4a3d0aa499ec6f785eeaa44765e8585cb3"></a>ESP_BT_MODE_BTDM&#160;</td><td class="fielddoc"><p>Bluetooth is operating in Dual mode. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Variable Documentation</h2>
<a id="gab981cf845089ef19df871466126b6f14"></a>
<h2 class="memtitle"><span class="permalink"><a href="#gab981cf845089ef19df871466126b6f14">&#9670;&nbsp;</a></span>ESP_AVRC_RN_PLAY_STATUS_CHANGE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">ESP_AVRC_RN_PLAY_STATUS_CHANGE = 0x01</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>AVRC event notification ids. </p>
<p>track status change, eg. from playing to paused </p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
