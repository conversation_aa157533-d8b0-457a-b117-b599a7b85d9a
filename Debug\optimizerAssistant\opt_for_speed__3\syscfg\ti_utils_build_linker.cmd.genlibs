/*
 *  ======== ti_utils_build_linker.cmd.genlibs ========
 *  Libraries needed to link this application's configuration
 *
 *  NOTE, this feature requires software components configured in your
 *  system to correctly indicate their dependencies and report the
 *  libraries needed for your specific configuration.  If you find
 *  errors, please report them on TI's E2E forums
 *  (https://e2e.ti.com/) so they can be addressed in a future
 *  release.
 *
 *  This file allows one to portably link applications that use SysConfig
 *  _without_ having to make changes to build rules when moving to a new
 *  device OR when upgrading to a new version of a SysConfig enabled
 *  product.
 *
 *  DO NOT EDIT - This file is generated by the SysConfig tool for the
 *                TI C/C++ toolchain
 */

/* libraries required for /ti/drivers */
-l"ti/drivers/lib/ccs/m4f/drivers_cc13x2.a"
-l"ti/grlib/lib/ccs/m4f/grlib.a"
-l"lib/ccs/m4f/nortos_cc13x2.a"

