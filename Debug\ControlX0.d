# FIXED

ControlX0.obj: ../ControlX0.c
ControlX0.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/ControlX0.h
ControlX0.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Devicetype.h
ControlX0.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdint.h
ControlX0.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_ti_config.h
ControlX0.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/linkage.h
ControlX0.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_stdint40.h
ControlX0.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/stdint.h
ControlX0.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/cdefs.h
ControlX0.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_types.h
ControlX0.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_types.h
ControlX0.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_stdint.h
ControlX0.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_stdint.h
ControlX0.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stddef.h
ControlX0.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdio.h
ControlX0.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdarg.h
ControlX0.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Serial.h
ControlX0.obj: ../time.h
ControlX0.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdbool.h
ControlX0.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/SoundTransmit.h
ControlX0.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/RFCommunication.h
ControlX0.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Debug/syscfg/ti_drivers_config.h
ControlX0.obj: C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/DeviceFamily.h
ControlX0.obj: C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/PIN.h
ControlX0.obj: ../SoundReceive.h
ControlX0.obj: ../Temperature.h
ControlX0.obj: ../Batteryvoltage.h

../ControlX0.c: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/ControlX0.h: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Devicetype.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_ti_config.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/linkage.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_stdint40.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/cdefs.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_types.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_types.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stddef.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdio.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdarg.h: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Serial.h: 
../time.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdbool.h: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/SoundTransmit.h: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/RFCommunication.h: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Debug/syscfg/ti_drivers_config.h: 
C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/DeviceFamily.h: 
C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/PIN.h: 
../SoundReceive.h: 
../Temperature.h: 
../Batteryvoltage.h: 
