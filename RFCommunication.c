#include <Devicetype.h>
#include <stdint.h>
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <ti/drivers/rf/RF.h>
#include <ti/drivers/PIN.h>
#include DeviceFamily_constructPath(driverlib/rf_prop_mailbox.h)
#include "ti_drivers_config.h"
#include "RFQueue.h"
#include <ti_radio_config.h>
#include <RFCommunication.h>
#include "time.h"
#include "SoundReceive.h"

// General
static RF_Object rfObject;
static RF_Handle rfHandle;
static RF_CmdHandle RFRXHandle;
static RF_Params rfParams;

// RX
#define MAX_RX_LENGTH  150 // Max length byte the radio will accept
#define NUM_DATA_ENTRIES    2
#define NUM_APPENDED_BYTES  2  // appended to the data :Header byte (rxConf.bIncludeHdr = 1) & RSSI byte( rxConf.bAppendRssi= 1)
static uint8_t RXpacket[MAX_RX_LENGTH + NUM_APPENDED_BYTES];
uint8_t RXpacketLength;
static int PacketReceivedFlag = 0;
#pragma DATA_ALIGN (rxDataEntryBuffer, 4); //Pragmas are needed to make sure this buffer is 4 byte aligned (requirement from the RF Core)
static uint8_t rxDataEntryBuffer[RF_QUEUE_DATA_ENTRY_BUFFER_SIZE(NUM_DATA_ENTRIES, MAX_RX_LENGTH,NUM_APPENDED_BYTES)];
static dataQueue_t dataQueue;  /* Receive dataQueue for RF Core to fill in data */
static rfc_dataEntryGeneral_t* currentDataEntry;

//TX
#define MAX_TX_LENGTH  150
static uint8_t TXpacketBuffer[MAX_TX_LENGTH ]; // Buffer for TX packets
static void RXcallback(RF_Handle h, RF_CmdHandle ch, RF_EventMask e);
void RF_TerminateRX();
void RF_StartRX();
void RFCommunication_init();
void RF_TxPacketBuffer(int16_t Size);


//*********************************************************************************************************
// determine type of received RF packet
//*********************************************************************************************************
RxPacketType RFCommunication_CheckReceivedPacket()
{
  RxPacketType Cmd = eRFNone;
  if (PacketReceivedFlag)
   {
    PacketReceivedFlag=0;
    Time_StartSoftTimer(eTimerRFReceived);
    if ((RXpacketLength==1) && (RXpacket[0]=='0')) Cmd=eRFListen0toM; else
    if ((RXpacketLength==1) && (RXpacket[0]=='1')) Cmd=eRFListenMto0; else
    if ((RXpacketLength==1) && (RXpacket[0]=='2')) Cmd=eRFListenXtoM; else
    if ((RXpacketLength==1) && (RXpacket[0]=='3')) Cmd=eRFListenMtoX; else
    if ((RXpacketLength==1) && (RXpacket[0]=='4')) Cmd=eRFListenXto0; else
    if ((RXpacketLength==1) && (RXpacket[0]=='5')) Cmd=eRFListen0toX; else
    if ((RXpacketLength==1) && (RXpacket[0]=='6')) Cmd=eRFReturnResults0; else
    if ((RXpacketLength==1) && (RXpacket[0]=='7')) Cmd=eRFReturnResultsX; else
    if ((RXpacketLength==1) && (RXpacket[0]=='8')) Cmd=eRFReadSamples0; else
    if ((RXpacketLength==1) && (RXpacket[0]=='9')) Cmd=eRFReadSamplesX; else
    if ((RXpacketLength==1) && (RXpacket[0]=='A')) Cmd=eRFXMeasureXto0; else
    if ((RXpacketLength==8) && (RXpacket[0]=='M')) Cmd=eRFMeasurement; else
    if ((RXpacketLength==131) && (RXpacket[0]=='D')) Cmd=eRFArraySection;
   }
 return(Cmd);
}

//*********************************************************************************************************
// Functions to extract specific information from a packet
//*********************************************************************************************************
uint16_t RFCommunication_GetDistanceFromReceivedMeasurementPacket()
{
  return (( (uint16_t)RXpacket[2]<<8) + (uint16_t)RXpacket[3] );
}
uint16_t RFCommunication_GetBatteryFromReceivedMeasurementPacket()
{
  return(( (uint16_t)RXpacket[4]<<8) + (uint16_t)RXpacket[5]);
}
uint8_t RFCommunication_GetSectionReceivedArraySectionPacket()
{
  return(RXpacket[2]);
}
void RFCommunication_GetDataReceivedArraySectionPacket(uint16_t *Buffer)
{
  uint16_t i;
  for (i=0;i<SamplesInASection;i++) Buffer[i]= ( (uint16_t)RXpacket[i*2+3]<<8) + (uint16_t)RXpacket[i*2+4];
}

//*********************************************************************************************************
// Functions that generate and transmit specific RF packets
//*********************************************************************************************************
void RFCommunication_TXListenToSound(DeviceTypes Source, DeviceTypes Target)
{
    TXpacketBuffer[0] = ' ';
    if ((Source==eDev0) && (Target==eDevMobile)) TXpacketBuffer[0] = '0';
    if ((Source==eDevMobile) && (Target==eDev0)) TXpacketBuffer[0] = '1';
    if ((Source==eDevX) && (Target==eDevMobile)) TXpacketBuffer[0] = '2';
    if ((Source==eDevMobile) && (Target==eDevX)) TXpacketBuffer[0] = '3';
    if ((Source==eDevX) && (Target==eDev0))      TXpacketBuffer[0] = '4';
    if ((Source==eDev0) && (Target==eDevX))      TXpacketBuffer[0] = '5';
    RF_TxPacketBuffer(1);
}

void RFCommunication_TXReturnResults(DeviceTypes From)
{
    if (From==eDevX)  {
                       TXpacketBuffer[0] = '7';
                       RF_TxPacketBuffer(1);
                      } else
    if (From==eDev0)  {
                       TXpacketBuffer[0] = '6';
                       RF_TxPacketBuffer(1);
                      }
}

void RFCommunication_TXReadSamples(DeviceTypes From)
{
    if (From==eDevX)  {
                       TXpacketBuffer[0] = '9';
                       RF_TxPacketBuffer(1);
                      } else
    if (From==eDev0)  {
                       TXpacketBuffer[0] = '8';
                       RF_TxPacketBuffer(1);
                      }
}

void RFCommunication_TXAskXToMeasureXto0(void)
{
    TXpacketBuffer[0] = 'A';
      RF_TxPacketBuffer(1);
}

void RFCommunication_TXArraySection(uint16_t* Array, uint16_t Section )
{
    TXpacketBuffer[0] = 'D';
    TXpacketBuffer[1] = (uint8_t) DeviceType;
    TXpacketBuffer[2] = Section;
    uint16_t i,s;
    for (i=0;i<SamplesInASection;i++)  {
                            s= Array[Section*SamplesInASection+i];
                            TXpacketBuffer[i*2+3]= (uint8_t)(s >> 8);
                            TXpacketBuffer[i*2+4]= (uint8_t)(s);
                        }
    RF_TxPacketBuffer(131);
}

void RFCommunication_TXMeasurements(uint16_t DistanceMM, uint16_t BatteryVoltagemV,uint16_t TemperatureDeciDegrees)
{
    TXpacketBuffer[0] = 'M';
    TXpacketBuffer[1] = (uint8_t) DeviceType;
    TXpacketBuffer[2] = (uint8_t)(DistanceMM >> 8);
    TXpacketBuffer[3] = (uint8_t)(DistanceMM);
    TXpacketBuffer[4] = (uint8_t)(BatteryVoltagemV >> 8);
    TXpacketBuffer[5] = (uint8_t)(BatteryVoltagemV);
    TXpacketBuffer[6] = (uint8_t)(TemperatureDeciDegrees >> 8);
    TXpacketBuffer[7] = (uint8_t)(TemperatureDeciDegrees);
    RF_TxPacketBuffer(8);
}

void RFCommunication_TXChar(char c)
{
    TXpacketBuffer[0] = c;
    RF_TxPacketBuffer(1);
}

//*********************************************************************************************************
// low level RF functions
//*********************************************************************************************************
static void RXcallback(RF_Handle h, RF_CmdHandle ch, RF_EventMask e)
{
    if (e & RF_EventRxEntryDone)
    {
      currentDataEntry = RFQueue_getDataEntry();
      // Handle the packet data, located at &currentDataEntry->data:
      RXpacketLength  = *(uint8_t*)(&currentDataEntry->data); //  first byte = Length, last byte = rssi
      uint8_t* packetDataPointer = (uint8_t*)(&currentDataEntry->data + 1);
      memcpy(RXpacket, packetDataPointer, (RXpacketLength + 1));
      RFQueue_nextEntry();
      PacketReceivedFlag=true;
    }
}

void RF_TerminateRX()
{
    RF_flushCmd(rfHandle,RFRXHandle,1);
}

void RF_StartRX()
{
    RFRXHandle = RF_postCmd(rfHandle, (RF_Op*)&RF_cmdPropRx, RF_PriorityNormal, &RXcallback, RF_EventRxEntryDone);
}

void RFCommunication_init()
{
  RF_Params_init(&rfParams);
  // queu for RX
  if( RFQueue_defineQueue(&dataQueue,rxDataEntryBuffer,sizeof(rxDataEntryBuffer),NUM_DATA_ENTRIES, MAX_RX_LENGTH + NUM_APPENDED_BYTES))
       {
         while(1);  /* Failed to allocate space for all data entries */
       }
  RF_cmdPropRx.pQueue = &dataQueue;            /* Set the Data Entity queue for received data */
  RF_cmdPropRx.rxConf.bAutoFlushIgnored = 1;   /* Discard ignored packets from Rx queue */
  RF_cmdPropRx.rxConf.bAutoFlushCrcErr = 1;    /* Discard packets with CRC error from Rx queue */
  RF_cmdPropRx.maxPktLen = MAX_RX_LENGTH;      /* Implement packet length filtering to avoid PROP_ERROR_RXBUF */
  RF_cmdPropRx.pktConf.bRepeatOk = 1;
  RF_cmdPropRx.pktConf.bRepeatNok = 1;
  RF_cmdPropRx.rxConf.bAppendRssi= 1;
  RF_cmdPropRx.rxConf.bIncludeHdr = 0x1;
  rfHandle = RF_open(&rfObject, &RF_prop, (RF_RadioSetup*)&RF_cmdPropRadioDivSetup, &rfParams);
  RF_postCmd(rfHandle, (RF_Op*)&RF_cmdFs, RF_PriorityNormal, NULL, 0);  /* Set the frequency */
  RF_StartRX();
 }

uint16_t GetRXpacketSize(void)
{
 return (RXpacketLength);
}

void RF_TxPacketBuffer(int16_t Size)
{
 RF_cmdPropTx.pktLen = Size;
 RF_cmdPropTx.pPkt = TXpacketBuffer;
 RF_cmdPropTx.startTrigger.triggerType = TRIG_NOW;
 RF_TerminateRX();
 RF_EventMask terminationReason = RF_runCmd(rfHandle, (RF_Op*)&RF_cmdPropTx,RF_PriorityNormal, NULL, 0);
 RF_StartRX();
}
