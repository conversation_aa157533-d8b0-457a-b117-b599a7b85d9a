
#ifndef I2S_RECEIVER_H
#define I2S_RECEIVER_H

#include <Arduino.h>
#include <driver/i2s.h>

class I2SReceiver {
public:
    void init();
    bool read(int16_t* buffer, size_t sampleCount);
    
private:
    i2s_pin_config_t pinConfig = {
        .bck_io_num = 2,          // I2S_SCK
        .ws_io_num = 15,          // I2S_WS
        .data_out_num = I2S_PIN_NO_CHANGE,
        .data_in_num = 13         // I2S_SD
    };
};

#endif // I2S_RECEIVER_H
