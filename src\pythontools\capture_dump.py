import serial
import time
import numpy as np
import soundfile as sf
from datetime import datetime

def capture_and_create_wav():
    """Capture dump data from NodeA and directly create WAV file"""

    print("🎵 NodeA Chirp Capture & WAV Creator")
    print("=" * 40)

    # Connect to NodeA
    print("📡 Connecting to NodeA on COM3...")
    ser = serial.Serial('COM3', 115200, timeout=5)
    time.sleep(2)

    # Send dump command
    print("📤 Sending 'dump' command...")
    ser.write(b'd\n')
    ser.flush()

    # Capture sample data directly to memory
    print("🎯 Capturing sample data...")
    samples = []
    capturing_data = False

    try:
        while True:
            line = ser.readline().decode(errors="ignore").strip()

            if line:
                print(f"📥 {line}")  # Show progress

                # Check if dump data starts
                if "--- DATA START ---" in line:
                    print("✅ Found data start, capturing samples...")
                    capturing_data = True
                    continue

                # Check if dump data ends
                if "--- DATA END ---" in line:
                    print(f"✅ Data capture complete! Got {len(samples)} samples")
                    break

                # Capture sample data (only numeric lines)
                if capturing_data and line.lstrip('-').isdigit():
                    samples.append(int(line))

                    if len(samples) % 500 == 0:
                        print(f"📊 Captured {len(samples)} samples...")

    except Exception as e:
        print(f"❌ Error during capture: {e}")
        return False
    finally:
        ser.close()
        print("🔌 Serial connection closed")

    # Check if we got data
    if not samples:
        print("❌ No sample data captured!")
        return False

    # Convert to numpy array
    print("🔄 Converting to audio format...")
    signal = np.array(samples, dtype=np.int16)

    # Create WAV file with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    wav_filename = f"chirp_{timestamp}.wav"

    print(f"💾 Creating WAV file: {wav_filename}")

    # WAV parameters (matching NodeA configuration)
    sample_rate = 48000  # 48 kHz

    try:
        sf.write(wav_filename, signal, samplerate=sample_rate, subtype='PCM_16')
        print(f"✅ WAV file created successfully!")
        print(f"📁 File: {wav_filename}")
        print(f"📊 Samples: {len(samples)}")
        print(f"⏱️  Duration: {len(samples)/sample_rate:.3f} seconds")
        print(f"🎵 Sample Rate: {sample_rate} Hz")
        return True

    except Exception as e:
        print(f"❌ Error creating WAV file: {e}")
        return False

if __name__ == "__main__":
    try:
        success = capture_and_create_wav()
        if success:
            print("\n🎉 Process completed successfully!")
        else:
            print("\n❌ Process failed!")
    except KeyboardInterrupt:
        print("\n⏹️  Process interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")

    print("🏁 Exiting...")
    exit()
