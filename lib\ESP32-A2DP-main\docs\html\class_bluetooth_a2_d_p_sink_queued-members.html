<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">BluetoothA2DPSinkQueued Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a22d52952a8ac8c78a483a53c2006a387">activate_pin_code</a>(bool active)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>address_validator</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a9892ecf2f81b99d861f2767f7b705188">app_a2d_callback</a>(esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param) override</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_alloc_meta_buffer</b>(esp_avrc_ct_cb_param_t *param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_gap_callback</b>(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t *param) override (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a7f8680e010057c3fea392c75c85c9f23">app_rc_ct_callback</a>(esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param) override</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_rc_tg_callback</b>(esp_avrc_tg_cb_event_t event, esp_avrc_tg_cb_param_t *param) override (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_send_msg</b>(bt_app_msg_t *msg) (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_task_handle</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_task_handler</b>(void *arg) (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_task_queue</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_task_shut_down</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_task_start_up</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_work_dispatch</b>(app_callback_t p_cback, uint16_t event, void *p_params, int param_len) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_work_dispatched</b>(bt_app_msg_t *msg) (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a2cf823459de7a757d94a4ced2f375a0c">audio_data_callback</a>(const uint8_t *data, uint32_t len)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>audio_state</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>audio_state_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>audio_state_callback_post</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>audio_state_obj</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>audio_state_obj_post</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>audio_type</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>av_hdl_a2d_evt</b>(uint16_t event, void *p_param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>av_hdl_avrc_evt</b>(uint16_t event, void *p_param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>av_hdl_avrc_tg_evt</b>(uint16_t event, void *p_param) override (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>av_hdl_stack_evt</b>(uint16_t event, void *p_param) override (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>av_new_track</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>av_notify_evt_handler</b>(uint8_t event_id, esp_avrc_rn_param_t *event_parameter) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>av_play_pos_changed</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>av_playback_changed</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>avrc_connection_state</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>avrc_connection_state_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>avrc_metadata_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>avrc_metadata_flags</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>avrc_rn_events</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>avrc_rn_play_pos_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>avrc_rn_playstatus_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>avrc_rn_track_change_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>avrc_rn_volchg_complete_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bluedroid_config</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bluedroid_init</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a7cbfe59ac018d6886622c24139742ebe">BluetoothA2DPCommon</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a2a383635d7b050833f56ee79867716bd">BluetoothA2DPSink</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aadfb815b992649822db74d3e2780d4c8">BluetoothA2DPSink</a>(BluetoothA2DPOutput &amp;out)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a9c47257f9af0d64008fd46e201fb9063">BluetoothA2DPSink</a>(audio_tools::AudioOutput &amp;output)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a62e0cb5c7a6552581ad43fdd98c4a022">BluetoothA2DPSink</a>(audio_tools::AudioStream &amp;output)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a7acf882642fea0df0c36847be134ec6f">BluetoothA2DPSink</a>(Print &amp;output)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>BluetoothA2DPSinkQueued</b>()=default (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#a2af5c1af762db64e77815000fedeec01">BluetoothA2DPSinkQueued</a>(audio_tools::AudioOutput &amp;output)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#a40c1d32c321431eff1e3fecae71e3563">BluetoothA2DPSinkQueued</a>(audio_tools::AudioStream &amp;output)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#a494c5b10321bdeba5b0431cc3c592a2d">BluetoothA2DPSinkQueued</a>(Print &amp;output)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bt_connected</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bt_dis_connected</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bt_i2s_task_shut_down</b>(void) override (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bt_i2s_task_start_up</b>(void) override (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bt_mode</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bt_name</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a8be3cf8679b236293658c06cd1ed010b">bt_start</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bt_volumechange</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a962cc9aef396b06c7eb6f56462a743ac">clean_last_connection</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5d4707195d0d6e79b65bef4ed48a57c2">confirm_pin_code</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a43369961e9858cf99798e9c1b6a634b9">confirm_pin_code</a>(int code)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a788d81fe538021f912d737de72ed6be6">connect_to</a>(esp_bd_addr_t peer)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>connection_rety_count</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>connection_state</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>connection_state_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>connection_state_obj</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>data_received</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#aa6601d3c57e37f77bfdd03a3ef6231e2">debounce</a>(void(*cb)(void), int ms)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>debounce_ms</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>default_reconnect_timout</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>default_volume_control</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a2302fff324e703c3906835f759e87307">delay_ms</a>(uint32_t millis)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#ab63e627832d6377be32dd700130bf0d8">disconnect</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>discoverability</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5a91e49987a2e39c09fc6c2a64feaed6">end</a>(bool release_memory=false)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>esp_a2d_connect</b>(esp_bd_addr_t peer) override (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>esp_a2d_disconnect</b>(esp_bd_addr_t remote_bda) override (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>esp_spp_mode</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>event_queue_size</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>event_stack_size</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>execute_avrc_command</b>(int cmd) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a42e01689353026b1ef9883fd5d32f00c">fast_forward</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a74eadbd69b5c7adf1b190c7e41b75b10">get_audio_state</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a77600cb1e36b7814eb9b4126cdec62d4">get_audio_type</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a2d277d15f94823eea70f80a327344939">get_connected_source_name</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a513b32676d8fc248bb481180f832ef97">get_connection_state</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ac08fff859e0ccfbb12cbb6b119dba438">get_current_peer_address</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>get_last_connection</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#ac21e1dbd2f5f475da871a7e778ba1a40">get_last_peer_address</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5a770be98d977a8df916d8cc044b310c">get_last_rssi</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a688bfd727bfed94f255b63c16a6b1b3c">get_millis</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a726f45e4d405f5c5f5b259f11aaf8246">get_name</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a1c88745461503e5b95f26e41f409e428">get_output</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ac9adc3ca64e68fb3d6e816698a725dd5">get_peer_name</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aca1119f20d2321fb950ae859000cce7b">get_volume</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>handle_audio_cfg</b>(uint16_t event, void *p_param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>handle_audio_state</b>(uint16_t event, void *p_param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>handle_avrc_connection_state</b>(bool connected) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>handle_connection_state</b>(uint16_t event, void *p_param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>has_last_connection</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>i2s_ringbuffer_prefetch_size</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>i2s_ringbuffer_size</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>i2s_stack_size</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#a914815f36b15b259d328da372b6c3d08">i2s_task_handler</a>(void *arg) override</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>i2s_task_priority</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>i2s_ticks</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ab59e6c716d9b5aaed69272e7d2a3d12a">i2s_write_data</a>(const uint8_t *data, size_t item_size)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>i2s_write_size_upto</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>init_bluetooth</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>init_i2s</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>init_nvs</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>is_autoreconnect_allowed</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a458dc625cbceb5e534b07094136f6533">is_avrc_connected</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af5d6399876738c8fa0766ea247476b3f">is_avrc_peer_rn_cap</a>(esp_avrc_rn_event_ids_t cmd)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a8281ab4339dd58c49d1d651dd74f5711">is_avrc_peer_rn_cap_available</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a5e76412770515732e3f54275decf02f0">is_connected</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>is_i2s_active</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>is_output</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a6eb9f31e643607224fa784ff20654924">is_output_active</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>is_pin_code_active</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>is_reconnect</b>(esp_a2d_disc_rsn_t type) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>is_start_disabled</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>is_starting</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>is_target_status_active</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>is_volume_used</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>isSource</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>last_bda_nvs_name</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>last_connection</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>last_rssi_delta</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a791432e5c800e75fb11b858071cff651">log_free_heap</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>m_a2d_audio_state_str</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>m_a2d_conn_state_str</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>m_avrc_playback_state_str</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>m_pkt_cnt</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>m_sample_rate</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a296fb7aaf8d8e78991d9d505353de94f">next</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>notif_interval_s</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>out</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>out_default</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aa6967e9329939596c62f16e8686cac13">pause</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>peer_bd_addr</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a3719138f63afaeed06b63cc48ea79335">pin_code</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>pin_code_int</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>pin_code_request</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>pin_code_str</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aafd2afad1960db8ab73d7c6977aeb686">play</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a341024c18eabdb06c734c2242d5ba505">previous</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>raw_stream_reader</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>read_address</b>(const char *name, esp_bd_addr_t &amp;bda) (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#ac795a023f85438355a1b00644f2b040f">reconnect</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>reconnect_delay</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>reconnect_status</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>reconnect_timout</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>remote_name</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a9ee01e6d11ee3c6c546a510029a23a12">rewind</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>ringbuffer_mode</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>ringbuffer_prefetch_percent</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>rssi_active</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>rssi_callbak</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>s_avrc_peer_rn_cap</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>s_bt_i2s_task_handle</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>s_i2s_write_semaphore</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>s_ringbuf_i2s</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>s_volume</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>s_volume_lock</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>s_volume_notify</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a90cee550d061836992616161d4215355">sample_rate</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>sample_rate_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ad25155f02bad11da6c130aae00c8ab9c">set_address_validator</a>(bool(*callBack)(esp_bd_addr_t remote_bda))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af7d10cfe632a3c2f95409f6a23daecdd">set_auto_reconnect</a>(bool reconnect, int count=AUTOCONNECT_TRY_NUM)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a537d576b12d1158eb0681a6195b258de">BluetoothA2DPCommon::set_auto_reconnect</a>(bool active)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aedd80fe4cf8bd887224efa2716ad9d69">set_avrc_connection_state_callback</a>(void(*callback)(bool))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a8281af353148544a0612f8f7c4d511b1">set_avrc_metadata_attribute_mask</a>(int flags)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aac9074521c80d7574a855f30b8301d13">set_avrc_metadata_callback</a>(void(*callback)(uint8_t, const uint8_t *))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a17013c6f40042c68821548cab9ddb5eb">set_avrc_rn_events</a>(std::vector&lt; esp_avrc_rn_event_ids_t &gt; events)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5dd8ed8e61d6bb6d0c1e05d5c17e45e7">set_avrc_rn_play_pos_callback</a>(void(*callback)(uint32_t play_pos), uint32_t notif_interval=10)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5e4806bad4ed634493643c5925ecf67f">set_avrc_rn_playstatus_callback</a>(void(*callback)(esp_avrc_playback_stat_t playback))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ae56f6a99a5c38e0bdd402a79faba6dd5">set_avrc_rn_track_change_callback</a>(void(*callback)(uint8_t *id))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#acb34360759c6a94028f74df35009b033">set_avrc_rn_volumechange</a>(void(*callBack)(int))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a9b16f9fec1e74eb3bb30f6cf572d21e9">set_avrc_rn_volumechange_completed</a>(void(*callBack)(int))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a145c20271b53d4329e0e2c7fa36692b0">set_bluedroid_config_t</a>(esp_bluedroid_config_t cfg)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a8b37f48a6b0bca33fb21b2a9ae9dab7c">set_connectable</a>(bool connectable)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a0a8a860a325348cdf210637e8d1159e6">set_connected</a>(bool active)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a41ab8453d4f7f88d68d6cdb1a866532b">set_default_bt_mode</a>(esp_bt_mode_t mode)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a8e53adc58f665113c9ac6a5521e58814">set_discoverability</a>(esp_bt_discovery_mode_t d)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#ae5e96c34428c50873a0ca7423a6b5402">set_event_queue_size</a>(int size)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a04bc52a4279a503203084492fe20c32e">set_event_stack_size</a>(int size)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>set_i2s_active</b>(bool active) override (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#a54861eac37a7f5902f6afb1a03200085">set_i2s_ringbuffer_prefetch_percent</a>(int percent)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#acc0410c71c7278cd3a858d037fdc1edc">set_i2s_ringbuffer_size</a>(int size)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#a8450f09c7af817cacc78ae9a3544ee28">set_i2s_stack_size</a>(int size)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#a430f730deb72b7edf5601e740c9b8563">set_i2s_task_priority</a>(UBaseType_t prio)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>set_i2s_ticks</b>(int ticks) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>set_i2s_write_size_upto</b>(size_t size) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>set_last_connection</b>(esp_bd_addr_t bda) (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a624040cce89a4a2f66495f57db6c1457">set_mono_downmix</a>(bool enabled)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a5f13ecf541393c21a5a489235bad27fb">set_on_audio_state_changed</a>(void(*callBack)(esp_a2d_audio_state_t state, void *), void *obj=nullptr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a169e9b94cbbfb7311a8722cc6d436e95">set_on_audio_state_changed_post</a>(void(*callBack)(esp_a2d_audio_state_t state, void *), void *obj=nullptr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#aa79cff78c075c9273ea2b5c03f052fcd">set_on_connection_state_changed</a>(void(*callBack)(esp_a2d_connection_state_t state, void *), void *obj=nullptr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af65219e635fadbbc90f4663b33abd3e0">set_on_data_received</a>(void(*callBack)())</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ac245103d3d5c47b0414c2de21c0d52a7">set_on_volumechange</a>(void(*callBack)(int))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a2076eebe22254b2238ba097e8749d24d">set_output</a>(BluetoothA2DPOutput &amp;output)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af87666dc2c00c5917db709e2edaf1dab">set_output</a>(Print &amp;output)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5bdae239eb76ad435a7999362a8019fc">set_output</a>(audio_tools::AudioOutput &amp;output)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a664eafe67a1f66b415159b84c3fe851f">set_output</a>(audio_tools::AudioStream &amp;output)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a68d88c33b8ec218fe3bd45f61c39f754">set_output_active</a>(bool flag)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a035b0e2534970acd2c35b65842374e51">set_raw_stream_reader</a>(void(*callBack)(const uint8_t *, uint32_t))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a96b4fabd27e7952fc3ea5edca3b95cbb">set_reconnect_delay</a>(int delay)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a452b509a46930ab1ed58188a4181c67b">set_rssi_active</a>(bool active)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a97c80237061c6e80d7c6e1e5e45773c8">set_rssi_callback</a>(void(*callback)(esp_bt_gap_cb_param_t::read_rssi_delta_param &amp;rssi))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a89d2694f880a2db22344b97b466c9a9d">set_sample_rate_callback</a>(void(*callback)(uint16_t rate))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#af1e2f14ddbe9266b61f5e721095c3685">set_scan_mode_connectable</a>(bool connectable)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>set_scan_mode_connectable_default</b>() override (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#abe6004e2b95d120d64c10cf947fefb55">set_spp_active</a>(bool flag)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a4f94426ff4899c437d31623e013cf7a5">set_stream_reader</a>(void(*callBack)(const uint8_t *, uint32_t), bool i2s_output=true)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a14d982730e2b9ea772fe9ede1563ed22">set_swap_lr_channels</a>(bool swap)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a694940fad2a2d498875cfbdf52eea58b">set_task_core</a>(BaseType_t core)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a68f9e168839f0faeb72705ccabbb6b7a">set_task_priority</a>(UBaseType_t priority)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a507e30ececfdc4382af60a0319cdaf1b">set_volume</a>(uint8_t volume)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a7757ddbf424aeb909dc952d7c40fc241">set_volume_control</a>(A2DPVolumeControl *ptr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>spp_active</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af85e99324638d1c814e221ac4ba815dd">start</a>(const char *name, bool auto_reconect)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a189424ab5dc8c44f00b461e9392a2ce8">start</a>(const char *name)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a37dcbcd418b84310ccedf3330e44834f">stop</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>stream_reader</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>swap_left_right</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>task_core</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>task_priority</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a2b78346084e12feeea035d006e7cf07a">to_str</a>(esp_a2d_connection_state_t state)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a38d70707790dec91d63da2006f2ff17a">to_str</a>(esp_a2d_audio_state_t state)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#afa76a15aa8922301e72a745b540b040c">to_str</a>(esp_bd_addr_t bda)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a7896335b8f2cc324da86e16efb1544c9">to_str</a>(esp_avrc_playback_stat_t state)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>try_reconnect_max_count</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a3b43ee67031c300d07638f9c969fa4ef">update_rssi</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a6fec0cfd3d0d9017b7ffcf82630ab89a">volume_control</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>volume_control_ptr</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ae823f16ed3ee17cf9c6d1731b9d19a34">volume_down</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>volume_set_by_controller</b>(uint8_t volume) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>volume_set_by_local_host</b>(uint8_t volume) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a42866994c045e27584e0438eb2d4cc79">volume_up</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>volume_value</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>write_address</b>(const char *name, esp_bd_addr_t bda) (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#a1846088388d294f04469885b9bb560e4">write_audio</a>(const uint8_t *data, size_t size) override</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a4bbbd1a2c9c85004afaa7c6dbad45322">~BluetoothA2DPCommon</a>()=default</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a0f83dea1a97baeb360e4e1221c0aeaa9">~BluetoothA2DPSink</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
