<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">BluetoothOutputAudioTools Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a>, including all inherited members.</p>
<table class="directory">
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>begin</b>() override (defined in <a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a>)</td><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>BluetoothOutputAudioTools</b>()=default (defined in <a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a>)</td><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>end</b>() override (defined in <a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a>)</td><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>operator bool</b>() (defined in <a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a>)</td><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>p_audio_print</b> (defined in <a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a>)</td><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>p_print</b> (defined in <a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a>)</td><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html#a6e2bb1656e8e9c821678a3e0b208c88b">set_output</a>(AudioOutput &amp;output)</td><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html#abd423c533f3ac76390d5360121833daf">set_output</a>(AudioStream &amp;output)</td><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html#affe43634c04afad72609ab8a158957b1">set_output</a>(Print &amp;output)</td><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>set_output_active</b>(bool active) override (defined in <a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a>)</td><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>set_sample_rate</b>(int rate) override (defined in <a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a>)</td><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>write</b>(const uint8_t *data, size_t len) override (defined in <a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a>)</td><td class="entry"><a class="el" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
