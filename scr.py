#!/usr/bin/env python3
"""
aggregate_project_files.py

Recursively finds all .c and .h files in the same directory as this script and concatenates their contents into a single output file named 'output' in that directory.
Includes debug prints to verify the script directory and files found.
"""
import os

def aggregate_files() -> None:
    """
    Walk through the directory where the script resides, find .c and .h files, and write their contents to the 'output' file.
    Each file is preceded by a comment header indicating its relative path.
    Debug prints included.
    """
    # Determine script directory
    script_dir = os.path.abspath(os.path.dirname(__file__))
    print(f"[DEBUG] Script directory: {script_dir}")

    # Prepare output file path
    output_file = os.path.join(script_dir, 'output')
    print(f"[DEBUG] Output will be written to: {output_file}")

    # Collect matching files
    matched = []
    for root, _, files in os.walk(script_dir):
        for filename in sorted(files):
            if filename.endswith(('.c', '.h')):
                filepath = os.path.join(root, filename)
                matched.append(filepath)
    print(f"[DEBUG] Found {len(matched)} .c/.h files")

    # Write contents
    with open(output_file, 'w', encoding='utf-8') as out:
        for filepath in matched:
            relpath = os.path.relpath(filepath, script_dir)
            out.write(f"// === File: {relpath} ===\n")
            try:
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    out.write(f.read())
            except Exception as e:
                out.write(f"// Error reading file: {e}\n")
            out.write("\n\n")

    print(f"Aggregated {len(matched)} files into {output_file}")

if __name__ == '__main__':
    aggregate_files()
