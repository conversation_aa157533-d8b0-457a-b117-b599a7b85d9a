# FIXED

BatteryVoltage.obj: ../BatteryVoltage.c
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdint.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_ti_config.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/linkage.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_stdint40.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/stdint.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/cdefs.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_types.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_types.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_stdint.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_stdint.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stddef.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdio.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdarg.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdlib.h
BatteryVoltage.obj: ../adc.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdlib.h
BatteryVoltage.obj: C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/Board.h
BatteryVoltage.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Debug/syscfg/ti_drivers_config.h
BatteryVoltage.obj: C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/DeviceFamily.h
BatteryVoltage.obj: C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/PIN.h
BatteryVoltage.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdbool.h
BatteryVoltage.obj: ../Devicetype.h

../BatteryVoltage.c: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_ti_config.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/linkage.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_stdint40.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/cdefs.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_types.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_types.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stddef.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdio.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdarg.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdlib.h: 
../adc.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdlib.h: 
C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/Board.h: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Debug/syscfg/ti_drivers_config.h: 
C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/DeviceFamily.h: 
C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/PIN.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdbool.h: 
../Devicetype.h: 
