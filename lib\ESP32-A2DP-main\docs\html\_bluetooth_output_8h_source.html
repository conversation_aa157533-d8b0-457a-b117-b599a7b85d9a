<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: src/BluetoothOutput.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">BluetoothOutput.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="_bluetooth_a2_d_p_common_8h.html">BluetoothA2DPCommon.h</a>&quot;</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160; </div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="preprocessor">#include &quot;Print.h&quot;</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160; </div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#if A2DP_LEGACY_I2S_SUPPORT</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &quot;driver/i2s.h&quot;</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160; </div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="preprocessor">#if A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="preprocessor">#include &quot;AudioTools.h&quot;</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160; </div>
<div class="line"><a name="l00022"></a><span class="lineno"><a class="line" href="class_bluetooth_output.html">   22</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_bluetooth_output.html">BluetoothOutput</a> {</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> begin() = 0;</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">size_t</span> write(<span class="keyword">const</span> uint8_t *data, <span class="keywordtype">size_t</span> len) = 0;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> end() = 0;</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_sample_rate(<span class="keywordtype">int</span> rate);</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_output_active(<span class="keywordtype">bool</span> active);</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160; </div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#if A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00032"></a><span class="lineno"><a class="line" href="class_bluetooth_output.html#a60317f7ee78ed580d61b5e15ab7c3267">   32</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_output.html#a60317f7ee78ed580d61b5e15ab7c3267">set_output</a>(AudioOutput &amp;output) {}</div>
<div class="line"><a name="l00034"></a><span class="lineno"><a class="line" href="class_bluetooth_output.html#a2b08d63e4ae8fddf96a0447f9ddebc6f">   34</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_output.html#a2b08d63e4ae8fddf96a0447f9ddebc6f">set_output</a>(AudioStream &amp;output) {}</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160; </div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00039"></a><span class="lineno"><a class="line" href="class_bluetooth_output.html#a637f0ee236742c8ab58a5d9d831c5de7">   39</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_output.html#a637f0ee236742c8ab58a5d9d831c5de7">set_output</a>(Print &amp;output) {}</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160; </div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#if A2DP_LEGACY_I2S_SUPPORT</span></div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_pin_config(i2s_pin_config_t pin_config) {}</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_port(i2s_port_t i2s_num) {}</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160; </div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_config(i2s_config_t i2s_config) {}</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160; </div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_bits_per_sample(<span class="keywordtype">int</span> bps) {}</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160; </div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &lt; ESP_IDF_VERSION_VAL(5, 1, 1)</span></div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  <span class="keyword">virtual</span> esp_err_t i2s_mclk_pin_select(<span class="keyword">const</span> uint8_t pin) { <span class="keywordflow">return</span> ESP_FAIL; };</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160; </div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;};</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160; </div>
<div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="class_bluetooth_output_audio_tools.html">   69</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a> : <span class="keyword">public</span> <a class="code" href="class_bluetooth_output.html">BluetoothOutput</a> {</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;  <a class="code" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  <span class="keywordtype">bool</span> begin() <span class="keyword">override</span>;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;  <span class="keywordtype">size_t</span> write(<span class="keyword">const</span> uint8_t *data, <span class="keywordtype">size_t</span> len) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <span class="keywordtype">void</span> end() <span class="keyword">override</span>;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  <span class="keywordtype">void</span> set_sample_rate(<span class="keywordtype">int</span> rate) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  <span class="keywordtype">void</span> set_output_active(<span class="keywordtype">bool</span> active) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160; </div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <span class="keyword">operator</span> bool() { <span class="keywordflow">return</span> p_print != <span class="keyword">nullptr</span>; }</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160; </div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;<span class="preprocessor">#if A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160; </div>
<div class="line"><a name="l00083"></a><span class="lineno"><a class="line" href="class_bluetooth_output_audio_tools.html#a6e2bb1656e8e9c821678a3e0b208c88b">   83</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_output_audio_tools.html#a6e2bb1656e8e9c821678a3e0b208c88b">set_output</a>(AudioOutput &amp;output) {</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;    p_print = &amp;output;</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    p_audio_print = &amp;output;</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  }</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160; </div>
<div class="line"><a name="l00089"></a><span class="lineno"><a class="line" href="class_bluetooth_output_audio_tools.html#abd423c533f3ac76390d5360121833daf">   89</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_output_audio_tools.html#abd423c533f3ac76390d5360121833daf">set_output</a>(AudioStream &amp;output) {</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    <span class="keyword">static</span> AdapterAudioStreamToAudioOutput adapter(output);</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;    p_print = &amp;output;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;    p_audio_print = &amp;adapter;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;  }</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160; </div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="class_bluetooth_output_audio_tools.html#affe43634c04afad72609ab8a158957b1">   98</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_output_audio_tools.html#affe43634c04afad72609ab8a158957b1">set_output</a>(Print &amp;output) { p_print = &amp;output; }</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160; </div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160; <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;<span class="preprocessor">#if defined(ARDUINO) || defined(A2DP_I2S_AUDIOTOOLS)</span></div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;  Print *p_print = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160; </div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="preprocessor">#if A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;  AudioOutput *p_audio_print = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;};</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160; </div>
<div class="line"><a name="l00116"></a><span class="lineno"><a class="line" href="class_bluetooth_output_legacy.html">  116</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_bluetooth_output_legacy.html">BluetoothOutputLegacy</a> : <span class="keyword">public</span> <a class="code" href="class_bluetooth_output.html">BluetoothOutput</a> {</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;  <a class="code" href="class_bluetooth_output_legacy.html">BluetoothOutputLegacy</a>();</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;  <span class="keywordtype">bool</span> begin() <span class="keyword">override</span>;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;  <span class="keywordtype">size_t</span> write(<span class="keyword">const</span> uint8_t *data, <span class="keywordtype">size_t</span> len) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;  <span class="keywordtype">void</span> end() <span class="keyword">override</span>;</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;  <span class="keywordtype">void</span> set_sample_rate(<span class="keywordtype">int</span> rate) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;  <span class="keywordtype">void</span> set_output_active(<span class="keywordtype">bool</span> active) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160; </div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;<span class="preprocessor">#if A2DP_LEGACY_I2S_SUPPORT</span></div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_pin_config(i2s_pin_config_t pin_config) {</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    this-&gt;pin_config = pin_config;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  }</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_port(i2s_port_t i2s_num) { i2s_port = i2s_num; }</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160; </div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_config(i2s_config_t i2s_config) {</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;    this-&gt;i2s_config = i2s_config;</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  }</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160; </div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_bits_per_sample(<span class="keywordtype">int</span> bps) {</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    i2s_config.bits_per_sample = (i2s_bits_per_sample_t)bps;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;  }</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160; </div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &lt; ESP_IDF_VERSION_VAL(5, 1, 1)</span></div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;  <span class="keyword">virtual</span> esp_err_t i2s_mclk_pin_select(<span class="keyword">const</span> uint8_t pin);</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160; </div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160; <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;  i2s_config_t i2s_config;</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;  i2s_pin_config_t pin_config;</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;  i2s_channel_t i2s_channels = I2S_CHANNEL_STEREO;</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;  i2s_port_t i2s_port = I2S_NUM_0;</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;};</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160; </div>
<div class="line"><a name="l00162"></a><span class="lineno"><a class="line" href="class_bluetooth_output_default.html">  162</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_bluetooth_output_default.html">BluetoothOutputDefault</a> : <span class="keyword">public</span> <a class="code" href="class_bluetooth_output.html">BluetoothOutput</a> {</div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;  <a class="code" href="class_bluetooth_output_default.html">BluetoothOutputDefault</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;  <span class="keywordtype">bool</span> begin() {</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;    <span class="keywordtype">bool</span> rc = <span class="keyword">true</span>;</div>
<div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;    <span class="keywordflow">if</span> (out_tools)</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;      rc = out_tools.begin();</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;    <span class="keywordflow">else</span></div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;      rc = out_legacy.begin();</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;    <span class="keywordflow">return</span> rc;</div>
<div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;  }</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;  <span class="keywordtype">size_t</span> write(<span class="keyword">const</span> uint8_t *data, <span class="keywordtype">size_t</span> len) {</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;    <span class="keywordtype">size_t</span> result = 0;</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;    <span class="keywordflow">if</span> (out_tools)</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;      result = out_tools.write(data, len);</div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;    <span class="keywordflow">else</span></div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;      result = out_legacy.write(data, len);</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;    <span class="keywordflow">return</span> result;</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;  }</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;  <span class="keywordtype">void</span> end()<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;    <span class="keywordflow">if</span> (out_tools)</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;      out_tools.end();</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;    <span class="keywordflow">else</span></div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;      out_legacy.end();</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;  }</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;  <span class="keywordtype">void</span> set_sample_rate(<span class="keywordtype">int</span> rate)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;    <span class="keywordflow">if</span> (out_tools)</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;      out_tools.set_sample_rate(rate);</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;    <span class="keywordflow">else</span></div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;      out_legacy.set_sample_rate(rate);</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;  }</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;  <span class="keywordtype">void</span> set_output_active(<span class="keywordtype">bool</span> active)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;    <span class="keywordflow">if</span> (out_tools)</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;      out_tools.set_output_active(active);</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;    <span class="keywordflow">else</span></div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;      out_legacy.set_output_active(active);</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;  }</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160; </div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;<span class="preprocessor">#if A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00202"></a><span class="lineno"><a class="line" href="class_bluetooth_output_default.html#ab0442a097f1f125d0cdc4c478023bb2b">  202</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_output_default.html#ab0442a097f1f125d0cdc4c478023bb2b">set_output</a>(AudioOutput &amp;output) { out_tools.<a class="code" href="class_bluetooth_output_audio_tools.html#a6e2bb1656e8e9c821678a3e0b208c88b">set_output</a>(output); }</div>
<div class="line"><a name="l00204"></a><span class="lineno"><a class="line" href="class_bluetooth_output_default.html#a0b2b09e4de6284433bccd92594a35b55">  204</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_output_default.html#a0b2b09e4de6284433bccd92594a35b55">set_output</a>(AudioStream &amp;output) { out_tools.<a class="code" href="class_bluetooth_output_audio_tools.html#a6e2bb1656e8e9c821678a3e0b208c88b">set_output</a>(output); }</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160; </div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00209"></a><span class="lineno"><a class="line" href="class_bluetooth_output_default.html#abfe6bfeac419b8a8d1033954d23dcb89">  209</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_output_default.html#abfe6bfeac419b8a8d1033954d23dcb89">set_output</a>(Print &amp;output) { out_tools.<a class="code" href="class_bluetooth_output_audio_tools.html#a6e2bb1656e8e9c821678a3e0b208c88b">set_output</a>(output); }</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160; </div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;<span class="preprocessor">#if A2DP_LEGACY_I2S_SUPPORT</span></div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_pin_config(i2s_pin_config_t pin_config) {</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;    out_legacy.set_pin_config(pin_config);</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;  }</div>
<div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_port(i2s_port_t i2s_num) {</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;    out_legacy.set_i2s_port(i2s_num);</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;  }</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160; </div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_config(i2s_config_t i2s_config) {</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;    out_legacy.set_i2s_config(i2s_config);</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;  }</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160; </div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_bits_per_sample(<span class="keywordtype">int</span> bps) {</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;    out_legacy.set_bits_per_sample(bps);</div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;  }</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160; </div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &lt; ESP_IDF_VERSION_VAL(5, 1, 1)</span></div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;  <span class="keyword">virtual</span> esp_err_t i2s_mclk_pin_select(<span class="keyword">const</span> uint8_t pin) {</div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;    <span class="keywordflow">return</span> out_legacy.i2s_mclk_pin_select(pin);</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;  }</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;<span class="preprocessor">#endif  </span><span class="comment">// A2DP_LEGACY_I2S_SUPPORT</span></div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160; <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;  <a class="code" href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a> out_tools;</div>
<div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;  <a class="code" href="class_bluetooth_output_legacy.html">BluetoothOutputLegacy</a> out_legacy;</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;};</div>
<div class="ttc" id="a_bluetooth_a2_d_p_common_8h_html"><div class="ttname"><a href="_bluetooth_a2_d_p_common_8h.html">BluetoothA2DPCommon.h</a></div></div>
<div class="ttc" id="aclass_bluetooth_output_audio_tools_html"><div class="ttname"><a href="class_bluetooth_output_audio_tools.html">BluetoothOutputAudioTools</a></div><div class="ttdoc">Output Class using AudioTools library: https://github.com/pschatzmann/arduino-audio-tools.</div><div class="ttdef"><b>Definition:</b> BluetoothOutput.h:69</div></div>
<div class="ttc" id="aclass_bluetooth_output_audio_tools_html_a6e2bb1656e8e9c821678a3e0b208c88b"><div class="ttname"><a href="class_bluetooth_output_audio_tools.html#a6e2bb1656e8e9c821678a3e0b208c88b">BluetoothOutputAudioTools::set_output</a></div><div class="ttdeci">void set_output(AudioOutput &amp;output)</div><div class="ttdoc">Output AudioStream using AudioTools library.</div><div class="ttdef"><b>Definition:</b> BluetoothOutput.h:83</div></div>
<div class="ttc" id="aclass_bluetooth_output_audio_tools_html_abd423c533f3ac76390d5360121833daf"><div class="ttname"><a href="class_bluetooth_output_audio_tools.html#abd423c533f3ac76390d5360121833daf">BluetoothOutputAudioTools::set_output</a></div><div class="ttdeci">void set_output(AudioStream &amp;output)</div><div class="ttdoc">Output AudioStream using AudioTools library.</div><div class="ttdef"><b>Definition:</b> BluetoothOutput.h:89</div></div>
<div class="ttc" id="aclass_bluetooth_output_audio_tools_html_affe43634c04afad72609ab8a158957b1"><div class="ttname"><a href="class_bluetooth_output_audio_tools.html#affe43634c04afad72609ab8a158957b1">BluetoothOutputAudioTools::set_output</a></div><div class="ttdeci">void set_output(Print &amp;output)</div><div class="ttdoc">Output to Arduino Print.</div><div class="ttdef"><b>Definition:</b> BluetoothOutput.h:98</div></div>
<div class="ttc" id="aclass_bluetooth_output_default_html"><div class="ttname"><a href="class_bluetooth_output_default.html">BluetoothOutputDefault</a></div><div class="ttdoc">Default Output Class providing both the Legacy I2S and the AudioTools I2S functionality.</div><div class="ttdef"><b>Definition:</b> BluetoothOutput.h:162</div></div>
<div class="ttc" id="aclass_bluetooth_output_default_html_a0b2b09e4de6284433bccd92594a35b55"><div class="ttname"><a href="class_bluetooth_output_default.html#a0b2b09e4de6284433bccd92594a35b55">BluetoothOutputDefault::set_output</a></div><div class="ttdeci">void set_output(AudioStream &amp;output)</div><div class="ttdoc">Output AudioStream using AudioTools library.</div><div class="ttdef"><b>Definition:</b> BluetoothOutput.h:204</div></div>
<div class="ttc" id="aclass_bluetooth_output_default_html_ab0442a097f1f125d0cdc4c478023bb2b"><div class="ttname"><a href="class_bluetooth_output_default.html#ab0442a097f1f125d0cdc4c478023bb2b">BluetoothOutputDefault::set_output</a></div><div class="ttdeci">void set_output(AudioOutput &amp;output)</div><div class="ttdoc">Output AudioStream using AudioTools library.</div><div class="ttdef"><b>Definition:</b> BluetoothOutput.h:202</div></div>
<div class="ttc" id="aclass_bluetooth_output_default_html_abfe6bfeac419b8a8d1033954d23dcb89"><div class="ttname"><a href="class_bluetooth_output_default.html#abfe6bfeac419b8a8d1033954d23dcb89">BluetoothOutputDefault::set_output</a></div><div class="ttdeci">void set_output(Print &amp;output)</div><div class="ttdoc">Output to Arduino Print.</div><div class="ttdef"><b>Definition:</b> BluetoothOutput.h:209</div></div>
<div class="ttc" id="aclass_bluetooth_output_html"><div class="ttname"><a href="class_bluetooth_output.html">BluetoothOutput</a></div><div class="ttdoc">Abstract Output Class.</div><div class="ttdef"><b>Definition:</b> BluetoothOutput.h:22</div></div>
<div class="ttc" id="aclass_bluetooth_output_html_a2b08d63e4ae8fddf96a0447f9ddebc6f"><div class="ttname"><a href="class_bluetooth_output.html#a2b08d63e4ae8fddf96a0447f9ddebc6f">BluetoothOutput::set_output</a></div><div class="ttdeci">void set_output(AudioStream &amp;output)</div><div class="ttdoc">Not implemented.</div><div class="ttdef"><b>Definition:</b> BluetoothOutput.h:34</div></div>
<div class="ttc" id="aclass_bluetooth_output_html_a60317f7ee78ed580d61b5e15ab7c3267"><div class="ttname"><a href="class_bluetooth_output.html#a60317f7ee78ed580d61b5e15ab7c3267">BluetoothOutput::set_output</a></div><div class="ttdeci">void set_output(AudioOutput &amp;output)</div><div class="ttdoc">Not implemented.</div><div class="ttdef"><b>Definition:</b> BluetoothOutput.h:32</div></div>
<div class="ttc" id="aclass_bluetooth_output_html_a637f0ee236742c8ab58a5d9d831c5de7"><div class="ttname"><a href="class_bluetooth_output.html#a637f0ee236742c8ab58a5d9d831c5de7">BluetoothOutput::set_output</a></div><div class="ttdeci">void set_output(Print &amp;output)</div><div class="ttdoc">Not implemented.</div><div class="ttdef"><b>Definition:</b> BluetoothOutput.h:39</div></div>
<div class="ttc" id="aclass_bluetooth_output_legacy_html"><div class="ttname"><a href="class_bluetooth_output_legacy.html">BluetoothOutputLegacy</a></div><div class="ttdoc">Legacy I2S Output Class.</div><div class="ttdef"><b>Definition:</b> BluetoothOutput.h:116</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
