import serial
import time
import os

def save_dump_data():
    """Capture and save I2S dump data from NodeA"""

    # Serial connection
    ser = serial.Serial('COM3', 115200, timeout=1)
    print("Connected to NodeA on COM3")
    print("Send 'dump' command to NodeA to capture data...")
    print("Press Ctrl+C to exit\n")

    try:
        while True:
            line = ser.readline().decode(errors="ignore").strip()

            if line:
                print(line)  # Show all output

                # Check if dump data starts
                if "--- DATA START ---" in line:
                    print("\n🎯 Dump data detected! Capturing samples...")

                    # Create filename with timestamp
                    timestamp = time.strftime("%Y%m%d_%H%M%S")
                    filename = f"chirp_dump_{timestamp}.txt"

                    sample_count = 0

                    with open(filename, "w") as f:
                        # Write header information
                        f.write("# NodeA I2S Chirp Dump Data\n")
                        f.write(f"# Captured: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write("# Format: 16-bit signed PCM samples (one per line)\n")
                        f.write("# Sample Rate: 48000 Hz\n")
                        f.write("# Duration: 50 ms\n")
                        f.write("# Frequency Range: 800 Hz - 3500 Hz\n")
                        f.write("# Expected Samples: 2400\n")
                        f.write("#\n")

                        # Capture sample data
                        while True:
                            data_line = ser.readline().decode(errors="ignore").strip()

                            if "--- DATA END ---" in data_line:
                                print(f"✅ Capture complete! Saved {sample_count} samples to {filename}")
                                break

                            # Check if line contains a valid sample (numeric)
                            if data_line and data_line.lstrip('-').isdigit():
                                f.write(data_line + "\n")
                                sample_count += 1

                                # Progress indicator every 500 samples
                                if sample_count % 500 == 0:
                                    print(f"   Captured {sample_count} samples...")

                    print(f"📁 File saved: {filename}")
                    print(f"📊 Total samples: {sample_count}")
                    print("Ready for next dump command...\n")

                # Check for hex dump data
                elif "--- HEX DATA START ---" in line:
                    print("\n🎯 Hex dump data detected! Capturing hex data...")

                    # Create filename with timestamp
                    timestamp = time.strftime("%Y%m%d_%H%M%S")
                    filename = f"chirp_hex_{timestamp}.txt"

                    with open(filename, "w") as f:
                        # Write header
                        f.write("# NodeA I2S Chirp Hex Dump Data\n")
                        f.write(f"# Captured: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write("# Format: 16-bit signed PCM (little-endian hex)\n")
                        f.write("#\n")

                        # Capture hex data
                        while True:
                            hex_line = ser.readline().decode(errors="ignore").strip()

                            if "--- HEX DATA END ---" in hex_line:
                                print(f"✅ Hex capture complete! Saved to {filename}")
                                break

                            # Save hex data lines
                            if hex_line and any(c in hex_line for c in "0123456789ABCDEF"):
                                f.write(hex_line + "\n")

                    print(f"📁 Hex file saved: {filename}")
                    print("Ready for next dump command...\n")

    except KeyboardInterrupt:
        print("\n👋 Exiting...")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        ser.close()
        print("Serial connection closed.")

if __name__ == "__main__":
    save_dump_data()