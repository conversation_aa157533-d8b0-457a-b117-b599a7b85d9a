<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: BluetoothA2DPSource Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="#pro-attribs">Protected Attributes</a> &#124;
<a href="#friends">Friends</a> &#124;
<a href="class_bluetooth_a2_d_p_source-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">BluetoothA2DPSource Class Reference<div class="ingroups"><a class="el" href="group__a2dp.html">ESP32 A2DP</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>A2DP Bluetooth Source.  
 <a href="class_bluetooth_a2_d_p_source.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_bluetooth_a2_d_p_source_8h_source.html">BluetoothA2DPSource.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for BluetoothA2DPSource:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_bluetooth_a2_d_p_source.png" usemap="#BluetoothA2DPSource_map" alt=""/>
  <map id="BluetoothA2DPSource_map" name="BluetoothA2DPSource_map">
<area href="class_bluetooth_a2_d_p_common.html" title="Common Bluetooth A2DP functions." alt="BluetoothA2DPCommon" shape="rect" coords="0,0,145,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a545a8b10ab474d787744f85fe784d49a"><td class="memItemLeft" align="right" valign="top"><a id="a545a8b10ab474d787744f85fe784d49a"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a545a8b10ab474d787744f85fe784d49a">BluetoothA2DPSource</a> ()</td></tr>
<tr class="memdesc:a545a8b10ab474d787744f85fe784d49a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor. <br /></td></tr>
<tr class="separator:a545a8b10ab474d787744f85fe784d49a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a417e7ef0049364c22c92a29e6c4b4ed1"><td class="memItemLeft" align="right" valign="top"><a id="a417e7ef0049364c22c92a29e6c4b4ed1"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a417e7ef0049364c22c92a29e6c4b4ed1">~BluetoothA2DPSource</a> ()</td></tr>
<tr class="memdesc:a417e7ef0049364c22c92a29e6c4b4ed1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Destructor. <br /></td></tr>
<tr class="separator:a417e7ef0049364c22c92a29e6c4b4ed1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a962cc9aef396b06c7eb6f56462a743ac"><td class="memItemLeft" align="right" valign="top"><a id="a962cc9aef396b06c7eb6f56462a743ac"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a962cc9aef396b06c7eb6f56462a743ac">clean_last_connection</a> ()</td></tr>
<tr class="memdesc:a962cc9aef396b06c7eb6f56462a743ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">clean last connection (delete) <br /></td></tr>
<tr class="separator:a962cc9aef396b06c7eb6f56462a743ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a788d81fe538021f912d737de72ed6be6"><td class="memItemLeft" align="right" valign="top"><a id="a788d81fe538021f912d737de72ed6be6"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a788d81fe538021f912d737de72ed6be6">connect_to</a> (<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> peer)</td></tr>
<tr class="memdesc:a788d81fe538021f912d737de72ed6be6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Connnects to the indicated address. <br /></td></tr>
<tr class="separator:a788d81fe538021f912d737de72ed6be6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6601d3c57e37f77bfdd03a3ef6231e2"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#aa6601d3c57e37f77bfdd03a3ef6231e2">debounce</a> (void(*cb)(void), int ms)</td></tr>
<tr class="separator:aa6601d3c57e37f77bfdd03a3ef6231e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2302fff324e703c3906835f759e87307"><td class="memItemLeft" align="right" valign="top"><a id="a2302fff324e703c3906835f759e87307"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a2302fff324e703c3906835f759e87307">delay_ms</a> (uint32_t millis)</td></tr>
<tr class="memdesc:a2302fff324e703c3906835f759e87307"><td class="mdescLeft">&#160;</td><td class="mdescRight">calls vTaskDelay to pause for the indicated number of milliseconds <br /></td></tr>
<tr class="separator:a2302fff324e703c3906835f759e87307"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab63e627832d6377be32dd700130bf0d8"><td class="memItemLeft" align="right" valign="top"><a id="ab63e627832d6377be32dd700130bf0d8"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#ab63e627832d6377be32dd700130bf0d8">disconnect</a> ()</td></tr>
<tr class="memdesc:ab63e627832d6377be32dd700130bf0d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Closes the connection. <br /></td></tr>
<tr class="separator:ab63e627832d6377be32dd700130bf0d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aef98c427f8e590be0a4dfaa28a5cb4fd"><td class="memItemLeft" align="right" valign="top"><a id="aef98c427f8e590be0a4dfaa28a5cb4fd"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#aef98c427f8e590be0a4dfaa28a5cb4fd">end</a> (bool releaseMemory=false) override</td></tr>
<tr class="memdesc:aef98c427f8e590be0a4dfaa28a5cb4fd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Ends the processing and releases the resources. <br /></td></tr>
<tr class="separator:aef98c427f8e590be0a4dfaa28a5cb4fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74eadbd69b5c7adf1b190c7e41b75b10"><td class="memItemLeft" align="right" valign="top"><a id="a74eadbd69b5c7adf1b190c7e41b75b10"></a>
virtual <a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a74eadbd69b5c7adf1b190c7e41b75b10">get_audio_state</a> ()</td></tr>
<tr class="memdesc:a74eadbd69b5c7adf1b190c7e41b75b10"><td class="mdescLeft">&#160;</td><td class="mdescRight">Determine the actual audio state. <br /></td></tr>
<tr class="separator:a74eadbd69b5c7adf1b190c7e41b75b10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a513b32676d8fc248bb481180f832ef97"><td class="memItemLeft" align="right" valign="top"><a id="a513b32676d8fc248bb481180f832ef97"></a>
virtual <a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a513b32676d8fc248bb481180f832ef97">get_connection_state</a> ()</td></tr>
<tr class="memdesc:a513b32676d8fc248bb481180f832ef97"><td class="mdescLeft">&#160;</td><td class="mdescRight">Determine the connection state. <br /></td></tr>
<tr class="separator:a513b32676d8fc248bb481180f832ef97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac21e1dbd2f5f475da871a7e778ba1a40"><td class="memItemLeft" align="right" valign="top"><a id="ac21e1dbd2f5f475da871a7e778ba1a40"></a>
virtual <a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#ac21e1dbd2f5f475da871a7e778ba1a40">get_last_peer_address</a> ()</td></tr>
<tr class="memdesc:ac21e1dbd2f5f475da871a7e778ba1a40"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the address of the last device. <br /></td></tr>
<tr class="separator:ac21e1dbd2f5f475da871a7e778ba1a40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a688bfd727bfed94f255b63c16a6b1b3c"><td class="memItemLeft" align="right" valign="top"><a id="a688bfd727bfed94f255b63c16a6b1b3c"></a>
unsigned long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a688bfd727bfed94f255b63c16a6b1b3c">get_millis</a> ()</td></tr>
<tr class="memdesc:a688bfd727bfed94f255b63c16a6b1b3c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the time in milliseconds since the last system boot. <br /></td></tr>
<tr class="separator:a688bfd727bfed94f255b63c16a6b1b3c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a726f45e4d405f5c5f5b259f11aaf8246"><td class="memItemLeft" align="right" valign="top"><a id="a726f45e4d405f5c5f5b259f11aaf8246"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a726f45e4d405f5c5f5b259f11aaf8246">get_name</a> ()</td></tr>
<tr class="memdesc:a726f45e4d405f5c5f5b259f11aaf8246"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the actual SSID name. <br /></td></tr>
<tr class="separator:a726f45e4d405f5c5f5b259f11aaf8246"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e570c2c2f9db40873286e0571f0d93a"><td class="memItemLeft" align="right" valign="top"><a id="a0e570c2c2f9db40873286e0571f0d93a"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a0e570c2c2f9db40873286e0571f0d93a">get_volume</a> ()</td></tr>
<tr class="memdesc:a0e570c2c2f9db40873286e0571f0d93a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Determines the actual volume. <br /></td></tr>
<tr class="separator:a0e570c2c2f9db40873286e0571f0d93a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5e76412770515732e3f54275decf02f0"><td class="memItemLeft" align="right" valign="top"><a id="a5e76412770515732e3f54275decf02f0"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a5e76412770515732e3f54275decf02f0">is_connected</a> ()</td></tr>
<tr class="memdesc:a5e76412770515732e3f54275decf02f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if A2DP is connected. <br /></td></tr>
<tr class="separator:a5e76412770515732e3f54275decf02f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af29d19e53e3585446fc294a3213a06af"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#af29d19e53e3585446fc294a3213a06af">is_discovery_active</a> ()</td></tr>
<tr class="separator:af29d19e53e3585446fc294a3213a06af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a791432e5c800e75fb11b858071cff651"><td class="memItemLeft" align="right" valign="top"><a id="a791432e5c800e75fb11b858071cff651"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a791432e5c800e75fb11b858071cff651">log_free_heap</a> ()</td></tr>
<tr class="memdesc:a791432e5c800e75fb11b858071cff651"><td class="mdescLeft">&#160;</td><td class="mdescRight">Logs the free heap. <br /></td></tr>
<tr class="separator:a791432e5c800e75fb11b858071cff651"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac795a023f85438355a1b00644f2b040f"><td class="memItemLeft" align="right" valign="top"><a id="ac795a023f85438355a1b00644f2b040f"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#ac795a023f85438355a1b00644f2b040f">reconnect</a> ()</td></tr>
<tr class="memdesc:ac795a023f85438355a1b00644f2b040f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reconnects to the last device. <br /></td></tr>
<tr class="separator:ac795a023f85438355a1b00644f2b040f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a65ac6f2b0777c97874ee358119de3790"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a65ac6f2b0777c97874ee358119de3790">set_auto_reconnect</a> (bool active)</td></tr>
<tr class="separator:a65ac6f2b0777c97874ee358119de3790"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab94a0596fd595994f3ae4d2d6d2e5a5b"><td class="memItemLeft" align="right" valign="top"><a id="ab94a0596fd595994f3ae4d2d6d2e5a5b"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#ab94a0596fd595994f3ae4d2d6d2e5a5b">set_auto_reconnect</a> (<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> addr)</td></tr>
<tr class="memdesc:ab94a0596fd595994f3ae4d2d6d2e5a5b"><td class="mdescLeft">&#160;</td><td class="mdescRight">automatically tries to reconnect to the indicated address <br /></td></tr>
<tr class="separator:ab94a0596fd595994f3ae4d2d6d2e5a5b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a72999af828301ef39f1f388fd7356b3b"><td class="memItemLeft" align="right" valign="top"><a id="a72999af828301ef39f1f388fd7356b3b"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a72999af828301ef39f1f388fd7356b3b">set_avrc_passthru_command_callback</a> (void(*cb)(uint8_t key, bool isReleased))</td></tr>
<tr class="memdesc:a72999af828301ef39f1f388fd7356b3b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define the handler fur button presses on the remote bluetooth speaker. <br /></td></tr>
<tr class="separator:a72999af828301ef39f1f388fd7356b3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a17013c6f40042c68821548cab9ddb5eb"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a17013c6f40042c68821548cab9ddb5eb">set_avrc_rn_events</a> (std::vector&lt; <a class="el" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a> &gt; events)</td></tr>
<tr class="separator:a17013c6f40042c68821548cab9ddb5eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a145c20271b53d4329e0e2c7fa36692b0"><td class="memItemLeft" align="right" valign="top"><a id="a145c20271b53d4329e0e2c7fa36692b0"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a145c20271b53d4329e0e2c7fa36692b0">set_bluedroid_config_t</a> (esp_bluedroid_config_t cfg)</td></tr>
<tr class="memdesc:a145c20271b53d4329e0e2c7fa36692b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the esp_bluedroid_config_t: Available from IDF 5.2.1. <br /></td></tr>
<tr class="separator:a145c20271b53d4329e0e2c7fa36692b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b37f48a6b0bca33fb21b2a9ae9dab7c"><td class="memItemLeft" align="right" valign="top"><a id="a8b37f48a6b0bca33fb21b2a9ae9dab7c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a8b37f48a6b0bca33fb21b2a9ae9dab7c">set_connectable</a> (bool connectable)</td></tr>
<tr class="memdesc:a8b37f48a6b0bca33fb21b2a9ae9dab7c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bluetooth connectable. <br /></td></tr>
<tr class="separator:a8b37f48a6b0bca33fb21b2a9ae9dab7c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a8a860a325348cdf210637e8d1159e6"><td class="memItemLeft" align="right" valign="top"><a id="a0a8a860a325348cdf210637e8d1159e6"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a0a8a860a325348cdf210637e8d1159e6">set_connected</a> (bool active)</td></tr>
<tr class="memdesc:a0a8a860a325348cdf210637e8d1159e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calls disconnect or reconnect. <br /></td></tr>
<tr class="separator:a0a8a860a325348cdf210637e8d1159e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9eb67e480675059a96014f2c1b84b0c3"><td class="memItemLeft" align="right" valign="top"><a id="a9eb67e480675059a96014f2c1b84b0c3"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a9eb67e480675059a96014f2c1b84b0c3">set_data_callback</a> (int32_t(cb)(uint8_t *data, int32_t len))</td></tr>
<tr class="memdesc:a9eb67e480675059a96014f2c1b84b0c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the data callback. <br /></td></tr>
<tr class="separator:a9eb67e480675059a96014f2c1b84b0c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad42fbe4ec00846ab22157545b3885db9"><td class="memItemLeft" align="right" valign="top"><a id="ad42fbe4ec00846ab22157545b3885db9"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#ad42fbe4ec00846ab22157545b3885db9">set_data_callback_in_frames</a> (int32_t(cb)(Frame *data, int32_t len))</td></tr>
<tr class="memdesc:ad42fbe4ec00846ab22157545b3885db9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the data callback. <br /></td></tr>
<tr class="separator:ad42fbe4ec00846ab22157545b3885db9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a75f389f93441fe0735c3bae5f68043ae"><td class="memItemLeft" align="right" valign="top"><a id="a75f389f93441fe0735c3bae5f68043ae"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a75f389f93441fe0735c3bae5f68043ae">set_data_source</a> (Stream &amp;data)</td></tr>
<tr class="memdesc:a75f389f93441fe0735c3bae5f68043ae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines a single Arduino Stream (e.g. File) as audio source. <br /></td></tr>
<tr class="separator:a75f389f93441fe0735c3bae5f68043ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a90f7e0445fffbc5ff4ac2f0d50f0b51e"><td class="memItemLeft" align="right" valign="top"><a id="a90f7e0445fffbc5ff4ac2f0d50f0b51e"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a90f7e0445fffbc5ff4ac2f0d50f0b51e">set_data_source_callback</a> (Stream &amp;(*next_stream)())</td></tr>
<tr class="memdesc:a90f7e0445fffbc5ff4ac2f0d50f0b51e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provide a callback which provides streams. <br /></td></tr>
<tr class="separator:a90f7e0445fffbc5ff4ac2f0d50f0b51e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41ab8453d4f7f88d68d6cdb1a866532b"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a41ab8453d4f7f88d68d6cdb1a866532b">set_default_bt_mode</a> (<a class="el" href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">esp_bt_mode_t</a> mode)</td></tr>
<tr class="separator:a41ab8453d4f7f88d68d6cdb1a866532b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e53adc58f665113c9ac6a5521e58814"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a8e53adc58f665113c9ac6a5521e58814">set_discoverability</a> (<a class="el" href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">esp_bt_discovery_mode_t</a> d)</td></tr>
<tr class="memdesc:a8e53adc58f665113c9ac6a5521e58814"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bluetooth discoverability.  <a href="class_bluetooth_a2_d_p_common.html#a8e53adc58f665113c9ac6a5521e58814">More...</a><br /></td></tr>
<tr class="separator:a8e53adc58f665113c9ac6a5521e58814"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73eea280b254473cab7c3b1e528b030f"><td class="memItemLeft" align="right" valign="top"><a id="a73eea280b254473cab7c3b1e528b030f"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a73eea280b254473cab7c3b1e528b030f">set_discovery_mode_callback</a> (void(*callback)(esp_bt_gap_discovery_state_t discoveryMode))</td></tr>
<tr class="memdesc:a73eea280b254473cab7c3b1e528b030f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define callback to be notified about bt discovery mode state changes. <br /></td></tr>
<tr class="separator:a73eea280b254473cab7c3b1e528b030f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae5e96c34428c50873a0ca7423a6b5402"><td class="memItemLeft" align="right" valign="top"><a id="ae5e96c34428c50873a0ca7423a6b5402"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#ae5e96c34428c50873a0ca7423a6b5402">set_event_queue_size</a> (int size)</td></tr>
<tr class="memdesc:ae5e96c34428c50873a0ca7423a6b5402"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the queue size of the event task. <br /></td></tr>
<tr class="separator:ae5e96c34428c50873a0ca7423a6b5402"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a04bc52a4279a503203084492fe20c32e"><td class="memItemLeft" align="right" valign="top"><a id="a04bc52a4279a503203084492fe20c32e"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a04bc52a4279a503203084492fe20c32e">set_event_stack_size</a> (int size)</td></tr>
<tr class="memdesc:a04bc52a4279a503203084492fe20c32e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the stack size of the event task (in bytes) <br /></td></tr>
<tr class="separator:a04bc52a4279a503203084492fe20c32e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acb9c5182525261c53b803719b3027014"><td class="memItemLeft" align="right" valign="top"><a id="acb9c5182525261c53b803719b3027014"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#acb9c5182525261c53b803719b3027014">set_local_name</a> (const char *name)</td></tr>
<tr class="memdesc:acb9c5182525261c53b803719b3027014"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the local name. <br /></td></tr>
<tr class="separator:acb9c5182525261c53b803719b3027014"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f13ecf541393c21a5a489235bad27fb"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a5f13ecf541393c21a5a489235bad27fb">set_on_audio_state_changed</a> (void(*callBack)(<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state, void *), void *obj=nullptr)</td></tr>
<tr class="memdesc:a5f13ecf541393c21a5a489235bad27fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the callback that is called when the audio state is changed.  <a href="class_bluetooth_a2_d_p_common.html#a5f13ecf541393c21a5a489235bad27fb">More...</a><br /></td></tr>
<tr class="separator:a5f13ecf541393c21a5a489235bad27fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a169e9b94cbbfb7311a8722cc6d436e95"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a169e9b94cbbfb7311a8722cc6d436e95">set_on_audio_state_changed_post</a> (void(*callBack)(<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state, void *), void *obj=nullptr)</td></tr>
<tr class="separator:a169e9b94cbbfb7311a8722cc6d436e95"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa79cff78c075c9273ea2b5c03f052fcd"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#aa79cff78c075c9273ea2b5c03f052fcd">set_on_connection_state_changed</a> (void(*callBack)(<a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> state, void *), void *obj=nullptr)</td></tr>
<tr class="memdesc:aa79cff78c075c9273ea2b5c03f052fcd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the callback that is called when the connection state is changed.  <a href="class_bluetooth_a2_d_p_common.html#aa79cff78c075c9273ea2b5c03f052fcd">More...</a><br /></td></tr>
<tr class="separator:aa79cff78c075c9273ea2b5c03f052fcd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a098d9fdb6cfe044406025e89725c449d"><td class="memItemLeft" align="right" valign="top"><a id="a098d9fdb6cfe044406025e89725c449d"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a098d9fdb6cfe044406025e89725c449d">set_pin_code</a> (const char *pin_code, esp_bt_pin_type_t pin_type)</td></tr>
<tr class="memdesc:a098d9fdb6cfe044406025e89725c449d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the pin code. If nothing is defined we use "1234". <br /></td></tr>
<tr class="separator:a098d9fdb6cfe044406025e89725c449d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5efcc4c32c6ec8ce7eac2c1acb59f27c"><td class="memItemLeft" align="right" valign="top"><a id="a5efcc4c32c6ec8ce7eac2c1acb59f27c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a5efcc4c32c6ec8ce7eac2c1acb59f27c">set_reset_ble</a> (bool doInit)</td></tr>
<tr class="memdesc:a5efcc4c32c6ec8ce7eac2c1acb59f27c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines if the BLE should be reset on start. <br /></td></tr>
<tr class="separator:a5efcc4c32c6ec8ce7eac2c1acb59f27c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6ba5496831ff64bdd515fc2ad811d76d"><td class="memItemLeft" align="right" valign="top"><a id="a6ba5496831ff64bdd515fc2ad811d76d"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a6ba5496831ff64bdd515fc2ad811d76d">set_ssid_callback</a> (bool(*callback)(const char *ssid, <a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> address, int rrsi))</td></tr>
<tr class="memdesc:a6ba5496831ff64bdd515fc2ad811d76d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define callback to be notified about the found ssids. <br /></td></tr>
<tr class="separator:a6ba5496831ff64bdd515fc2ad811d76d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a959ff7f30a6064f018dcdad5deb8e3d9"><td class="memItemLeft" align="right" valign="top"><a id="a959ff7f30a6064f018dcdad5deb8e3d9"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a959ff7f30a6064f018dcdad5deb8e3d9">set_ssp_enabled</a> (bool active)</td></tr>
<tr class="memdesc:a959ff7f30a6064f018dcdad5deb8e3d9"><td class="mdescLeft">&#160;</td><td class="mdescRight">activate Secure Simple Pairing <br /></td></tr>
<tr class="separator:a959ff7f30a6064f018dcdad5deb8e3d9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a694940fad2a2d498875cfbdf52eea58b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a694940fad2a2d498875cfbdf52eea58b">set_task_core</a> (BaseType_t core)</td></tr>
<tr class="separator:a694940fad2a2d498875cfbdf52eea58b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68f9e168839f0faeb72705ccabbb6b7a"><td class="memItemLeft" align="right" valign="top"><a id="a68f9e168839f0faeb72705ccabbb6b7a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a68f9e168839f0faeb72705ccabbb6b7a">set_task_priority</a> (UBaseType_t priority)</td></tr>
<tr class="memdesc:a68f9e168839f0faeb72705ccabbb6b7a"><td class="mdescLeft">&#160;</td><td class="mdescRight">defines the task priority (the default value is configMAX_PRIORITIES - 10) <br /></td></tr>
<tr class="separator:a68f9e168839f0faeb72705ccabbb6b7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2923c8e2a689f21fc5acf7895ad2f7a7"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a2923c8e2a689f21fc5acf7895ad2f7a7">set_valid_cod_service</a> (uint16_t filter)</td></tr>
<tr class="separator:a2923c8e2a689f21fc5acf7895ad2f7a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0bab92d9317837ecaeacbfe26814e28c"><td class="memItemLeft" align="right" valign="top"><a id="a0bab92d9317837ecaeacbfe26814e28c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a0bab92d9317837ecaeacbfe26814e28c">set_volume</a> (uint8_t volume)</td></tr>
<tr class="memdesc:a0bab92d9317837ecaeacbfe26814e28c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Sets the volume (range 0 - 127) <br /></td></tr>
<tr class="separator:a0bab92d9317837ecaeacbfe26814e28c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7757ddbf424aeb909dc952d7c40fc241"><td class="memItemLeft" align="right" valign="top"><a id="a7757ddbf424aeb909dc952d7c40fc241"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a7757ddbf424aeb909dc952d7c40fc241">set_volume_control</a> (<a class="el" href="class_a2_d_p_volume_control.html">A2DPVolumeControl</a> *ptr)</td></tr>
<tr class="memdesc:a7757ddbf424aeb909dc952d7c40fc241"><td class="mdescLeft">&#160;</td><td class="mdescRight">you can define a custom VolumeControl implementation <br /></td></tr>
<tr class="separator:a7757ddbf424aeb909dc952d7c40fc241"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a98c1eb3ad55af189fd3f1ddeac8f3636"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">start</a> ()</td></tr>
<tr class="separator:a98c1eb3ad55af189fd3f1ddeac8f3636"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:affb5e5c979a96e43a0cec7c4e0d0065d"><td class="memItemLeft" align="right" valign="top"><a id="affb5e5c979a96e43a0cec7c4e0d0065d"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#affb5e5c979a96e43a0cec7c4e0d0065d">start</a> (const char *name)</td></tr>
<tr class="memdesc:affb5e5c979a96e43a0cec7c4e0d0065d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Starts the A2DP source. <br /></td></tr>
<tr class="separator:affb5e5c979a96e43a0cec7c4e0d0065d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a690ae791e0b9256dc2b6d460e0f9eed5"><td class="memItemLeft" align="right" valign="top"><a id="a690ae791e0b9256dc2b6d460e0f9eed5"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a690ae791e0b9256dc2b6d460e0f9eed5">start</a> (const char *name, music_data_frames_cb_t callback)</td></tr>
<tr class="memdesc:a690ae791e0b9256dc2b6d460e0f9eed5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Obsolete: use the start w/o callback and set the callback separately. <br /></td></tr>
<tr class="separator:a690ae791e0b9256dc2b6d460e0f9eed5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace5835661dbf0ecb5f09600a6bf90304"><td class="memItemLeft" align="right" valign="top"><a id="ace5835661dbf0ecb5f09600a6bf90304"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#ace5835661dbf0ecb5f09600a6bf90304">start</a> (music_data_frames_cb_t callback)</td></tr>
<tr class="memdesc:ace5835661dbf0ecb5f09600a6bf90304"><td class="mdescLeft">&#160;</td><td class="mdescRight">Obsolete: use the start w/o callback and set the callback separately. <br /></td></tr>
<tr class="separator:ace5835661dbf0ecb5f09600a6bf90304"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5983a6899889b57170ba27f2d4e93e1c"><td class="memItemLeft" align="right" valign="top"><a id="a5983a6899889b57170ba27f2d4e93e1c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a5983a6899889b57170ba27f2d4e93e1c">start</a> (std::vector&lt; const char * &gt; names)</td></tr>
<tr class="memdesc:a5983a6899889b57170ba27f2d4e93e1c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Starts the first available A2DP source. <br /></td></tr>
<tr class="separator:a5983a6899889b57170ba27f2d4e93e1c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19f050d45d834b32f069608af38a46a1"><td class="memItemLeft" align="right" valign="top"><a id="a19f050d45d834b32f069608af38a46a1"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a19f050d45d834b32f069608af38a46a1">start</a> (std::vector&lt; const char * &gt; names, music_data_frames_cb_t callback)</td></tr>
<tr class="memdesc:a19f050d45d834b32f069608af38a46a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Obsolete: use the start w/o callback and set the callback separately. <br /></td></tr>
<tr class="separator:a19f050d45d834b32f069608af38a46a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad6aac053cc521667ea15a87277009574"><td class="memItemLeft" align="right" valign="top"><a id="ad6aac053cc521667ea15a87277009574"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#ad6aac053cc521667ea15a87277009574">start_raw</a> (const char *name, music_data_cb_t callback=nullptr)</td></tr>
<tr class="memdesc:ad6aac053cc521667ea15a87277009574"><td class="mdescLeft">&#160;</td><td class="mdescRight">Obsolete: use the start w/o callback and set the callback separately. <br /></td></tr>
<tr class="separator:ad6aac053cc521667ea15a87277009574"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37eda6b257aadbc20c3f5a7556d595ca"><td class="memItemLeft" align="right" valign="top"><a id="a37eda6b257aadbc20c3f5a7556d595ca"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a37eda6b257aadbc20c3f5a7556d595ca">start_raw</a> (music_data_cb_t callback=nullptr)</td></tr>
<tr class="memdesc:a37eda6b257aadbc20c3f5a7556d595ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Obsolete: use the start w/o callback and set the callback separately. <br /></td></tr>
<tr class="separator:a37eda6b257aadbc20c3f5a7556d595ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06fa3a9b19ea4450a9b24c4cade2ee61"><td class="memItemLeft" align="right" valign="top"><a id="a06fa3a9b19ea4450a9b24c4cade2ee61"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a06fa3a9b19ea4450a9b24c4cade2ee61">start_raw</a> (std::vector&lt; const char * &gt; names, music_data_cb_t callback=nullptr)</td></tr>
<tr class="memdesc:a06fa3a9b19ea4450a9b24c4cade2ee61"><td class="mdescLeft">&#160;</td><td class="mdescRight">Obsolete: use the start w/o callback and set the callback separately. <br /></td></tr>
<tr class="separator:a06fa3a9b19ea4450a9b24c4cade2ee61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38d70707790dec91d63da2006f2ff17a"><td class="memItemLeft" align="right" valign="top"><a id="a38d70707790dec91d63da2006f2ff17a"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a38d70707790dec91d63da2006f2ff17a">to_str</a> (<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state)</td></tr>
<tr class="memdesc:a38d70707790dec91d63da2006f2ff17a"><td class="mdescLeft">&#160;</td><td class="mdescRight">converts a esp_a2d_audio_state_t to a string <br /></td></tr>
<tr class="separator:a38d70707790dec91d63da2006f2ff17a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2b78346084e12feeea035d006e7cf07a"><td class="memItemLeft" align="right" valign="top"><a id="a2b78346084e12feeea035d006e7cf07a"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a2b78346084e12feeea035d006e7cf07a">to_str</a> (<a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> state)</td></tr>
<tr class="memdesc:a2b78346084e12feeea035d006e7cf07a"><td class="mdescLeft">&#160;</td><td class="mdescRight">converts esp_a2d_connection_state_t to a string <br /></td></tr>
<tr class="separator:a2b78346084e12feeea035d006e7cf07a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7896335b8f2cc324da86e16efb1544c9"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a7896335b8f2cc324da86e16efb1544c9">to_str</a> (<a class="el" href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a> state)</td></tr>
<tr class="memdesc:a7896335b8f2cc324da86e16efb1544c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">converts esp_avrc_playback_stat_t to a string  <a href="class_bluetooth_a2_d_p_common.html#a7896335b8f2cc324da86e16efb1544c9">More...</a><br /></td></tr>
<tr class="separator:a7896335b8f2cc324da86e16efb1544c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa76a15aa8922301e72a745b540b040c"><td class="memItemLeft" align="right" valign="top"><a id="afa76a15aa8922301e72a745b540b040c"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#afa76a15aa8922301e72a745b540b040c">to_str</a> (<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> bda)</td></tr>
<tr class="memdesc:afa76a15aa8922301e72a745b540b040c"><td class="mdescLeft">&#160;</td><td class="mdescRight">converts a esp_bd_addr_t to a string - the string is 18 characters long! <br /></td></tr>
<tr class="separator:afa76a15aa8922301e72a745b540b040c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a036c89c702ec1a5a3fa10f8017ff91cc"><td class="memItemLeft" align="right" valign="top"><a id="a036c89c702ec1a5a3fa10f8017ff91cc"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>a2d_app_heart_beat</b> (void *arg)</td></tr>
<tr class="separator:a036c89c702ec1a5a3fa10f8017ff91cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa44bdbc77afd851d305a5412e3cc92e1"><td class="memItemLeft" align="right" valign="top"><a id="aa44bdbc77afd851d305a5412e3cc92e1"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#aa44bdbc77afd851d305a5412e3cc92e1">app_a2d_callback</a> (esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param) override</td></tr>
<tr class="memdesc:aa44bdbc77afd851d305a5412e3cc92e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">callback function for A2DP source <br /></td></tr>
<tr class="separator:aa44bdbc77afd851d305a5412e3cc92e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a732664ba630263724c62f146380c81d7"><td class="memItemLeft" align="right" valign="top"><a id="a732664ba630263724c62f146380c81d7"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>app_gap_callback</b> (esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t *param) override</td></tr>
<tr class="separator:a732664ba630263724c62f146380c81d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e2a51edb571273cbd349fbed8874cc1"><td class="memItemLeft" align="right" valign="top"><a id="a1e2a51edb571273cbd349fbed8874cc1"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a1e2a51edb571273cbd349fbed8874cc1">app_rc_ct_callback</a> (esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param) override</td></tr>
<tr class="memdesc:a1e2a51edb571273cbd349fbed8874cc1"><td class="mdescLeft">&#160;</td><td class="mdescRight">callback function for AVRCP controller <br /></td></tr>
<tr class="separator:a1e2a51edb571273cbd349fbed8874cc1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff227b14b0c2cced1d4c203f2491ea85"><td class="memItemLeft" align="right" valign="top"><a id="aff227b14b0c2cced1d4c203f2491ea85"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_rc_tg_callback</b> (esp_avrc_tg_cb_event_t event, esp_avrc_tg_cb_param_t *param) override</td></tr>
<tr class="separator:aff227b14b0c2cced1d4c203f2491ea85"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79bd60b9299d8936ad49036181400f95"><td class="memItemLeft" align="right" valign="top"><a id="a79bd60b9299d8936ad49036181400f95"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>app_send_msg</b> (<a class="el" href="structbt__app__msg__t.html">bt_app_msg_t</a> *msg)</td></tr>
<tr class="separator:a79bd60b9299d8936ad49036181400f95"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2cbcdb1d650ee5e472b86fd6fda03d14"><td class="memItemLeft" align="right" valign="top"><a id="a2cbcdb1d650ee5e472b86fd6fda03d14"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_task_handler</b> (void *arg)</td></tr>
<tr class="separator:a2cbcdb1d650ee5e472b86fd6fda03d14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f6518060f0a1d114ff7da3b923868e8"><td class="memItemLeft" align="right" valign="top"><a id="a7f6518060f0a1d114ff7da3b923868e8"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_task_shut_down</b> ()</td></tr>
<tr class="separator:a7f6518060f0a1d114ff7da3b923868e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8005f3e388e1ea5e0daab828713b34c6"><td class="memItemLeft" align="right" valign="top"><a id="a8005f3e388e1ea5e0daab828713b34c6"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_task_start_up</b> ()</td></tr>
<tr class="separator:a8005f3e388e1ea5e0daab828713b34c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a69518d11bb3efb446cd49d5c08337327"><td class="memItemLeft" align="right" valign="top"><a id="a69518d11bb3efb446cd49d5c08337327"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_work_dispatched</b> (<a class="el" href="structbt__app__msg__t.html">bt_app_msg_t</a> *msg)</td></tr>
<tr class="separator:a69518d11bb3efb446cd49d5c08337327"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aead01574605ffcb8daf621f6632d8345"><td class="memItemLeft" align="right" valign="top"><a id="aead01574605ffcb8daf621f6632d8345"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_avrc_tg_evt</b> (uint16_t event, void *p_param) override</td></tr>
<tr class="separator:aead01574605ffcb8daf621f6632d8345"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a521918ed44b30e20e67550bc7c09e82d"><td class="memItemLeft" align="right" valign="top"><a id="a521918ed44b30e20e67550bc7c09e82d"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_stack_evt</b> (uint16_t event, void *p_param) override</td></tr>
<tr class="separator:a521918ed44b30e20e67550bc7c09e82d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aecacd14836e61b3a08a0f415095d3f98"><td class="memItemLeft" align="right" valign="top"><a id="aecacd14836e61b3a08a0f415095d3f98"></a>
virtual esp_err_t&#160;</td><td class="memItemRight" valign="bottom"><b>bluedroid_init</b> ()</td></tr>
<tr class="separator:aecacd14836e61b3a08a0f415095d3f98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a95068a630e4b6347b38af539da78adde"><td class="memItemLeft" align="right" valign="top"><a id="a95068a630e4b6347b38af539da78adde"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>bt_app_av_media_proc</b> (uint16_t event, void *param)</td></tr>
<tr class="separator:a95068a630e4b6347b38af539da78adde"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad3bb1aaddd9dcbd9da6a37c5aded8727"><td class="memItemLeft" align="right" valign="top"><a id="ad3bb1aaddd9dcbd9da6a37c5aded8727"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#ad3bb1aaddd9dcbd9da6a37c5aded8727">bt_app_av_sm_hdlr</a> (uint16_t event, void *param)</td></tr>
<tr class="memdesc:ad3bb1aaddd9dcbd9da6a37c5aded8727"><td class="mdescLeft">&#160;</td><td class="mdescRight">A2DP application state machine. <br /></td></tr>
<tr class="separator:ad3bb1aaddd9dcbd9da6a37c5aded8727"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a60d14eb7baa67c428810cc73f51d2073"><td class="memItemLeft" align="right" valign="top"><a id="a60d14eb7baa67c428810cc73f51d2073"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>bt_app_av_state_connected_hdlr</b> (uint16_t event, void *param)</td></tr>
<tr class="separator:a60d14eb7baa67c428810cc73f51d2073"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7131b626b43a516ae4ae9df6a7ec366"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#ac7131b626b43a516ae4ae9df6a7ec366">bt_app_av_state_connecting_hdlr</a> (uint16_t event, void *param)</td></tr>
<tr class="separator:ac7131b626b43a516ae4ae9df6a7ec366"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6df41ff739785f329727d9c5b82ec949"><td class="memItemLeft" align="right" valign="top"><a id="a6df41ff739785f329727d9c5b82ec949"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>bt_app_av_state_disconnecting_hdlr</b> (uint16_t event, void *param)</td></tr>
<tr class="separator:a6df41ff739785f329727d9c5b82ec949"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a025e54e7d0e8a4e07d5a125273fcb875"><td class="memItemLeft" align="right" valign="top"><a id="a025e54e7d0e8a4e07d5a125273fcb875"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a025e54e7d0e8a4e07d5a125273fcb875">bt_app_av_state_unconnected_hdlr</a> (uint16_t event, void *param)</td></tr>
<tr class="memdesc:a025e54e7d0e8a4e07d5a125273fcb875"><td class="mdescLeft">&#160;</td><td class="mdescRight">A2DP application state machine handler for each state. <br /></td></tr>
<tr class="separator:a025e54e7d0e8a4e07d5a125273fcb875"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae5bf1de3b7b4238749ac79b0f18db2dd"><td class="memItemLeft" align="right" valign="top"><a id="ae5bf1de3b7b4238749ac79b0f18db2dd"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>bt_app_work_dispatch</b> (bt_app_cb_t p_cback, uint16_t event, void *p_params, int param_len, bt_app_copy_cb_t p_copy_cback)</td></tr>
<tr class="separator:ae5bf1de3b7b4238749ac79b0f18db2dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae9f14078c1d5dd00c93049fa8b2e283e"><td class="memItemLeft" align="right" valign="top"><a id="ae9f14078c1d5dd00c93049fa8b2e283e"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#ae9f14078c1d5dd00c93049fa8b2e283e">bt_av_hdl_avrc_ct_evt</a> (uint16_t event, void *p_param)</td></tr>
<tr class="memdesc:ae9f14078c1d5dd00c93049fa8b2e283e"><td class="mdescLeft">&#160;</td><td class="mdescRight">avrc CT event handler <br /></td></tr>
<tr class="separator:ae9f14078c1d5dd00c93049fa8b2e283e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a389f69aa8791470ae23800f6e4b6ff3f"><td class="memItemLeft" align="right" valign="top"><a id="a389f69aa8791470ae23800f6e4b6ff3f"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>bt_av_notify_evt_handler</b> (uint8_t event, esp_avrc_rn_param_t *param)</td></tr>
<tr class="separator:a389f69aa8791470ae23800f6e4b6ff3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9545946bc03882355f5d15b0611e40e8"><td class="memItemLeft" align="right" valign="top"><a id="a9545946bc03882355f5d15b0611e40e8"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>bt_av_volume_changed</b> (void)</td></tr>
<tr class="separator:a9545946bc03882355f5d15b0611e40e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8be3cf8679b236293658c06cd1ed010b"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a8be3cf8679b236293658c06cd1ed010b">bt_start</a> ()</td></tr>
<tr class="memdesc:a8be3cf8679b236293658c06cd1ed010b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Startup logic as implemented by Arduino.  <a href="class_bluetooth_a2_d_p_common.html#a8be3cf8679b236293658c06cd1ed010b">More...</a><br /></td></tr>
<tr class="separator:a8be3cf8679b236293658c06cd1ed010b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3956be0a376b8a0b8c5deca08c8ae7a0"><td class="memItemLeft" align="right" valign="top"><a id="a3956be0a376b8a0b8c5deca08c8ae7a0"></a>
esp_err_t&#160;</td><td class="memItemRight" valign="bottom"><b>esp_a2d_connect</b> (<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> peer) override</td></tr>
<tr class="separator:a3956be0a376b8a0b8c5deca08c8ae7a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7e8eaac56cce4822b4241e5d55aa0413"><td class="memItemLeft" align="right" valign="top"><a id="a7e8eaac56cce4822b4241e5d55aa0413"></a>
esp_err_t&#160;</td><td class="memItemRight" valign="bottom"><b>esp_a2d_disconnect</b> (<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> peer) override</td></tr>
<tr class="separator:a7e8eaac56cce4822b4241e5d55aa0413"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf9c78e8b30f3c3faa74001bf102cef3"><td class="memItemLeft" align="right" valign="top"><a id="adf9c78e8b30f3c3faa74001bf102cef3"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>filter_inquiry_scan_result</b> (esp_bt_gap_cb_param_t *param)</td></tr>
<tr class="separator:adf9c78e8b30f3c3faa74001bf102cef3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c3a7aa140cf42a1f324e7669a65e5cc"><td class="memItemLeft" align="right" valign="top"><a id="a2c3a7aa140cf42a1f324e7669a65e5cc"></a>
virtual int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a2c3a7aa140cf42a1f324e7669a65e5cc">get_audio_data</a> (uint8_t *data, int32_t len)</td></tr>
<tr class="memdesc:a2c3a7aa140cf42a1f324e7669a65e5cc"><td class="mdescLeft">&#160;</td><td class="mdescRight">provides the audio data to be sent <br /></td></tr>
<tr class="separator:a2c3a7aa140cf42a1f324e7669a65e5cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a03d1c23eb8a98bccdd15970f9d35db8c"><td class="memItemLeft" align="right" valign="top"><a id="a03d1c23eb8a98bccdd15970f9d35db8c"></a>
virtual int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a03d1c23eb8a98bccdd15970f9d35db8c">get_audio_data_volume</a> (uint8_t *data, int32_t len)</td></tr>
<tr class="memdesc:a03d1c23eb8a98bccdd15970f9d35db8c"><td class="mdescLeft">&#160;</td><td class="mdescRight">provides the audio after applying the volume <br /></td></tr>
<tr class="separator:a03d1c23eb8a98bccdd15970f9d35db8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3771c6d300c7709f2b76ab8847574cf4"><td class="memItemLeft" align="right" valign="top"><a id="a3771c6d300c7709f2b76ab8847574cf4"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>get_last_connection</b> ()</td></tr>
<tr class="separator:a3771c6d300c7709f2b76ab8847574cf4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a166ad62ed68cf5d827d9670f5d70f026"><td class="memItemLeft" align="right" valign="top"><a id="a166ad62ed68cf5d827d9670f5d70f026"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>get_name_from_eir</b> (uint8_t *eir, uint8_t *bdname, uint8_t *bdname_len)</td></tr>
<tr class="separator:a166ad62ed68cf5d827d9670f5d70f026"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f98f07ba4cf5962a7cdda50317b3112"><td class="memItemLeft" align="right" valign="top"><a id="a4f98f07ba4cf5962a7cdda50317b3112"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>has_last_connection</b> ()</td></tr>
<tr class="separator:a4f98f07ba4cf5962a7cdda50317b3112"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51e9ff42c269979cb0e11f484e883fab"><td class="memItemLeft" align="right" valign="top"><a id="a51e9ff42c269979cb0e11f484e883fab"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>init_nvs</b> ()</td></tr>
<tr class="separator:a51e9ff42c269979cb0e11f484e883fab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51f93bebf73f8bf9b98fa3c5fc4fcb18"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a51f93bebf73f8bf9b98fa3c5fc4fcb18">is_valid_cod_service</a> (uint32_t cod)</td></tr>
<tr class="separator:a51f93bebf73f8bf9b98fa3c5fc4fcb18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a726b279e22ee043b911bf8ed9d29a88a"><td class="memItemLeft" align="right" valign="top"><a id="a726b279e22ee043b911bf8ed9d29a88a"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>isSource</b> ()</td></tr>
<tr class="separator:a726b279e22ee043b911bf8ed9d29a88a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab13c705e17c70267ba2804a98b44a689"><td class="memItemLeft" align="right" valign="top"><a id="ab13c705e17c70267ba2804a98b44a689"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><b>last_bda_nvs_name</b> ()</td></tr>
<tr class="separator:ab13c705e17c70267ba2804a98b44a689"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3d189b51d912e0f7711d9d68cc9a2b82"><td class="memItemLeft" align="right" valign="top"><a id="a3d189b51d912e0f7711d9d68cc9a2b82"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>process_user_state_callbacks</b> (uint16_t event, void *param)</td></tr>
<tr class="separator:a3d189b51d912e0f7711d9d68cc9a2b82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace4f3d087602a6db592df68ef8a2ded5"><td class="memItemLeft" align="right" valign="top"><a id="ace4f3d087602a6db592df68ef8a2ded5"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>read_address</b> (const char *name, <a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> &amp;bda)</td></tr>
<tr class="separator:ace4f3d087602a6db592df68ef8a2ded5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a190c59464f53e2d4c3f121afbb7a3c21"><td class="memItemLeft" align="right" valign="top"><a id="a190c59464f53e2d4c3f121afbb7a3c21"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#a190c59464f53e2d4c3f121afbb7a3c21">reset_last_connection</a> ()</td></tr>
<tr class="memdesc:a190c59464f53e2d4c3f121afbb7a3c21"><td class="mdescLeft">&#160;</td><td class="mdescRight">resets the last connectioin so that we can reconnect <br /></td></tr>
<tr class="separator:a190c59464f53e2d4c3f121afbb7a3c21"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af3a2ee313a97da4e788ba3489847115c"><td class="memItemLeft" align="right" valign="top"><a id="af3a2ee313a97da4e788ba3489847115c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>set_last_connection</b> (<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> bda)</td></tr>
<tr class="separator:af3a2ee313a97da4e788ba3489847115c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af1e2f14ddbe9266b61f5e721095c3685"><td class="memItemLeft" align="right" valign="top"><a id="af1e2f14ddbe9266b61f5e721095c3685"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#af1e2f14ddbe9266b61f5e721095c3685">set_scan_mode_connectable</a> (bool connectable)</td></tr>
<tr class="memdesc:af1e2f14ddbe9266b61f5e721095c3685"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines if the bluetooth is connectable. <br /></td></tr>
<tr class="separator:af1e2f14ddbe9266b61f5e721095c3685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a66327c9c0ff6fc8a586acc86d3815c3e"><td class="memItemLeft" align="right" valign="top"><a id="a66327c9c0ff6fc8a586acc86d3815c3e"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_scan_mode_connectable_default</b> () override</td></tr>
<tr class="separator:a66327c9c0ff6fc8a586acc86d3815c3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7cd3655a7e2cfbd7e6c3474a5f2bc34"><td class="memItemLeft" align="right" valign="top"><a id="ac7cd3655a7e2cfbd7e6c3474a5f2bc34"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#ac7cd3655a7e2cfbd7e6c3474a5f2bc34">to_state_str</a> (int state)</td></tr>
<tr class="memdesc:ac7cd3655a7e2cfbd7e6c3474a5f2bc34"><td class="mdescLeft">&#160;</td><td class="mdescRight">converts a APP_AV_STATE_ENUM to a string <br /></td></tr>
<tr class="separator:ac7cd3655a7e2cfbd7e6c3474a5f2bc34"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6fec0cfd3d0d9017b7ffcf82630ab89a"><td class="memItemLeft" align="right" valign="top"><a id="a6fec0cfd3d0d9017b7ffcf82630ab89a"></a>
virtual <a class="el" href="class_a2_d_p_volume_control.html">A2DPVolumeControl</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a6fec0cfd3d0d9017b7ffcf82630ab89a">volume_control</a> ()</td></tr>
<tr class="memdesc:a6fec0cfd3d0d9017b7ffcf82630ab89a"><td class="mdescLeft">&#160;</td><td class="mdescRight">provides access to the VolumeControl object <br /></td></tr>
<tr class="separator:a6fec0cfd3d0d9017b7ffcf82630ab89a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aadd7d6f698bc8b97b3833c210eb38025"><td class="memItemLeft" align="right" valign="top"><a id="aadd7d6f698bc8b97b3833c210eb38025"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>write_address</b> (const char *name, <a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> bda)</td></tr>
<tr class="separator:aadd7d6f698bc8b97b3833c210eb38025"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-attribs"></a>
Protected Attributes</h2></td></tr>
<tr class="memitem:a6358be19cd16d24c31e538bbdf821fca"><td class="memItemLeft" align="right" valign="top"><a id="a6358be19cd16d24c31e538bbdf821fca"></a>
TaskHandle_t&#160;</td><td class="memItemRight" valign="bottom"><b>app_task_handle</b> = nullptr</td></tr>
<tr class="separator:a6358be19cd16d24c31e538bbdf821fca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d1d8b85c5a1ac1822496e7742fc4ed6"><td class="memItemLeft" align="right" valign="top"><a id="a8d1d8b85c5a1ac1822496e7742fc4ed6"></a>
QueueHandle_t&#160;</td><td class="memItemRight" valign="bottom"><b>app_task_queue</b> = nullptr</td></tr>
<tr class="separator:a8d1d8b85c5a1ac1822496e7742fc4ed6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d557628ca4cdb883fa005df2f494f32"><td class="memItemLeft" align="right" valign="top"><a id="a1d557628ca4cdb883fa005df2f494f32"></a>
<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>audio_state</b> = <a class="el" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a2cac45c7b95065ed02420c5a632ab538">ESP_A2D_AUDIO_STATE_STOPPED</a></td></tr>
<tr class="separator:a1d557628ca4cdb883fa005df2f494f32"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa227651f633af4d364515a7691d68782"><td class="memItemLeft" align="right" valign="top"><a id="aa227651f633af4d364515a7691d68782"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>audio_state_callback</b> )(<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state, void *obj) = nullptr</td></tr>
<tr class="separator:aa227651f633af4d364515a7691d68782"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a586a5e9db2ad916cdcb32a2ab91d9776"><td class="memItemLeft" align="right" valign="top"><a id="a586a5e9db2ad916cdcb32a2ab91d9776"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>audio_state_callback_post</b> )(<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state, void *obj) = nullptr</td></tr>
<tr class="separator:a586a5e9db2ad916cdcb32a2ab91d9776"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a430ec514ce2596d8d1f644defa2090b0"><td class="memItemLeft" align="right" valign="top"><a id="a430ec514ce2596d8d1f644defa2090b0"></a>
void *&#160;</td><td class="memItemRight" valign="bottom"><b>audio_state_obj</b> = nullptr</td></tr>
<tr class="separator:a430ec514ce2596d8d1f644defa2090b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7d6ebc612bc39d8c0d774b05b5fb5435"><td class="memItemLeft" align="right" valign="top"><a id="a7d6ebc612bc39d8c0d774b05b5fb5435"></a>
void *&#160;</td><td class="memItemRight" valign="bottom"><b>audio_state_obj_post</b> = nullptr</td></tr>
<tr class="separator:a7d6ebc612bc39d8c0d774b05b5fb5435"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2eaf5d6672d7cfcac7622790254b1afd"><td class="memItemLeft" align="right" valign="top">std::vector&lt; <a class="el" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>avrc_rn_events</b></td></tr>
<tr class="separator:a2eaf5d6672d7cfcac7622790254b1afd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a729840e194330a64ffbccc52d853ac4a"><td class="memItemLeft" align="right" valign="top"><a id="a729840e194330a64ffbccc52d853ac4a"></a>
esp_bluedroid_config_t&#160;</td><td class="memItemRight" valign="bottom"><b>bluedroid_config</b> {.ssp_en = true}</td></tr>
<tr class="separator:a729840e194330a64ffbccc52d853ac4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1cd750d4718e5f4053131668c9851b63"><td class="memItemLeft" align="right" valign="top"><a id="a1cd750d4718e5f4053131668c9851b63"></a>
<a class="el" href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">esp_bt_mode_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>bt_mode</b> = <a class="el" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a9fab3664192991a2bd656ba090c1da85">ESP_BT_MODE_CLASSIC_BT</a></td></tr>
<tr class="separator:a1cd750d4718e5f4053131668c9851b63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e53210a1eeea015457cfc94df7883e1"><td class="memItemLeft" align="right" valign="top"><a id="a6e53210a1eeea015457cfc94df7883e1"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><b>bt_name</b> = {0}</td></tr>
<tr class="separator:a6e53210a1eeea015457cfc94df7883e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9d60044f24d48abc4dfbc5494ffb9767"><td class="memItemLeft" align="right" valign="top"><a id="a9d60044f24d48abc4dfbc5494ffb9767"></a>
std::vector&lt; const char * &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>bt_names</b></td></tr>
<tr class="separator:a9d60044f24d48abc4dfbc5494ffb9767"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac11aaa770b2754858223a4bcdae83b5b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>connection_state</b></td></tr>
<tr class="separator:ac11aaa770b2754858223a4bcdae83b5b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefac3515fd23a006c95a754c7ad9ee28"><td class="memItemLeft" align="right" valign="top"><a id="aefac3515fd23a006c95a754c7ad9ee28"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>connection_state_callback</b> )(<a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> state, void *obj) = nullptr</td></tr>
<tr class="separator:aefac3515fd23a006c95a754c7ad9ee28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76c7cf9a20791dbfa9ee0117e3d42b95"><td class="memItemLeft" align="right" valign="top"><a id="a76c7cf9a20791dbfa9ee0117e3d42b95"></a>
void *&#160;</td><td class="memItemRight" valign="bottom"><b>connection_state_obj</b> = nullptr</td></tr>
<tr class="separator:a76c7cf9a20791dbfa9ee0117e3d42b95"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad04d173227dbd77d1956747cc10837c4"><td class="memItemLeft" align="right" valign="top"><a id="ad04d173227dbd77d1956747cc10837c4"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>debounce_ms</b> = 0</td></tr>
<tr class="separator:ad04d173227dbd77d1956747cc10837c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa84a9c336d81ce5f8a64ce9f3b03fdc6"><td class="memItemLeft" align="right" valign="top"><a id="aa84a9c336d81ce5f8a64ce9f3b03fdc6"></a>
unsigned int&#160;</td><td class="memItemRight" valign="bottom"><b>default_reconnect_timout</b> = 10000</td></tr>
<tr class="separator:aa84a9c336d81ce5f8a64ce9f3b03fdc6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7d15ed8662f01083e09b8966ced8234"><td class="memItemLeft" align="right" valign="top"><a id="af7d15ed8662f01083e09b8966ced8234"></a>
<a class="el" href="class_a2_d_p_default_volume_control.html">A2DPDefaultVolumeControl</a>&#160;</td><td class="memItemRight" valign="bottom"><b>default_volume_control</b></td></tr>
<tr class="separator:af7d15ed8662f01083e09b8966ced8234"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5b050c002df7459c95a3d1dc325c5c20"><td class="memItemLeft" align="right" valign="top"><a id="a5b050c002df7459c95a3d1dc325c5c20"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><b>dev_name</b> = &quot;ESP32_A2DP_SRC&quot;</td></tr>
<tr class="separator:a5b050c002df7459c95a3d1dc325c5c20"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2ca23dacb41661dd91b202c0f7939f69"><td class="memItemLeft" align="right" valign="top"><a id="a2ca23dacb41661dd91b202c0f7939f69"></a>
<a class="el" href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">esp_bt_discovery_mode_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>discoverability</b> = <a class="el" href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da4fa5e4d5a80a86974515a8a362989711">ESP_BT_GENERAL_DISCOVERABLE</a></td></tr>
<tr class="separator:a2ca23dacb41661dd91b202c0f7939f69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af0030e1cfe0ff1e30a290e7deb7c77f1"><td class="memItemLeft" align="right" valign="top"><a id="af0030e1cfe0ff1e30a290e7deb7c77f1"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>discovery_active</b> = false</td></tr>
<tr class="separator:af0030e1cfe0ff1e30a290e7deb7c77f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b4258cfaa3a05be3e84f358d8358d29"><td class="memItemLeft" align="right" valign="top">void(*&#160;</td><td class="memItemRight" valign="bottom"><b>discovery_mode_callback</b> )(esp_bt_gap_discovery_state_t discoveryMode)</td></tr>
<tr class="separator:a8b4258cfaa3a05be3e84f358d8358d29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6737dd3e78e2f979815615b143fcfda"><td class="memItemLeft" align="right" valign="top"><a id="ae6737dd3e78e2f979815615b143fcfda"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>event_queue_size</b> = 20</td></tr>
<tr class="separator:ae6737dd3e78e2f979815615b143fcfda"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abdc17e14de376b611b41974052b4dd2c"><td class="memItemLeft" align="right" valign="top"><a id="abdc17e14de376b611b41974052b4dd2c"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>event_stack_size</b> = 3072</td></tr>
<tr class="separator:abdc17e14de376b611b41974052b4dd2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac77d0c29cc27815f703469aa0083439e"><td class="memItemLeft" align="right" valign="top"><a id="ac77d0c29cc27815f703469aa0083439e"></a>
int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_source.html#ac77d0c29cc27815f703469aa0083439e">get_data_cb</a> )(uint8_t *data, int32_t len) = nullptr</td></tr>
<tr class="memdesc:ac77d0c29cc27815f703469aa0083439e"><td class="mdescLeft">&#160;</td><td class="mdescRight">callback for data <br /></td></tr>
<tr class="separator:ac77d0c29cc27815f703469aa0083439e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:affa4a8ba171807b9228edf31c6fd0714"><td class="memItemLeft" align="right" valign="top"><a id="affa4a8ba171807b9228edf31c6fd0714"></a>
int32_t(*&#160;</td><td class="memItemRight" valign="bottom"><b>get_data_in_frames_cb</b> )(Frame *data, int32_t len) = nullptr</td></tr>
<tr class="separator:affa4a8ba171807b9228edf31c6fd0714"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae927a7be2894142b7091e2e961a44105"><td class="memItemLeft" align="right" valign="top"><a id="ae927a7be2894142b7091e2e961a44105"></a>
Stream &amp;(*&#160;</td><td class="memItemRight" valign="bottom"><b>get_next_stream_cb</b> )() = nullptr</td></tr>
<tr class="separator:ae927a7be2894142b7091e2e961a44105"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad383ee1fca4929326b02b671aa03ee44"><td class="memItemLeft" align="right" valign="top"><a id="ad383ee1fca4929326b02b671aa03ee44"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_autoreconnect_allowed</b> = false</td></tr>
<tr class="separator:ad383ee1fca4929326b02b671aa03ee44"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a50a71340c824a79e6ad6db87741b2d7d"><td class="memItemLeft" align="right" valign="top"><a id="a50a71340c824a79e6ad6db87741b2d7d"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_passthru_active</b> = false</td></tr>
<tr class="separator:a50a71340c824a79e6ad6db87741b2d7d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44c96686a0e2d7da22805a1c9fb7ab85"><td class="memItemLeft" align="right" valign="top"><a id="a44c96686a0e2d7da22805a1c9fb7ab85"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_start_disabled</b> = false</td></tr>
<tr class="separator:a44c96686a0e2d7da22805a1c9fb7ab85"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeafe33810da405203ac3a38de6b43086"><td class="memItemLeft" align="right" valign="top"><a id="aeafe33810da405203ac3a38de6b43086"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_target_status_active</b> = true</td></tr>
<tr class="separator:aeafe33810da405203ac3a38de6b43086"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3f1abcd092657807bd4a4cab205ba88"><td class="memItemLeft" align="right" valign="top"><a id="aa3f1abcd092657807bd4a4cab205ba88"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_volume_used</b> = false</td></tr>
<tr class="separator:aa3f1abcd092657807bd4a4cab205ba88"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add25164fa6c099827e04db70d7ab0f62"><td class="memItemLeft" align="right" valign="top"><a id="add25164fa6c099827e04db70d7ab0f62"></a>
<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>last_connection</b> = {0, 0, 0, 0, 0, 0}</td></tr>
<tr class="separator:add25164fa6c099827e04db70d7ab0f62"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a25feb62133e76e60a1808a303713c973"><td class="memItemLeft" align="right" valign="top"><a id="a25feb62133e76e60a1808a303713c973"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><b>m_a2d_audio_state_str</b> [4] = {&quot;Suspended&quot;, &quot;Started&quot;, &quot;Suspended&quot;, &quot;Suspended&quot;}</td></tr>
<tr class="separator:a25feb62133e76e60a1808a303713c973"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8614127c04ef9b9ef6d0c9fa2f33d1ed"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><b>m_a2d_conn_state_str</b> [4]</td></tr>
<tr class="separator:a8614127c04ef9b9ef6d0c9fa2f33d1ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44aeeb4214776fea1bca6ecf2e8e8dc2"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><b>m_avrc_playback_state_str</b> [5]</td></tr>
<tr class="separator:a44aeeb4214776fea1bca6ecf2e8e8dc2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c4e9df57c7e74f736e440de61ec1662"><td class="memItemLeft" align="right" valign="top"><a id="a6c4e9df57c7e74f736e440de61ec1662"></a>
Stream *&#160;</td><td class="memItemRight" valign="bottom"><b>p_stream</b> = nullptr</td></tr>
<tr class="separator:a6c4e9df57c7e74f736e440de61ec1662"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a865b4c8aa72f5fe7f28e8fc385989d4e"><td class="memItemLeft" align="right" valign="top"><a id="a865b4c8aa72f5fe7f28e8fc385989d4e"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>passthru_command_callback</b> )(uint8_t, bool) = nullptr</td></tr>
<tr class="separator:a865b4c8aa72f5fe7f28e8fc385989d4e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a83cff3bdeb407a6c19618ba208438604"><td class="memItemLeft" align="right" valign="top"><a id="a83cff3bdeb407a6c19618ba208438604"></a>
<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>peer_bd_addr</b></td></tr>
<tr class="separator:a83cff3bdeb407a6c19618ba208438604"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad4fabc0ba19e85aeae783aedf9adb9da"><td class="memItemLeft" align="right" valign="top"><a id="ad4fabc0ba19e85aeae783aedf9adb9da"></a>
esp_bt_pin_code_t&#160;</td><td class="memItemRight" valign="bottom"><b>pin_code</b></td></tr>
<tr class="separator:ad4fabc0ba19e85aeae783aedf9adb9da"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af8a1ebe90cfa00f7ae464eeb054af499"><td class="memItemLeft" align="right" valign="top"><a id="af8a1ebe90cfa00f7ae464eeb054af499"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>pin_code_len</b></td></tr>
<tr class="separator:af8a1ebe90cfa00f7ae464eeb054af499"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8be5c6322c55159a608c559ccadefaca"><td class="memItemLeft" align="right" valign="top"><a id="a8be5c6322c55159a608c559ccadefaca"></a>
esp_bt_pin_type_t&#160;</td><td class="memItemRight" valign="bottom"><b>pin_type</b></td></tr>
<tr class="separator:a8be5c6322c55159a608c559ccadefaca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e0c2fae7d742947d47bae41a690a712"><td class="memItemLeft" align="right" valign="top"><a id="a0e0c2fae7d742947d47bae41a690a712"></a>
<a class="el" href="group__a2dp.html#ga28a6ac1cbaf47c9d341da5391e2e72b3">ReconnectStatus</a>&#160;</td><td class="memItemRight" valign="bottom"><b>reconnect_status</b> = NoReconnect</td></tr>
<tr class="separator:a0e0c2fae7d742947d47bae41a690a712"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa350c922076ae8584f26d1693edc0ba8"><td class="memItemLeft" align="right" valign="top"><a id="aa350c922076ae8584f26d1693edc0ba8"></a>
unsigned long&#160;</td><td class="memItemRight" valign="bottom"><b>reconnect_timout</b> = 0</td></tr>
<tr class="separator:aa350c922076ae8584f26d1693edc0ba8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeeca78610802e0628899e6d7e773095f"><td class="memItemLeft" align="right" valign="top"><a id="aeeca78610802e0628899e6d7e773095f"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>reset_ble</b> = false</td></tr>
<tr class="separator:aeeca78610802e0628899e6d7e773095f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a02e4f073b4fc04a6e592f50cd3e64c1c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__a2dp.html#gabf9f46a0805b93eedaeccb8e512ef7fa">APP_AV_STATE</a>&#160;</td><td class="memItemRight" valign="bottom"><b>s_a2d_last_state</b></td></tr>
<tr class="separator:a02e4f073b4fc04a6e592f50cd3e64c1c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3766fb8f8b68c34b6a079fc23f3a376a"><td class="memItemLeft" align="right" valign="top"><a id="a3766fb8f8b68c34b6a079fc23f3a376a"></a>
<a class="el" href="group__a2dp.html#gabf9f46a0805b93eedaeccb8e512ef7fa">APP_AV_STATE</a>&#160;</td><td class="memItemRight" valign="bottom"><b>s_a2d_state</b> = APP_AV_STATE_IDLE</td></tr>
<tr class="separator:a3766fb8f8b68c34b6a079fc23f3a376a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a879f4819b7e2a26c7245726a453fcc61"><td class="memItemLeft" align="right" valign="top"><a id="a879f4819b7e2a26c7245726a453fcc61"></a>
esp_avrc_rn_evt_cap_mask_t&#160;</td><td class="memItemRight" valign="bottom"><b>s_avrc_peer_rn_cap</b></td></tr>
<tr class="separator:a879f4819b7e2a26c7245726a453fcc61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abfe3887361e647d12139780ed2deac86"><td class="memItemLeft" align="right" valign="top"><a id="abfe3887361e647d12139780ed2deac86"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>s_connecting_heatbeat_count</b></td></tr>
<tr class="separator:abfe3887361e647d12139780ed2deac86"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa71fda71754508b0857edbd00ee6ee3d"><td class="memItemLeft" align="right" valign="top"><a id="aa71fda71754508b0857edbd00ee6ee3d"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>s_intv_cnt</b> = 0</td></tr>
<tr class="separator:aa71fda71754508b0857edbd00ee6ee3d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ee4922232755bcb43038fab3748dbcb"><td class="memItemLeft" align="right" valign="top"><a id="a9ee4922232755bcb43038fab3748dbcb"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>s_media_state</b> = 0</td></tr>
<tr class="separator:a9ee4922232755bcb43038fab3748dbcb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a633e599632e5bcd466b37294ed4bf8b3"><td class="memItemLeft" align="right" valign="top"><a id="a633e599632e5bcd466b37294ed4bf8b3"></a>
uint8_t&#160;</td><td class="memItemRight" valign="bottom"><b>s_peer_bdname</b> [ESP_BT_GAP_MAX_BDNAME_LEN+1]</td></tr>
<tr class="separator:a633e599632e5bcd466b37294ed4bf8b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa89018df587edc2a98338fafb749383e"><td class="memItemLeft" align="right" valign="top"><a id="aa89018df587edc2a98338fafb749383e"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>s_pkt_cnt</b></td></tr>
<tr class="separator:aa89018df587edc2a98338fafb749383e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2675e1945024bcfc5d124cd34f4d8578"><td class="memItemLeft" align="right" valign="top"><a id="a2675e1945024bcfc5d124cd34f4d8578"></a>
TimerHandle_t&#160;</td><td class="memItemRight" valign="bottom"><b>s_tmr</b></td></tr>
<tr class="separator:a2675e1945024bcfc5d124cd34f4d8578"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae8d936972af8aae81f06c17af6e22113"><td class="memItemLeft" align="right" valign="top"><a id="ae8d936972af8aae81f06c17af6e22113"></a>
bool(*&#160;</td><td class="memItemRight" valign="bottom"><b>ssid_callback</b> )(const char *ssid, <a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> address, int rrsi) = nullptr</td></tr>
<tr class="separator:ae8d936972af8aae81f06c17af6e22113"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a63d502a55a545054c10bae478c01f66c"><td class="memItemLeft" align="right" valign="top"><a id="a63d502a55a545054c10bae478c01f66c"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>ssp_enabled</b> = false</td></tr>
<tr class="separator:a63d502a55a545054c10bae478c01f66c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5b0acd3b6c295785607d326128eaa52e"><td class="memItemLeft" align="right" valign="top"><a id="a5b0acd3b6c295785607d326128eaa52e"></a>
BaseType_t&#160;</td><td class="memItemRight" valign="bottom"><b>task_core</b> = 1</td></tr>
<tr class="separator:a5b0acd3b6c295785607d326128eaa52e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e218a2956baea1be621e8c31d2875c8"><td class="memItemLeft" align="right" valign="top"><a id="a2e218a2956baea1be621e8c31d2875c8"></a>
UBaseType_t&#160;</td><td class="memItemRight" valign="bottom"><b>task_priority</b> = configMAX_PRIORITIES - 10</td></tr>
<tr class="separator:a2e218a2956baea1be621e8c31d2875c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adbd56e6a344b879166f56dbf6b574822"><td class="memItemLeft" align="right" valign="top">uint16_t&#160;</td><td class="memItemRight" valign="bottom"><b>valid_cod_services</b></td></tr>
<tr class="separator:adbd56e6a344b879166f56dbf6b574822"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1cb800c8abaaffe89b343b03f5fc77ef"><td class="memItemLeft" align="right" valign="top"><a id="a1cb800c8abaaffe89b343b03f5fc77ef"></a>
<a class="el" href="class_a2_d_p_volume_control.html">A2DPVolumeControl</a> *&#160;</td><td class="memItemRight" valign="bottom"><b>volume_control_ptr</b> = nullptr</td></tr>
<tr class="separator:a1cb800c8abaaffe89b343b03f5fc77ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf02ccde9ba626cea64870a255672dba"><td class="memItemLeft" align="right" valign="top"><a id="aaf02ccde9ba626cea64870a255672dba"></a>
uint8_t&#160;</td><td class="memItemRight" valign="bottom"><b>volume_value</b> = 0</td></tr>
<tr class="separator:aaf02ccde9ba626cea64870a255672dba"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="friends"></a>
Friends</h2></td></tr>
<tr class="memitem:a40df53e940884921a9282e2c9e133d1f"><td class="memItemLeft" align="right" valign="top"><a id="a40df53e940884921a9282e2c9e133d1f"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>ccall_a2d_app_heart_beat</b> (TIMER_ARG_TYPE arg)</td></tr>
<tr class="separator:a40df53e940884921a9282e2c9e133d1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add9a125e7249e32cb43a64c92643dd9a"><td class="memItemLeft" align="right" valign="top"><a id="add9a125e7249e32cb43a64c92643dd9a"></a>
int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>ccall_bt_app_a2d_data_cb</b> (uint8_t *data, int32_t len)</td></tr>
<tr class="separator:add9a125e7249e32cb43a64c92643dd9a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a053dbacc4e564c74d61d7be3be03f851"><td class="memItemLeft" align="right" valign="top"><a id="a053dbacc4e564c74d61d7be3be03f851"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>ccall_bt_app_av_sm_hdlr</b> (uint16_t event, void *param)</td></tr>
<tr class="separator:a053dbacc4e564c74d61d7be3be03f851"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a93123a41154cd9cc64753f5430b9f045"><td class="memItemLeft" align="right" valign="top"><a id="a93123a41154cd9cc64753f5430b9f045"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>ccall_bt_av_hdl_avrc_ct_evt</b> (uint16_t event, void *param)</td></tr>
<tr class="separator:a93123a41154cd9cc64753f5430b9f045"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>A2DP Bluetooth Source. </p>
<dl class="section author"><dt>Author</dt><dd>Phil Schatzmann </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Apache License Version 2 </dd></dl>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="ac7131b626b43a516ae4ae9df6a7ec366"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac7131b626b43a516ae4ae9df6a7ec366">&#9670;&nbsp;</a></span>bt_app_av_state_connecting_hdlr()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPSource::bt_app_av_state_connecting_hdlr </td>
          <td>(</td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>event</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>param</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Switch state to APP_AV_STATE_UNCONNECTED when connecting lasts more than 2 heart beat intervals.</p>

</div>
</div>
<a id="a8be3cf8679b236293658c06cd1ed010b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8be3cf8679b236293658c06cd1ed010b">&#9670;&nbsp;</a></span>bt_start()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool BluetoothA2DPCommon::bt_start </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Startup logic as implemented by Arduino. </p>
<dl class="section return"><dt>Returns</dt><dd>true </dd>
<dd>
false </dd></dl>

</div>
</div>
<a id="aa6601d3c57e37f77bfdd03a3ef6231e2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa6601d3c57e37f77bfdd03a3ef6231e2">&#9670;&nbsp;</a></span>debounce()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPCommon::debounce </td>
          <td>(</td>
          <td class="paramtype">void(*)(void)&#160;</td>
          <td class="paramname"><em>cb</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>ms</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Prevents that the same method is executed multiple times within the indicated time limit </p>

</div>
</div>
<a id="af29d19e53e3585446fc294a3213a06af"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af29d19e53e3585446fc294a3213a06af">&#9670;&nbsp;</a></span>is_discovery_active()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool BluetoothA2DPSource::is_discovery_active </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Provides the current discovery state: returns true when the discovery is in progress </p>

</div>
</div>
<a id="a51f93bebf73f8bf9b98fa3c5fc4fcb18"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a51f93bebf73f8bf9b98fa3c5fc4fcb18">&#9670;&nbsp;</a></span>is_valid_cod_service()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool BluetoothA2DPSource::is_valid_cod_service </td>
          <td>(</td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>cod</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns true for ESP_BT_COD_SRVC_RENDERING,ESP_BT_COD_SRVC_AUDIO,ESP_BT_COD_SRVC_TELEPHONY </p>

</div>
</div>
<a id="a65ac6f2b0777c97874ee358119de3790"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a65ac6f2b0777c97874ee358119de3790">&#9670;&nbsp;</a></span>set_auto_reconnect()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void BluetoothA2DPSource::set_auto_reconnect </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>active</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>activate / deactivate the automatic reconnection to the last address (per default this is on) </p>

</div>
</div>
<a id="a17013c6f40042c68821548cab9ddb5eb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a17013c6f40042c68821548cab9ddb5eb">&#9670;&nbsp;</a></span>set_avrc_rn_events()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void BluetoothA2DPCommon::set_avrc_rn_events </td>
          <td>(</td>
          <td class="paramtype">std::vector&lt; <a class="el" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a> &gt;&#160;</td>
          <td class="paramname"><em>events</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Define the vector of esp_avrc_rn_event_ids_t with e.g. ESP_AVRC_RN_PLAY_STATUS_CHANGE | ESP_AVRC_RN_TRACK_CHANGE | ESP_AVRC_RN_TRACK_REACHED_END | ESP_AVRC_RN_TRACK_REACHED_START | ESP_AVRC_RN_PLAY_POS_CHANGED | ESP_AVRC_RN_BATTERY_STATUS_CHANGE | ESP_AVRC_RN_SYSTEM_STATUS_CHANGE | ESP_AVRC_RN_APP_SETTING_CHANGE | ESP_AVRC_RN_NOW_PLAYING_CHANGE | ESP_AVRC_RN_AVAILABLE_PLAYERS_CHANGE | ESP_AVRC_RN_ADDRESSED_PLAYER_CHANGE | ESP_AVRC_RN_UIDS_CHANGE|ESP_AVRC_RN_VOLUME_CHANGE </p>

</div>
</div>
<a id="a41ab8453d4f7f88d68d6cdb1a866532b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a41ab8453d4f7f88d68d6cdb1a866532b">&#9670;&nbsp;</a></span>set_default_bt_mode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void BluetoothA2DPCommon::set_default_bt_mode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">esp_bt_mode_t</a>&#160;</td>
          <td class="paramname"><em>mode</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Defines the default bt mode. The default is ESP_BT_MODE_CLASSIC_BT: use this e.g. to set to ESP_BT_MODE_BTDM </p>

</div>
</div>
<a id="a8e53adc58f665113c9ac6a5521e58814"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e53adc58f665113c9ac6a5521e58814">&#9670;&nbsp;</a></span>set_discoverability()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPCommon::set_discoverability </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">esp_bt_discovery_mode_t</a>&#160;</td>
          <td class="paramname"><em>d</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Bluetooth discoverability. </p>
<p>Defines if the bluetooth is discoverable. </p>

</div>
</div>
<a id="a5f13ecf541393c21a5a489235bad27fb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5f13ecf541393c21a5a489235bad27fb">&#9670;&nbsp;</a></span>set_on_audio_state_changed()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPCommon::set_on_audio_state_changed </td>
          <td>(</td>
          <td class="paramtype">void(*)(<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state, void *)&#160;</td>
          <td class="paramname"><em>callBack</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>obj</em> = <code>nullptr</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set the callback that is called when the audio state is changed. </p>
<p>Set the callback that is called when the audio state is changed This callback is called before the I2S bus is changed. </p>

</div>
</div>
<a id="a169e9b94cbbfb7311a8722cc6d436e95"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a169e9b94cbbfb7311a8722cc6d436e95">&#9670;&nbsp;</a></span>set_on_audio_state_changed_post()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPCommon::set_on_audio_state_changed_post </td>
          <td>(</td>
          <td class="paramtype">void(*)(<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state, void *)&#160;</td>
          <td class="paramname"><em>callBack</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>obj</em> = <code>nullptr</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set the callback that is called after the audio state has changed. This callback is called after the I2S bus has changed. </p>

</div>
</div>
<a id="aa79cff78c075c9273ea2b5c03f052fcd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa79cff78c075c9273ea2b5c03f052fcd">&#9670;&nbsp;</a></span>set_on_connection_state_changed()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPCommon::set_on_connection_state_changed </td>
          <td>(</td>
          <td class="paramtype">void(*)(<a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> state, void *)&#160;</td>
          <td class="paramname"><em>callBack</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>obj</em> = <code>nullptr</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set the callback that is called when the connection state is changed. </p>
<p>Set the callback that is called when the connection state is changed This callback is called before the I2S bus is changed. </p>

</div>
</div>
<a id="a694940fad2a2d498875cfbdf52eea58b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a694940fad2a2d498875cfbdf52eea58b">&#9670;&nbsp;</a></span>set_task_core()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPCommon::set_task_core </td>
          <td>(</td>
          <td class="paramtype">BaseType_t&#160;</td>
          <td class="paramname"><em>core</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Defines the core which is used to start the tasks (to process the events and audio queue) </p>

</div>
</div>
<a id="a2923c8e2a689f21fc5acf7895ad2f7a7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2923c8e2a689f21fc5acf7895ad2f7a7">&#9670;&nbsp;</a></span>set_valid_cod_service()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void BluetoothA2DPSource::set_valid_cod_service </td>
          <td>(</td>
          <td class="paramtype">uint16_t&#160;</td>
          <td class="paramname"><em>filter</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Defines the valid esp_bt_cod_srvc_t values that are used to identify an audio service. e.g (ESP_BT_COD_SRVC_RENDERING | ESP_BT_COD_SRVC_AUDIO | ESP_BT_COD_SRVC_TELEPHONY) </p>

</div>
</div>
<a id="a98c1eb3ad55af189fd3f1ddeac8f3636"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a98c1eb3ad55af189fd3f1ddeac8f3636">&#9670;&nbsp;</a></span>start()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void BluetoothA2DPSource::start </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Starts the A2DP source w/o indicating any names: use the ssid callback to select the device </p>

</div>
</div>
<a id="a7896335b8f2cc324da86e16efb1544c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7896335b8f2cc324da86e16efb1544c9">&#9670;&nbsp;</a></span>to_str()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char * BluetoothA2DPCommon::to_str </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a>&#160;</td>
          <td class="paramname"><em>state</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>converts esp_avrc_playback_stat_t to a string </p>
<p>converts a esp_a2d_audio_state_t to a string </p>

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a id="a2eaf5d6672d7cfcac7622790254b1afd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2eaf5d6672d7cfcac7622790254b1afd">&#9670;&nbsp;</a></span>avrc_rn_events</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">std::vector&lt;<a class="el" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a>&gt; BluetoothA2DPCommon::avrc_rn_events</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= {</div>
<div class="line">      ESP_AVRC_RN_VOLUME_CHANGE}</div>
</div><!-- fragment -->
</div>
</div>
<a id="ac11aaa770b2754858223a4bcdae83b5b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac11aaa770b2754858223a4bcdae83b5b">&#9670;&nbsp;</a></span>connection_state</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> BluetoothA2DPCommon::connection_state</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">=</div>
<div class="line">      <a class="code" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410">ESP_A2D_CONNECTION_STATE_DISCONNECTED</a></div>
<div class="ttc" id="agroup__a2dp_html_gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410"><div class="ttname"><a href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410">ESP_A2D_CONNECTION_STATE_DISCONNECTED</a></div><div class="ttdeci">@ ESP_A2D_CONNECTION_STATE_DISCONNECTED</div><div class="ttdef"><b>Definition:</b> external_lists.h:17</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a8b4258cfaa3a05be3e84f358d8358d29"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8b4258cfaa3a05be3e84f358d8358d29">&#9670;&nbsp;</a></span>discovery_mode_callback</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void(* BluetoothA2DPSource::discovery_mode_callback) (esp_bt_gap_discovery_state_t discoveryMode)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">=</div>
<div class="line">      <span class="keyword">nullptr</span></div>
</div><!-- fragment -->
</div>
</div>
<a id="a8614127c04ef9b9ef6d0c9fa2f33d1ed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8614127c04ef9b9ef6d0c9fa2f33d1ed">&#9670;&nbsp;</a></span>m_a2d_conn_state_str</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* BluetoothA2DPCommon::m_a2d_conn_state_str[4]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= {<span class="stringliteral">&quot;Disconnected&quot;</span>, <span class="stringliteral">&quot;Connecting&quot;</span>,</div>
<div class="line">                                         <span class="stringliteral">&quot;Connected&quot;</span>, <span class="stringliteral">&quot;Disconnecting&quot;</span>}</div>
</div><!-- fragment -->
</div>
</div>
<a id="a44aeeb4214776fea1bca6ecf2e8e8dc2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44aeeb4214776fea1bca6ecf2e8e8dc2">&#9670;&nbsp;</a></span>m_avrc_playback_state_str</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* BluetoothA2DPCommon::m_avrc_playback_state_str[5]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= {<span class="stringliteral">&quot;stopped&quot;</span>, <span class="stringliteral">&quot;playing&quot;</span>, <span class="stringliteral">&quot;paused&quot;</span>,</div>
<div class="line">                                              <span class="stringliteral">&quot;forward seek&quot;</span>, <span class="stringliteral">&quot;reverse seek&quot;</span>}</div>
</div><!-- fragment -->
</div>
</div>
<a id="a02e4f073b4fc04a6e592f50cd3e64c1c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a02e4f073b4fc04a6e592f50cd3e64c1c">&#9670;&nbsp;</a></span>s_a2d_last_state</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__a2dp.html#gabf9f46a0805b93eedaeccb8e512ef7fa">APP_AV_STATE</a> BluetoothA2DPSource::s_a2d_last_state</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">=</div>
<div class="line">      APP_AV_STATE_IDLE</div>
</div><!-- fragment -->
</div>
</div>
<a id="adbd56e6a344b879166f56dbf6b574822"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adbd56e6a344b879166f56dbf6b574822">&#9670;&nbsp;</a></span>valid_cod_services</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t BluetoothA2DPSource::valid_cod_services</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= ESP_BT_COD_SRVC_RENDERING |</div>
<div class="line">                                ESP_BT_COD_SRVC_AUDIO |</div>
<div class="line">                                ESP_BT_COD_SRVC_TELEPHONY</div>
</div><!-- fragment -->
</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>src/<a class="el" href="_bluetooth_a2_d_p_source_8h_source.html">BluetoothA2DPSource.h</a></li>
<li>src/BluetoothA2DPSource.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
