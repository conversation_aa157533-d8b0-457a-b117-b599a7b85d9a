#ifndef GPIO_H_
#define GPIO_H_

#include <ti/devices/cc13x2_cc26x2/driverlib/gpio.h>
#include <ti/devices/cc13x2_cc26x2/driverlib/ioc.h>

#define SpeakerOutput1 IOID_5
#define SpeakerOutput2 IOID_6
#define DetectBoardType_ID0 IOID_19
#define DetectBoardType_ID1 IOID_18

void Gpio_Init(void);
#define Gpio_SetSpeakerOutput1    GPIO_setDio(SpeakerOutput1);
#define Gpio_ClearSpeakerOutput1  GPIO_clearDio(SpeakerOutput1);
#define Gpio_SetSpeakerOutput2    GPIO_setDio(SpeakerOutput2);
#define Gpio_ClearSpeakerOutput2  GPIO_clearDio(SpeakerOutput2);
#define Gpio_GetBoardType_ID1     GPIO_readDio( DetectBoardType_ID1 )
#define Gpio_GetBoardType_ID0     GPIO_readDio( DetectBoardType_ID0 )

#endif /* GPIO_H_ */
