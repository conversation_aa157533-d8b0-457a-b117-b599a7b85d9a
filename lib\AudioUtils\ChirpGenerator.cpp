#include "ChirpGenerator.h"
#include <cmath>
#include <Arduino.h>

ChirpGenerator::ChirpGenerator() {
    if(!generated) {
        // Pre-generate chirp samples for faster playback
        uint32_t sampleIndex = 0;
        
        // For each state/delay pair in the TI chirp pattern
        for(uint32_t i = 0; i < 31 && sampleIndex < NUM_SAMPLES; i++) {
            int16_t state = CHIRP_STATES[i];
            int16_t delay_us = CHIRP_DELAYS_US[i];
            
            // Calculate how many samples this delay represents
            uint32_t delaySamples = (delay_us * SAMPLE_RATE) / 1000000;
            
            // Fill samples with the current state value
            for(uint32_t j = 0; j < delaySamples && sampleIndex < NUM_SAMPLES; j++) {
                chirp[sampleIndex++] = state * CHIRP_AMPLITUDE;
            }
        }
        
        // Fill any remaining samples with zeros
        while(sampleIndex < NUM_SAMPLES) {
            chirp[sampleIndex++] = 0;
        }
        
        generated = true;
    }
}

const int16_t* ChirpGenerator::generate() { 
    return chirp; 
}

std::size_t ChirpGenerator::size() const { 
    return NUM_SAMPLES; 
}

// Real-time transmission method using microsecond delays
void ChirpGenerator::transmitChirp(void (*outputFunction)(int16_t)) {
    for(uint32_t i = 0; i < 31; i++) {
        int16_t state = CHIRP_STATES[i];
        int16_t delay_us = CHIRP_DELAYS_US[i];
        
        // Output the current state
        outputFunction(state * CHIRP_AMPLITUDE);
        
        // Wait for the specified delay in microseconds
        delayMicroseconds(delay_us);
    }
    
    // End with zero output
    outputFunction(0);
}
