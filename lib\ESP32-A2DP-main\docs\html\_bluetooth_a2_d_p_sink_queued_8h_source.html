<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: src/BluetoothA2DPSinkQueued.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">BluetoothA2DPSinkQueued.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160; </div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="preprocessor">#include &quot;BluetoothA2DPSink.h&quot;</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160; </div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="preprocessor">#define RINGBUF_HIGHEST_WATER_LEVEL (32 * 1024)</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#define RINGBUF_PREFETCH_PERCENT 65</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160; </div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="keyword">enum</span> A2DPRingBufferMode : <span class="keywordtype">char</span> {</div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;  RINGBUFFER_MODE_PROCESSING,  <span class="comment">/* ringbuffer is buffering incoming audio data,</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment">                                  I2S is working */</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;  RINGBUFFER_MODE_PREFETCHING, <span class="comment">/* ringbuffer is buffering incoming audio data,</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment">                                  I2S is waiting */</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;  RINGBUFFER_MODE_DROPPING <span class="comment">/* ringbuffer is not buffering (dropping) incoming</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment">                              audio data, I2S is working */</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;};</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160; </div>
<div class="line"><a name="l00024"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink_queued.html">   24</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a> : <span class="keyword">public</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a> {</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a>() = <span class="keywordflow">default</span>;</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160; </div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#if A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00030"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink_queued.html#a2af5c1af762db64e77815000fedeec01">   30</a></span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_sink_queued.html#a2af5c1af762db64e77815000fedeec01">BluetoothA2DPSinkQueued</a>(audio_tools::AudioOutput &amp;output) {</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;    actual_bluetooth_a2dp_sink = <span class="keyword">this</span>;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;    out-&gt;<a class="code" href="class_bluetooth_a2_d_p_output.html#a67fc2cf760ae2a48ac6bfa6ed235f8b8">set_output</a>(output);</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;  }</div>
<div class="line"><a name="l00035"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink_queued.html#a40c1d32c321431eff1e3fecae71e3563">   35</a></span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_sink_queued.html#a40c1d32c321431eff1e3fecae71e3563">BluetoothA2DPSinkQueued</a>(audio_tools::AudioStream &amp;output) {</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;    actual_bluetooth_a2dp_sink = <span class="keyword">this</span>;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;    out-&gt;<a class="code" href="class_bluetooth_a2_d_p_output.html#a67fc2cf760ae2a48ac6bfa6ed235f8b8">set_output</a>(output);</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;  }</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160; </div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00043"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink_queued.html#a494c5b10321bdeba5b0431cc3c592a2d">   43</a></span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_sink_queued.html#a494c5b10321bdeba5b0431cc3c592a2d">BluetoothA2DPSinkQueued</a>(Print &amp;output) {</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;    actual_bluetooth_a2dp_sink = <span class="keyword">this</span>;</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;    out-&gt;<a class="code" href="class_bluetooth_a2_d_p_output.html#a67fc2cf760ae2a48ac6bfa6ed235f8b8">set_output</a>(output);</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;  }</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160; </div>
<div class="line"><a name="l00050"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink_queued.html#a8450f09c7af817cacc78ae9a3544ee28">   50</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink_queued.html#a8450f09c7af817cacc78ae9a3544ee28">set_i2s_stack_size</a>(<span class="keywordtype">int</span> size) { i2s_stack_size = size; }</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160; </div>
<div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink_queued.html#acc0410c71c7278cd3a858d037fdc1edc">   53</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink_queued.html#acc0410c71c7278cd3a858d037fdc1edc">set_i2s_ringbuffer_size</a>(<span class="keywordtype">int</span> size) { i2s_ringbuffer_size = size; }</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160; </div>
<div class="line"><a name="l00056"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink_queued.html#a54861eac37a7f5902f6afb1a03200085">   56</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink_queued.html#a54861eac37a7f5902f6afb1a03200085">set_i2s_ringbuffer_prefetch_percent</a>(<span class="keywordtype">int</span> percent) {</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;    <span class="keywordflow">if</span> (percent &lt; 0) <span class="keywordflow">return</span>;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;    <span class="keywordflow">if</span> (percent &gt; 100) <span class="keywordflow">return</span>;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;    ringbuffer_prefetch_percent = percent;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;  }</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160; </div>
<div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink_queued.html#a430f730deb72b7edf5601e740c9b8563">   63</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink_queued.html#a430f730deb72b7edf5601e740c9b8563">set_i2s_task_priority</a>(UBaseType_t prio) { i2s_task_priority = prio; }</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160; </div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  <span class="keywordtype">void</span> set_i2s_write_size_upto(<span class="keywordtype">size_t</span> size) { i2s_write_size_upto = size; }</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160; </div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  <span class="keywordtype">void</span> set_i2s_ticks(<span class="keywordtype">int</span> ticks) { i2s_ticks = ticks; }</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160; </div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160; <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;  TaskHandle_t s_bt_i2s_task_handle = <span class="keyword">nullptr</span>; <span class="comment">/* handle of I2S task */</span></div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;  RingbufHandle_t s_ringbuf_i2s = <span class="keyword">nullptr</span>;    <span class="comment">/* handle of ringbuffer for I2S */</span></div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  SemaphoreHandle_t s_i2s_write_semaphore = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;  <span class="comment">// I2S task</span></div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <span class="keywordtype">int</span> i2s_stack_size = 2048;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  <span class="keywordtype">int</span> i2s_ringbuffer_size = RINGBUF_HIGHEST_WATER_LEVEL;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  UBaseType_t i2s_task_priority = configMAX_PRIORITIES - 3;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;  <span class="keyword">volatile</span> A2DPRingBufferMode ringbuffer_mode = RINGBUFFER_MODE_PROCESSING;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <span class="keyword">volatile</span> <span class="keywordtype">bool</span> is_starting = <span class="keyword">true</span>;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;  <span class="keywordtype">size_t</span> i2s_write_size_upto = 240 * 6;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;  <span class="keywordtype">int</span> i2s_ticks = 20;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;  <span class="keywordtype">int</span> ringbuffer_prefetch_percent = RINGBUF_PREFETCH_PERCENT;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160; </div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;  <span class="keywordtype">void</span> bt_i2s_task_start_up(<span class="keywordtype">void</span>) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  <span class="keywordtype">void</span> bt_i2s_task_shut_down(<span class="keywordtype">void</span>) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink_queued.html#a914815f36b15b259d328da372b6c3d08">i2s_task_handler</a>(<span class="keywordtype">void</span> *arg) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  <span class="keywordtype">size_t</span> <a class="code" href="class_bluetooth_a2_d_p_sink_queued.html#a1846088388d294f04469885b9bb560e4">write_audio</a>(<span class="keyword">const</span> uint8_t *data, <span class="keywordtype">size_t</span> size) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160; </div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;  <span class="keywordtype">void</span> set_i2s_active(<span class="keywordtype">bool</span> active)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    BluetoothA2DPSink::set_i2s_active(active);</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    <span class="keywordflow">if</span> (active) {</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;      ringbuffer_mode = RINGBUFFER_MODE_PREFETCHING;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;      is_starting = <span class="keyword">true</span>;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    }</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;  }</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160; </div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;  <span class="keywordtype">int</span> i2s_ringbuffer_prefetch_size() {</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;    <span class="keywordtype">int</span> bytes = i2s_ringbuffer_size * ringbuffer_prefetch_percent / 100;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;    <span class="keywordflow">return</span> (bytes / 4 * 4);</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;  }</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;};</div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_html_a67fc2cf760ae2a48ac6bfa6ed235f8b8"><div class="ttname"><a href="class_bluetooth_a2_d_p_output.html#a67fc2cf760ae2a48ac6bfa6ed235f8b8">BluetoothA2DPOutput::set_output</a></div><div class="ttdeci">virtual void set_output(audio_tools::AudioOutput &amp;output)</div><div class="ttdoc">Not implemented.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:32</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></div><div class="ttdoc">A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example https://github....</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:59</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_queued_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink_queued.html">BluetoothA2DPSinkQueued</a></div><div class="ttdoc">The BluetoothA2DPSinkQueued is using a separate Task with an additinal Queue to write the I2S data....</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSinkQueued.h:24</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_queued_html_a1846088388d294f04469885b9bb560e4"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink_queued.html#a1846088388d294f04469885b9bb560e4">BluetoothA2DPSinkQueued::write_audio</a></div><div class="ttdeci">size_t write_audio(const uint8_t *data, size_t size) override</div><div class="ttdoc">output audio data e.g. to i2s or to queue</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSinkQueued.cpp:87</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_queued_html_a2af5c1af762db64e77815000fedeec01"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink_queued.html#a2af5c1af762db64e77815000fedeec01">BluetoothA2DPSinkQueued::BluetoothA2DPSinkQueued</a></div><div class="ttdeci">BluetoothA2DPSinkQueued(audio_tools::AudioOutput &amp;output)</div><div class="ttdoc">Output AudioOutput using AudioTools library.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSinkQueued.h:30</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_queued_html_a40c1d32c321431eff1e3fecae71e3563"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink_queued.html#a40c1d32c321431eff1e3fecae71e3563">BluetoothA2DPSinkQueued::BluetoothA2DPSinkQueued</a></div><div class="ttdeci">BluetoothA2DPSinkQueued(audio_tools::AudioStream &amp;output)</div><div class="ttdoc">Output AudioStream using AudioTools library.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSinkQueued.h:35</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_queued_html_a430f730deb72b7edf5601e740c9b8563"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink_queued.html#a430f730deb72b7edf5601e740c9b8563">BluetoothA2DPSinkQueued::set_i2s_task_priority</a></div><div class="ttdeci">void set_i2s_task_priority(UBaseType_t prio)</div><div class="ttdoc">Defines the priority of the I2S task.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSinkQueued.h:63</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_queued_html_a494c5b10321bdeba5b0431cc3c592a2d"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink_queued.html#a494c5b10321bdeba5b0431cc3c592a2d">BluetoothA2DPSinkQueued::BluetoothA2DPSinkQueued</a></div><div class="ttdeci">BluetoothA2DPSinkQueued(Print &amp;output)</div><div class="ttdoc">Output to Arduino Print.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSinkQueued.h:43</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_queued_html_a54861eac37a7f5902f6afb1a03200085"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink_queued.html#a54861eac37a7f5902f6afb1a03200085">BluetoothA2DPSinkQueued::set_i2s_ringbuffer_prefetch_percent</a></div><div class="ttdeci">void set_i2s_ringbuffer_prefetch_percent(int percent)</div><div class="ttdoc">Audio starts to play when limit exeeded.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSinkQueued.h:56</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_queued_html_a8450f09c7af817cacc78ae9a3544ee28"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink_queued.html#a8450f09c7af817cacc78ae9a3544ee28">BluetoothA2DPSinkQueued::set_i2s_stack_size</a></div><div class="ttdeci">void set_i2s_stack_size(int size)</div><div class="ttdoc">Defines the stack size of the i2s task (in bytes)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSinkQueued.h:50</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_queued_html_a914815f36b15b259d328da372b6c3d08"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink_queued.html#a914815f36b15b259d328da372b6c3d08">BluetoothA2DPSinkQueued::i2s_task_handler</a></div><div class="ttdeci">void i2s_task_handler(void *arg) override</div><div class="ttdoc">dummy functions needed for BluetoothA2DPSinkQueued</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSinkQueued.cpp:43</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_queued_html_acc0410c71c7278cd3a858d037fdc1edc"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink_queued.html#acc0410c71c7278cd3a858d037fdc1edc">BluetoothA2DPSinkQueued::set_i2s_ringbuffer_size</a></div><div class="ttdeci">void set_i2s_ringbuffer_size(int size)</div><div class="ttdoc">Defines the ringbuffer size used by the i2s task (in bytes)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSinkQueued.h:53</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
