#include <stdint.h>
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <ti/drivers/ADCBuf.h>
#include <ti/drivers/adcbuf/ADCBufCC26XX.h>
#include <ti/drivers/Board.h>
#include "ti_drivers_config.h"
#include "time.h"
#include "RFCommunication.h"
#include <stdbool.h>

#define InternalADCBufferSize   100
volatile uint16_t InternalADCBuffer1[InternalADCBufferSize];
volatile uint16_t InternalADCBuffer2[InternalADCBufferSize];
volatile uint16_t DestinationArrayIndex;
volatile uint16_t* pDestinationArray=NULL;
uint16_t DestinationArraySize;

void ADCBufCallback(ADCBuf_Handle handle, ADCBuf_Conversion *conversion, void *completedADCBuffer, uint32_t completedChannel,int_fast16_t status);

//*********************************************************************************************************
// Initialize ADC
//*********************************************************************************************************
void ADC_Init(void)
{
  ADCBuf_init();
}

//*********************************************************************************************************
// Fills an array with raw (value directly from ADC) samples.
//*********************************************************************************************************
void ADC_ReadArrayADCraw(uint32_t adcChannel, volatile uint16_t *DestinationArray, uint16_t NumberOfSamples,uint32_t SampleFrequency )
{
  ADCBuf_Handle adcBuf;
  ADCBuf_Params adcBufParams;
  ADCBuf_Conversion continuousConversion= {0};
  ADCBufCC26XX_ParamsExtension customParams;
  pDestinationArray = DestinationArray;
  DestinationArrayIndex=0;
  DestinationArraySize=NumberOfSamples;
  ADCBuf_Params_init(&adcBufParams);
  adcBufParams.callbackFxn =  ADCBufCallback;
  adcBufParams.recurrenceMode = ADCBuf_RECURRENCE_MODE_CONTINUOUS;
  adcBufParams.returnMode = ADCBuf_RETURN_MODE_CALLBACK;
  adcBufParams.samplingFrequency = SampleFrequency;
  adcBufParams.blockingTimeout = 50000;
  customParams.refSource     = ADCBufCC26XX_FIXED_REFERENCE;
  customParams.inputScalingEnabled = true;
  customParams.samplingMode = ADCBufCC26XX_SAMPING_MODE_ASYNCHRONOUS;
  adcBufParams.custom = &customParams;
  adcBuf = ADCBuf_open(ADCBUF, &adcBufParams);
  continuousConversion.arg = NULL;
  continuousConversion.adcChannel = adcChannel;
  continuousConversion.sampleBuffer = (void*)InternalADCBuffer1;
  continuousConversion.sampleBufferTwo = (void*)InternalADCBuffer2;
  continuousConversion.samplesRequestedCount = InternalADCBufferSize;
  if (adcBuf == NULL){  while(1);    }
    if (ADCBuf_convert(adcBuf, &continuousConversion, 1) != ADCBuf_STATUS_SUCCESS) { while(1); }
  while (DestinationArrayIndex<NumberOfSamples ) ;
  ADCBuf_convertCancel(adcBuf);
  ADCBuf_close(adcBuf);
}

//*********************************************************************************************************
//returns average of 4 samples of a specific ADC channel. Converted to mV
//*********************************************************************************************************
uint16_t ADC_ReadSingleValueADCmV(uint32_t adcChannel)
{
  uint16_t Buffer[10];
  ADC_ReadArrayADCraw(adcChannel, Buffer, 10,50000 );  // take 10 samples at 50000Hz
  uint32_t sum =0;
  uint16_t i;
  for (i=5;i<9;i++) sum+= *(( (uint16_t *)Buffer) + i);
  uint16_t ResultmV = (uint16_t)((sum*4347)/16384);
  return(ResultmV);
}

//*********************************************************************************************************
// This function is called if the ADC buffer is full.
//*********************************************************************************************************
void ADCBufCallback(ADCBuf_Handle handle, ADCBuf_Conversion *conversion, void *completedADCBuffer, uint32_t completedChannel,int_fast16_t status)
{
  uint16_t i;
  for (i = 0; (i < InternalADCBufferSize) && (DestinationArrayIndex<DestinationArraySize); i++)
        { *(pDestinationArray+DestinationArrayIndex++)= *((  (uint16_t *)completedADCBuffer) + i); }
}
