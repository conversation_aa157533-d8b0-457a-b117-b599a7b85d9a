<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: SoundData Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="class_sound_data-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">SoundData Class Reference<span class="mlabels"><span class="mlabel">abstract</span></span><div class="ingroups"><a class="el" href="group__a2dp.html">ESP32 A2DP</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Sound data as byte stream. We support <a class="el" href="class_two_channel_sound_data.html" title="Data is provided in two channels of int16 data: so len is in 4 byte entries (int16 + int16)">TwoChannelSoundData</a> (uint16_t + uint16_t) and <a class="el" href="class_one_channel_sound_data.html" title="1 Channel data is provided as int16 values">OneChannelSoundData</a> which stores the data as array of uint16_t We provide the complete sound data as a simple c array which can be prepared e.g. in the following way.  
 <a href="class_sound_data.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_sound_data_8h_source.html">SoundData.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for SoundData:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_sound_data.png" usemap="#SoundData_map" alt=""/>
  <map id="SoundData_map" name="SoundData_map">
<area href="class_one_channel8_bit_sound_data.html" title="1 Channel data is provided as signed int8 values." alt="OneChannel8BitSoundData" shape="rect" coords="0,56,166,80"/>
<area href="class_one_channel_sound_data.html" title="1 Channel data is provided as int16 values" alt="OneChannelSoundData" shape="rect" coords="176,56,342,80"/>
<area href="class_two_channel_sound_data.html" title="Data is provided in two channels of int16 data: so len is in 4 byte entries (int16 + int16)" alt="TwoChannelSoundData" shape="rect" coords="352,56,518,80"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ac79933ed3379cf5ef58d5675aa4bf12e"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_sound_data.html#ac79933ed3379cf5ef58d5675aa4bf12e">doLoop</a> ()</td></tr>
<tr class="separator:ac79933ed3379cf5ef58d5675aa4bf12e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acf84aea9000bf0e65ec08527a4b0a08b"><td class="memItemLeft" align="right" valign="top"><a id="acf84aea9000bf0e65ec08527a4b0a08b"></a>
virtual int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>get2ChannelData</b> (int32_t pos, int32_t len, uint8_t *data)=0</td></tr>
<tr class="separator:acf84aea9000bf0e65ec08527a4b0a08b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7139f565fee3fdb5fc8d645db470837f"><td class="memItemLeft" align="right" valign="top"><a id="a7139f565fee3fdb5fc8d645db470837f"></a>
virtual int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>getData</b> (int32_t pos, Frame &amp;channels)=0</td></tr>
<tr class="separator:a7139f565fee3fdb5fc8d645db470837f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4051b1310e8c9dec86d688593c3d5efc"><td class="memItemLeft" align="right" valign="top"><a id="a4051b1310e8c9dec86d688593c3d5efc"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>setDataRaw</b> (uint8_t *data, int32_t len)=0</td></tr>
<tr class="separator:a4051b1310e8c9dec86d688593c3d5efc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed157f28a14d6fd8ef1571db60afd003"><td class="memItemLeft" align="right" valign="top"><a id="aed157f28a14d6fd8ef1571db60afd003"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>setLoop</b> (bool loop)</td></tr>
<tr class="separator:aed157f28a14d6fd8ef1571db60afd003"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Sound data as byte stream. We support <a class="el" href="class_two_channel_sound_data.html" title="Data is provided in two channels of int16 data: so len is in 4 byte entries (int16 + int16)">TwoChannelSoundData</a> (uint16_t + uint16_t) and <a class="el" href="class_one_channel_sound_data.html" title="1 Channel data is provided as int16 values">OneChannelSoundData</a> which stores the data as array of uint16_t We provide the complete sound data as a simple c array which can be prepared e.g. in the following way. </p>
<ul>
<li>Open any sound file in Audacity. Make sure that it contains 2 channels<ul>
<li>Select Tracks -&gt; Resample and select 44100</li>
<li>Export -&gt; Export Audio -&gt; Header Raw ; Signed 16 bit PCM</li>
</ul>
</li>
<li>Convert to c file e.g. with "xxd -i file_example_WAV_1MG.raw file_example_WAV_1MG.c"<ul>
<li>add the const qualifier to the array definition. E.g const unsigned char file_example_WAV_1MG_raw[] = {</li>
</ul>
</li>
</ul>
<dl class="section author"><dt>Author</dt><dd>Phil Schatzmann </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Apache License Version 2 </dd></dl>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="ac79933ed3379cf5ef58d5675aa4bf12e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac79933ed3379cf5ef58d5675aa4bf12e">&#9670;&nbsp;</a></span>doLoop()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool SoundData::doLoop </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Automatic restart playing on end </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>src/<a class="el" href="_sound_data_8h_source.html">SoundData.h</a></li>
<li>src/SoundData.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
