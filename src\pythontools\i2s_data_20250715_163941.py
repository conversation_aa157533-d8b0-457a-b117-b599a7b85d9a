# I2S Audio Data Array
# Generated from ESP32 NodeA I2S output
# Captured on: 20250715_163941
# Data format: signed 16-bit (int16_t)
# Sample count: 2400
# Sample rate: 48000 Hz
# Duration: 50.00 ms

import numpy as np

# I2S data as Python list (signed 16-bit values)
i2s_data_list = [
         0,      0,      3,     10,     25,     48,     82,    128,    185,    255,    337,    430,    532,    641,    755,    869,
       980,   1084,   1176,   1252,   1306,   1333,   1330,   1291,   1213,   1092,    925,    711,    447,    135,   -224,   -629,
     -1077,  -1562,  -2078,  -2619,  -3177,  -3742,  -4304,  -4853,  -5377,  -5863,  -6301,  -6677,  -6981,  -7200,  -7324,  -7344,
     -7251,  -7038,  -6700,  -6234,  -5639,  -4914,  -4064,  -3095,  -2014,   -832,    438,   1781,   3181,   4617,   6069,   7515,
      8931,  10294,  11579,  12761,  13817,  14725,  15461,  16007,  16346,  16461,  16342,  15979,  15367,  14506,  13398,  12049,
     10472,   8681,   6696,   4539,   2238,   -175,  -2671,  -5212,  -7761, -10278, -12723, -15057, -17240, -19234, -21001, -22508,
    -23724, -24621, -25177, -25374, -25199, -24645, -23711, -22402, -20729, -18709, -16366, -13730, -10834,  -7719,  -4429,  -1012,
      2481,   5997,   9481,  12877,  16130,  19187,  21994,  24505,  26673,  28461,  29833,  30761,  31225,  31212,  30714,  29735,
     28284,  26380,  24048,  21321,  18239,  14850,  11205,   7362,   3382,   -671,  -4733,  -8736, -12614, -16303, -19740, -22869,
    -25636, -27997, -29917, -31362, -32308, -32739, -32646, -32027, -30893, -29258, -27148, -24595, -21640, -18328, -14714, -10854,
     -6812,  -2653,   1553,   5739,   9835,  13772,  17485,  20911,  23991,  26673,  28910,  30663,  31902,  32602,  32752,  32345,
     31388,  29894,  27888,  25402,  22477,  19162,  15513,  11592,   7465,   3205,  -1116,  -5422,  -9638, -13691, -17507, -21020,
    -24166, -26888, -29137, -30871, -32058, -32675, -32709, -32157, -31028, -29339, -27119, -24407, -21251, -17705, -13833,  -9706,
     -5396,   -983,   3451,   7828,  12066,  16085,  19811,  23174,  26110,  28562,  30485,  31839,  32598,  32746,  32277,  31199,
     29530,  27300,  24549,  21329,  17698,  13725,   9486,   5059,    531,  -4011,  -8481, -12792, -16860, -20604, -23951, -26834,
    -29195, -30987, -32172, -32725, -32633, -31897, -30528, -28552, -26006, -22939, -19411, -15491, -11256,  -6791,  -2184,   2470,
      7080,  11552,  15794,  19718,  23245,  26300,  28821,  30752,  32054,  32697,  32665,  31958,  30589,  28582,  25979,  22832,
     19204,  15171,  10815,   6228,   1504,  -3254,  -7950, -12482, -16753, -20672, -24155, -27125, -29517, -31279, -32370, -32765,
    -32454, -31441, -29745, -27403, -24462, -20985, -17046, -12731,  -8132,  -3350,   1510,   6342,  11038,  15495,  19612,  23298,
     26468,  29051,  30986,  32230,  32750,  32535,  31587,  29924,  27583,  24614,  21083,  17069,  12662,   7961,   3074,  -1888,
     -6812, -11584, -16093, -20235, -23912, -27038, -29538, -31353, -32437, -32764, -32324, -31125, -29193, -26571, -23320, -19513,
    -15241, -10603,  -5708,   -673,   4383,   9339,  14076,  18480,  22443,  25868,  28673,  30786,  32154,  32743,  32536,  31536,
     29764,  27262,  24089,  20322,  16051,  11381,   6425,   1305,  -3851,  -8917, -13765, -18275, -22333, -25835, -28693, -30833,
    -32199, -32754, -32483, -31389, -29498, -26857, -23530, -19599, -15165, -10339,  -5243,     -8,   5231,  10341,  15189,  19648,
     23602,  26946,  29593,  31471,  32530,  32738,  32089,  30597,  28299,  25254,  21539,  17252,  12503,   7419,   2133,  -3214,
     -8480, -13525, -18212, -22415, -26019, -28926, -31055, -32348, -32766, -32296, -30949, -28759, -25782, -22099, -17809, -13026,
     -7882,  -2517,   2922,   8286,  13425,  18197,  22468,  26119,  29046,  31165,  32415,  32759,  32185,  30707,  28363,  25218,
     21358,  16891,  11941,   6648,   1162,  -4362,  -9767, -14897, -19605, -23755, -27226, -29916, -31747, -32662, -32633, -31658,
    -29764, -27002, -23452, -19214, -14411,  -9182,  -3679,   1935,   7499,  12846,  17818,  22267,  26061,  29083,  31244,  32476,
     32741,  32027,  30353,  27768,  24347,  20191,  15422,  10184,   4634,  -1061,  -6729, -12198, -17299, -21877, -25791, -28919,
    -31163, -32452, -32744, -32028, -30323, -27679, -24177, -19921, -15043,  -9693,  -4037,   1749,   7486,  12993,  18098,  22637,
     26468,  29468,  31539,  32614,  32657,  31663,  29662,  26715,  22913,  18376,  13248,   7690,   1881,  -3992,  -9742, -15183,
    -20137, -24443, -27961, -30573, -32193, -32766, -32269, -30718, -28160, -24676, -20379, -15408,  -9925,  -4110,   1844,   7743,
     13390,  18596,  23188,  27011,  29935,  31861,  32722,  32485,  31158,  28780,  25431,  21221,  16290,  10804,   4946,  -1083,
     -7081, -12842, -18169, -22880, -26810, -29825, -31817, -32716, -32488, -31139, -28713, -25290, -20988, -15954, -10360,  -4402,
      1714,   7776,  13571,  18894,  23557,  27396,  30273,  32084,  32762,  32282,  30657,  27942,  24232,  19656,  14375,   8577,
      2468,  -3734,  -9807, -15533, -20704, -25132, -28656, -31146, -32510, -32696, -31694, -29537, -26303, -22106, -17097, -11459,
     -5396,    868,   7107,  13088,  18591,  23411,  27368,  30314,  32137,  32766,  32176,  30386,  27459,  23503,  18664,  13121,
      7080,    769,  -5575, -11714, -17416, -22462, -26660, -29849, -31905, -32747, -32341, -30699, -27882, -23994, -19182, -13630,
     -7549,  -1172,   5254,  11482,  17270,  22393,  26650,  29873,  31936,  32754,  32293,  30568,  27645,  23634,  18692,  13011,
      6812,    340,  -6149, -12400, -18165, -23212, -27340, -30382, -32215, -32762, -31999, -29955, -26706, -22383, -17156, -11234,
     -4854,   1725,   8241,  14427,  20032,  24827,  28614,  31238,  32589,  32608,  31292,  28693,  24913,  20106,  14468,   8229,
      1646,  -5009, -11463, -17445, -22707, -27028, -30227, -32166, -32764, -31991, -29877, -26509, -22024, -16609, -10491,  -3925,
      2810,   9432,  15658,  21224,  25893,  29463,  31780,  32741,  32305,  30485,  27357,  23053,  17756,  11690,   5116,  -1683,
     -8414, -14785, -20521, -25369, -29119, -31603, -32711, -32392, -30657, -27579, -23289, -17975, -11866,  -5231,   1638,   8441,
     14875,  20656,  25524,  29263,  31704,  32735,  32307,  30438,  27206,  22754,  17280,  11026,   4272,  -2677,  -9512, -15923,
    -21617, -26338, -29867, -32044, -32766, -31998, -29771, -26183, -21397, -15629,  -9140,  -2228,   4791,  11594,  17868,  23322,
     27702,  30804,  32481,  32652,  31307,  28504,  24373,  19101,  12934,   6158,   -910,  -7941, -14603, -20584, -25599, -29411,
    -31837, -32759, -32132, -29980, -26405, -21572, -15709,  -9094,  -2040,   5115,  12031,  18375,  23841,  28166,  31139,  32614,
     32517,  30849,  27688,  23184,  17553,  11066,   4038,  -3192, -10272, -16855, -22618, -27278, -30604, -32430, -32665, -31292,
    -28376, -24059, -18551, -12121,  -5087,   2203,   9389,  16113,  22040,  26871,  30364,  32341,  32702,  31423,  28567,  24274,
     18756,  12291,   5201,  -2156,  -9408, -16190, -22154, -26998, -30472, -32397, -32672, -31280, -28288, -23847, -18182, -11580,
     -4378,   3051,  10330,  17080,  22953,  27643,  30904,  32567,  32539,  30821,  27498,  22740,  16794,   9966,   2612,  -4882,
    -12125, -18737, -24368, -28720, -31561, -32738, -32186, -29931, -26089, -20861, -14522,  -7407,    107,   7620,  14731,  21059,
     26264,  30066,  32256,  32716,  31416,  28424,  23897,  18077,  11276,   3861,  -3767, -11196, -18022, -23871, -28424, -31430,
    -32722, -32225, -29964, -26060, -20724, -14245,  -6979,    675,   8298,  15467,  21785,  26901,  30530,  32466,  32600,  30920,
     27518,  22578,  16375,   9252,   1608,  -6131, -13531, -20176, -25690, -29760, -32154, -32734, -31464, -28413, -23749, -17735,
    -10711,  -3072,   4745,  12297,  19152,  24916,  29258,  31926,  32764,  31720,  28852,  24321,  18385,  11385,   3722,  -4160,
    -11806, -18773, -24653, -29103, -31862, -32766, -31758, -28894, -24339, -18355, -11292,  -3561,   4383,  12074,  19059,  24923,
     29319,  31983,  32756,  31588,  28545,  23803,  17643,  10428,   2587,  -5412, -13092, -19995, -25703, -29873, -32250, -32690,
    -31163, -27756, -22672, -16215,  -8774,   -797,   7232,  14828,  21527,  26920,  30676,  32563,  32463,  30378,  26432,  20864,
     14013,   6297,  -1810,  -9811, -17211, -23555, -28445, -31580, -32760, -31909, -29077, -24436, -18271, -10965,  -2971,   5212,
     13076,  20125,  25918,  30087,  32369,  32617,  30811,  27063,  21604,  14778,   7012,  -1200,  -9343, -16897, -23382, -28382,
    -31578, -32762, -31855, -28911, -24117, -17774, -10287,  -2136,   6156,  14058,  21058,  26704,  30628,  32575,  32414,  30152,
     25932,  20026,  12815,   4765,  -3599, -11734, -19107, -25235, -29713, -32247, -32665, -30938, -27175, -21620, -14637,  -6684,
      1714,  10005,  17638,  24105,  28976,  31924,  32750,  31396,  27947,  22631,  15799,   7907,   -519,  -8915, -16717, -23400,
    -28513, -31708, -32766, -31613, -28323, -23114, -16338,  -8449,     17,   8486,  16384,  23169,  28379,  31653,  32766,  31638,
     28342,  23101,  16271,   8319,   -209,  -8728, -16651, -23427, -28589, -31775, -32762, -31477, -28007, -22589, -15596,  -7513,
      1097,   9637,  17507,  24157,  29119,  32042,  32717,  31094,  27283,  21548,  14290,   6021,  -2677, -11192, -18918, -25308,
    -29905, -32381, -32554, -30410, -26097, -19920, -12316,  -3827,   4939,  13356,  20821,  26793,  30843,  32674,  32152,  29310,
     24350,  17627,   9625,    921,  -7853, -16063, -23109, -28478, -31775, -32757, -31348, -27647, -21922, -14588,  -6181,   2684,
     11357,  19199,  25631,  30178,  32500,  32422,  29947,  25253,  18686,  10729,   1971,  -6939, -15336, -22595, -28173, -31650,
    -32764, -31426, -27733, -21960, -14535,  -6013,   2965,  11725,  19605,  26010,  30453,  32596,  32272,  29502,  24492,  17620,
      9404,    468,  -8505, -16838, -23889, -29117, -32120, -32665, -30706, -26388, -20041, -12149,  -3318,   5773,  14424,  21965,
     27811,  31509,  32766,  31483,  27755,  21867,  14275,   5566,  -3578, -12450, -20354, -26669, -30900, -32711, -31957, -28692,
    -23169, -15821,  -7222,   1950,  10974,  19135,  25786,  30398,  32603,  32221,  29280,  24009,  16823,   8294,   -901, -10029,
    -18361, -25227, -30075, -32515, -32346, -29578, -24430, -17312,  -8795,    435,   9635,  18061,  25029,  29973,  32490,  32369,
     29618,  24457,  17302,   8733,   -552,  -9799, -18248, -25207, -30106, -32537, -32299, -29406, -24093, -16794,  -8106,   1254,
     10516,  18912,  25748,  30454,  32637,  32111,  28917,  23316,  15770,   6907,  -2537, -11775, -20032, -26616, -30975, -32738,
    -31754, -28101, -22083, -14203,  -5121,   4396,  13547,  21557,  27748,  31591,  32757,  31144,  26884,  20335,  12053,   2739,
     -6812, -15787, -23417, -29048, -32193, -32581, -30172, -25171, -18004,  -9283,    241,   9749,  18421,  25505,  30387,  32639,
     32065,  28709,  22857,  15016,   5865,  -3800, -13139, -21337, -27675, -31596, -32753, -31041, -26607, -19834, -11316,  -1798,
      7883,  16873,  24379,  29737,  32469,  32329,  29326,  23722,  16010,   6873,  -2880, -12381, -20783, -27337, -31454, -32762,
    -31142, -26733, -19926, -11328,  -1710,   8066,  17121,  24639,  29941,  32543,  32208,  28962,  23094,  15131,   5792,  -4075,
    -13577, -21850, -28138, -31866, -32690, -30531, -25582, -18291,  -9323,    504,  10289,  19135,  26226,  30909,  32748,  31570,
     27480,  20851,  12292,   2592,  -7353, -16621, -24350, -29824, -32527, -32206, -28885, -22870, -14719,  -5188,   4830,  14402,
     22630,  28739,  32155,  32553,  29890,  24413,  16634,   7283,  -2759, -12545, -21151, -27757, -31738, -32711, -30581, -25544,
    -18076,  -8884,   1158,  11095,  19977,  26955,  31358,  32762,  31028,  26316,  19078,  10003,    -38, -10081, -19156, -26389,
    -31077, -32765, -31285, -26775, -19670, -10653,   -599,   9517,  18714,  26093,  30933,  32758,  31387,  26948,  19871,  10846,
       755,  -9415, -18666, -26087, -30945, -32760, -31348, -26843, -19687, -10582,   -427,   9774,  19012,  26370,  31113,  32766,
     31163,  26457,  19113,   9860,   -381, -10589, -19743, -26924, -31412, -32752, -30806, -25764, -18129,  -8666,   1673,  11849,
     20833,  27717,  31802,  32671,  30231,  24725,  16708,   6990,  -3442, -13529, -22242, -28693, -32219, -32456, -29375, -23289,
    -14814,  -4818,   5677,  15593,  23912,  29775,  32574,  32018,  28160,  21392,  12411,   2142,  -8352, -17987, -25759, -30862,
    -32761, -31253, -26493, -18970,  -9466,   1029,  11422,  20626,  27675,  31829,  32647,  30041,  24278,  15962,   5961,  -4669,
    -14814, -23400, -29519, -32521, -32085, -28251, -21422, -12318,  -1903,   8718,  18419,  26162,  31121,  32765,  30913,  25757,
     17846,   8020,  -2666, -13072, -22081, -28722, -32279, -32365, -28966, -22444, -13499,  -3091,   7653,  17575,  25599,  30853,
     32764,  31119,  26094,  18229,   8377,  -2392, -12904, -22012, -28722, -32294, -32336, -28837, -22176, -13080,  -2544,   8275,
     18189,  26106,  31150,  32761,  30757,  25356,  17148,   7041,  -3850, -14321, -23207, -29519, -32554, -31969, -27825, -20579,
    -11034,   -254,  10557,  20195,  27576,  31871,  32596,  29664,  23398,  14502,   3968,  -7014, -17212, -25472, -30862, -32766,
    -30966, -25662, -17447,  -7252,   3771,  14369,  23339,  29658,  32603,  31833,  27433,  19899,  10089,   -878, -11751, -21282,
    -28377, -32218, -32362, -28785, -21896, -12483,  -1628,   9419,  19384,  27114,  31712,  32641,  29791,  23486,  14452,   3738,
     -7415, -17710, -25949, -31167, -32752, -30516, -24714, -16019,  -5447,   5767,  16309,  24943,  30653,  32762,  31019,  25624,
     17208,   6760,  -4489, -15215, -24145, -30224, -32724, -31349, -26254, -18041,  -7681,   3596,  14450,  23587,  29916,  32679,
     31541,  26633,  18537,   8219,  -3088, -14028, -23290, -29759, -32653, -31619, -26777, -18707,  -8376,   2968,  13960,  23268,
     29764,  32657,  31594,  26696,  18555,   8155,  -3237, -14243, -23519, -29929, -32691, -31461, -26385, -18078,  -7556,   3896,
     14875,  24033,  30243,  32737,  31203,  25828,  17266,   6570,  -4938, -15844, -24793, -30678, -32766, -30793, -24998, -16098,
     -5193,   6361,  17128,  25767,  31196,  32734,  30186,  23864,  14553,   3418,  -8148, -18700, -26908, -31740, -32584, -29329,
    -22380, -12608,  -1241,  10284,  20518,  28159,  32242,  32244,  28159,  20501,  10239,  -1329, -12733, -22521, -29445, -32618,
    -31632, -26608, -18182,  -7427,   4280,  15446,  24636,  30669,  32766,  30655,  24601,  15376,   4171,  -7579, -18355, -26764,
    -31720, -32575, -29214, -22067, -12057,   -481,  11164,  21362,  28787,  32468,  31920,  27211,  18949,   8211,  -3605, -14954,
    -24350, -30560, -32766, -30674, -24553, -15203,  -3850,   8013,  18827,  27162,  31916,  32459,  28712,  21166,  10815,   -970,
    -12635, -22624, -29616, -32673, -31387, -25922, -17002,  -5811,   6157,  17309,  26151,  31501,  32636,  29401,  22222,  12060,
       274, -11552, -21829, -29174, -32591, -31617, -26376, -17573,  -6392,   5658,  16945,  25943,  31428,  32652,  29444,  22234,
     11998,    127, -11767, -22061, -29351, -32637, -31465, -25991, -16958,  -5601,   6528,  17767,  26571,  31730,  32528,  28851,
     21202,  10628,  -1416, -13269, -23293, -30102, -32750, -30865, -24702, -15113,  -3423,   8745,  19703,  27927,  32265,  32111,
     27480,  19014,   7888,  -4341, -15968, -25368, -31215, -32691, -29582, -22319, -11917,    163,  12225,  22571,  29739,  32716,
     31077,  25046,  15475,   3710,  -8585, -19666, -27966, -32299, -32047, -27241, -18559,  -7231,   5129,  16764,  26012,  31548,
     32580,  28953,  21181,  10373,  -1924, -13951, -23980, -30568, -32764, -30247, -23374, -13132,   -991,  11297,  21956,  29449,
     32683,  31188,  25175,  15511,   3591,  -8853, -20016, -28272, -32418, -31843, -26628, -17527,  -5866,   6659,  18212,  27106,
     32036,  32274,  27779,  19207,   7812,  -4736, -16591, -26010, -31601, -32536, -28672, -20574,  -9433,   3105,  15189,  25029,
     31163,  32678,  29345,  21653,  10739,  -1775, -14031, -24199, -30765, -32744, -29835, -22467, -11738,    752,  13132,  23549,
     30440,  32765,  30169,  23038,  12440,    -34, -12508, -23101, -30213, -32766, -30369, -23381, -12851,   -374,  12167,  22866,
     30099,  32763,  30449,  23505,  12977,    470, -12111, -22851, -30106, -32764, -30414, -23410, -12820,   -262,  12341,  23055,
     30233,  32766,  30262,  23099,  12376,   -257, -12856, -23476, -30472, -32760, -29981, -22560, -11641,   1084,  13647,  24098,
     30806,  32727,  29556,  21781,  10608,  -2219, -14708, -24903, -31210, -32638, -28958, -20739,  -9269,   3658,  16019,  25866,
     31650,  32457,  28155,  19415,   7612,  -5397, -17558, -26949, -32082, -32141, -27109, -17781,  -5628,   7420,  19295,  28108,
     32454,  31636,  25779,  15812,   3317,  -9709, -21190, -29287, -32703, -30885, -24119, -13482,   -682,  12231,  23188,  30419,
     32758,  29824,  22083,  10778,  -2266, -14953, -25225, -31424, -32539, -28386, -19630,  -7690,   5502,  17807,  27221,  32212,
     31962,  26507,  16730,   4221,  -8982, -20720, -29078, -32682, -30939, -24126, -13357,   -394,  12638,  23599,  30685,  32725,
     29380,  21193,   9511,  -3745, -16387, -26326, -31916, -32229, -27206, -17675,  -5208,   8123,  20112,  28766,  32638,  31082,
     24350,  13561,    506, -12637, -23677, -30762, -32707, -29180, -20767,  -8871,   4515,  17150,  26908,  32149,  31986,  26442,
     16442,   3671,  -9725, -21485, -29620, -32754, -30349, -22811, -11405,   1935,  14953,  25437,  31606,  32407,  27697,  18271,
      5735,  -7785, -19982, -28773, -32655, -30957, -23968, -12871,    429,  13662,  24556,  31241,  32564,  28293,  19155,   6718,
     -6879, -19296, -28390, -32588, -31162, -24350, -13325,      5,  13342,  24372,  31179,  32579,  28323,  19145,   6635,  -7032,
    -19482, -28539, -32625, -31021, -24000, -12784,    668,  14013,  24905,  31438,  32463,  27794,  18243,   5485,  -8241, -20523,
    -29195, -32728, -30494, -22878, -11224,   2418,  15636,  26092,  31936,  32124,  26618,  16391,   3251, -10472, -22336, -30234,
    -32752, -29440, -20879,  -8592,   5231,  18125,  27785,  32479,  31363,  24631,  13482,    -84, -13643, -24757, -31425, -32445,
    -27629, -17836,  -4827,   9054,  21309,  29721,  32766,  29888,  21604,   9406,  -4500, -17593, -27501, -32421, -31453, -24768,
    -13578,     88,  13741,  24896,  31514,  32383,  27339,  17297,   4090,  -9871, -22028, -30153, -32752, -29344, -20546,  -7971,
      6071,  19004,  28441,  32642,  30828,  23326,  11516,  -2423, -15922, -26477, -32137, -31847, -25655, -14703,  -1019,  12858,
     24350,  31319,  32462,  27562,  17525,   4215,  -9881, -22140, -30266, -32737, -29085, -19987,  -7143,   7043,  19913,  29050,
     32735,  30265,  22099,   9776,  -4375, -17677, -27621, -32328, -30920, -23681, -11997,   1902,  15371,  25863,  31398,  30956,
     24654,  13728,    287, -13096, -23879, -30028, -30418, -25025, -14938,  -2124,  10941,  21765,  28308,  29372,  24827,  15616,
      3566,  -8978, -19608, -26321, -27893, -24108, -15783,  -4598,   7252,  17478,  24150,  26058,  22932,  15479,   5229,  -5785,
    -15426, -21867, -23948, -21373, -14759,  -5487,   4582,  13485,  19533,  21638,  19512,  13692,   5418,  -3630, -11677, -17195,
    -19202, -17428, -12353,  -5078,   2898,  10006,  14896,  16704,  15204,  10823,   4537,  -2352,  -8472, -12667, -14208, -12918,
     -9184,  -3856,   1945,   7064,  10535,  11769,  10645,   7516,   3110,  -1636,  -5772,  -8525,  -9440,  -8458,  -5895,  -2360,
      1382,   4586,   6657,   7273,   6422,   4388,   1665,  -1152,  -3501,  -4957,  -5313,  -4598,  -3054,  -1068,    922,   2522,
      3454,   3606,   3035,   1939,    600,   -683,  -1661,  -2178,  -2193,  -1775,  -1073,   -275,    440,    942,   1163,   1109,
       846,    470,     88,   -218,   -398,   -445,   -384,   -260,   -123,    -11,     53,     73,     60,     34,     12,      1
]

# I2S data as NumPy array (int16)
i2s_data_numpy = np.array(i2s_data_list, dtype=np.int16)

# Array properties
SAMPLE_COUNT = 2400
SAMPLE_RATE = 48000
DURATION_MS = 50.00
DATA_TYPE = 'int16'  # signed 16-bit
VALUE_RANGE = (-32768, 32767)  # signed 16-bit range
CAPTURE_TIMESTAMP = '20250715_163941'

# Data statistics
MIN_VALUE = -32766
MAX_VALUE = 32766
MEAN_VALUE = -1.86
RMS_VALUE = 22283.38

# Usage example:
# from i2s_data_20250715_163941 import i2s_data_numpy, SAMPLE_RATE
# import soundfile as sf
# sf.write("reconstructed.wav", i2s_data_numpy, SAMPLE_RATE)
