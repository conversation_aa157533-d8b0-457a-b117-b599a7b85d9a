******************************************************************************
                  TI ARM Linker PC v20.2.4                     
******************************************************************************
>> Linked Mon Feb 22 19:51:11 2021

OUTPUT FILE NAME:   <SoundPositioning.out>
ENTRY POINT SYMBOL: "resetISR"  address: 0000492d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00058000  00004f6a  00053096  R  X
  GPRAM                 11000000   00002000  00000000  00002000  RW X
  SRAM                  20000000   00014000  00004ee2  0000f11e  RW X
  LOG_DATA              90000000   00040000  00000000  00040000  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004e2c   00004e2c    r-x
  00000000    00000000    000000d8   000000d8    r-- .intvecs
  000000d8    000000d8    00004aba   00004aba    r-x .text
  00004b94    00004b94    00000298   00000298    r-- .const
00004e2c    00004e2c    00000008   00000008    rw-
  00004e2c    00004e2c    00000008   00000008    rw- .args
00004e38    00004e38    000000e0   000000e0    r--
  00004e38    00004e38    000000e0   000000e0    r-- .cinit
00057fa8    00057fa8    00000058   00000058    r--
  00057fa8    00057fa8    00000058   00000058    r-- .ccfg
20000000    20000000    000009ea   00000000    rw-
  20000000    20000000    00000604   00000000    rw- .bss
  20000604    20000604    000003e6   00000000    rw- .data
20000a00    20000a00    000000d8   00000000    rw-
  20000a00    20000a00    000000d8   00000000    rw- .vtable_ram
20001870    20001870    00000010   00000000    rw-
  20001870    20001870    00000010   00000000    rw- .TI.bound:dmaADCPriControlTableEntry
20001a70    20001a70    00004010   00000000    rw-
  20001a70    20001a70    00000010   00000000    rw- .TI.bound:dmaADCAltControlTableEntry
  20001a80    20001a80    00004000   00000000    rw- .sysmem
20013c00    20013c00    00000400   00000000    rw-
  20013c00    20013c00    00000400   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000d8     
                  00000000    000000d8     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.intvecs)

.text      0    000000d8    00004aba     
                  000000d8    000001e0     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_init)
                  000002b8    00000198                      : PowerCC26X2.oem4f (.text:Power_sleep)
                  00000450    00000190                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf1)
                  000005e0    0000018c                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:runCalibrateFsm)
                  0000076c    00000174                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_open)
                  000008e0    00000174                      : PowerCC26X2.oem4f (.text:Power_init)
                  00000a54    00000164                      : UARTCC26XX.oem4f (.text:UARTCC26XX_open)
                  00000bb8    00000154                      : UARTCC26XX.oem4f (.text:UARTCC26XX_hwiIntFxn)
                  00000d0c    0000013a                      : UARTCC26XX.oem4f (.text:UARTCC26XX_read)
                  00000e46    00000002     driverlib.lib : interrupt.obj (.text:IntDefaultHandler)
                  00000e48    00000120     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureXOSCHF)
                  00000f68    00000114                      : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfg)
                  0000107c    00000110                      : GPIOCC26XX.oem4f (.text:GPIO_setConfig)
                  0000118c    00000100                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convert)
                  0000128c    00000100     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:aligned_alloc)
                  0000138c    000000f0     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlStandby)
                  0000147c    000000e4     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free)
                  00001560    000000e0     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_standbyPolicy)
                  00001640    000000e0     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_write)
                  00001720    000000d8     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_construct)
                  000017f8    000000c4     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_init)
                  000018bc    000000c0                      : PowerCC26X2.oem4f (.text:Power_releaseDependency)
                  0000197c    000000c0                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initIO)
                  00001a3c    000000bc     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_construct)
                  00001af8    000000b8     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_setDependency)
                  00001bb0    000000b8                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initHw)
                  00001c68    000000b4     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_startup)
                  00001d1c    00000004     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_getResolution)
                  00001d20    000000b4     SoundTX.obj (.text:Timer2AInterruptHandler)
                  00001dd4    000000ac     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_workFuncDynamic)
                  00001e80    000000ac     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_open)
                  00001f2c    000000ac     driverlib.lib : setup.obj (.text:TrimAfterColdResetWakeupFromShutDown)
                  00001fd8    000000a0     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_pend)
                  00002078    0000009c     SoundTX.obj (.text:InitTimer2)
                  00002114    0000009c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_dispatch)
                  000021b0    0000009c     rtsv7M4_T_le_v4SPD16_eabi.lib : memcpy_t2.asm.obj (.text)
                  0000224c    00000098     driverlib.lib : setup.obj (.text:NOROM_SetupTrimDevice)
                  000022e4    00000098     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeTxFifoFlush)
                  0000237c    00000096     rtsv7M4_T_le_v4SPD16_eabi.lib : ull_div_t2.asm.obj (.text)
                  00002412    00000092     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_writeCancel)
                  000024a4    00000088                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_open)
                  0000252c    00000088     driverlib.lib : osc.obj (.text:NOROM_OSCHF_AttemptToSwitchToXosc)
                  000025b4    00000088     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf2)
                  0000263c    00000088                      : PowerCC26X2.oem4f (.text:disableLFClockQualifiers)
                  000026c4    00000088                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:updateSubSecInc)
                  0000274c    00000004     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:CPUwfi)
                  00002750    00000084     ti_drivers_config.obj (.text:Board_sendExtFlashByte)
                  000027d4    00000084     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_close)
                  00002858    00000080                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_hwiFxn)
                  000028d8    00000080     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_start)
                  00002958    00000080                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_construct)
                  000029d8    00000080     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlSetRechargeBeforePowerDown)
                  00002a58    0000007a     rtsv7M4_T_le_v4SPD16_eabi.lib : memset_t2.asm.obj (.text)
                  00002ad2    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_destruct)
                  00002ad4    00000078                     : ClockPTimer_nortos.oem4f (.text:ClockP_walkQueueDynamic)
                  00002b4c    00000074     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_resetHw)
                  00002bc0    00000074                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_auxISR)
                  00002c34    00000070                      : PINCC26XX.oem4f (.text:PIN_add)
                  00002ca4    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_isActive)
                  00002ca8    00000070     mainNew.obj (.text:main)
                  00002d18    00000070     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:split)
                  00002d88    0000006c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipType)
                  00002df4    00000068                   : osc.obj (.text:NOROM_OSCHF_SwitchToRcOscTurnOffXosc)
                  00002e5c    00000068     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_post)
                  00002ec4    00000068                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_start)
                  00002f2c    00000068     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_lzss.c.obj (.text:decompress:lzss:__TI_decompress_lzss)
                  00002f94    00000064     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_control)
                  00002ff8    00000062                      : UARTCC26XX.oem4f (.text:UARTCC26XX_swiIntFxn)
                  0000305a    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.text:_nop)
                  0000305c    00000060     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_write)
                  000030bc    0000005c                      : PINCC26XX.oem4f (.text:PIN_remove)
                  00003118    0000005c                      : UARTCC26XX.oem4f (.text:startTxFifoEmptyClk)
                  00003174    00000058                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_loadDMAControlTableEntry)
                  000031cc    00000058                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_stop)
                  00003224    00000058                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_initiateCalibration)
                  0000327c    00000054                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_adjustRawValues)
                  000032d0    00000054     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOff)
                  00003324    00000050     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_construct)
                  00003374    0000004e     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_close)
                  000033c2    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:clkFxn)
                  000033c4    0000004c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_cleanADC)
                  00003410    0000004c     ti_drivers_config.obj (.text:Board_wakeUpExtFlash)
                  0000345c    0000004c     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_initHw)
                  000034a8    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_start)
                  000034f4    0000004c     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOn)
                  00003540    0000004c     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readCancel)
                  0000358c    0000004c                      : UDMACC26XX.oem4f (.text:UDMACC26XX_open)
                  000035d8    0000004a                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_control)
                  00003622    00000002                      : PowerCC26X2.oem4f (.text:emptyClockFunc)
                  00003624    00000048                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertAdjustedToMicroVolts)
                  0000366c    00000048     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_construct)
                  000036b4    00000048     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputEnable)
                  000036fc    00000048                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_RCOSC_clockFunc)
                  00003744    00000048                      : PowerCC26X2.oem4f (.text:switchXOSCHF)
                  0000378c    00000048                      : UARTCC26XX.oem4f (.text:writeFinishedDoCallback)
                  000037d4    00000046                      : RingBuf.oem4f (.text:RingBuf_put)
                  0000381a    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:faultISR)
                  0000381c    00000044     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_init)
                  00003860    00000044     mainNew.obj (.text:InitUart)
                  000038a4    00000044     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetHwRevision)
                  000038e8    00000044     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_swi)
                  0000392c    00000044     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_post)
                  00003970    00000044                     : SwiP_nortos.oem4f (.text:SwiP_restore)
                  000039b4    00000044     drivers_cc13x2.a : UART.oem4f (.text:UART_init)
                  000039f8    00000044     rtsv7M4_T_le_v4SPD16_eabi.lib : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit:__TI_auto_init_nobinit_nopinit)
                  00003a3c    00000042     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_swiFxn)
                  00003a7e    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:intDefaultHandler)
                  00003a80    00000040     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertCancel)
                  00003ac0    00000040     ti_drivers_config.obj (.text:Board_shutDownExtFlash)
                  00003b00    00000040     driverlib.lib : osc.obj (.text:NOROM_OSC_HPOSCRelativeFrequencyOffsetGet)
                  00003b40    00000040                   : timer.obj (.text:NOROM_TimerIntRegister)
                  00003b80    00000040     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_get)
                  00003bc0    00000040                      : UART.oem4f (.text:UART_open)
                  00003c00    0000003c                      : PINCC26XX.oem4f (.text:PIN_setOutputValue)
                  00003c3c    00000038                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_configDMA)
                  00003c74    00000038                      : PINCC26XX.oem4f (.text:PIN_setConfig)
                  00003cac    00000038     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCurrentTick)
                  00003ce4    00000038                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_initDevice)
                  00003d1c    00000038     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:notify)
                  00003d54    00000038                      : UARTCC26XX.oem4f (.text:readData)
                  00003d8c    00000038                      : UARTCC26XX.oem4f (.text:writeData)
                  00003dc4    00000036     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setNextTick)
                  00003dfa    00000036     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_close)
                  00003e30    00000034     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicksUntilInterrupt)
                  00003e64    00000034     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XXSetLoadMatch)
                  00003e98    00000034                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_configureDebugStall)
                  00003ecc    00000034     driverlib.lib : aux_sysif.obj (.text:NOROM_AUXSYSIFOpModeChange)
                  00003f00    00000034                   : interrupt.obj (.text:NOROM_IntRegister)
                  00003f34    00000034                   : sys_ctrl.obj (.text:NOROM_SysCtrlIdle)
                  00003f68    00000034     SoundTX.obj (.text:SoundTransmit)
                  00003f9c    00000034     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_destruct)
                  00003fd0    00000034     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:readData2RingBuf)
                  00004004    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_hwiIntFxn)
                  00004034    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_setCallback)
                  00004064    00000030                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_close)
                  00004094    00000030                      : PINCC26XX.oem4f (.text:PINCC26XX_setMux)
                  000040c4    00000030                      : PowerCC26X2.oem4f (.text:Power_releaseConstraint)
                  000040f4    00000030                      : PowerCC26X2.oem4f (.text:Power_setConstraint)
                  00004124    00000030     driverlib.lib : timer.obj (.text:TimerIntNumberGet)
                  00004154    00000030     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.text:_c_int00:_c_int00)
                  00004184    0000002c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_acquireADCSemaphore)
                  000041b0    0000002c                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_releaseADCSemaphore)
                  000041dc    0000002c     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setThreshold)
                  00004208    0000002a     SoundTX.obj (.text:TimerLoadSet)
                  00004232    00000006     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_stop)
                  00004238    0000002a     SoundTX.obj (.text:TimerMatchSet)
                  00004262    00000006     driverlib.lib : cpu.obj (.text:NOROM_CPUdelay)
                  00004268    0000002a     SoundTX.obj (.text:TimerPrescaleMatchSet)
                  00004292    00000006     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_init)
                  00004298    0000002a     SoundTX.obj (.text:TimerPrescaleSet)
                  000042c2    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readPolling)
                  000042c8    00000028     ti_drivers_config.obj (.text:Board_init)
                  000042f0    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_registerNotify)
                  00004318    00000028     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_insert)
                  00004340    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:oscillatorISR)
                  00004368    00000026                      : List.oem4f (.text:List_put)
                  0000438e    00000026                      : List.oem4f (.text:List_remove)
                  000043b4    00000024     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicks)
                  000043d8    00000024                     : ClockPTimer_nortos.oem4f (.text:ClockP_scheduleNextTick)
                  000043fc    00000024                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_dispatch)
                  00004420    00000024     driverlib.lib : osc.obj (.text:NOROM_OSCHF_TurnOnXosc)
                  00004444    00000024     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getMaxTicks)
                  00004468    00000024     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:getTdcSemaphore)
                  0000448c    00000020     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_add)
                  000044ac    00000020     driverlib.lib : aux_adc.obj (.text:NOROM_AUXADCEnableSync)
                  000044cc    00000020     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfgMux)
                  000044ec    00000020                      : PowerCC26X2.oem4f (.text:Power_unregisterNotify)
                  0000450c    00000020     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_put)
                  0000452c    00000020                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_dynamicStub)
                  0000454c    00000020     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:threadSafeStdbyDisRelease)
                  0000456c    00000020                      : UARTCC26XX.oem4f (.text:threadSafeStdbyDisSet)
                  0000458c    0000001e                      : PINCC26XX.oem4f (.text:PIN_close)
                  000045aa    0000001e                      : PowerCC26X2.oem4f (.text:Power_getTransitionLatency)
                  000045c8    0000001e     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_constructBinary)
                  000045e6    0000001e                     : SwiP_nortos.oem4f (.text:SwiP_or)
                  00004604    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_setTimeout)
                  00004608    0000001e     SoundTX.obj (.text:TimerDisable)
                  00004626    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:nmiISR)
                  00004628    0000001e     SoundTX.obj (.text:TimerEnable)
                  00004646    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : div0.asm.obj (.text)
                  00004648    0000001c     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_destruct)
                  00004664    0000001c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipFamily)
                  00004680    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_hwi)
                  0000469c    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_enable)
                  000046a0    0000001c     SoundTX.obj (.text:PRCMLoadGet)
                  000046bc    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_disable)
                  000046d8    0000001c     SoundTX.obj (.text:TimerMatchGet)
                  000046f4    0000001c     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_initHw)
                  00004710    0000001c     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_remove)
                  0000472c    0000001a     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:PowerCC26XX_calibrate)
                  00004746    0000001a     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_get)
                  00004760    0000001a                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_createBinary)
                  0000477a    00000018                     : ClockPTimer_nortos.oem4f (.text:ClockP_destruct)
                  00004792    00000002     --HOLE-- [fill = 0]
                  00004794    00000018                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_inISR)
                  000047ac    00000018     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetPackageType)
                  000047c4    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_idleFunc)
                  000047dc    00000004     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_head)
                  000047e0    00000018     SoundTX.obj (.text:TimerIntEnable)
                  000047f8    00000018     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_startup)
                  00004810    00000018     drivers_cc13x2.a : UART.oem4f (.text:UART_Params_init)
                  00004828    00000018     rtsv7M4_T_le_v4SPD16_eabi.lib : args_main.c.obj (.text:_args_main)
                  00004840    00000018                                   : ll_mul_t2.asm.obj (.text)
                  00004858    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:switchToTCXO)
                  00004870    00000016     driverlib.lib : chipinfo.obj (.text:NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated)
                  00004886    00000016     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_create)
                  0000489c    00000016                     : SwiP_nortos.oem4f (.text:maxbit)
                  000048b2    00000014     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_freqToCounts)
                  000048c6    00000002     --HOLE-- [fill = 0]
                  000048c8    00000014     driverlib.lib : interrupt.obj (.text:NOROM_IntUnregister)
                  000048dc    00000014     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_calibrate)
                  000048f0    00000014                      : PowerCC26X2.oem4f (.text:Power_disablePolicy)
                  00004904    00000014     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_handleHwi)
                  00004918    00000014                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCount64)
                  0000492c    00000014                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:resetISR)
                  00004940    00000012     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_Params_init)
                  00004952    00000012     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_Params_init)
                  00004964    00000012     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_construct)
                  00004976    00000002     --HOLE-- [fill = 0]
                  00004978    00000012     SoundTX.obj (.text:TimerIntClear)
                  0000498a    00000002     --HOLE-- [fill = 0]
                  0000498c    00000010     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_Params_init)
                  0000499c    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_getCpuFreq)
                  000049ac    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_clearInterrupt)
                  000049bc    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_disable)
                  000049cc    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_post)
                  000049dc    00000010     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_getPinCount)
                  000049ec    00000010     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerDisable)
                  000049fc    00000010     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_enablePolicy)
                  00004a0c    00000010     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_Params_init)
                  00004a1c    00000010                     : SwiP_nortos.oem4f (.text:SwiP_Params_init)
                  00004a2c    00000010                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_Params_init)
                  00004a3c    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:uartPostNotify)
                  00004a4c    0000000e     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_empty)
                  00004a5a    0000000e                     : QueueP_nortos.oem4f (.text:QueueP_remove)
                  00004a68    0000000e     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_none.c.obj (.text:decompress:none:__TI_decompress_none)
                  00004a76    00000002     --HOLE-- [fill = 0]
                  00004a78    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_doTick)
                  00004a84    0000000c                     : ClockPTimer_nortos.oem4f (.text:ClockP_getSystemTickPeriod)
                  00004a90    0000000c     driverlib.lib : cpu.obj (.text:NOROM_CPUcpsid)
                  00004a9c    0000000c                   : cpu.obj (.text:NOROM_CPUcpsie)
                  00004aa8    0000000c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_registerIntCb)
                  00004ab4    00000004     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_next)
                  00004ab8    0000000c     SoundTX.obj (.text:PRCMLoadSet)
                  00004ac4    0000000c     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerRestore)
                  00004ad0    0000000c     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_getConstraintMask)
                  00004adc    0000000c                      : PowerCC26X2.oem4f (.text:Power_getDependencyCount)
                  00004ae8    0000000c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_getTrigger)
                  00004af4    0000000c                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getFreq)
                  00004b00    0000000c     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init)
                  00004b0c    0000000a     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_convertCancel)
                  00004b16    0000000a                      : UDMACC26XX.oem4f (.text:UDMACC26XX_hwiIntFxn)
                  00004b20    0000000a                      : UARTCC26XX.oem4f (.text:readSemCallback)
                  00004b2a    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_conversionCallback)
                  00004b32    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_init)
                  00004b3a    00000008                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_setLoadValue)
                  00004b42    00000008     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_restore)
                  00004b4a    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_writePolling)
                  00004b50    00000008     ADC.obj (.text:InitADC)
                  00004b58    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_init)
                  00004b60    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:malloc)
                  00004b68    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeSemCallback)
                  00004b70    00000006                      : PowerCC26X2.oem4f (.text:delayUs)
                  00004b76    00000004     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_delete)
                  00004b7a    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : pre_init.c.obj (.text:_system_pre_init)
                  00004b7e    00000004                                   : exit.c.obj (.text:abort:abort)
                  00004b82    00000004     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureRFCoreClocks)
                  00004b86    00000004                      : PowerCC26X2.oem4f (.text:nopResourceHandler)
                  00004b8a    00000006     --HOLE-- [fill = 0]
                  00004b90    00000002     ti_drivers_config.obj (.text:Board_initHook)

.const     0    00004b94    00000298     
                  00004b94    00000054     drivers_cc13x2.a : PowerCC26X2.oem4f (.const:resourceDB)
                  00004be8    00000028     ti_drivers_config.obj (.const:BoardGpioInitTable)
                  00004c10    00000028     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:UARTCC26XX_fxnTable)
                  00004c38    00000028     ti_drivers_config.obj (.const:uartCC26XXHWAttrs)
                  00004c60    00000024     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.const:ADCBufCC26X2_fxnTable)
                  00004c84    00000024                      : UART.oem4f (.const:UART_defaultParams)
                  00004ca8    0000001c                      : GPTimerCC26XX.oem4f (.const:GPT_LUT)
                  00004cc4    00000018     ti_drivers_config.obj (.const:GPTimerCC26XX_config)
                  00004cdc    00000018     ti_drivers_config.obj (.const:gptimerCC26XXHWAttrs)
                  00004cf4    00000018     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:interruptType)
                  00004d0c    00000014     ti_drivers_config.obj (.const:$P$T1$2)
                  00004d20    00000014     ti_drivers_config.obj (.const:GPIOCC26XX_config)
                  00004d34    00000014     ti_drivers_config.obj (.const:PowerCC26X2_config)
                  00004d48    00000014     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:parityType)
                  00004d5c    00000010     nortos_cc13x2.a : SwiP_nortos.oem4f (.const:SwiP_defaultParams)
                  00004d6c    00000010     ti_drivers_config.obj (.const:adcbufCC26XXHWAttrs)
                  00004d7c    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:dataLength)
                  00004d8c    00000010                      : GPIOCC26XX.oem4f (.const:outPinTypes)
                  00004d9c    0000000e     ti_drivers_config.obj (.const)
                  00004daa    00000002     --HOLE-- [fill = 0]
                  00004dac    0000000c     ti_drivers_config.obj (.const:ADCBuf_config)
                  00004db8    0000000c     ti_drivers_config.obj (.const:UART_config)
                  00004dc4    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:inPinTypes)
                  00004dd0    0000000c                      : GPIOCC26XX.oem4f (.const:outPinStrengths)
                  00004ddc    00000008     driverlib.lib : aux_sysif.obj (.const:$O1$$)
                  00004de4    00000008     ti_drivers_config.obj (.const:$P$T0$1)
                  00004dec    00000008     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.const:GPT_DefaultParams)
                  00004df4    00000008     ti_drivers_config.obj (.const:PINCC26XX_hwAttrs)
                  00004dfc    00000008     ti_drivers_config.obj (.const:UDMACC26XX_config)
                  00004e04    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:stopBits)
                  00004e0c    00000008     ti_drivers_config.obj (.const:udmaCC26XXHWAttrs)
                  00004e14    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:.string:rxFifoBytes)
                  00004e1a    00000006                      : UARTCC26XX.oem4f (.const:.string:rxFifoThreshold)
                  00004e20    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoBytes)
                  00004e26    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoThreshold)

.binit     0    00000000    00000000     

.cinit     0    00004e38    000000e0     
                  00004e38    00000088     (.cinit..data.load) [load image, compression = lzss]
                  00004ec0    0000000c     (__TI_handler_table)
                  00004ecc    00000004     --HOLE-- [fill = 0]
                  00004ed0    00000008     (.cinit..TI.bound:dmaADCAltControlTableEntry.load) [load image, compression = zero_init]
                  00004ed8    00000008     (.cinit..TI.bound:dmaADCPriControlTableEntry.load) [load image, compression = zero_init]
                  00004ee0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004ee8    00000008     (.cinit..vtable_ram.load) [load image, compression = zero_init]
                  00004ef0    00000028     (__TI_cinit_table)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.ccfg      0    00057fa8    00000058     
                  00057fa8    00000058     ti_devices_config.obj (.ccfg:retain)

.bss       0    20000000    00000604     UNINITIALIZED
                  20000000    000001b4     (.common:uartCC26XXObjects)
                  200001b4    000000dc     (.common:adcbufCC26XXbjects)
                  20000290    000000a0     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:ClockP_module)
                  20000330    00000098     (.common:gptimerCC26XXObjects)
                  200003c8    0000007c     (.common:pinHandleTable)
                  20000444    00000050     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinSemaphore)
                  20000494    00000034                      : PINCC26XX.oem4f (.bss:pinSwi)
                  200004c8    00000020     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_readyList)
                  200004e8    00000020     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioCallbackInfo)
                  20000508    00000020                      : PINCC26XX.oem4f (.bss:pinGpioConfigTable)
                  20000528    00000020     ti_drivers_config.obj (.bss:uartCC26XXRingBuffer0)
                  20000548    00000020     (.common:udmaCC26XXObject)
                  20000568    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_hwiStruct)
                  20000584    0000001c                     : TimerPCC26XX_nortos.oem4f (.bss:TimerP_hwiStruct)
                  200005a0    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinHwi)
                  200005bc    0000000d     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss)
                  200005c9    00000001     (.common:driverlib_release_0_59848)
                  200005ca    00000002     --HOLE--
                  200005cc    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioPinState)
                  200005d8    00000008                      : GPIOCC26XX.oem4f (.bss)
                  200005e0    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:timerFreq)
                  200005e8    00000004     drivers_cc13x2.a : PINCC26XX.oem4f (.bss)
                  200005ec    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss)
                  200005f0    00000004                     : PowerCC26X2_nortos.oem4f (.bss)
                  200005f4    00000004                     : TimerPCC26XX_nortos.oem4f (.bss)
                  200005f8    00000004     (.common:i)
                  200005fc    00000004     (.common:j)
                  20000600    00000004     (.common:uart)

.vtable_ram 
*          0    20000a00    000000d8     UNINITIALIZED
                  20000a00    000000d8     driverlib.lib : interrupt.obj (.vtable_ram)

.data      0    20000604    000003e6     UNINITIALIZED
                  20000604    00000170     drivers_cc13x2.a : PowerCC26X2.oem4f (.data:PowerCC26X2_module)
                  20000774    000000d8     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data:HwiP_dispatchTable)
                  2000084c    0000007c     SoundTX.obj (.data:ChirpDelay)
                  200008c8    0000007c     SoundTX.obj (.data:ChirpState)
                  20000944    00000024     driverlib.lib : osc.obj (.data:$O1$$)
                  20000968    00000010     ti_drivers_config.obj (.data:gpioCallbackFunctions)
                  20000978    00000010     ti_drivers_config.obj (.data:gpioPinConfigs)
                  20000988    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data:ClockP_defaultParams)
                  20000994    0000000c                     : TimerPCC26XX_nortos.oem4f (.data:TimerP_defaultParams)
                  200009a0    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.data:$O1$$)
                  200009a8    00000008                                   : memory.c.obj (.data:$O1$$)
                  200009b0    00000008     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data:SemaphoreP_defaultParams)
                  200009b8    00000008     SoundTX.obj (.data)
                  200009c0    00000008     drivers_cc13x2.a : PINCC26XX.oem4f (.data)
                  200009c8    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data)
                  200009d0    00000006     ti_drivers_config.obj (.data:adcBufCC26XXChannelLut0)
                  200009d6    00000001     drivers_cc13x2.a : ADCBuf.oem4f (.data)
                  200009d7    00000001                      : UART.oem4f (.data)
                  200009d8    00000005                      : GPIOCC26XX.oem4f (.data)
                  200009dd    00000003     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data)
                  200009e0    00000004     drivers_cc13x2.a : GPIOCC26XX.oem4f (.data:gpioPinTable)
                  200009e4    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data)
                  200009e8    00000001                     : SwiP_nortos.oem4f (.data)
                  200009e9    00000001                     : TimerPCC26XX_nortos.oem4f (.data)

.sysmem    0    20001a80    00004000     UNINITIALIZED
                  20001a80    00000010     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.sysmem)
                  20001a90    00003ff0     --HOLE--

.stack     0    20013c00    00000400     UNINITIALIZED
                  20013c00    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.stack)
                  20013c04    000003fc     --HOLE--

.log_data 
*          0    90000000    00000000     COPY SECTION

__llvm_prf_cnts 
*          0    20000000    00000000     UNINITIALIZED

.TI.bound:dmaADCPriControlTableEntry 
*          0    20001870    00000010     UNINITIALIZED
                  20001870    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCPriControlTableEntry)

.TI.bound:dmaADCAltControlTableEntry 
*          0    20001a70    00000010     UNINITIALIZED
                  20001a70    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCAltControlTableEntry)

.args      0    00004e2c    00000008     
                  00004e2c    00000008     --HOLE-- [fill = 0]

MODULE SUMMARY

       Module                             code    ro data   rw data
       ------                             ----    -------   -------
    .\
       SoundTX.obj                        726     0         256    
       mainNew.obj                        180     0         12     
       ADC.obj                            8       0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             914     0         268    
                                                                   
    .\syscfg\
       ti_drivers_config.obj              314     274       910    
       ti_devices_config.obj              0       88        0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             314     362       910    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos/lib/ccs/m4f/nortos_cc13x2.a
       ClockPTimer_nortos.oem4f           922     0         192    
       SwiP_nortos.oem4f                  724     16        74     
       TimerPCC26XX_nortos.oem4f          642     0         45     
       HwiPCC26XX_nortos.oem4f            294     0         220    
       SemaphoreP_nortos.oem4f            410     0         11     
       PowerCC26X2_nortos.oem4f           256     0         4      
       startup_cc13x2_cc26x2_ccs.oem4f    26      216       0      
       QueueP_nortos.oem4f                100     0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             3374    232       546    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/cc13x2_cc26x2/driverlib/bin/ccs/driverlib.lib
       sys_ctrl.obj                       420     0         0      
       osc.obj                            340     0         36     
       setup.obj                          324     0         0      
       interrupt.obj                      74      0         216    
       chipinfo.obj                       250     0         0      
       prcm.obj                           160     0         0      
       timer.obj                          112     0         0      
       aux_sysif.obj                      52      8         0      
       aux_adc.obj                        32      0         0      
       cpu.obj                            30      0         0      
       driverlib_release.obj              0       0         1      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1794    8         253    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/lib/ccs/m4f/drivers_cc13x2.a
       UARTCC26XX.oem4f                   2760    108       0      
       PowerCC26X2.oem4f                  2096    84        368    
       PINCC26XX.oem4f                    1554    0         328    
       ADCBufCC26X2.oem4f                 1542    36        32     
       PowerCC26X2_calibrateRCOSC.oem4f   1400    0         0      
       GPIOCC26XX.oem4f                   660     64        61     
       GPTimerCC26XX.oem4f                670     36        0      
       UART.oem4f                         156     36        1      
       UDMACC26XX.oem4f                   168     0         0      
       RingBuf.oem4f                      152     0         0      
       ADCBuf.oem4f                       78      0         1      
       List.oem4f                         76      0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             11312   364       791    
                                                                   
    C:\ti\ccs1011\ccs\tools\compiler\ti-cgt-arm_20.2.4.LTS\lib\rtsv7M4_T_le_v4SPD16_eabi.lib
       memory.c.obj                       672     0         8      
       memcpy_t2.asm.obj                  156     0         0      
       ull_div_t2.asm.obj                 150     0         0      
       memset_t2.asm.obj                  122     0         0      
       copy_decompress_lzss.c.obj         104     0         0      
       autoinit.c.obj                     68      0         0      
       boot_cortex_m.c.obj                48      0         0      
       args_main.c.obj                    24      0         0      
       ll_mul_t2.asm.obj                  24      0         0      
       copy_decompress_none.c.obj         14      0         0      
       copy_zero_init.c.obj               12      0         0      
       _lock.c.obj                        2       0         8      
       exit.c.obj                         4       0         0      
       pre_init.c.obj                     4       0         0      
       div0.asm.obj                       2       0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1406    0         16     
                                                                   
       Heap:                              0       0         16384  
       Stack:                             0       0         1024   
       Linker Generated:                  0       220       0      
    +--+----------------------------------+-------+---------+---------+
       Grand Total:                       19114   1186      20192  


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004ef0 records: 5, size/record: 8, table size: 40
	.data: load addr=00004e38, load size=00000088 bytes, run addr=20000604, run size=000003e6 bytes, compression=lzss
	.TI.bound:dmaADCAltControlTableEntry: load addr=00004ed0, load size=00000008 bytes, run addr=20001a70, run size=00000010 bytes, compression=zero_init
	.TI.bound:dmaADCPriControlTableEntry: load addr=00004ed8, load size=00000008 bytes, run addr=20001870, run size=00000010 bytes, compression=zero_init
	.bss: load addr=00004ee0, load size=00000008 bytes, run addr=20000000, run size=00000604 bytes, compression=zero_init
	.vtable_ram: load addr=00004ee8, load size=00000008 bytes, run addr=20000a00, run size=000000d8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004ec0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_lzss
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                                               
-------   ----                                                               
00004d9f  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004d9c  ADCBUF_CONST                                                       
00004d9d  ADCBUF_SOUND_CONST                                                 
00004d9e  ADCBUF_TEMPERATURE_CONST                                           
0000327d  ADCBufCC26X2_adjustRawValues                                       
00003375  ADCBufCC26X2_close                                                 
000035d9  ADCBufCC26X2_control                                               
0000118d  ADCBufCC26X2_convert                                               
00003625  ADCBufCC26X2_convertAdjustedToMicroVolts                           
00003a81  ADCBufCC26X2_convertCancel                                         
00004c60  ADCBufCC26X2_fxnTable                                              
00001d1d  ADCBufCC26X2_getResolution                                         
00004b33  ADCBufCC26X2_init                                                  
0000076d  ADCBufCC26X2_open                                                  
00004dac  ADCBuf_config                                                      
00004b0d  ADCBuf_convertCancel                                               
00004da0  ADCBuf_count                                                       
0000381d  ADCBuf_init                                                        
00004be8  BoardGpioInitTable                                                 
000042c9  Board_init                                                         
00004b91  Board_initHook                                                     
00002751  Board_sendExtFlashByte                                             
00003ac1  Board_shutDownExtFlash                                             
00003411  Board_wakeUpExtFlash                                               
00004b7f  C$$EXIT                                                            
00004da7  CONFIG_GPTIMER_0_CONST                                             
00004da8  CONFIG_GPTIMER_1_CONST                                             
2000084c  ChirpDelay                                                         
200009bc  ChirpIndex                                                         
200009b8  ChirpSize                                                          
200008c8  ChirpState                                                         
0000498d  ClockP_Params_init                                                 
0000448d  ClockP_add                                                         
0000366d  ClockP_construct                                                   
0000477b  ClockP_destruct                                                    
00004a79  ClockP_doTick                                                      
0000499d  ClockP_getCpuFreq                                                  
00004a85  ClockP_getSystemTickPeriod                                         
000043b5  ClockP_getTicks                                                    
00003e31  ClockP_getTicksUntilInterrupt                                      
00002ca5  ClockP_isActive                                                    
000043d9  ClockP_scheduleNextTick                                            
00004605  ClockP_setTimeout                                                  
000028d9  ClockP_start                                                       
00001c69  ClockP_startup                                                     
00004233  ClockP_stop                                                        
200009cc  ClockP_tickPeriod                                                  
00002ad5  ClockP_walkQueueDynamic                                            
00001dd5  ClockP_workFuncDynamic                                             
00004d20  GPIOCC26XX_config                                                  
00004005  GPIO_hwiIntFxn                                                     
000017f9  GPIO_init                                                          
00004035  GPIO_setCallback                                                   
0000107d  GPIO_setConfig                                                     
0000305d  GPIO_write                                                         
00004941  GPTimerCC26XX_Params_init                                          
00004065  GPTimerCC26XX_close                                                
00004cc4  GPTimerCC26XX_config                                               
00003e99  GPTimerCC26XX_configureDebugStall                                  
000024a5  GPTimerCC26XX_open                                                 
00004b3b  GPTimerCC26XX_setLoadValue                                         
000034a9  GPTimerCC26XX_start                                                
000031cd  GPTimerCC26XX_stop                                                 
00004da9  GPTimer_count                                                      
00004953  HwiP_Params_init                                                   
000049ad  HwiP_clearInterrupt                                                
00002959  HwiP_construct                                                     
00004649  HwiP_destruct                                                      
000049bd  HwiP_disable                                                       
0000469d  HwiP_enable                                                        
00004795  HwiP_inISR                                                         
000049cd  HwiP_post                                                          
00004b43  HwiP_restore                                                       
200009e4  HwiP_swiPIntNum                                                    
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
00004b51  InitADC                                                            
00002079  InitTimer2                                                         
00003861  InitUart                                                           
00004369  List_put                                                           
0000438f  List_remove                                                        
000044ad  NOROM_AUXADCEnableSync                                             
00003ecd  NOROM_AUXSYSIFOpModeChange                                         
00004a91  NOROM_CPUcpsid                                                     
00004a9d  NOROM_CPUcpsie                                                     
00004263  NOROM_CPUdelay                                                     
00004665  NOROM_ChipInfo_GetChipFamily                                       
00002d89  NOROM_ChipInfo_GetChipType                                         
000038a5  NOROM_ChipInfo_GetHwRevision                                       
000047ad  NOROM_ChipInfo_GetPackageType                                      
00003f01  NOROM_IntRegister                                                  
000048c9  NOROM_IntUnregister                                                
0000252d  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002df5  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
00004421  NOROM_OSCHF_TurnOnXosc                                             
00003b01  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
000032d1  NOROM_PRCMPowerDomainsAllOff                                       
000034f5  NOROM_PRCMPowerDomainsAllOn                                        
0000224d  NOROM_SetupTrimDevice                                              
00003f35  NOROM_SysCtrlIdle                                                  
000029d9  NOROM_SysCtrlSetRechargeBeforePowerDown                            
0000138d  NOROM_SysCtrlStandby                                               
00004871  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00003b41  NOROM_TimerIntRegister                                             
0000469d  NoRTOS_start                                                       
000049dd  PINCC26XX_getPinCount                                              
00004df4  PINCC26XX_hwAttrs                                                  
00004095  PINCC26XX_setMux                                                   
00004da3  PIN_DRIVE_SPEAKER_A_CONST                                          
00004da4  PIN_DRIVE_SPEAKER_B_CONST                                          
00004da1  PIN_TEST1_CONST                                                    
00004da2  PIN_TEST2_CONST                                                    
00002c35  PIN_add                                                            
0000458d  PIN_close                                                          
000000d9  PIN_init                                                           
00001e81  PIN_open                                                           
00004aa9  PIN_registerIntCb                                                  
000030bd  PIN_remove                                                         
00003c75  PIN_setConfig                                                      
000036b5  PIN_setOutputEnable                                                
00003c01  PIN_setOutputValue                                                 
000036fd  PowerCC26X2_RCOSC_clockFunc                                        
00002bc1  PowerCC26X2_auxISR                                                 
000048dd  PowerCC26X2_calibrate                                              
00004d34  PowerCC26X2_config                                                 
00003225  PowerCC26X2_initiateCalibration                                    
20000604  PowerCC26X2_module                                                 
0000472d  PowerCC26XX_calibrate                                              
000049ed  PowerCC26XX_schedulerDisable                                       
00004ac5  PowerCC26XX_schedulerRestore                                       
00001561  PowerCC26XX_standbyPolicy                                          
000048f1  Power_disablePolicy                                                
000049fd  Power_enablePolicy                                                 
00004ad1  Power_getConstraintMask                                            
00004add  Power_getDependencyCount                                           
000045ab  Power_getTransitionLatency                                         
000047c5  Power_idleFunc                                                     
000008e1  Power_init                                                         
000042f1  Power_registerNotify                                               
000040c5  Power_releaseConstraint                                            
000018bd  Power_releaseDependency                                            
000040f5  Power_setConstraint                                                
00001af9  Power_setDependency                                                
000002b9  Power_sleep                                                        
000044ed  Power_unregisterNotify                                             
00004a4d  QueueP_empty                                                       
00004747  QueueP_get                                                         
000047dd  QueueP_head                                                        
00004293  QueueP_init                                                        
00004ab5  QueueP_next                                                        
0000450d  QueueP_put                                                         
00004a5b  QueueP_remove                                                      
00004965  RingBuf_construct                                                  
00003b81  RingBuf_get                                                        
000037d5  RingBuf_put                                                        
00004a0d  SemaphoreP_Params_init                                             
00003325  SemaphoreP_construct                                               
000045c9  SemaphoreP_constructBinary                                         
00004887  SemaphoreP_create                                                  
00004761  SemaphoreP_createBinary                                            
200009b0  SemaphoreP_defaultParams                                           
00004b77  SemaphoreP_delete                                                  
00002ad3  SemaphoreP_destruct                                                
00001fd9  SemaphoreP_pend                                                    
0000392d  SemaphoreP_post                                                    
00003f69  SoundTransmit                                                      
00004a1d  SwiP_Params_init                                                   
00001721  SwiP_construct                                                     
00003f9d  SwiP_destruct                                                      
000046bd  SwiP_disable                                                       
00002115  SwiP_dispatch                                                      
00004ae9  SwiP_getTrigger                                                    
000045e7  SwiP_or                                                            
00002e5d  SwiP_post                                                          
00003971  SwiP_restore                                                       
00001d21  Timer2AInterruptHandler                                            
00004a2d  TimerP_Params_init                                                 
00001a3d  TimerP_construct                                                   
0000452d  TimerP_dynamicStub                                                 
00004919  TimerP_getCount64                                                  
00003cad  TimerP_getCurrentTick                                              
00004af5  TimerP_getFreq                                                     
00004445  TimerP_getMaxTicks                                                 
00003ce5  TimerP_initDevice                                                  
00003dc5  TimerP_setNextTick                                                 
000041dd  TimerP_setThreshold                                                
00002ec5  TimerP_start                                                       
000047f9  TimerP_startup                                                     
000027d5  UARTCC26XX_close                                                   
00002f95  UARTCC26XX_control                                                 
00004c10  UARTCC26XX_fxnTable                                                
00000bb9  UARTCC26XX_hwiIntFxn                                               
00004b59  UARTCC26XX_init                                                    
00000a55  UARTCC26XX_open                                                    
00000d0d  UARTCC26XX_read                                                    
00003541  UARTCC26XX_readCancel                                              
000042c3  UARTCC26XX_readPolling                                             
00002ff9  UARTCC26XX_swiIntFxn                                               
00001641  UARTCC26XX_write                                                   
00002413  UARTCC26XX_writeCancel                                             
00004b4b  UARTCC26XX_writePolling                                            
00004da5  UART_0_CONST                                                       
00004811  UART_Params_init                                                   
00004db8  UART_config                                                        
00004da6  UART_count                                                         
00004c84  UART_defaultParams                                                 
000039b5  UART_init                                                          
00003bc1  UART_open                                                          
00003dfb  UDMACC26XX_close                                                   
00004dfc  UDMACC26XX_config                                                  
00004b17  UDMACC26XX_hwiIntFxn                                               
0000358d  UDMACC26XX_open                                                    
20014000  __STACK_END                                                        
00000400  __STACK_SIZE                                                       
00004000  __SYSMEM_SIZE                                                      
00004ef0  __TI_CINIT_Base                                                    
00004f18  __TI_CINIT_Limit                                                   
00004ec0  __TI_Handler_Table_Base                                            
00004ecc  __TI_Handler_Table_Limit                                           
000039f9  __TI_auto_init_nobinit_nopinit                                     
00002f2d  __TI_decompress_lzss                                               
00004a69  __TI_decompress_none                                               
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
00000000  __TI_static_base__                                                 
00004b01  __TI_zero_init                                                     
00004647  __aeabi_idiv0                                                      
00004647  __aeabi_ldiv0                                                      
00004841  __aeabi_lmul                                                       
00002a59  __aeabi_memclr                                                     
00002a59  __aeabi_memclr4                                                    
00002a59  __aeabi_memclr8                                                    
000021b1  __aeabi_memcpy                                                     
000021b1  __aeabi_memcpy4                                                    
000021b1  __aeabi_memcpy8                                                    
00002a5b  __aeabi_memset                                                     
00002a5b  __aeabi_memset4                                                    
00002a5b  __aeabi_memset8                                                    
0000237d  __aeabi_uldivmod                                                   
ffffffff  __binit__                                                          
00004e2c  __c_args__                                                         
00057fa8  __ccfg                                                             
UNDEFED   __mpu_init                                                         
20013c00  __stack                                                            
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
00004829  _args_main                                                         
00004155  _c_int00                                                           
20000944  _hposcCoeffs                                                       
200009a0  _lock                                                              
0000305b  _nop                                                               
20001a80  _sys_memory                                                        
UNDEFED   _system_post_cinit                                                 
00004b7b  _system_pre_init                                                   
200009a4  _unlock                                                            
00004b7f  abort                                                              
200009d0  adcBufCC26XXChannelLut0                                            
00004d6c  adcbufCC26XXHWAttrs                                                
200001b4  adcbufCC26XXbjects                                                 
0000128d  aligned_alloc                                                      
ffffffff  binit                                                              
000033c3  clkFxn                                                             
200005c9  driverlib_release_0_59848                                          
0000147d  free                                                               
20000a00  g_pfnRAMVectors                                                    
00000000  g_pfnVectors                                                       
20000968  gpioCallbackFunctions                                              
20000978  gpioPinConfigs                                                     
00004cdc  gptimerCC26XXHWAttrs                                               
20000330  gptimerCC26XXObjects                                               
200005f8  i                                                                  
00004dc4  inPinTypes                                                         
200005fc  j                                                                  
00002ca9  main                                                               
00004b61  malloc                                                             
0000128d  memalign                                                           
000021b1  memcpy                                                             
00002a61  memset                                                             
00004dd0  outPinStrengths                                                    
00004d8c  outPinTypes                                                        
200003c8  pinHandleTable                                                     
200009c4  pinLowerBound                                                      
200009c0  pinUpperBound                                                      
0000492d  resetISR                                                           
00004b94  resourceDB                                                         
20000600  uart                                                               
20000000  uartCC26XXObjects                                                  
00004e0c  udmaCC26XXHWAttrs                                                  
20000548  udmaCC26XXObject                                                   


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                                               
-------   ----                                                               
00000000  __TI_static_base__                                                 
00000000  g_pfnVectors                                                       
000000d9  PIN_init                                                           
000002b9  Power_sleep                                                        
00000400  __STACK_SIZE                                                       
0000076d  ADCBufCC26X2_open                                                  
000008e1  Power_init                                                         
00000a55  UARTCC26XX_open                                                    
00000bb9  UARTCC26XX_hwiIntFxn                                               
00000d0d  UARTCC26XX_read                                                    
0000107d  GPIO_setConfig                                                     
0000118d  ADCBufCC26X2_convert                                               
0000128d  aligned_alloc                                                      
0000128d  memalign                                                           
0000138d  NOROM_SysCtrlStandby                                               
0000147d  free                                                               
00001561  PowerCC26XX_standbyPolicy                                          
00001641  UARTCC26XX_write                                                   
00001721  SwiP_construct                                                     
000017f9  GPIO_init                                                          
000018bd  Power_releaseDependency                                            
00001a3d  TimerP_construct                                                   
00001af9  Power_setDependency                                                
00001c69  ClockP_startup                                                     
00001d1d  ADCBufCC26X2_getResolution                                         
00001d21  Timer2AInterruptHandler                                            
00001dd5  ClockP_workFuncDynamic                                             
00001e81  PIN_open                                                           
00001fd9  SemaphoreP_pend                                                    
00002079  InitTimer2                                                         
00002115  SwiP_dispatch                                                      
000021b1  __aeabi_memcpy                                                     
000021b1  __aeabi_memcpy4                                                    
000021b1  __aeabi_memcpy8                                                    
000021b1  memcpy                                                             
0000224d  NOROM_SetupTrimDevice                                              
0000237d  __aeabi_uldivmod                                                   
00002413  UARTCC26XX_writeCancel                                             
000024a5  GPTimerCC26XX_open                                                 
0000252d  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002751  Board_sendExtFlashByte                                             
000027d5  UARTCC26XX_close                                                   
000028d9  ClockP_start                                                       
00002959  HwiP_construct                                                     
000029d9  NOROM_SysCtrlSetRechargeBeforePowerDown                            
00002a59  __aeabi_memclr                                                     
00002a59  __aeabi_memclr4                                                    
00002a59  __aeabi_memclr8                                                    
00002a5b  __aeabi_memset                                                     
00002a5b  __aeabi_memset4                                                    
00002a5b  __aeabi_memset8                                                    
00002a61  memset                                                             
00002ad3  SemaphoreP_destruct                                                
00002ad5  ClockP_walkQueueDynamic                                            
00002bc1  PowerCC26X2_auxISR                                                 
00002c35  PIN_add                                                            
00002ca5  ClockP_isActive                                                    
00002ca9  main                                                               
00002d89  NOROM_ChipInfo_GetChipType                                         
00002df5  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
00002e5d  SwiP_post                                                          
00002ec5  TimerP_start                                                       
00002f2d  __TI_decompress_lzss                                               
00002f95  UARTCC26XX_control                                                 
00002ff9  UARTCC26XX_swiIntFxn                                               
0000305b  _nop                                                               
0000305d  GPIO_write                                                         
000030bd  PIN_remove                                                         
000031cd  GPTimerCC26XX_stop                                                 
00003225  PowerCC26X2_initiateCalibration                                    
0000327d  ADCBufCC26X2_adjustRawValues                                       
000032d1  NOROM_PRCMPowerDomainsAllOff                                       
00003325  SemaphoreP_construct                                               
00003375  ADCBufCC26X2_close                                                 
000033c3  clkFxn                                                             
00003411  Board_wakeUpExtFlash                                               
000034a9  GPTimerCC26XX_start                                                
000034f5  NOROM_PRCMPowerDomainsAllOn                                        
00003541  UARTCC26XX_readCancel                                              
0000358d  UDMACC26XX_open                                                    
000035d9  ADCBufCC26X2_control                                               
00003625  ADCBufCC26X2_convertAdjustedToMicroVolts                           
0000366d  ClockP_construct                                                   
000036b5  PIN_setOutputEnable                                                
000036fd  PowerCC26X2_RCOSC_clockFunc                                        
000037d5  RingBuf_put                                                        
0000381d  ADCBuf_init                                                        
00003861  InitUart                                                           
000038a5  NOROM_ChipInfo_GetHwRevision                                       
0000392d  SemaphoreP_post                                                    
00003971  SwiP_restore                                                       
000039b5  UART_init                                                          
000039f9  __TI_auto_init_nobinit_nopinit                                     
00003a81  ADCBufCC26X2_convertCancel                                         
00003ac1  Board_shutDownExtFlash                                             
00003b01  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00003b41  NOROM_TimerIntRegister                                             
00003b81  RingBuf_get                                                        
00003bc1  UART_open                                                          
00003c01  PIN_setOutputValue                                                 
00003c75  PIN_setConfig                                                      
00003cad  TimerP_getCurrentTick                                              
00003ce5  TimerP_initDevice                                                  
00003dc5  TimerP_setNextTick                                                 
00003dfb  UDMACC26XX_close                                                   
00003e31  ClockP_getTicksUntilInterrupt                                      
00003e99  GPTimerCC26XX_configureDebugStall                                  
00003ecd  NOROM_AUXSYSIFOpModeChange                                         
00003f01  NOROM_IntRegister                                                  
00003f35  NOROM_SysCtrlIdle                                                  
00003f69  SoundTransmit                                                      
00003f9d  SwiP_destruct                                                      
00004000  __SYSMEM_SIZE                                                      
00004005  GPIO_hwiIntFxn                                                     
00004035  GPIO_setCallback                                                   
00004065  GPTimerCC26XX_close                                                
00004095  PINCC26XX_setMux                                                   
000040c5  Power_releaseConstraint                                            
000040f5  Power_setConstraint                                                
00004155  _c_int00                                                           
000041dd  TimerP_setThreshold                                                
00004233  ClockP_stop                                                        
00004263  NOROM_CPUdelay                                                     
00004293  QueueP_init                                                        
000042c3  UARTCC26XX_readPolling                                             
000042c9  Board_init                                                         
000042f1  Power_registerNotify                                               
00004369  List_put                                                           
0000438f  List_remove                                                        
000043b5  ClockP_getTicks                                                    
000043d9  ClockP_scheduleNextTick                                            
00004421  NOROM_OSCHF_TurnOnXosc                                             
00004445  TimerP_getMaxTicks                                                 
0000448d  ClockP_add                                                         
000044ad  NOROM_AUXADCEnableSync                                             
000044ed  Power_unregisterNotify                                             
0000450d  QueueP_put                                                         
0000452d  TimerP_dynamicStub                                                 
0000458d  PIN_close                                                          
000045ab  Power_getTransitionLatency                                         
000045c9  SemaphoreP_constructBinary                                         
000045e7  SwiP_or                                                            
00004605  ClockP_setTimeout                                                  
00004647  __aeabi_idiv0                                                      
00004647  __aeabi_ldiv0                                                      
00004649  HwiP_destruct                                                      
00004665  NOROM_ChipInfo_GetChipFamily                                       
0000469d  HwiP_enable                                                        
0000469d  NoRTOS_start                                                       
000046bd  SwiP_disable                                                       
0000472d  PowerCC26XX_calibrate                                              
00004747  QueueP_get                                                         
00004761  SemaphoreP_createBinary                                            
0000477b  ClockP_destruct                                                    
00004795  HwiP_inISR                                                         
000047ad  NOROM_ChipInfo_GetPackageType                                      
000047c5  Power_idleFunc                                                     
000047dd  QueueP_head                                                        
000047f9  TimerP_startup                                                     
00004811  UART_Params_init                                                   
00004829  _args_main                                                         
00004841  __aeabi_lmul                                                       
00004871  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00004887  SemaphoreP_create                                                  
000048c9  NOROM_IntUnregister                                                
000048dd  PowerCC26X2_calibrate                                              
000048f1  Power_disablePolicy                                                
00004919  TimerP_getCount64                                                  
0000492d  resetISR                                                           
00004941  GPTimerCC26XX_Params_init                                          
00004953  HwiP_Params_init                                                   
00004965  RingBuf_construct                                                  
0000498d  ClockP_Params_init                                                 
0000499d  ClockP_getCpuFreq                                                  
000049ad  HwiP_clearInterrupt                                                
000049bd  HwiP_disable                                                       
000049cd  HwiP_post                                                          
000049dd  PINCC26XX_getPinCount                                              
000049ed  PowerCC26XX_schedulerDisable                                       
000049fd  Power_enablePolicy                                                 
00004a0d  SemaphoreP_Params_init                                             
00004a1d  SwiP_Params_init                                                   
00004a2d  TimerP_Params_init                                                 
00004a4d  QueueP_empty                                                       
00004a5b  QueueP_remove                                                      
00004a69  __TI_decompress_none                                               
00004a79  ClockP_doTick                                                      
00004a85  ClockP_getSystemTickPeriod                                         
00004a91  NOROM_CPUcpsid                                                     
00004a9d  NOROM_CPUcpsie                                                     
00004aa9  PIN_registerIntCb                                                  
00004ab5  QueueP_next                                                        
00004ac5  PowerCC26XX_schedulerRestore                                       
00004ad1  Power_getConstraintMask                                            
00004add  Power_getDependencyCount                                           
00004ae9  SwiP_getTrigger                                                    
00004af5  TimerP_getFreq                                                     
00004b01  __TI_zero_init                                                     
00004b0d  ADCBuf_convertCancel                                               
00004b17  UDMACC26XX_hwiIntFxn                                               
00004b33  ADCBufCC26X2_init                                                  
00004b3b  GPTimerCC26XX_setLoadValue                                         
00004b43  HwiP_restore                                                       
00004b4b  UARTCC26XX_writePolling                                            
00004b51  InitADC                                                            
00004b59  UARTCC26XX_init                                                    
00004b61  malloc                                                             
00004b77  SemaphoreP_delete                                                  
00004b7b  _system_pre_init                                                   
00004b7f  C$$EXIT                                                            
00004b7f  abort                                                              
00004b91  Board_initHook                                                     
00004b94  resourceDB                                                         
00004be8  BoardGpioInitTable                                                 
00004c10  UARTCC26XX_fxnTable                                                
00004c60  ADCBufCC26X2_fxnTable                                              
00004c84  UART_defaultParams                                                 
00004cc4  GPTimerCC26XX_config                                               
00004cdc  gptimerCC26XXHWAttrs                                               
00004d20  GPIOCC26XX_config                                                  
00004d34  PowerCC26X2_config                                                 
00004d6c  adcbufCC26XXHWAttrs                                                
00004d8c  outPinTypes                                                        
00004d9c  ADCBUF_CONST                                                       
00004d9d  ADCBUF_SOUND_CONST                                                 
00004d9e  ADCBUF_TEMPERATURE_CONST                                           
00004d9f  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004da0  ADCBuf_count                                                       
00004da1  PIN_TEST1_CONST                                                    
00004da2  PIN_TEST2_CONST                                                    
00004da3  PIN_DRIVE_SPEAKER_A_CONST                                          
00004da4  PIN_DRIVE_SPEAKER_B_CONST                                          
00004da5  UART_0_CONST                                                       
00004da6  UART_count                                                         
00004da7  CONFIG_GPTIMER_0_CONST                                             
00004da8  CONFIG_GPTIMER_1_CONST                                             
00004da9  GPTimer_count                                                      
00004dac  ADCBuf_config                                                      
00004db8  UART_config                                                        
00004dc4  inPinTypes                                                         
00004dd0  outPinStrengths                                                    
00004df4  PINCC26XX_hwAttrs                                                  
00004dfc  UDMACC26XX_config                                                  
00004e0c  udmaCC26XXHWAttrs                                                  
00004e2c  __c_args__                                                         
00004ec0  __TI_Handler_Table_Base                                            
00004ecc  __TI_Handler_Table_Limit                                           
00004ef0  __TI_CINIT_Base                                                    
00004f18  __TI_CINIT_Limit                                                   
00057fa8  __ccfg                                                             
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
20000000  uartCC26XXObjects                                                  
200001b4  adcbufCC26XXbjects                                                 
20000330  gptimerCC26XXObjects                                               
200003c8  pinHandleTable                                                     
20000548  udmaCC26XXObject                                                   
200005c9  driverlib_release_0_59848                                          
200005f8  i                                                                  
200005fc  j                                                                  
20000600  uart                                                               
20000604  PowerCC26X2_module                                                 
2000084c  ChirpDelay                                                         
200008c8  ChirpState                                                         
20000944  _hposcCoeffs                                                       
20000968  gpioCallbackFunctions                                              
20000978  gpioPinConfigs                                                     
200009a0  _lock                                                              
200009a4  _unlock                                                            
200009b0  SemaphoreP_defaultParams                                           
200009b8  ChirpSize                                                          
200009bc  ChirpIndex                                                         
200009c0  pinUpperBound                                                      
200009c4  pinLowerBound                                                      
200009cc  ClockP_tickPeriod                                                  
200009d0  adcBufCC26XXChannelLut0                                            
200009e4  HwiP_swiPIntNum                                                    
20000a00  g_pfnRAMVectors                                                    
20001a80  _sys_memory                                                        
20013c00  __stack                                                            
20014000  __STACK_END                                                        
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
ffffffff  __binit__                                                          
ffffffff  binit                                                              
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
UNDEFED   __mpu_init                                                         
UNDEFED   _system_post_cinit                                                 

[290 symbols]
