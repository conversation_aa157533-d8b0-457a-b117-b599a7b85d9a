******************************************************************************
                  TI ARM Linker PC v20.2.4                     
******************************************************************************
>> Linked Mon Feb 22 19:52:09 2021

OUTPUT FILE NAME:   <SoundPositioning.out>
ENTRY POINT SYMBOL: "resetISR"  address: 00004739


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00058000  00004d76  0005328a  R  X
  GPRAM                 11000000   00002000  00000000  00002000  RW X
  SRAM                  20000000   00014000  00004ee2  0000f11e  RW X
  LOG_DATA              90000000   00040000  00000000  00040000  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004c38   00004c38    r-x
  00000000    00000000    000000d8   000000d8    r-- .intvecs
  000000d8    000000d8    000048c6   000048c6    r-x .text
  000049a0    000049a0    00000298   00000298    r-- .const
00004c38    00004c38    00000008   00000008    rw-
  00004c38    00004c38    00000008   00000008    rw- .args
00004c40    00004c40    000000e0   000000e0    r--
  00004c40    00004c40    000000e0   000000e0    r-- .cinit
00057fa8    00057fa8    00000058   00000058    r--
  00057fa8    00057fa8    00000058   00000058    r-- .ccfg
20000000    20000000    000009ea   00000000    rw-
  20000000    20000000    00000604   00000000    rw- .bss
  20000604    20000604    000003e6   00000000    rw- .data
20000a00    20000a00    000000d8   00000000    rw-
  20000a00    20000a00    000000d8   00000000    rw- .vtable_ram
20001870    20001870    00000010   00000000    rw-
  20001870    20001870    00000010   00000000    rw- .TI.bound:dmaADCPriControlTableEntry
20001a70    20001a70    00004010   00000000    rw-
  20001a70    20001a70    00000010   00000000    rw- .TI.bound:dmaADCAltControlTableEntry
  20001a80    20001a80    00004000   00000000    rw- .sysmem
20013c00    20013c00    00000400   00000000    rw-
  20013c00    20013c00    00000400   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000d8     
                  00000000    000000d8     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.intvecs)

.text      0    000000d8    000048c6     
                  000000d8    000001e0     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_init)
                  000002b8    00000198                      : PowerCC26X2.oem4f (.text:Power_sleep)
                  00000450    00000190                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf1)
                  000005e0    0000018c                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:runCalibrateFsm)
                  0000076c    00000174                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_open)
                  000008e0    00000174                      : PowerCC26X2.oem4f (.text:Power_init)
                  00000a54    00000164                      : UARTCC26XX.oem4f (.text:UARTCC26XX_open)
                  00000bb8    00000154                      : UARTCC26XX.oem4f (.text:UARTCC26XX_hwiIntFxn)
                  00000d0c    0000013a                      : UARTCC26XX.oem4f (.text:UARTCC26XX_read)
                  00000e46    00000002     ti_drivers_config.obj (.text:Board_initHook)
                  00000e48    00000120     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureXOSCHF)
                  00000f68    00000114                      : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfg)
                  0000107c    00000110                      : GPIOCC26XX.oem4f (.text:GPIO_setConfig)
                  0000118c    00000100                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convert)
                  0000128c    00000100     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:aligned_alloc)
                  0000138c    000000f0     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlStandby)
                  0000147c    000000e4     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free)
                  00001560    000000e0     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_standbyPolicy)
                  00001640    000000e0     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_write)
                  00001720    000000d8     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_construct)
                  000017f8    000000c4     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_init)
                  000018bc    000000c0                      : PowerCC26X2.oem4f (.text:Power_releaseDependency)
                  0000197c    000000c0                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initIO)
                  00001a3c    000000bc     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_construct)
                  00001af8    000000b8     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_setDependency)
                  00001bb0    000000b8                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initHw)
                  00001c68    000000b4     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_startup)
                  00001d1c    000000ac                     : ClockPTimer_nortos.oem4f (.text:ClockP_workFuncDynamic)
                  00001dc8    000000ac     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_open)
                  00001e74    000000ac     driverlib.lib : setup.obj (.text:TrimAfterColdResetWakeupFromShutDown)
                  00001f20    000000a0     SoundTX.obj (.text:InitTimer2)
                  00001fc0    000000a0     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_pend)
                  00002060    0000009c                     : SwiP_nortos.oem4f (.text:SwiP_dispatch)
                  000020fc    0000009c     rtsv7M4_T_le_v4SPD16_eabi.lib : memcpy_t2.asm.obj (.text)
                  00002198    00000098     driverlib.lib : setup.obj (.text:NOROM_SetupTrimDevice)
                  00002230    00000098     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeTxFifoFlush)
                  000022c8    00000096     rtsv7M4_T_le_v4SPD16_eabi.lib : ull_div_t2.asm.obj (.text)
                  0000235e    00000092     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_writeCancel)
                  000023f0    00000088                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_open)
                  00002478    00000088     driverlib.lib : osc.obj (.text:NOROM_OSCHF_AttemptToSwitchToXosc)
                  00002500    00000088     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf2)
                  00002588    00000088                      : PowerCC26X2.oem4f (.text:disableLFClockQualifiers)
                  00002610    00000088                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:updateSubSecInc)
                  00002698    00000084     SoundTX.obj (.text:Timer2AInterruptHandler)
                  0000271c    00000084     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_close)
                  000027a0    00000080                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_hwiFxn)
                  00002820    00000080     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_start)
                  000028a0    00000080                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_construct)
                  00002920    00000080     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlSetRechargeBeforePowerDown)
                  000029a0    0000007a     rtsv7M4_T_le_v4SPD16_eabi.lib : memset_t2.asm.obj (.text)
                  00002a1a    00000002     driverlib.lib : interrupt.obj (.text:IntDefaultHandler)
                  00002a1c    00000078     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_walkQueueDynamic)
                  00002a94    00000074     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_resetHw)
                  00002b08    00000074                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_auxISR)
                  00002b7c    00000070                      : PINCC26XX.oem4f (.text:PIN_add)
                  00002bec    00000070     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:split)
                  00002c5c    0000006c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipType)
                  00002cc8    00000068                   : osc.obj (.text:NOROM_OSCHF_SwitchToRcOscTurnOffXosc)
                  00002d30    00000068     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_post)
                  00002d98    00000068                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_start)
                  00002e00    00000068     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_lzss.c.obj (.text:decompress:lzss:__TI_decompress_lzss)
                  00002e68    00000064     ti_drivers_config.obj (.text:Board_sendExtFlashByte)
                  00002ecc    00000064     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_control)
                  00002f30    00000062                      : UARTCC26XX.oem4f (.text:UARTCC26XX_swiIntFxn)
                  00002f92    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_destruct)
                  00002f94    00000060     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_write)
                  00002ff4    0000005c                      : PINCC26XX.oem4f (.text:PIN_remove)
                  00003050    0000005c                      : UARTCC26XX.oem4f (.text:startTxFifoEmptyClk)
                  000030ac    00000058                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_loadDMAControlTableEntry)
                  00003104    00000058                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_stop)
                  0000315c    00000058                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_initiateCalibration)
                  000031b4    00000054                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_adjustRawValues)
                  00003208    00000054     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOff)
                  0000325c    00000054     mainNew.obj (.text:main)
                  000032b0    00000050     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_construct)
                  00003300    0000004e     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_close)
                  0000334e    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.text:_nop)
                  00003350    0000004c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_cleanADC)
                  0000339c    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_initHw)
                  000033e8    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_start)
                  00003434    0000004c     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOn)
                  00003480    0000004c     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readCancel)
                  000034cc    0000004c                      : UDMACC26XX.oem4f (.text:UDMACC26XX_open)
                  00003518    0000004a                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_control)
                  00003562    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:clkFxn)
                  00003564    00000048     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertAdjustedToMicroVolts)
                  000035ac    00000048     ti_drivers_config.obj (.text:Board_wakeUpExtFlash)
                  000035f4    00000048     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_construct)
                  0000363c    00000048     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputEnable)
                  00003684    00000048                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_RCOSC_clockFunc)
                  000036cc    00000048                      : PowerCC26X2.oem4f (.text:switchXOSCHF)
                  00003714    00000048                      : UARTCC26XX.oem4f (.text:writeFinishedDoCallback)
                  0000375c    00000046                      : RingBuf.oem4f (.text:RingBuf_put)
                  000037a2    00000002                      : PowerCC26X2.oem4f (.text:emptyClockFunc)
                  000037a4    00000044                      : ADCBuf.oem4f (.text:ADCBuf_init)
                  000037e8    00000044     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetHwRevision)
                  0000382c    00000044     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_swi)
                  00003870    00000044     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_post)
                  000038b4    00000044                     : SwiP_nortos.oem4f (.text:SwiP_restore)
                  000038f8    00000044     drivers_cc13x2.a : UART.oem4f (.text:UART_init)
                  0000393c    00000044     rtsv7M4_T_le_v4SPD16_eabi.lib : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit:__TI_auto_init_nobinit_nopinit)
                  00003980    00000042     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_swiFxn)
                  000039c2    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:faultISR)
                  000039c4    00000040     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertCancel)
                  00003a04    00000040     driverlib.lib : osc.obj (.text:NOROM_OSC_HPOSCRelativeFrequencyOffsetGet)
                  00003a44    00000040                   : timer.obj (.text:NOROM_TimerIntRegister)
                  00003a84    00000040     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_get)
                  00003ac4    00000040                      : UART.oem4f (.text:UART_open)
                  00003b04    0000003c     mainNew.obj (.text:InitUart)
                  00003b40    0000003c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputValue)
                  00003b7c    00000038                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_configDMA)
                  00003bb4    00000038     ti_drivers_config.obj (.text:Board_shutDownExtFlash)
                  00003bec    00000038     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setConfig)
                  00003c24    00000038     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCurrentTick)
                  00003c5c    00000038                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_initDevice)
                  00003c94    00000038     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:notify)
                  00003ccc    00000038                      : UARTCC26XX.oem4f (.text:readData)
                  00003d04    00000038                      : UARTCC26XX.oem4f (.text:writeData)
                  00003d3c    00000036     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setNextTick)
                  00003d72    00000036     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_close)
                  00003da8    00000034     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicksUntilInterrupt)
                  00003ddc    00000034     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XXSetLoadMatch)
                  00003e10    00000034                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_configureDebugStall)
                  00003e44    00000034     driverlib.lib : aux_sysif.obj (.text:NOROM_AUXSYSIFOpModeChange)
                  00003e78    00000034                   : interrupt.obj (.text:NOROM_IntRegister)
                  00003eac    00000034                   : sys_ctrl.obj (.text:NOROM_SysCtrlIdle)
                  00003ee0    00000034     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_destruct)
                  00003f14    00000034     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:readData2RingBuf)
                  00003f48    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_hwiIntFxn)
                  00003f78    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_setCallback)
                  00003fa8    00000030                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_close)
                  00003fd8    00000030                      : PINCC26XX.oem4f (.text:PINCC26XX_setMux)
                  00004008    00000030                      : PowerCC26X2.oem4f (.text:Power_releaseConstraint)
                  00004038    00000030                      : PowerCC26X2.oem4f (.text:Power_setConstraint)
                  00004068    00000030     driverlib.lib : timer.obj (.text:TimerIntNumberGet)
                  00004098    00000030     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.text:_c_int00:_c_int00)
                  000040c8    0000002c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_acquireADCSemaphore)
                  000040f4    0000002c                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_releaseADCSemaphore)
                  00004120    0000002c     SoundTX.obj (.text:SoundTransmit)
                  0000414c    0000002c     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setThreshold)
                  00004178    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_registerNotify)
                  000041a0    00000028     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_insert)
                  000041c8    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:oscillatorISR)
                  000041f0    00000026                      : List.oem4f (.text:List_put)
                  00004216    00000026                      : List.oem4f (.text:List_remove)
                  0000423c    00000024     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicks)
                  00004260    00000024                     : ClockPTimer_nortos.oem4f (.text:ClockP_scheduleNextTick)
                  00004284    00000024                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_dispatch)
                  000042a8    00000024     driverlib.lib : osc.obj (.text:NOROM_OSCHF_TurnOnXosc)
                  000042cc    00000024     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getMaxTicks)
                  000042f0    00000024     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:getTdcSemaphore)
                  00004314    00000020     ti_drivers_config.obj (.text:Board_init)
                  00004334    00000020     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_add)
                  00004354    00000020     driverlib.lib : aux_adc.obj (.text:NOROM_AUXADCEnableSync)
                  00004374    00000020     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfgMux)
                  00004394    00000020                      : PowerCC26X2.oem4f (.text:Power_unregisterNotify)
                  000043b4    00000020     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_put)
                  000043d4    00000020                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_dynamicStub)
                  000043f4    00000020     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:threadSafeStdbyDisRelease)
                  00004414    00000020                      : UARTCC26XX.oem4f (.text:threadSafeStdbyDisSet)
                  00004434    0000001e                      : PINCC26XX.oem4f (.text:PIN_close)
                  00004452    0000001e                      : PowerCC26X2.oem4f (.text:Power_getTransitionLatency)
                  00004470    0000001e     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_constructBinary)
                  0000448e    0000001e                     : SwiP_nortos.oem4f (.text:SwiP_or)
                  000044ac    0000001c                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_destruct)
                  000044c8    0000001c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipFamily)
                  000044e4    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_hwi)
                  00004500    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_disable)
                  0000451c    0000001c     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_initHw)
                  00004538    0000001c     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_remove)
                  00004554    0000001a     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:PowerCC26XX_calibrate)
                  0000456e    0000001a     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_get)
                  00004588    0000001a                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_createBinary)
                  000045a2    00000018                     : ClockPTimer_nortos.oem4f (.text:ClockP_destruct)
                  000045ba    00000002                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:intDefaultHandler)
                  000045bc    00000018                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_inISR)
                  000045d4    00000018     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetPackageType)
                  000045ec    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_idleFunc)
                  00004604    00000018     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_startup)
                  0000461c    00000018     drivers_cc13x2.a : UART.oem4f (.text:UART_Params_init)
                  00004634    00000018     rtsv7M4_T_le_v4SPD16_eabi.lib : args_main.c.obj (.text:_args_main)
                  0000464c    00000018                                   : ll_mul_t2.asm.obj (.text)
                  00004664    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:switchToTCXO)
                  0000467c    00000016     driverlib.lib : chipinfo.obj (.text:NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated)
                  00004692    00000016     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_create)
                  000046a8    00000016                     : SwiP_nortos.oem4f (.text:maxbit)
                  000046be    00000014     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_freqToCounts)
                  000046d2    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:nmiISR)
                  000046d4    00000014     driverlib.lib : interrupt.obj (.text:NOROM_IntUnregister)
                  000046e8    00000014     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_calibrate)
                  000046fc    00000014                      : PowerCC26X2.oem4f (.text:Power_disablePolicy)
                  00004710    00000014     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_handleHwi)
                  00004724    00000014                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCount64)
                  00004738    00000014                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:resetISR)
                  0000474c    00000012     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_Params_init)
                  0000475e    00000012     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_Params_init)
                  00004770    00000012     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_construct)
                  00004782    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : div0.asm.obj (.text)
                  00004784    00000010     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_Params_init)
                  00004794    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_getCpuFreq)
                  000047a4    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_clearInterrupt)
                  000047b4    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_disable)
                  000047c4    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_post)
                  000047d4    00000010     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_getPinCount)
                  000047e4    00000010     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerDisable)
                  000047f4    00000010     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_enablePolicy)
                  00004804    00000010     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_Params_init)
                  00004814    00000010                     : SwiP_nortos.oem4f (.text:SwiP_Params_init)
                  00004824    00000010                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_Params_init)
                  00004834    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:uartPostNotify)
                  00004844    0000000e     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_empty)
                  00004852    0000000e                     : QueueP_nortos.oem4f (.text:QueueP_remove)
                  00004860    0000000e     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_none.c.obj (.text:decompress:none:__TI_decompress_none)
                  0000486e    00000002     --HOLE-- [fill = 0]
                  00004870    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_doTick)
                  0000487c    0000000c                     : ClockPTimer_nortos.oem4f (.text:ClockP_getSystemTickPeriod)
                  00004888    0000000c     driverlib.lib : cpu.obj (.text:NOROM_CPUcpsid)
                  00004894    0000000c                   : cpu.obj (.text:NOROM_CPUcpsie)
                  000048a0    0000000c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_registerIntCb)
                  000048ac    0000000c     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerRestore)
                  000048b8    0000000c     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_getConstraintMask)
                  000048c4    0000000c                      : PowerCC26X2.oem4f (.text:Power_getDependencyCount)
                  000048d0    0000000c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_getTrigger)
                  000048dc    0000000c                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getFreq)
                  000048e8    0000000c     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init)
                  000048f4    0000000a     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_convertCancel)
                  000048fe    0000000a                      : UDMACC26XX.oem4f (.text:UDMACC26XX_hwiIntFxn)
                  00004908    0000000a                      : UARTCC26XX.oem4f (.text:readSemCallback)
                  00004912    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_conversionCallback)
                  0000491a    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_init)
                  00004922    00000008                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_setLoadValue)
                  0000492a    00000008     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_restore)
                  00004932    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_init)
                  0000493a    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:malloc)
                  00004942    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeSemCallback)
                  0000494a    00000006     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_stop)
                  00004950    00000006     driverlib.lib : cpu.obj (.text:NOROM_CPUdelay)
                  00004956    00000006     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_init)
                  0000495c    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readPolling)
                  00004962    00000006                      : UARTCC26XX.oem4f (.text:UARTCC26XX_writePolling)
                  00004968    00000006                      : PowerCC26X2.oem4f (.text:delayUs)
                  0000496e    00000004                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_getResolution)
                  00004972    00000004     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:CPUwfi)
                  00004976    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_isActive)
                  0000497a    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_setTimeout)
                  0000497e    00000004                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_enable)
                  00004982    00000004                     : QueueP_nortos.oem4f (.text:QueueP_head)
                  00004986    00000004                     : QueueP_nortos.oem4f (.text:QueueP_next)
                  0000498a    00000004                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_delete)
                  0000498e    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : pre_init.c.obj (.text:_system_pre_init)
                  00004992    00000004                                   : exit.c.obj (.text:abort:abort)
                  00004996    00000004     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureRFCoreClocks)
                  0000499a    00000004                      : PowerCC26X2.oem4f (.text:nopResourceHandler)

.const     0    000049a0    00000298     
                  000049a0    00000054     drivers_cc13x2.a : PowerCC26X2.oem4f (.const:resourceDB)
                  000049f4    00000028     ti_drivers_config.obj (.const:BoardGpioInitTable)
                  00004a1c    00000028     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:UARTCC26XX_fxnTable)
                  00004a44    00000028     ti_drivers_config.obj (.const:uartCC26XXHWAttrs)
                  00004a6c    00000024     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.const:ADCBufCC26X2_fxnTable)
                  00004a90    00000024                      : UART.oem4f (.const:UART_defaultParams)
                  00004ab4    0000001c                      : GPTimerCC26XX.oem4f (.const:GPT_LUT)
                  00004ad0    00000018     ti_drivers_config.obj (.const:GPTimerCC26XX_config)
                  00004ae8    00000018     ti_drivers_config.obj (.const:gptimerCC26XXHWAttrs)
                  00004b00    00000018     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:interruptType)
                  00004b18    00000014     ti_drivers_config.obj (.const:$P$T1$2)
                  00004b2c    00000014     ti_drivers_config.obj (.const:GPIOCC26XX_config)
                  00004b40    00000014     ti_drivers_config.obj (.const:PowerCC26X2_config)
                  00004b54    00000014     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:parityType)
                  00004b68    00000010     nortos_cc13x2.a : SwiP_nortos.oem4f (.const:SwiP_defaultParams)
                  00004b78    00000010     ti_drivers_config.obj (.const:adcbufCC26XXHWAttrs)
                  00004b88    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:dataLength)
                  00004b98    00000010                      : GPIOCC26XX.oem4f (.const:outPinTypes)
                  00004ba8    0000000e     ti_drivers_config.obj (.const)
                  00004bb6    00000002     --HOLE-- [fill = 0]
                  00004bb8    0000000c     ti_drivers_config.obj (.const:ADCBuf_config)
                  00004bc4    0000000c     ti_drivers_config.obj (.const:UART_config)
                  00004bd0    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:inPinTypes)
                  00004bdc    0000000c                      : GPIOCC26XX.oem4f (.const:outPinStrengths)
                  00004be8    00000008     driverlib.lib : aux_sysif.obj (.const:$O1$$)
                  00004bf0    00000008     ti_drivers_config.obj (.const:$P$T0$1)
                  00004bf8    00000008     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.const:GPT_DefaultParams)
                  00004c00    00000008     ti_drivers_config.obj (.const:PINCC26XX_hwAttrs)
                  00004c08    00000008     ti_drivers_config.obj (.const:UDMACC26XX_config)
                  00004c10    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:stopBits)
                  00004c18    00000008     ti_drivers_config.obj (.const:udmaCC26XXHWAttrs)
                  00004c20    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:.string:rxFifoBytes)
                  00004c26    00000006                      : UARTCC26XX.oem4f (.const:.string:rxFifoThreshold)
                  00004c2c    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoBytes)
                  00004c32    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoThreshold)

.binit     0    00000000    00000000     

.cinit     0    00004c40    000000e0     
                  00004c40    00000088     (.cinit..data.load) [load image, compression = lzss]
                  00004cc8    0000000c     (__TI_handler_table)
                  00004cd4    00000004     --HOLE-- [fill = 0]
                  00004cd8    00000008     (.cinit..TI.bound:dmaADCAltControlTableEntry.load) [load image, compression = zero_init]
                  00004ce0    00000008     (.cinit..TI.bound:dmaADCPriControlTableEntry.load) [load image, compression = zero_init]
                  00004ce8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004cf0    00000008     (.cinit..vtable_ram.load) [load image, compression = zero_init]
                  00004cf8    00000028     (__TI_cinit_table)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.ccfg      0    00057fa8    00000058     
                  00057fa8    00000058     ti_devices_config.obj (.ccfg:retain)

.bss       0    20000000    00000604     UNINITIALIZED
                  20000000    000001b4     (.common:uartCC26XXObjects)
                  200001b4    000000dc     (.common:adcbufCC26XXbjects)
                  20000290    000000a0     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:ClockP_module)
                  20000330    00000098     (.common:gptimerCC26XXObjects)
                  200003c8    0000007c     (.common:pinHandleTable)
                  20000444    00000050     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinSemaphore)
                  20000494    00000034                      : PINCC26XX.oem4f (.bss:pinSwi)
                  200004c8    00000020     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_readyList)
                  200004e8    00000020     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioCallbackInfo)
                  20000508    00000020                      : PINCC26XX.oem4f (.bss:pinGpioConfigTable)
                  20000528    00000020     ti_drivers_config.obj (.bss:uartCC26XXRingBuffer0)
                  20000548    00000020     (.common:udmaCC26XXObject)
                  20000568    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_hwiStruct)
                  20000584    0000001c                     : TimerPCC26XX_nortos.oem4f (.bss:TimerP_hwiStruct)
                  200005a0    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinHwi)
                  200005bc    0000000d     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss)
                  200005c9    00000001     (.common:driverlib_release_0_59848)
                  200005ca    00000002     --HOLE--
                  200005cc    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioPinState)
                  200005d8    00000008                      : GPIOCC26XX.oem4f (.bss)
                  200005e0    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:timerFreq)
                  200005e8    00000004     drivers_cc13x2.a : PINCC26XX.oem4f (.bss)
                  200005ec    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss)
                  200005f0    00000004                     : PowerCC26X2_nortos.oem4f (.bss)
                  200005f4    00000004                     : TimerPCC26XX_nortos.oem4f (.bss)
                  200005f8    00000004     (.common:i)
                  200005fc    00000004     (.common:j)
                  20000600    00000004     (.common:uart)

.vtable_ram 
*          0    20000a00    000000d8     UNINITIALIZED
                  20000a00    000000d8     driverlib.lib : interrupt.obj (.vtable_ram)

.data      0    20000604    000003e6     UNINITIALIZED
                  20000604    00000170     drivers_cc13x2.a : PowerCC26X2.oem4f (.data:PowerCC26X2_module)
                  20000774    000000d8     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data:HwiP_dispatchTable)
                  2000084c    0000007c     SoundTX.obj (.data:ChirpDelay)
                  200008c8    0000007c     SoundTX.obj (.data:ChirpState)
                  20000944    00000024     driverlib.lib : osc.obj (.data:$O1$$)
                  20000968    00000010     ti_drivers_config.obj (.data:gpioCallbackFunctions)
                  20000978    00000010     ti_drivers_config.obj (.data:gpioPinConfigs)
                  20000988    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data:ClockP_defaultParams)
                  20000994    0000000c                     : TimerPCC26XX_nortos.oem4f (.data:TimerP_defaultParams)
                  200009a0    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.data:$O1$$)
                  200009a8    00000008                                   : memory.c.obj (.data:$O1$$)
                  200009b0    00000008     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data:SemaphoreP_defaultParams)
                  200009b8    00000008     SoundTX.obj (.data)
                  200009c0    00000008     drivers_cc13x2.a : PINCC26XX.oem4f (.data)
                  200009c8    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data)
                  200009d0    00000006     ti_drivers_config.obj (.data:adcBufCC26XXChannelLut0)
                  200009d6    00000001     drivers_cc13x2.a : ADCBuf.oem4f (.data)
                  200009d7    00000001                      : UART.oem4f (.data)
                  200009d8    00000005                      : GPIOCC26XX.oem4f (.data)
                  200009dd    00000003     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data)
                  200009e0    00000004     drivers_cc13x2.a : GPIOCC26XX.oem4f (.data:gpioPinTable)
                  200009e4    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data)
                  200009e8    00000001                     : SwiP_nortos.oem4f (.data)
                  200009e9    00000001                     : TimerPCC26XX_nortos.oem4f (.data)

.sysmem    0    20001a80    00004000     UNINITIALIZED
                  20001a80    00000010     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.sysmem)
                  20001a90    00003ff0     --HOLE--

.stack     0    20013c00    00000400     UNINITIALIZED
                  20013c00    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.stack)
                  20013c04    000003fc     --HOLE--

.log_data 
*          0    90000000    00000000     COPY SECTION

__llvm_prf_cnts 
*          0    20000000    00000000     UNINITIALIZED

.TI.bound:dmaADCPriControlTableEntry 
*          0    20001870    00000010     UNINITIALIZED
                  20001870    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCPriControlTableEntry)

.TI.bound:dmaADCAltControlTableEntry 
*          0    20001a70    00000010     UNINITIALIZED
                  20001a70    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCAltControlTableEntry)

.args      0    00004c38    00000008     
                  00004c38    00000008     --HOLE-- [fill = 0]

MODULE SUMMARY

       Module                             code    ro data   rw data
       ------                             ----    -------   -------
    .\
       SoundTX.obj                        336     0         256    
       mainNew.obj                        144     0         12     
    +--+----------------------------------+-------+---------+---------+
       Total:                             480     0         268    
                                                                   
    .\syscfg\
       ti_drivers_config.obj              262     274       910    
       ti_devices_config.obj              0       88        0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             262     362       910    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos/lib/ccs/m4f/nortos_cc13x2.a
       ClockPTimer_nortos.oem4f           922     0         192    
       SwiP_nortos.oem4f                  724     16        74     
       TimerPCC26XX_nortos.oem4f          642     0         45     
       HwiPCC26XX_nortos.oem4f            294     0         220    
       SemaphoreP_nortos.oem4f            410     0         11     
       PowerCC26X2_nortos.oem4f           256     0         4      
       startup_cc13x2_cc26x2_ccs.oem4f    26      216       0      
       QueueP_nortos.oem4f                100     0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             3374    232       546    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/cc13x2_cc26x2/driverlib/bin/ccs/driverlib.lib
       sys_ctrl.obj                       420     0         0      
       osc.obj                            340     0         36     
       setup.obj                          324     0         0      
       interrupt.obj                      74      0         216    
       chipinfo.obj                       250     0         0      
       prcm.obj                           160     0         0      
       timer.obj                          112     0         0      
       aux_sysif.obj                      52      8         0      
       aux_adc.obj                        32      0         0      
       cpu.obj                            30      0         0      
       driverlib_release.obj              0       0         1      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1794    8         253    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/lib/ccs/m4f/drivers_cc13x2.a
       UARTCC26XX.oem4f                   2760    108       0      
       PowerCC26X2.oem4f                  2096    84        368    
       PINCC26XX.oem4f                    1554    0         328    
       ADCBufCC26X2.oem4f                 1542    36        32     
       PowerCC26X2_calibrateRCOSC.oem4f   1400    0         0      
       GPIOCC26XX.oem4f                   660     64        61     
       GPTimerCC26XX.oem4f                670     36        0      
       UART.oem4f                         156     36        1      
       UDMACC26XX.oem4f                   168     0         0      
       RingBuf.oem4f                      152     0         0      
       ADCBuf.oem4f                       78      0         1      
       List.oem4f                         76      0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             11312   364       791    
                                                                   
    C:\ti\ccs1011\ccs\tools\compiler\ti-cgt-arm_20.2.4.LTS\lib\rtsv7M4_T_le_v4SPD16_eabi.lib
       memory.c.obj                       672     0         8      
       memcpy_t2.asm.obj                  156     0         0      
       ull_div_t2.asm.obj                 150     0         0      
       memset_t2.asm.obj                  122     0         0      
       copy_decompress_lzss.c.obj         104     0         0      
       autoinit.c.obj                     68      0         0      
       boot_cortex_m.c.obj                48      0         0      
       args_main.c.obj                    24      0         0      
       ll_mul_t2.asm.obj                  24      0         0      
       copy_decompress_none.c.obj         14      0         0      
       copy_zero_init.c.obj               12      0         0      
       _lock.c.obj                        2       0         8      
       exit.c.obj                         4       0         0      
       pre_init.c.obj                     4       0         0      
       div0.asm.obj                       2       0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1406    0         16     
                                                                   
       Heap:                              0       0         16384  
       Stack:                             0       0         1024   
       Linker Generated:                  0       220       0      
    +--+----------------------------------+-------+---------+---------+
       Grand Total:                       18628   1186      20192  


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004cf8 records: 5, size/record: 8, table size: 40
	.data: load addr=00004c40, load size=00000088 bytes, run addr=20000604, run size=000003e6 bytes, compression=lzss
	.TI.bound:dmaADCAltControlTableEntry: load addr=00004cd8, load size=00000008 bytes, run addr=20001a70, run size=00000010 bytes, compression=zero_init
	.TI.bound:dmaADCPriControlTableEntry: load addr=00004ce0, load size=00000008 bytes, run addr=20001870, run size=00000010 bytes, compression=zero_init
	.bss: load addr=00004ce8, load size=00000008 bytes, run addr=20000000, run size=00000604 bytes, compression=zero_init
	.vtable_ram: load addr=00004cf0, load size=00000008 bytes, run addr=20000a00, run size=000000d8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004cc8 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_lzss
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                                               
-------   ----                                                               
00004bab  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004ba8  ADCBUF_CONST                                                       
00004ba9  ADCBUF_SOUND_CONST                                                 
00004baa  ADCBUF_TEMPERATURE_CONST                                           
000031b5  ADCBufCC26X2_adjustRawValues                                       
00003301  ADCBufCC26X2_close                                                 
00003519  ADCBufCC26X2_control                                               
0000118d  ADCBufCC26X2_convert                                               
00003565  ADCBufCC26X2_convertAdjustedToMicroVolts                           
000039c5  ADCBufCC26X2_convertCancel                                         
00004a6c  ADCBufCC26X2_fxnTable                                              
0000496f  ADCBufCC26X2_getResolution                                         
0000491b  ADCBufCC26X2_init                                                  
0000076d  ADCBufCC26X2_open                                                  
00004bb8  ADCBuf_config                                                      
000048f5  ADCBuf_convertCancel                                               
00004bac  ADCBuf_count                                                       
000037a5  ADCBuf_init                                                        
000049f4  BoardGpioInitTable                                                 
00004315  Board_init                                                         
00000e47  Board_initHook                                                     
00002e69  Board_sendExtFlashByte                                             
00003bb5  Board_shutDownExtFlash                                             
000035ad  Board_wakeUpExtFlash                                               
00004993  C$$EXIT                                                            
00004bb3  CONFIG_GPTIMER_0_CONST                                             
00004bb4  CONFIG_GPTIMER_1_CONST                                             
2000084c  ChirpDelay                                                         
200009bc  ChirpIndex                                                         
200009b8  ChirpSize                                                          
200008c8  ChirpState                                                         
00004785  ClockP_Params_init                                                 
00004335  ClockP_add                                                         
000035f5  ClockP_construct                                                   
000045a3  ClockP_destruct                                                    
00004871  ClockP_doTick                                                      
00004795  ClockP_getCpuFreq                                                  
0000487d  ClockP_getSystemTickPeriod                                         
0000423d  ClockP_getTicks                                                    
00003da9  ClockP_getTicksUntilInterrupt                                      
00004977  ClockP_isActive                                                    
00004261  ClockP_scheduleNextTick                                            
0000497b  ClockP_setTimeout                                                  
00002821  ClockP_start                                                       
00001c69  ClockP_startup                                                     
0000494b  ClockP_stop                                                        
200009cc  ClockP_tickPeriod                                                  
00002a1d  ClockP_walkQueueDynamic                                            
00001d1d  ClockP_workFuncDynamic                                             
00004b2c  GPIOCC26XX_config                                                  
00003f49  GPIO_hwiIntFxn                                                     
000017f9  GPIO_init                                                          
00003f79  GPIO_setCallback                                                   
0000107d  GPIO_setConfig                                                     
00002f95  GPIO_write                                                         
0000474d  GPTimerCC26XX_Params_init                                          
00003fa9  GPTimerCC26XX_close                                                
00004ad0  GPTimerCC26XX_config                                               
00003e11  GPTimerCC26XX_configureDebugStall                                  
000023f1  GPTimerCC26XX_open                                                 
00004923  GPTimerCC26XX_setLoadValue                                         
000033e9  GPTimerCC26XX_start                                                
00003105  GPTimerCC26XX_stop                                                 
00004bb5  GPTimer_count                                                      
0000475f  HwiP_Params_init                                                   
000047a5  HwiP_clearInterrupt                                                
000028a1  HwiP_construct                                                     
000044ad  HwiP_destruct                                                      
000047b5  HwiP_disable                                                       
0000497f  HwiP_enable                                                        
000045bd  HwiP_inISR                                                         
000047c5  HwiP_post                                                          
0000492b  HwiP_restore                                                       
200009e4  HwiP_swiPIntNum                                                    
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
000037a5  InitADC                                                            
00001f21  InitTimer2                                                         
00003b05  InitUart                                                           
000041f1  List_put                                                           
00004217  List_remove                                                        
00004355  NOROM_AUXADCEnableSync                                             
00003e45  NOROM_AUXSYSIFOpModeChange                                         
00004889  NOROM_CPUcpsid                                                     
00004895  NOROM_CPUcpsie                                                     
00004951  NOROM_CPUdelay                                                     
000044c9  NOROM_ChipInfo_GetChipFamily                                       
00002c5d  NOROM_ChipInfo_GetChipType                                         
000037e9  NOROM_ChipInfo_GetHwRevision                                       
000045d5  NOROM_ChipInfo_GetPackageType                                      
00003e79  NOROM_IntRegister                                                  
000046d5  NOROM_IntUnregister                                                
00002479  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002cc9  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
000042a9  NOROM_OSCHF_TurnOnXosc                                             
00003a05  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00003209  NOROM_PRCMPowerDomainsAllOff                                       
00003435  NOROM_PRCMPowerDomainsAllOn                                        
00002199  NOROM_SetupTrimDevice                                              
00003ead  NOROM_SysCtrlIdle                                                  
00002921  NOROM_SysCtrlSetRechargeBeforePowerDown                            
0000138d  NOROM_SysCtrlStandby                                               
0000467d  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00003a45  NOROM_TimerIntRegister                                             
0000497f  NoRTOS_start                                                       
000047d5  PINCC26XX_getPinCount                                              
00004c00  PINCC26XX_hwAttrs                                                  
00003fd9  PINCC26XX_setMux                                                   
00004baf  PIN_DRIVE_SPEAKER_A_CONST                                          
00004bb0  PIN_DRIVE_SPEAKER_B_CONST                                          
00004bad  PIN_TEST1_CONST                                                    
00004bae  PIN_TEST2_CONST                                                    
00002b7d  PIN_add                                                            
00004435  PIN_close                                                          
000000d9  PIN_init                                                           
00001dc9  PIN_open                                                           
000048a1  PIN_registerIntCb                                                  
00002ff5  PIN_remove                                                         
00003bed  PIN_setConfig                                                      
0000363d  PIN_setOutputEnable                                                
00003b41  PIN_setOutputValue                                                 
00003685  PowerCC26X2_RCOSC_clockFunc                                        
00002b09  PowerCC26X2_auxISR                                                 
000046e9  PowerCC26X2_calibrate                                              
00004b40  PowerCC26X2_config                                                 
0000315d  PowerCC26X2_initiateCalibration                                    
20000604  PowerCC26X2_module                                                 
00004555  PowerCC26XX_calibrate                                              
000047e5  PowerCC26XX_schedulerDisable                                       
000048ad  PowerCC26XX_schedulerRestore                                       
00001561  PowerCC26XX_standbyPolicy                                          
000046fd  Power_disablePolicy                                                
000047f5  Power_enablePolicy                                                 
000048b9  Power_getConstraintMask                                            
000048c5  Power_getDependencyCount                                           
00004453  Power_getTransitionLatency                                         
000045ed  Power_idleFunc                                                     
000008e1  Power_init                                                         
00004179  Power_registerNotify                                               
00004009  Power_releaseConstraint                                            
000018bd  Power_releaseDependency                                            
00004039  Power_setConstraint                                                
00001af9  Power_setDependency                                                
000002b9  Power_sleep                                                        
00004395  Power_unregisterNotify                                             
00004845  QueueP_empty                                                       
0000456f  QueueP_get                                                         
00004983  QueueP_head                                                        
00004957  QueueP_init                                                        
00004987  QueueP_next                                                        
000043b5  QueueP_put                                                         
00004853  QueueP_remove                                                      
00004771  RingBuf_construct                                                  
00003a85  RingBuf_get                                                        
0000375d  RingBuf_put                                                        
00004805  SemaphoreP_Params_init                                             
000032b1  SemaphoreP_construct                                               
00004471  SemaphoreP_constructBinary                                         
00004693  SemaphoreP_create                                                  
00004589  SemaphoreP_createBinary                                            
200009b0  SemaphoreP_defaultParams                                           
0000498b  SemaphoreP_delete                                                  
00002f93  SemaphoreP_destruct                                                
00001fc1  SemaphoreP_pend                                                    
00003871  SemaphoreP_post                                                    
00004121  SoundTransmit                                                      
00004815  SwiP_Params_init                                                   
00001721  SwiP_construct                                                     
00003ee1  SwiP_destruct                                                      
00004501  SwiP_disable                                                       
00002061  SwiP_dispatch                                                      
000048d1  SwiP_getTrigger                                                    
0000448f  SwiP_or                                                            
00002d31  SwiP_post                                                          
000038b5  SwiP_restore                                                       
00002699  Timer2AInterruptHandler                                            
00004825  TimerP_Params_init                                                 
00001a3d  TimerP_construct                                                   
000043d5  TimerP_dynamicStub                                                 
00004725  TimerP_getCount64                                                  
00003c25  TimerP_getCurrentTick                                              
000048dd  TimerP_getFreq                                                     
000042cd  TimerP_getMaxTicks                                                 
00003c5d  TimerP_initDevice                                                  
00003d3d  TimerP_setNextTick                                                 
0000414d  TimerP_setThreshold                                                
00002d99  TimerP_start                                                       
00004605  TimerP_startup                                                     
0000271d  UARTCC26XX_close                                                   
00002ecd  UARTCC26XX_control                                                 
00004a1c  UARTCC26XX_fxnTable                                                
00000bb9  UARTCC26XX_hwiIntFxn                                               
00004933  UARTCC26XX_init                                                    
00000a55  UARTCC26XX_open                                                    
00000d0d  UARTCC26XX_read                                                    
00003481  UARTCC26XX_readCancel                                              
0000495d  UARTCC26XX_readPolling                                             
00002f31  UARTCC26XX_swiIntFxn                                               
00001641  UARTCC26XX_write                                                   
0000235f  UARTCC26XX_writeCancel                                             
00004963  UARTCC26XX_writePolling                                            
00004bb1  UART_0_CONST                                                       
0000461d  UART_Params_init                                                   
00004bc4  UART_config                                                        
00004bb2  UART_count                                                         
00004a90  UART_defaultParams                                                 
000038f9  UART_init                                                          
00003ac5  UART_open                                                          
00003d73  UDMACC26XX_close                                                   
00004c08  UDMACC26XX_config                                                  
000048ff  UDMACC26XX_hwiIntFxn                                               
000034cd  UDMACC26XX_open                                                    
20014000  __STACK_END                                                        
00000400  __STACK_SIZE                                                       
00004000  __SYSMEM_SIZE                                                      
00004cf8  __TI_CINIT_Base                                                    
00004d20  __TI_CINIT_Limit                                                   
00004cc8  __TI_Handler_Table_Base                                            
00004cd4  __TI_Handler_Table_Limit                                           
0000393d  __TI_auto_init_nobinit_nopinit                                     
00002e01  __TI_decompress_lzss                                               
00004861  __TI_decompress_none                                               
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
00000000  __TI_static_base__                                                 
000048e9  __TI_zero_init                                                     
00004783  __aeabi_idiv0                                                      
00004783  __aeabi_ldiv0                                                      
0000464d  __aeabi_lmul                                                       
000029a1  __aeabi_memclr                                                     
000029a1  __aeabi_memclr4                                                    
000029a1  __aeabi_memclr8                                                    
000020fd  __aeabi_memcpy                                                     
000020fd  __aeabi_memcpy4                                                    
000020fd  __aeabi_memcpy8                                                    
000029a3  __aeabi_memset                                                     
000029a3  __aeabi_memset4                                                    
000029a3  __aeabi_memset8                                                    
000022c9  __aeabi_uldivmod                                                   
ffffffff  __binit__                                                          
00004c38  __c_args__                                                         
00057fa8  __ccfg                                                             
UNDEFED   __mpu_init                                                         
20013c00  __stack                                                            
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
00004635  _args_main                                                         
00004099  _c_int00                                                           
20000944  _hposcCoeffs                                                       
200009a0  _lock                                                              
0000334f  _nop                                                               
20001a80  _sys_memory                                                        
UNDEFED   _system_post_cinit                                                 
0000498f  _system_pre_init                                                   
200009a4  _unlock                                                            
00004993  abort                                                              
200009d0  adcBufCC26XXChannelLut0                                            
00004b78  adcbufCC26XXHWAttrs                                                
200001b4  adcbufCC26XXbjects                                                 
0000128d  aligned_alloc                                                      
ffffffff  binit                                                              
00003563  clkFxn                                                             
200005c9  driverlib_release_0_59848                                          
0000147d  free                                                               
20000a00  g_pfnRAMVectors                                                    
00000000  g_pfnVectors                                                       
20000968  gpioCallbackFunctions                                              
20000978  gpioPinConfigs                                                     
00004ae8  gptimerCC26XXHWAttrs                                               
20000330  gptimerCC26XXObjects                                               
200005f8  i                                                                  
00004bd0  inPinTypes                                                         
200005fc  j                                                                  
0000325d  main                                                               
0000493b  malloc                                                             
0000128d  memalign                                                           
000020fd  memcpy                                                             
000029a9  memset                                                             
00004bdc  outPinStrengths                                                    
00004b98  outPinTypes                                                        
200003c8  pinHandleTable                                                     
200009c4  pinLowerBound                                                      
200009c0  pinUpperBound                                                      
00004739  resetISR                                                           
000049a0  resourceDB                                                         
20000600  uart                                                               
20000000  uartCC26XXObjects                                                  
00004c18  udmaCC26XXHWAttrs                                                  
20000548  udmaCC26XXObject                                                   


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                                               
-------   ----                                                               
00000000  __TI_static_base__                                                 
00000000  g_pfnVectors                                                       
000000d9  PIN_init                                                           
000002b9  Power_sleep                                                        
00000400  __STACK_SIZE                                                       
0000076d  ADCBufCC26X2_open                                                  
000008e1  Power_init                                                         
00000a55  UARTCC26XX_open                                                    
00000bb9  UARTCC26XX_hwiIntFxn                                               
00000d0d  UARTCC26XX_read                                                    
00000e47  Board_initHook                                                     
0000107d  GPIO_setConfig                                                     
0000118d  ADCBufCC26X2_convert                                               
0000128d  aligned_alloc                                                      
0000128d  memalign                                                           
0000138d  NOROM_SysCtrlStandby                                               
0000147d  free                                                               
00001561  PowerCC26XX_standbyPolicy                                          
00001641  UARTCC26XX_write                                                   
00001721  SwiP_construct                                                     
000017f9  GPIO_init                                                          
000018bd  Power_releaseDependency                                            
00001a3d  TimerP_construct                                                   
00001af9  Power_setDependency                                                
00001c69  ClockP_startup                                                     
00001d1d  ClockP_workFuncDynamic                                             
00001dc9  PIN_open                                                           
00001f21  InitTimer2                                                         
00001fc1  SemaphoreP_pend                                                    
00002061  SwiP_dispatch                                                      
000020fd  __aeabi_memcpy                                                     
000020fd  __aeabi_memcpy4                                                    
000020fd  __aeabi_memcpy8                                                    
000020fd  memcpy                                                             
00002199  NOROM_SetupTrimDevice                                              
000022c9  __aeabi_uldivmod                                                   
0000235f  UARTCC26XX_writeCancel                                             
000023f1  GPTimerCC26XX_open                                                 
00002479  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002699  Timer2AInterruptHandler                                            
0000271d  UARTCC26XX_close                                                   
00002821  ClockP_start                                                       
000028a1  HwiP_construct                                                     
00002921  NOROM_SysCtrlSetRechargeBeforePowerDown                            
000029a1  __aeabi_memclr                                                     
000029a1  __aeabi_memclr4                                                    
000029a1  __aeabi_memclr8                                                    
000029a3  __aeabi_memset                                                     
000029a3  __aeabi_memset4                                                    
000029a3  __aeabi_memset8                                                    
000029a9  memset                                                             
00002a1d  ClockP_walkQueueDynamic                                            
00002b09  PowerCC26X2_auxISR                                                 
00002b7d  PIN_add                                                            
00002c5d  NOROM_ChipInfo_GetChipType                                         
00002cc9  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
00002d31  SwiP_post                                                          
00002d99  TimerP_start                                                       
00002e01  __TI_decompress_lzss                                               
00002e69  Board_sendExtFlashByte                                             
00002ecd  UARTCC26XX_control                                                 
00002f31  UARTCC26XX_swiIntFxn                                               
00002f93  SemaphoreP_destruct                                                
00002f95  GPIO_write                                                         
00002ff5  PIN_remove                                                         
00003105  GPTimerCC26XX_stop                                                 
0000315d  PowerCC26X2_initiateCalibration                                    
000031b5  ADCBufCC26X2_adjustRawValues                                       
00003209  NOROM_PRCMPowerDomainsAllOff                                       
0000325d  main                                                               
000032b1  SemaphoreP_construct                                               
00003301  ADCBufCC26X2_close                                                 
0000334f  _nop                                                               
000033e9  GPTimerCC26XX_start                                                
00003435  NOROM_PRCMPowerDomainsAllOn                                        
00003481  UARTCC26XX_readCancel                                              
000034cd  UDMACC26XX_open                                                    
00003519  ADCBufCC26X2_control                                               
00003563  clkFxn                                                             
00003565  ADCBufCC26X2_convertAdjustedToMicroVolts                           
000035ad  Board_wakeUpExtFlash                                               
000035f5  ClockP_construct                                                   
0000363d  PIN_setOutputEnable                                                
00003685  PowerCC26X2_RCOSC_clockFunc                                        
0000375d  RingBuf_put                                                        
000037a5  ADCBuf_init                                                        
000037a5  InitADC                                                            
000037e9  NOROM_ChipInfo_GetHwRevision                                       
00003871  SemaphoreP_post                                                    
000038b5  SwiP_restore                                                       
000038f9  UART_init                                                          
0000393d  __TI_auto_init_nobinit_nopinit                                     
000039c5  ADCBufCC26X2_convertCancel                                         
00003a05  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00003a45  NOROM_TimerIntRegister                                             
00003a85  RingBuf_get                                                        
00003ac5  UART_open                                                          
00003b05  InitUart                                                           
00003b41  PIN_setOutputValue                                                 
00003bb5  Board_shutDownExtFlash                                             
00003bed  PIN_setConfig                                                      
00003c25  TimerP_getCurrentTick                                              
00003c5d  TimerP_initDevice                                                  
00003d3d  TimerP_setNextTick                                                 
00003d73  UDMACC26XX_close                                                   
00003da9  ClockP_getTicksUntilInterrupt                                      
00003e11  GPTimerCC26XX_configureDebugStall                                  
00003e45  NOROM_AUXSYSIFOpModeChange                                         
00003e79  NOROM_IntRegister                                                  
00003ead  NOROM_SysCtrlIdle                                                  
00003ee1  SwiP_destruct                                                      
00003f49  GPIO_hwiIntFxn                                                     
00003f79  GPIO_setCallback                                                   
00003fa9  GPTimerCC26XX_close                                                
00003fd9  PINCC26XX_setMux                                                   
00004000  __SYSMEM_SIZE                                                      
00004009  Power_releaseConstraint                                            
00004039  Power_setConstraint                                                
00004099  _c_int00                                                           
00004121  SoundTransmit                                                      
0000414d  TimerP_setThreshold                                                
00004179  Power_registerNotify                                               
000041f1  List_put                                                           
00004217  List_remove                                                        
0000423d  ClockP_getTicks                                                    
00004261  ClockP_scheduleNextTick                                            
000042a9  NOROM_OSCHF_TurnOnXosc                                             
000042cd  TimerP_getMaxTicks                                                 
00004315  Board_init                                                         
00004335  ClockP_add                                                         
00004355  NOROM_AUXADCEnableSync                                             
00004395  Power_unregisterNotify                                             
000043b5  QueueP_put                                                         
000043d5  TimerP_dynamicStub                                                 
00004435  PIN_close                                                          
00004453  Power_getTransitionLatency                                         
00004471  SemaphoreP_constructBinary                                         
0000448f  SwiP_or                                                            
000044ad  HwiP_destruct                                                      
000044c9  NOROM_ChipInfo_GetChipFamily                                       
00004501  SwiP_disable                                                       
00004555  PowerCC26XX_calibrate                                              
0000456f  QueueP_get                                                         
00004589  SemaphoreP_createBinary                                            
000045a3  ClockP_destruct                                                    
000045bd  HwiP_inISR                                                         
000045d5  NOROM_ChipInfo_GetPackageType                                      
000045ed  Power_idleFunc                                                     
00004605  TimerP_startup                                                     
0000461d  UART_Params_init                                                   
00004635  _args_main                                                         
0000464d  __aeabi_lmul                                                       
0000467d  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00004693  SemaphoreP_create                                                  
000046d5  NOROM_IntUnregister                                                
000046e9  PowerCC26X2_calibrate                                              
000046fd  Power_disablePolicy                                                
00004725  TimerP_getCount64                                                  
00004739  resetISR                                                           
0000474d  GPTimerCC26XX_Params_init                                          
0000475f  HwiP_Params_init                                                   
00004771  RingBuf_construct                                                  
00004783  __aeabi_idiv0                                                      
00004783  __aeabi_ldiv0                                                      
00004785  ClockP_Params_init                                                 
00004795  ClockP_getCpuFreq                                                  
000047a5  HwiP_clearInterrupt                                                
000047b5  HwiP_disable                                                       
000047c5  HwiP_post                                                          
000047d5  PINCC26XX_getPinCount                                              
000047e5  PowerCC26XX_schedulerDisable                                       
000047f5  Power_enablePolicy                                                 
00004805  SemaphoreP_Params_init                                             
00004815  SwiP_Params_init                                                   
00004825  TimerP_Params_init                                                 
00004845  QueueP_empty                                                       
00004853  QueueP_remove                                                      
00004861  __TI_decompress_none                                               
00004871  ClockP_doTick                                                      
0000487d  ClockP_getSystemTickPeriod                                         
00004889  NOROM_CPUcpsid                                                     
00004895  NOROM_CPUcpsie                                                     
000048a1  PIN_registerIntCb                                                  
000048ad  PowerCC26XX_schedulerRestore                                       
000048b9  Power_getConstraintMask                                            
000048c5  Power_getDependencyCount                                           
000048d1  SwiP_getTrigger                                                    
000048dd  TimerP_getFreq                                                     
000048e9  __TI_zero_init                                                     
000048f5  ADCBuf_convertCancel                                               
000048ff  UDMACC26XX_hwiIntFxn                                               
0000491b  ADCBufCC26X2_init                                                  
00004923  GPTimerCC26XX_setLoadValue                                         
0000492b  HwiP_restore                                                       
00004933  UARTCC26XX_init                                                    
0000493b  malloc                                                             
0000494b  ClockP_stop                                                        
00004951  NOROM_CPUdelay                                                     
00004957  QueueP_init                                                        
0000495d  UARTCC26XX_readPolling                                             
00004963  UARTCC26XX_writePolling                                            
0000496f  ADCBufCC26X2_getResolution                                         
00004977  ClockP_isActive                                                    
0000497b  ClockP_setTimeout                                                  
0000497f  HwiP_enable                                                        
0000497f  NoRTOS_start                                                       
00004983  QueueP_head                                                        
00004987  QueueP_next                                                        
0000498b  SemaphoreP_delete                                                  
0000498f  _system_pre_init                                                   
00004993  C$$EXIT                                                            
00004993  abort                                                              
000049a0  resourceDB                                                         
000049f4  BoardGpioInitTable                                                 
00004a1c  UARTCC26XX_fxnTable                                                
00004a6c  ADCBufCC26X2_fxnTable                                              
00004a90  UART_defaultParams                                                 
00004ad0  GPTimerCC26XX_config                                               
00004ae8  gptimerCC26XXHWAttrs                                               
00004b2c  GPIOCC26XX_config                                                  
00004b40  PowerCC26X2_config                                                 
00004b78  adcbufCC26XXHWAttrs                                                
00004b98  outPinTypes                                                        
00004ba8  ADCBUF_CONST                                                       
00004ba9  ADCBUF_SOUND_CONST                                                 
00004baa  ADCBUF_TEMPERATURE_CONST                                           
00004bab  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004bac  ADCBuf_count                                                       
00004bad  PIN_TEST1_CONST                                                    
00004bae  PIN_TEST2_CONST                                                    
00004baf  PIN_DRIVE_SPEAKER_A_CONST                                          
00004bb0  PIN_DRIVE_SPEAKER_B_CONST                                          
00004bb1  UART_0_CONST                                                       
00004bb2  UART_count                                                         
00004bb3  CONFIG_GPTIMER_0_CONST                                             
00004bb4  CONFIG_GPTIMER_1_CONST                                             
00004bb5  GPTimer_count                                                      
00004bb8  ADCBuf_config                                                      
00004bc4  UART_config                                                        
00004bd0  inPinTypes                                                         
00004bdc  outPinStrengths                                                    
00004c00  PINCC26XX_hwAttrs                                                  
00004c08  UDMACC26XX_config                                                  
00004c18  udmaCC26XXHWAttrs                                                  
00004c38  __c_args__                                                         
00004cc8  __TI_Handler_Table_Base                                            
00004cd4  __TI_Handler_Table_Limit                                           
00004cf8  __TI_CINIT_Base                                                    
00004d20  __TI_CINIT_Limit                                                   
00057fa8  __ccfg                                                             
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
20000000  uartCC26XXObjects                                                  
200001b4  adcbufCC26XXbjects                                                 
20000330  gptimerCC26XXObjects                                               
200003c8  pinHandleTable                                                     
20000548  udmaCC26XXObject                                                   
200005c9  driverlib_release_0_59848                                          
200005f8  i                                                                  
200005fc  j                                                                  
20000600  uart                                                               
20000604  PowerCC26X2_module                                                 
2000084c  ChirpDelay                                                         
200008c8  ChirpState                                                         
20000944  _hposcCoeffs                                                       
20000968  gpioCallbackFunctions                                              
20000978  gpioPinConfigs                                                     
200009a0  _lock                                                              
200009a4  _unlock                                                            
200009b0  SemaphoreP_defaultParams                                           
200009b8  ChirpSize                                                          
200009bc  ChirpIndex                                                         
200009c0  pinUpperBound                                                      
200009c4  pinLowerBound                                                      
200009cc  ClockP_tickPeriod                                                  
200009d0  adcBufCC26XXChannelLut0                                            
200009e4  HwiP_swiPIntNum                                                    
20000a00  g_pfnRAMVectors                                                    
20001a80  _sys_memory                                                        
20013c00  __stack                                                            
20014000  __STACK_END                                                        
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
ffffffff  __binit__                                                          
ffffffff  binit                                                              
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
UNDEFED   __mpu_init                                                         
UNDEFED   _system_post_cinit                                                 

[290 symbols]
