#include <ChirpGenerator.h>
#include <AudioUtils.h>
#include <cassert>
#include <cmath>

void testChirp() {
    ChirpGenerator gen;
    const int16_t* chirp = gen.generate();

    // Verify properties
    assert(gen.size() == CHIRP_TOTAL);  // Should be 1440 samples at 48kHz for 30ms
    assert(gen.size() == 1440);         // Explicit check for expected sample count

    // Check that chirp starts with low amplitude (fade-in)
    assert(abs(chirp[0]) == 0);         // Should start at zero due to fade-in

    // Check that middle samples have reasonable amplitude
    assert(abs(chirp[gen.size()/2]) > 15000);  // Middle should have good amplitude

    // Check that chirp ends with low amplitude (fade-out)
    assert(abs(chirp[gen.size()-1]) < 5000);   // Should end quiet due to fade-out

    // Verify sample rate and duration calculations
    assert(SAMPLE_RATE == 48000);
    assert(CHIRP_TOTAL == 1440);
    assert(F_START == 1000.0f);
    assert(F_END == 8000.0f);
}

int main() {
    testChirp();
    return 0;
}