<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: BluetoothA2DPSink Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="#pro-attribs">Protected Attributes</a> &#124;
<a href="#friends">Friends</a> &#124;
<a href="class_bluetooth_a2_d_p_sink-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">BluetoothA2DPSink Class Reference<div class="ingroups"><a class="el" href="group__a2dp.html">ESP32 A2DP</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example <a href="https://github.com/espressif/esp-idf/tree/master/examples/bluetooth/bluedroid/classic_bt/a2dp_sink">https://github.com/espressif/esp-idf/tree/master/examples/bluetooth/bluedroid/classic_bt/a2dp_sink</a> was refactered into a C++ class.  
 <a href="class_bluetooth_a2_d_p_sink.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_bluetooth_a2_d_p_sink_8h_source.html">BluetoothA2DPSink.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for BluetoothA2DPSink:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_bluetooth_a2_d_p_sink.png" usemap="#BluetoothA2DPSink_map" alt=""/>
  <map id="BluetoothA2DPSink_map" name="BluetoothA2DPSink_map">
<area href="class_bluetooth_a2_d_p_common.html" title="Common Bluetooth A2DP functions." alt="BluetoothA2DPCommon" shape="rect" coords="0,0,166,24"/>
<area href="class_bluetooth_a2_d_p_sink_queued.html" title="The BluetoothA2DPSinkQueued is using a separate Task with an additinal Queue to write the I2S data...." alt="BluetoothA2DPSinkQueued" shape="rect" coords="0,112,166,136"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a2a383635d7b050833f56ee79867716bd"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a2a383635d7b050833f56ee79867716bd">BluetoothA2DPSink</a> ()</td></tr>
<tr class="memdesc:a2a383635d7b050833f56ee79867716bd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default Constructor: output via callback or Legacy I2S.  <a href="class_bluetooth_a2_d_p_sink.html#a2a383635d7b050833f56ee79867716bd">More...</a><br /></td></tr>
<tr class="separator:a2a383635d7b050833f56ee79867716bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9c47257f9af0d64008fd46e201fb9063"><td class="memItemLeft" align="right" valign="top"><a id="a9c47257f9af0d64008fd46e201fb9063"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a9c47257f9af0d64008fd46e201fb9063">BluetoothA2DPSink</a> (audio_tools::AudioOutput &amp;output)</td></tr>
<tr class="memdesc:a9c47257f9af0d64008fd46e201fb9063"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output AudioOutput using AudioTools library. <br /></td></tr>
<tr class="separator:a9c47257f9af0d64008fd46e201fb9063"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62e0cb5c7a6552581ad43fdd98c4a022"><td class="memItemLeft" align="right" valign="top"><a id="a62e0cb5c7a6552581ad43fdd98c4a022"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a62e0cb5c7a6552581ad43fdd98c4a022">BluetoothA2DPSink</a> (audio_tools::AudioStream &amp;output)</td></tr>
<tr class="memdesc:a62e0cb5c7a6552581ad43fdd98c4a022"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output AudioStream using AudioTools library. <br /></td></tr>
<tr class="separator:a62e0cb5c7a6552581ad43fdd98c4a022"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aadfb815b992649822db74d3e2780d4c8"><td class="memItemLeft" align="right" valign="top"><a id="aadfb815b992649822db74d3e2780d4c8"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aadfb815b992649822db74d3e2780d4c8">BluetoothA2DPSink</a> (<a class="el" href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a> &amp;out)</td></tr>
<tr class="memdesc:aadfb815b992649822db74d3e2780d4c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define output scenario class. <br /></td></tr>
<tr class="separator:aadfb815b992649822db74d3e2780d4c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7acf882642fea0df0c36847be134ec6f"><td class="memItemLeft" align="right" valign="top"><a id="a7acf882642fea0df0c36847be134ec6f"></a>
&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a7acf882642fea0df0c36847be134ec6f">BluetoothA2DPSink</a> (Print &amp;output)</td></tr>
<tr class="memdesc:a7acf882642fea0df0c36847be134ec6f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output to Arduino Print. <br /></td></tr>
<tr class="separator:a7acf882642fea0df0c36847be134ec6f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0f83dea1a97baeb360e4e1221c0aeaa9"><td class="memItemLeft" align="right" valign="top"><a id="a0f83dea1a97baeb360e4e1221c0aeaa9"></a>
virtual&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a0f83dea1a97baeb360e4e1221c0aeaa9">~BluetoothA2DPSink</a> ()</td></tr>
<tr class="memdesc:a0f83dea1a97baeb360e4e1221c0aeaa9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Destructor - stops the playback and releases all resources. <br /></td></tr>
<tr class="separator:a0f83dea1a97baeb360e4e1221c0aeaa9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a22d52952a8ac8c78a483a53c2006a387"><td class="memItemLeft" align="right" valign="top"><a id="a22d52952a8ac8c78a483a53c2006a387"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a22d52952a8ac8c78a483a53c2006a387">activate_pin_code</a> (bool active)</td></tr>
<tr class="memdesc:a22d52952a8ac8c78a483a53c2006a387"><td class="mdescLeft">&#160;</td><td class="mdescRight">We need to confirm a new seesion by calling <a class="el" href="class_bluetooth_a2_d_p_sink.html#a5d4707195d0d6e79b65bef4ed48a57c2" title="confirms the connection request by returning the receivedn pin code">confirm_pin_code()</a> <br /></td></tr>
<tr class="separator:a22d52952a8ac8c78a483a53c2006a387"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a962cc9aef396b06c7eb6f56462a743ac"><td class="memItemLeft" align="right" valign="top"><a id="a962cc9aef396b06c7eb6f56462a743ac"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a962cc9aef396b06c7eb6f56462a743ac">clean_last_connection</a> ()</td></tr>
<tr class="memdesc:a962cc9aef396b06c7eb6f56462a743ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">clean last connection (delete) <br /></td></tr>
<tr class="separator:a962cc9aef396b06c7eb6f56462a743ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d4707195d0d6e79b65bef4ed48a57c2"><td class="memItemLeft" align="right" valign="top"><a id="a5d4707195d0d6e79b65bef4ed48a57c2"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5d4707195d0d6e79b65bef4ed48a57c2">confirm_pin_code</a> ()</td></tr>
<tr class="memdesc:a5d4707195d0d6e79b65bef4ed48a57c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">confirms the connection request by returning the receivedn pin code <br /></td></tr>
<tr class="separator:a5d4707195d0d6e79b65bef4ed48a57c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a43369961e9858cf99798e9c1b6a634b9"><td class="memItemLeft" align="right" valign="top"><a id="a43369961e9858cf99798e9c1b6a634b9"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a43369961e9858cf99798e9c1b6a634b9">confirm_pin_code</a> (int code)</td></tr>
<tr class="memdesc:a43369961e9858cf99798e9c1b6a634b9"><td class="mdescLeft">&#160;</td><td class="mdescRight">confirms the connection request by returning the indicated pin code <br /></td></tr>
<tr class="separator:a43369961e9858cf99798e9c1b6a634b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a788d81fe538021f912d737de72ed6be6"><td class="memItemLeft" align="right" valign="top"><a id="a788d81fe538021f912d737de72ed6be6"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a788d81fe538021f912d737de72ed6be6">connect_to</a> (<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> peer)</td></tr>
<tr class="memdesc:a788d81fe538021f912d737de72ed6be6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Connnects to the indicated address. <br /></td></tr>
<tr class="separator:a788d81fe538021f912d737de72ed6be6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6601d3c57e37f77bfdd03a3ef6231e2"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#aa6601d3c57e37f77bfdd03a3ef6231e2">debounce</a> (void(*cb)(void), int ms)</td></tr>
<tr class="separator:aa6601d3c57e37f77bfdd03a3ef6231e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2302fff324e703c3906835f759e87307"><td class="memItemLeft" align="right" valign="top"><a id="a2302fff324e703c3906835f759e87307"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a2302fff324e703c3906835f759e87307">delay_ms</a> (uint32_t millis)</td></tr>
<tr class="memdesc:a2302fff324e703c3906835f759e87307"><td class="mdescLeft">&#160;</td><td class="mdescRight">calls vTaskDelay to pause for the indicated number of milliseconds <br /></td></tr>
<tr class="separator:a2302fff324e703c3906835f759e87307"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab63e627832d6377be32dd700130bf0d8"><td class="memItemLeft" align="right" valign="top"><a id="ab63e627832d6377be32dd700130bf0d8"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#ab63e627832d6377be32dd700130bf0d8">disconnect</a> ()</td></tr>
<tr class="memdesc:ab63e627832d6377be32dd700130bf0d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Closes the connection. <br /></td></tr>
<tr class="separator:ab63e627832d6377be32dd700130bf0d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a91e49987a2e39c09fc6c2a64feaed6"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5a91e49987a2e39c09fc6c2a64feaed6">end</a> (bool release_memory=false)</td></tr>
<tr class="separator:a5a91e49987a2e39c09fc6c2a64feaed6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42e01689353026b1ef9883fd5d32f00c"><td class="memItemLeft" align="right" valign="top"><a id="a42e01689353026b1ef9883fd5d32f00c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a42e01689353026b1ef9883fd5d32f00c">fast_forward</a> ()</td></tr>
<tr class="memdesc:a42e01689353026b1ef9883fd5d32f00c"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC fast_forward. <br /></td></tr>
<tr class="separator:a42e01689353026b1ef9883fd5d32f00c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74eadbd69b5c7adf1b190c7e41b75b10"><td class="memItemLeft" align="right" valign="top"><a id="a74eadbd69b5c7adf1b190c7e41b75b10"></a>
virtual <a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a74eadbd69b5c7adf1b190c7e41b75b10">get_audio_state</a> ()</td></tr>
<tr class="memdesc:a74eadbd69b5c7adf1b190c7e41b75b10"><td class="mdescLeft">&#160;</td><td class="mdescRight">Determine the actual audio state. <br /></td></tr>
<tr class="separator:a74eadbd69b5c7adf1b190c7e41b75b10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77600cb1e36b7814eb9b4126cdec62d4"><td class="memItemLeft" align="right" valign="top"><a id="a77600cb1e36b7814eb9b4126cdec62d4"></a>
virtual esp_a2d_mct_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a77600cb1e36b7814eb9b4126cdec62d4">get_audio_type</a> ()</td></tr>
<tr class="memdesc:a77600cb1e36b7814eb9b4126cdec62d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Determine the actual audio type. <br /></td></tr>
<tr class="separator:a77600cb1e36b7814eb9b4126cdec62d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a513b32676d8fc248bb481180f832ef97"><td class="memItemLeft" align="right" valign="top"><a id="a513b32676d8fc248bb481180f832ef97"></a>
virtual <a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a513b32676d8fc248bb481180f832ef97">get_connection_state</a> ()</td></tr>
<tr class="memdesc:a513b32676d8fc248bb481180f832ef97"><td class="mdescLeft">&#160;</td><td class="mdescRight">Determine the connection state. <br /></td></tr>
<tr class="separator:a513b32676d8fc248bb481180f832ef97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac08fff859e0ccfbb12cbb6b119dba438"><td class="memItemLeft" align="right" valign="top"><a id="ac08fff859e0ccfbb12cbb6b119dba438"></a>
virtual <a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ac08fff859e0ccfbb12cbb6b119dba438">get_current_peer_address</a> ()</td></tr>
<tr class="memdesc:ac08fff859e0ccfbb12cbb6b119dba438"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the address of the connected device. <br /></td></tr>
<tr class="separator:ac08fff859e0ccfbb12cbb6b119dba438"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac21e1dbd2f5f475da871a7e778ba1a40"><td class="memItemLeft" align="right" valign="top"><a id="ac21e1dbd2f5f475da871a7e778ba1a40"></a>
virtual <a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#ac21e1dbd2f5f475da871a7e778ba1a40">get_last_peer_address</a> ()</td></tr>
<tr class="memdesc:ac21e1dbd2f5f475da871a7e778ba1a40"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the address of the last device. <br /></td></tr>
<tr class="separator:ac21e1dbd2f5f475da871a7e778ba1a40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a770be98d977a8df916d8cc044b310c"><td class="memItemLeft" align="right" valign="top"><a id="a5a770be98d977a8df916d8cc044b310c"></a>
esp_bt_gap_cb_param_t::read_rssi_delta_param&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5a770be98d977a8df916d8cc044b310c">get_last_rssi</a> ()</td></tr>
<tr class="memdesc:a5a770be98d977a8df916d8cc044b310c"><td class="mdescLeft">&#160;</td><td class="mdescRight">provides the last rssi parameters <br /></td></tr>
<tr class="separator:a5a770be98d977a8df916d8cc044b310c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a688bfd727bfed94f255b63c16a6b1b3c"><td class="memItemLeft" align="right" valign="top"><a id="a688bfd727bfed94f255b63c16a6b1b3c"></a>
unsigned long&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a688bfd727bfed94f255b63c16a6b1b3c">get_millis</a> ()</td></tr>
<tr class="memdesc:a688bfd727bfed94f255b63c16a6b1b3c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the time in milliseconds since the last system boot. <br /></td></tr>
<tr class="separator:a688bfd727bfed94f255b63c16a6b1b3c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a726f45e4d405f5c5f5b259f11aaf8246"><td class="memItemLeft" align="right" valign="top"><a id="a726f45e4d405f5c5f5b259f11aaf8246"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a726f45e4d405f5c5f5b259f11aaf8246">get_name</a> ()</td></tr>
<tr class="memdesc:a726f45e4d405f5c5f5b259f11aaf8246"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the actual SSID name. <br /></td></tr>
<tr class="separator:a726f45e4d405f5c5f5b259f11aaf8246"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c88745461503e5b95f26e41f409e428"><td class="memItemLeft" align="right" valign="top"><a id="a1c88745461503e5b95f26e41f409e428"></a>
<a class="el" href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a1c88745461503e5b95f26e41f409e428">get_output</a> ()</td></tr>
<tr class="memdesc:a1c88745461503e5b95f26e41f409e428"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides access to the output class. <br /></td></tr>
<tr class="separator:a1c88745461503e5b95f26e41f409e428"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac9adc3ca64e68fb3d6e816698a725dd5"><td class="memItemLeft" align="right" valign="top"><a id="ac9adc3ca64e68fb3d6e816698a725dd5"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ac9adc3ca64e68fb3d6e816698a725dd5">get_peer_name</a> ()</td></tr>
<tr class="memdesc:ac9adc3ca64e68fb3d6e816698a725dd5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the name of the connected source device. <br /></td></tr>
<tr class="separator:ac9adc3ca64e68fb3d6e816698a725dd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aca1119f20d2321fb950ae859000cce7b"><td class="memItemLeft" align="right" valign="top"><a id="aca1119f20d2321fb950ae859000cce7b"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aca1119f20d2321fb950ae859000cce7b">get_volume</a> ()</td></tr>
<tr class="memdesc:aca1119f20d2321fb950ae859000cce7b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Determines the volume. <br /></td></tr>
<tr class="separator:aca1119f20d2321fb950ae859000cce7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a458dc625cbceb5e534b07094136f6533"><td class="memItemLeft" align="right" valign="top"><a id="a458dc625cbceb5e534b07094136f6533"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a458dc625cbceb5e534b07094136f6533">is_avrc_connected</a> ()</td></tr>
<tr class="memdesc:a458dc625cbceb5e534b07094136f6533"><td class="mdescLeft">&#160;</td><td class="mdescRight">returns true if the avrc service is connected <br /></td></tr>
<tr class="separator:a458dc625cbceb5e534b07094136f6533"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af5d6399876738c8fa0766ea247476b3f"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af5d6399876738c8fa0766ea247476b3f">is_avrc_peer_rn_cap</a> (<a class="el" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a> cmd)</td></tr>
<tr class="separator:af5d6399876738c8fa0766ea247476b3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8281ab4339dd58c49d1d651dd74f5711"><td class="memItemLeft" align="right" valign="top"><a id="a8281ab4339dd58c49d1d651dd74f5711"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a8281ab4339dd58c49d1d651dd74f5711">is_avrc_peer_rn_cap_available</a> ()</td></tr>
<tr class="memdesc:a8281ab4339dd58c49d1d651dd74f5711"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the <a class="el" href="class_bluetooth_a2_d_p_sink.html#af5d6399876738c8fa0766ea247476b3f">is_avrc_peer_rn_cap()</a> method can be called. <br /></td></tr>
<tr class="separator:a8281ab4339dd58c49d1d651dd74f5711"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5e76412770515732e3f54275decf02f0"><td class="memItemLeft" align="right" valign="top"><a id="a5e76412770515732e3f54275decf02f0"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a5e76412770515732e3f54275decf02f0">is_connected</a> ()</td></tr>
<tr class="memdesc:a5e76412770515732e3f54275decf02f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if A2DP is connected. <br /></td></tr>
<tr class="separator:a5e76412770515732e3f54275decf02f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6eb9f31e643607224fa784ff20654924"><td class="memItemLeft" align="right" valign="top"><a id="a6eb9f31e643607224fa784ff20654924"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a6eb9f31e643607224fa784ff20654924">is_output_active</a> ()</td></tr>
<tr class="memdesc:a6eb9f31e643607224fa784ff20654924"><td class="mdescLeft">&#160;</td><td class="mdescRight">Checks if output is active. <br /></td></tr>
<tr class="separator:a6eb9f31e643607224fa784ff20654924"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a791432e5c800e75fb11b858071cff651"><td class="memItemLeft" align="right" valign="top"><a id="a791432e5c800e75fb11b858071cff651"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a791432e5c800e75fb11b858071cff651">log_free_heap</a> ()</td></tr>
<tr class="memdesc:a791432e5c800e75fb11b858071cff651"><td class="mdescLeft">&#160;</td><td class="mdescRight">Logs the free heap. <br /></td></tr>
<tr class="separator:a791432e5c800e75fb11b858071cff651"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a296fb7aaf8d8e78991d9d505353de94f"><td class="memItemLeft" align="right" valign="top"><a id="a296fb7aaf8d8e78991d9d505353de94f"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a296fb7aaf8d8e78991d9d505353de94f">next</a> ()</td></tr>
<tr class="memdesc:a296fb7aaf8d8e78991d9d505353de94f"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC next. <br /></td></tr>
<tr class="separator:a296fb7aaf8d8e78991d9d505353de94f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6967e9329939596c62f16e8686cac13"><td class="memItemLeft" align="right" valign="top"><a id="aa6967e9329939596c62f16e8686cac13"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aa6967e9329939596c62f16e8686cac13">pause</a> ()</td></tr>
<tr class="memdesc:aa6967e9329939596c62f16e8686cac13"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC pause. <br /></td></tr>
<tr class="separator:aa6967e9329939596c62f16e8686cac13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3719138f63afaeed06b63cc48ea79335"><td class="memItemLeft" align="right" valign="top"><a id="a3719138f63afaeed06b63cc48ea79335"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a3719138f63afaeed06b63cc48ea79335">pin_code</a> ()</td></tr>
<tr class="memdesc:a3719138f63afaeed06b63cc48ea79335"><td class="mdescLeft">&#160;</td><td class="mdescRight">provides the requested pin code (0 = undefined) <br /></td></tr>
<tr class="separator:a3719138f63afaeed06b63cc48ea79335"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aafd2afad1960db8ab73d7c6977aeb686"><td class="memItemLeft" align="right" valign="top"><a id="aafd2afad1960db8ab73d7c6977aeb686"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aafd2afad1960db8ab73d7c6977aeb686">play</a> ()</td></tr>
<tr class="memdesc:aafd2afad1960db8ab73d7c6977aeb686"><td class="mdescLeft">&#160;</td><td class="mdescRight">Starts to play music using AVRC. <br /></td></tr>
<tr class="separator:aafd2afad1960db8ab73d7c6977aeb686"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a341024c18eabdb06c734c2242d5ba505"><td class="memItemLeft" align="right" valign="top"><a id="a341024c18eabdb06c734c2242d5ba505"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a341024c18eabdb06c734c2242d5ba505">previous</a> ()</td></tr>
<tr class="memdesc:a341024c18eabdb06c734c2242d5ba505"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC previous. <br /></td></tr>
<tr class="separator:a341024c18eabdb06c734c2242d5ba505"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac795a023f85438355a1b00644f2b040f"><td class="memItemLeft" align="right" valign="top"><a id="ac795a023f85438355a1b00644f2b040f"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#ac795a023f85438355a1b00644f2b040f">reconnect</a> ()</td></tr>
<tr class="memdesc:ac795a023f85438355a1b00644f2b040f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reconnects to the last device. <br /></td></tr>
<tr class="separator:ac795a023f85438355a1b00644f2b040f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ee01e6d11ee3c6c546a510029a23a12"><td class="memItemLeft" align="right" valign="top"><a id="a9ee01e6d11ee3c6c546a510029a23a12"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a9ee01e6d11ee3c6c546a510029a23a12">rewind</a> ()</td></tr>
<tr class="memdesc:a9ee01e6d11ee3c6c546a510029a23a12"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC rewind. <br /></td></tr>
<tr class="separator:a9ee01e6d11ee3c6c546a510029a23a12"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a90cee550d061836992616161d4215355"><td class="memItemLeft" align="right" valign="top"><a id="a90cee550d061836992616161d4215355"></a>
virtual uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a90cee550d061836992616161d4215355">sample_rate</a> ()</td></tr>
<tr class="memdesc:a90cee550d061836992616161d4215355"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the actually set data rate (in samples per second) <br /></td></tr>
<tr class="separator:a90cee550d061836992616161d4215355"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad25155f02bad11da6c130aae00c8ab9c"><td class="memItemLeft" align="right" valign="top"><a id="ad25155f02bad11da6c130aae00c8ab9c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ad25155f02bad11da6c130aae00c8ab9c">set_address_validator</a> (bool(*callBack)(<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> remote_bda))</td></tr>
<tr class="memdesc:ad25155f02bad11da6c130aae00c8ab9c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Allows you to reject unauthorized addresses. <br /></td></tr>
<tr class="separator:ad25155f02bad11da6c130aae00c8ab9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a537d576b12d1158eb0681a6195b258de"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a537d576b12d1158eb0681a6195b258de">set_auto_reconnect</a> (bool active)</td></tr>
<tr class="separator:a537d576b12d1158eb0681a6195b258de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7d10cfe632a3c2f95409f6a23daecdd"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af7d10cfe632a3c2f95409f6a23daecdd">set_auto_reconnect</a> (bool <a class="el" href="class_bluetooth_a2_d_p_common.html#ac795a023f85438355a1b00644f2b040f">reconnect</a>, int count=AUTOCONNECT_TRY_NUM)</td></tr>
<tr class="separator:af7d10cfe632a3c2f95409f6a23daecdd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aedd80fe4cf8bd887224efa2716ad9d69"><td class="memItemLeft" align="right" valign="top"><a id="aedd80fe4cf8bd887224efa2716ad9d69"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aedd80fe4cf8bd887224efa2716ad9d69">set_avrc_connection_state_callback</a> (void(*callback)(bool))</td></tr>
<tr class="memdesc:aedd80fe4cf8bd887224efa2716ad9d69"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define a callback method which provides connection state of AVRC service. <br /></td></tr>
<tr class="separator:aedd80fe4cf8bd887224efa2716ad9d69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8281af353148544a0612f8f7c4d511b1"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a8281af353148544a0612f8f7c4d511b1">set_avrc_metadata_attribute_mask</a> (int flags)</td></tr>
<tr class="separator:a8281af353148544a0612f8f7c4d511b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac9074521c80d7574a855f30b8301d13"><td class="memItemLeft" align="right" valign="top"><a id="aac9074521c80d7574a855f30b8301d13"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aac9074521c80d7574a855f30b8301d13">set_avrc_metadata_callback</a> (void(*callback)(uint8_t, const uint8_t *))</td></tr>
<tr class="memdesc:aac9074521c80d7574a855f30b8301d13"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define a callback method which provides the meta data. <br /></td></tr>
<tr class="separator:aac9074521c80d7574a855f30b8301d13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a17013c6f40042c68821548cab9ddb5eb"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a17013c6f40042c68821548cab9ddb5eb">set_avrc_rn_events</a> (std::vector&lt; <a class="el" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a> &gt; events)</td></tr>
<tr class="separator:a17013c6f40042c68821548cab9ddb5eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5dd8ed8e61d6bb6d0c1e05d5c17e45e7"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5dd8ed8e61d6bb6d0c1e05d5c17e45e7">set_avrc_rn_play_pos_callback</a> (void(*callback)(uint32_t play_pos), uint32_t notif_interval=10)</td></tr>
<tr class="separator:a5dd8ed8e61d6bb6d0c1e05d5c17e45e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5e4806bad4ed634493643c5925ecf67f"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5e4806bad4ed634493643c5925ecf67f">set_avrc_rn_playstatus_callback</a> (void(*callback)(<a class="el" href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a> playback))</td></tr>
<tr class="separator:a5e4806bad4ed634493643c5925ecf67f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae56f6a99a5c38e0bdd402a79faba6dd5"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ae56f6a99a5c38e0bdd402a79faba6dd5">set_avrc_rn_track_change_callback</a> (void(*callback)(uint8_t *id))</td></tr>
<tr class="separator:ae56f6a99a5c38e0bdd402a79faba6dd5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acb34360759c6a94028f74df35009b033"><td class="memItemLeft" align="right" valign="top"><a id="acb34360759c6a94028f74df35009b033"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#acb34360759c6a94028f74df35009b033">set_avrc_rn_volumechange</a> (void(*callBack)(int))</td></tr>
<tr class="memdesc:acb34360759c6a94028f74df35009b033"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the callback that is called when remote changes the volume. <br /></td></tr>
<tr class="separator:acb34360759c6a94028f74df35009b033"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b16f9fec1e74eb3bb30f6cf572d21e9"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a9b16f9fec1e74eb3bb30f6cf572d21e9">set_avrc_rn_volumechange_completed</a> (void(*callBack)(int))</td></tr>
<tr class="separator:a9b16f9fec1e74eb3bb30f6cf572d21e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a145c20271b53d4329e0e2c7fa36692b0"><td class="memItemLeft" align="right" valign="top"><a id="a145c20271b53d4329e0e2c7fa36692b0"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a145c20271b53d4329e0e2c7fa36692b0">set_bluedroid_config_t</a> (esp_bluedroid_config_t cfg)</td></tr>
<tr class="memdesc:a145c20271b53d4329e0e2c7fa36692b0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the esp_bluedroid_config_t: Available from IDF 5.2.1. <br /></td></tr>
<tr class="separator:a145c20271b53d4329e0e2c7fa36692b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b37f48a6b0bca33fb21b2a9ae9dab7c"><td class="memItemLeft" align="right" valign="top"><a id="a8b37f48a6b0bca33fb21b2a9ae9dab7c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a8b37f48a6b0bca33fb21b2a9ae9dab7c">set_connectable</a> (bool connectable)</td></tr>
<tr class="memdesc:a8b37f48a6b0bca33fb21b2a9ae9dab7c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bluetooth connectable. <br /></td></tr>
<tr class="separator:a8b37f48a6b0bca33fb21b2a9ae9dab7c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a8a860a325348cdf210637e8d1159e6"><td class="memItemLeft" align="right" valign="top"><a id="a0a8a860a325348cdf210637e8d1159e6"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a0a8a860a325348cdf210637e8d1159e6">set_connected</a> (bool active)</td></tr>
<tr class="memdesc:a0a8a860a325348cdf210637e8d1159e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calls disconnect or reconnect. <br /></td></tr>
<tr class="separator:a0a8a860a325348cdf210637e8d1159e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41ab8453d4f7f88d68d6cdb1a866532b"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a41ab8453d4f7f88d68d6cdb1a866532b">set_default_bt_mode</a> (<a class="el" href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">esp_bt_mode_t</a> mode)</td></tr>
<tr class="separator:a41ab8453d4f7f88d68d6cdb1a866532b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e53adc58f665113c9ac6a5521e58814"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a8e53adc58f665113c9ac6a5521e58814">set_discoverability</a> (<a class="el" href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">esp_bt_discovery_mode_t</a> d)</td></tr>
<tr class="memdesc:a8e53adc58f665113c9ac6a5521e58814"><td class="mdescLeft">&#160;</td><td class="mdescRight">Bluetooth discoverability.  <a href="class_bluetooth_a2_d_p_common.html#a8e53adc58f665113c9ac6a5521e58814">More...</a><br /></td></tr>
<tr class="separator:a8e53adc58f665113c9ac6a5521e58814"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae5e96c34428c50873a0ca7423a6b5402"><td class="memItemLeft" align="right" valign="top"><a id="ae5e96c34428c50873a0ca7423a6b5402"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#ae5e96c34428c50873a0ca7423a6b5402">set_event_queue_size</a> (int size)</td></tr>
<tr class="memdesc:ae5e96c34428c50873a0ca7423a6b5402"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the queue size of the event task. <br /></td></tr>
<tr class="separator:ae5e96c34428c50873a0ca7423a6b5402"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a04bc52a4279a503203084492fe20c32e"><td class="memItemLeft" align="right" valign="top"><a id="a04bc52a4279a503203084492fe20c32e"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a04bc52a4279a503203084492fe20c32e">set_event_stack_size</a> (int size)</td></tr>
<tr class="memdesc:a04bc52a4279a503203084492fe20c32e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the stack size of the event task (in bytes) <br /></td></tr>
<tr class="separator:a04bc52a4279a503203084492fe20c32e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a624040cce89a4a2f66495f57db6c1457"><td class="memItemLeft" align="right" valign="top"><a id="a624040cce89a4a2f66495f57db6c1457"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a624040cce89a4a2f66495f57db6c1457">set_mono_downmix</a> (bool enabled)</td></tr>
<tr class="memdesc:a624040cce89a4a2f66495f57db6c1457"><td class="mdescLeft">&#160;</td><td class="mdescRight">mix stereo into single mono signal <br /></td></tr>
<tr class="separator:a624040cce89a4a2f66495f57db6c1457"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f13ecf541393c21a5a489235bad27fb"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a5f13ecf541393c21a5a489235bad27fb">set_on_audio_state_changed</a> (void(*callBack)(<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state, void *), void *obj=nullptr)</td></tr>
<tr class="memdesc:a5f13ecf541393c21a5a489235bad27fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the callback that is called when the audio state is changed.  <a href="class_bluetooth_a2_d_p_common.html#a5f13ecf541393c21a5a489235bad27fb">More...</a><br /></td></tr>
<tr class="separator:a5f13ecf541393c21a5a489235bad27fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a169e9b94cbbfb7311a8722cc6d436e95"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a169e9b94cbbfb7311a8722cc6d436e95">set_on_audio_state_changed_post</a> (void(*callBack)(<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state, void *), void *obj=nullptr)</td></tr>
<tr class="separator:a169e9b94cbbfb7311a8722cc6d436e95"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa79cff78c075c9273ea2b5c03f052fcd"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#aa79cff78c075c9273ea2b5c03f052fcd">set_on_connection_state_changed</a> (void(*callBack)(<a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> state, void *), void *obj=nullptr)</td></tr>
<tr class="memdesc:aa79cff78c075c9273ea2b5c03f052fcd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the callback that is called when the connection state is changed.  <a href="class_bluetooth_a2_d_p_common.html#aa79cff78c075c9273ea2b5c03f052fcd">More...</a><br /></td></tr>
<tr class="separator:aa79cff78c075c9273ea2b5c03f052fcd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af65219e635fadbbc90f4663b33abd3e0"><td class="memItemLeft" align="right" valign="top"><a id="af65219e635fadbbc90f4663b33abd3e0"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af65219e635fadbbc90f4663b33abd3e0">set_on_data_received</a> (void(*callBack)())</td></tr>
<tr class="memdesc:af65219e635fadbbc90f4663b33abd3e0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define callback which is called when we receive data. <br /></td></tr>
<tr class="separator:af65219e635fadbbc90f4663b33abd3e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac245103d3d5c47b0414c2de21c0d52a7"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ac245103d3d5c47b0414c2de21c0d52a7">set_on_volumechange</a> (void(*callBack)(int))</td></tr>
<tr class="separator:ac245103d3d5c47b0414c2de21c0d52a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5bdae239eb76ad435a7999362a8019fc"><td class="memItemLeft" align="right" valign="top"><a id="a5bdae239eb76ad435a7999362a8019fc"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5bdae239eb76ad435a7999362a8019fc">set_output</a> (audio_tools::AudioOutput &amp;output)</td></tr>
<tr class="memdesc:a5bdae239eb76ad435a7999362a8019fc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output AudioOutput using AudioTools library. <br /></td></tr>
<tr class="separator:a5bdae239eb76ad435a7999362a8019fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a664eafe67a1f66b415159b84c3fe851f"><td class="memItemLeft" align="right" valign="top"><a id="a664eafe67a1f66b415159b84c3fe851f"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a664eafe67a1f66b415159b84c3fe851f">set_output</a> (audio_tools::AudioStream &amp;output)</td></tr>
<tr class="memdesc:a664eafe67a1f66b415159b84c3fe851f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output AudioStream using AudioTools library. <br /></td></tr>
<tr class="separator:a664eafe67a1f66b415159b84c3fe851f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2076eebe22254b2238ba097e8749d24d"><td class="memItemLeft" align="right" valign="top"><a id="a2076eebe22254b2238ba097e8749d24d"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a2076eebe22254b2238ba097e8749d24d">set_output</a> (<a class="el" href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a> &amp;output)</td></tr>
<tr class="memdesc:a2076eebe22254b2238ba097e8749d24d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the output class: by default we use <a class="el" href="class_bluetooth_a2_d_p_output_default.html" title="Default Output Class providing both the Legacy I2S and the AudioTools I2S functionality.">BluetoothA2DPOutputDefault</a>. <br /></td></tr>
<tr class="separator:a2076eebe22254b2238ba097e8749d24d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af87666dc2c00c5917db709e2edaf1dab"><td class="memItemLeft" align="right" valign="top"><a id="af87666dc2c00c5917db709e2edaf1dab"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af87666dc2c00c5917db709e2edaf1dab">set_output</a> (Print &amp;output)</td></tr>
<tr class="memdesc:af87666dc2c00c5917db709e2edaf1dab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output to Arduino Print. <br /></td></tr>
<tr class="separator:af87666dc2c00c5917db709e2edaf1dab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68d88c33b8ec218fe3bd45f61c39f754"><td class="memItemLeft" align="right" valign="top"><a id="a68d88c33b8ec218fe3bd45f61c39f754"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a68d88c33b8ec218fe3bd45f61c39f754">set_output_active</a> (bool flag)</td></tr>
<tr class="memdesc:a68d88c33b8ec218fe3bd45f61c39f754"><td class="mdescLeft">&#160;</td><td class="mdescRight">Activate/Deactivate output e.g. to I2S. <br /></td></tr>
<tr class="separator:a68d88c33b8ec218fe3bd45f61c39f754"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a035b0e2534970acd2c35b65842374e51"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a035b0e2534970acd2c35b65842374e51">set_raw_stream_reader</a> (void(*callBack)(const uint8_t *, uint32_t))</td></tr>
<tr class="separator:a035b0e2534970acd2c35b65842374e51"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a96b4fabd27e7952fc3ea5edca3b95cbb"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a96b4fabd27e7952fc3ea5edca3b95cbb">set_reconnect_delay</a> (int delay)</td></tr>
<tr class="separator:a96b4fabd27e7952fc3ea5edca3b95cbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a452b509a46930ab1ed58188a4181c67b"><td class="memItemLeft" align="right" valign="top"><a id="a452b509a46930ab1ed58188a4181c67b"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a452b509a46930ab1ed58188a4181c67b">set_rssi_active</a> (bool active)</td></tr>
<tr class="memdesc:a452b509a46930ab1ed58188a4181c67b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Activates the rssi reporting. <br /></td></tr>
<tr class="separator:a452b509a46930ab1ed58188a4181c67b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a97c80237061c6e80d7c6e1e5e45773c8"><td class="memItemLeft" align="right" valign="top"><a id="a97c80237061c6e80d7c6e1e5e45773c8"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a97c80237061c6e80d7c6e1e5e45773c8">set_rssi_callback</a> (void(*callback)(esp_bt_gap_cb_param_t::read_rssi_delta_param &amp;rssi))</td></tr>
<tr class="memdesc:a97c80237061c6e80d7c6e1e5e45773c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the callback that is called when we get an new rssi value. <br /></td></tr>
<tr class="separator:a97c80237061c6e80d7c6e1e5e45773c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a89d2694f880a2db22344b97b466c9a9d"><td class="memItemLeft" align="right" valign="top"><a id="a89d2694f880a2db22344b97b466c9a9d"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a89d2694f880a2db22344b97b466c9a9d">set_sample_rate_callback</a> (void(*callback)(uint16_t rate))</td></tr>
<tr class="memdesc:a89d2694f880a2db22344b97b466c9a9d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the method which will be called with the sample rate is updated. <br /></td></tr>
<tr class="separator:a89d2694f880a2db22344b97b466c9a9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe6004e2b95d120d64c10cf947fefb55"><td class="memItemLeft" align="right" valign="top"><a id="abe6004e2b95d120d64c10cf947fefb55"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#abe6004e2b95d120d64c10cf947fefb55">set_spp_active</a> (bool flag)</td></tr>
<tr class="memdesc:abe6004e2b95d120d64c10cf947fefb55"><td class="mdescLeft">&#160;</td><td class="mdescRight">Activates SSP (Serial protocol) <br /></td></tr>
<tr class="separator:abe6004e2b95d120d64c10cf947fefb55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f94426ff4899c437d31623e013cf7a5"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a4f94426ff4899c437d31623e013cf7a5">set_stream_reader</a> (void(*callBack)(const uint8_t *, uint32_t), bool i2s_output=true)</td></tr>
<tr class="separator:a4f94426ff4899c437d31623e013cf7a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a14d982730e2b9ea772fe9ede1563ed22"><td class="memItemLeft" align="right" valign="top"><a id="a14d982730e2b9ea772fe9ede1563ed22"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a14d982730e2b9ea772fe9ede1563ed22">set_swap_lr_channels</a> (bool swap)</td></tr>
<tr class="memdesc:a14d982730e2b9ea772fe9ede1563ed22"><td class="mdescLeft">&#160;</td><td class="mdescRight">swaps the left and right channel <br /></td></tr>
<tr class="separator:a14d982730e2b9ea772fe9ede1563ed22"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a694940fad2a2d498875cfbdf52eea58b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a694940fad2a2d498875cfbdf52eea58b">set_task_core</a> (BaseType_t core)</td></tr>
<tr class="separator:a694940fad2a2d498875cfbdf52eea58b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68f9e168839f0faeb72705ccabbb6b7a"><td class="memItemLeft" align="right" valign="top"><a id="a68f9e168839f0faeb72705ccabbb6b7a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a68f9e168839f0faeb72705ccabbb6b7a">set_task_priority</a> (UBaseType_t priority)</td></tr>
<tr class="memdesc:a68f9e168839f0faeb72705ccabbb6b7a"><td class="mdescLeft">&#160;</td><td class="mdescRight">defines the task priority (the default value is configMAX_PRIORITIES - 10) <br /></td></tr>
<tr class="separator:a68f9e168839f0faeb72705ccabbb6b7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a507e30ececfdc4382af60a0319cdaf1b"><td class="memItemLeft" align="right" valign="top"><a id="a507e30ececfdc4382af60a0319cdaf1b"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a507e30ececfdc4382af60a0319cdaf1b">set_volume</a> (uint8_t volume)</td></tr>
<tr class="memdesc:a507e30ececfdc4382af60a0319cdaf1b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Changes the volume. <br /></td></tr>
<tr class="separator:a507e30ececfdc4382af60a0319cdaf1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7757ddbf424aeb909dc952d7c40fc241"><td class="memItemLeft" align="right" valign="top"><a id="a7757ddbf424aeb909dc952d7c40fc241"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a7757ddbf424aeb909dc952d7c40fc241">set_volume_control</a> (<a class="el" href="class_a2_d_p_volume_control.html">A2DPVolumeControl</a> *ptr)</td></tr>
<tr class="memdesc:a7757ddbf424aeb909dc952d7c40fc241"><td class="mdescLeft">&#160;</td><td class="mdescRight">you can define a custom VolumeControl implementation <br /></td></tr>
<tr class="separator:a7757ddbf424aeb909dc952d7c40fc241"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a189424ab5dc8c44f00b461e9392a2ce8"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a189424ab5dc8c44f00b461e9392a2ce8">start</a> (const char *name)</td></tr>
<tr class="memdesc:a189424ab5dc8c44f00b461e9392a2ce8"><td class="mdescLeft">&#160;</td><td class="mdescRight">starts the I2S bluetooth sink with the inidicated name  <a href="class_bluetooth_a2_d_p_sink.html#a189424ab5dc8c44f00b461e9392a2ce8">More...</a><br /></td></tr>
<tr class="separator:a189424ab5dc8c44f00b461e9392a2ce8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af85e99324638d1c814e221ac4ba815dd"><td class="memItemLeft" align="right" valign="top"><a id="af85e99324638d1c814e221ac4ba815dd"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af85e99324638d1c814e221ac4ba815dd">start</a> (const char *name, bool auto_reconect)</td></tr>
<tr class="memdesc:af85e99324638d1c814e221ac4ba815dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">starts the I2S bluetooth sink with the inidicated name <br /></td></tr>
<tr class="separator:af85e99324638d1c814e221ac4ba815dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37dcbcd418b84310ccedf3330e44834f"><td class="memItemLeft" align="right" valign="top"><a id="a37dcbcd418b84310ccedf3330e44834f"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a37dcbcd418b84310ccedf3330e44834f">stop</a> ()</td></tr>
<tr class="memdesc:a37dcbcd418b84310ccedf3330e44834f"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC stop. <br /></td></tr>
<tr class="separator:a37dcbcd418b84310ccedf3330e44834f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38d70707790dec91d63da2006f2ff17a"><td class="memItemLeft" align="right" valign="top"><a id="a38d70707790dec91d63da2006f2ff17a"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a38d70707790dec91d63da2006f2ff17a">to_str</a> (<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state)</td></tr>
<tr class="memdesc:a38d70707790dec91d63da2006f2ff17a"><td class="mdescLeft">&#160;</td><td class="mdescRight">converts a esp_a2d_audio_state_t to a string <br /></td></tr>
<tr class="separator:a38d70707790dec91d63da2006f2ff17a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2b78346084e12feeea035d006e7cf07a"><td class="memItemLeft" align="right" valign="top"><a id="a2b78346084e12feeea035d006e7cf07a"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a2b78346084e12feeea035d006e7cf07a">to_str</a> (<a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> state)</td></tr>
<tr class="memdesc:a2b78346084e12feeea035d006e7cf07a"><td class="mdescLeft">&#160;</td><td class="mdescRight">converts esp_a2d_connection_state_t to a string <br /></td></tr>
<tr class="separator:a2b78346084e12feeea035d006e7cf07a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7896335b8f2cc324da86e16efb1544c9"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a7896335b8f2cc324da86e16efb1544c9">to_str</a> (<a class="el" href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a> state)</td></tr>
<tr class="memdesc:a7896335b8f2cc324da86e16efb1544c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">converts esp_avrc_playback_stat_t to a string  <a href="class_bluetooth_a2_d_p_common.html#a7896335b8f2cc324da86e16efb1544c9">More...</a><br /></td></tr>
<tr class="separator:a7896335b8f2cc324da86e16efb1544c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa76a15aa8922301e72a745b540b040c"><td class="memItemLeft" align="right" valign="top"><a id="afa76a15aa8922301e72a745b540b040c"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#afa76a15aa8922301e72a745b540b040c">to_str</a> (<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> bda)</td></tr>
<tr class="memdesc:afa76a15aa8922301e72a745b540b040c"><td class="mdescLeft">&#160;</td><td class="mdescRight">converts a esp_bd_addr_t to a string - the string is 18 characters long! <br /></td></tr>
<tr class="separator:afa76a15aa8922301e72a745b540b040c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b43ee67031c300d07638f9c969fa4ef"><td class="memItemLeft" align="right" valign="top"><a id="a3b43ee67031c300d07638f9c969fa4ef"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a3b43ee67031c300d07638f9c969fa4ef">update_rssi</a> ()</td></tr>
<tr class="memdesc:a3b43ee67031c300d07638f9c969fa4ef"><td class="mdescLeft">&#160;</td><td class="mdescRight">Requests an update of the rssi delta value. <br /></td></tr>
<tr class="separator:a3b43ee67031c300d07638f9c969fa4ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae823f16ed3ee17cf9c6d1731b9d19a34"><td class="memItemLeft" align="right" valign="top"><a id="ae823f16ed3ee17cf9c6d1731b9d19a34"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ae823f16ed3ee17cf9c6d1731b9d19a34">volume_down</a> ()</td></tr>
<tr class="memdesc:ae823f16ed3ee17cf9c6d1731b9d19a34"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC decrease the volume. <br /></td></tr>
<tr class="separator:ae823f16ed3ee17cf9c6d1731b9d19a34"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42866994c045e27584e0438eb2d4cc79"><td class="memItemLeft" align="right" valign="top"><a id="a42866994c045e27584e0438eb2d4cc79"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a42866994c045e27584e0438eb2d4cc79">volume_up</a> ()</td></tr>
<tr class="memdesc:a42866994c045e27584e0438eb2d4cc79"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC increase the volume. <br /></td></tr>
<tr class="separator:a42866994c045e27584e0438eb2d4cc79"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a9892ecf2f81b99d861f2767f7b705188"><td class="memItemLeft" align="right" valign="top"><a id="a9892ecf2f81b99d861f2767f7b705188"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a9892ecf2f81b99d861f2767f7b705188">app_a2d_callback</a> (esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param) override</td></tr>
<tr class="memdesc:a9892ecf2f81b99d861f2767f7b705188"><td class="mdescLeft">&#160;</td><td class="mdescRight">callback function for A2DP source <br /></td></tr>
<tr class="separator:a9892ecf2f81b99d861f2767f7b705188"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3afdd55a114e11dbf0618f0accf928aa"><td class="memItemLeft" align="right" valign="top"><a id="a3afdd55a114e11dbf0618f0accf928aa"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_alloc_meta_buffer</b> (esp_avrc_ct_cb_param_t *param)</td></tr>
<tr class="separator:a3afdd55a114e11dbf0618f0accf928aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab9ed8f41fcb7d4890618ce82eb9f82a1"><td class="memItemLeft" align="right" valign="top"><a id="ab9ed8f41fcb7d4890618ce82eb9f82a1"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>app_gap_callback</b> (esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t *param) override</td></tr>
<tr class="separator:ab9ed8f41fcb7d4890618ce82eb9f82a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f8680e010057c3fea392c75c85c9f23"><td class="memItemLeft" align="right" valign="top"><a id="a7f8680e010057c3fea392c75c85c9f23"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a7f8680e010057c3fea392c75c85c9f23">app_rc_ct_callback</a> (esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param) override</td></tr>
<tr class="memdesc:a7f8680e010057c3fea392c75c85c9f23"><td class="mdescLeft">&#160;</td><td class="mdescRight">callback function for AVRCP controller <br /></td></tr>
<tr class="separator:a7f8680e010057c3fea392c75c85c9f23"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0fac4c5cd734abf986d5ccf1851689e7"><td class="memItemLeft" align="right" valign="top"><a id="a0fac4c5cd734abf986d5ccf1851689e7"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_rc_tg_callback</b> (esp_avrc_tg_cb_event_t event, esp_avrc_tg_cb_param_t *param) override</td></tr>
<tr class="separator:a0fac4c5cd734abf986d5ccf1851689e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a79bd60b9299d8936ad49036181400f95"><td class="memItemLeft" align="right" valign="top"><a id="a79bd60b9299d8936ad49036181400f95"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>app_send_msg</b> (<a class="el" href="structbt__app__msg__t.html">bt_app_msg_t</a> *msg)</td></tr>
<tr class="separator:a79bd60b9299d8936ad49036181400f95"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2cbcdb1d650ee5e472b86fd6fda03d14"><td class="memItemLeft" align="right" valign="top"><a id="a2cbcdb1d650ee5e472b86fd6fda03d14"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_task_handler</b> (void *arg)</td></tr>
<tr class="separator:a2cbcdb1d650ee5e472b86fd6fda03d14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f6518060f0a1d114ff7da3b923868e8"><td class="memItemLeft" align="right" valign="top"><a id="a7f6518060f0a1d114ff7da3b923868e8"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_task_shut_down</b> ()</td></tr>
<tr class="separator:a7f6518060f0a1d114ff7da3b923868e8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8005f3e388e1ea5e0daab828713b34c6"><td class="memItemLeft" align="right" valign="top"><a id="a8005f3e388e1ea5e0daab828713b34c6"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_task_start_up</b> ()</td></tr>
<tr class="separator:a8005f3e388e1ea5e0daab828713b34c6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba7d5111b1cf179b8d3859bfde6e88d3"><td class="memItemLeft" align="right" valign="top"><a id="aba7d5111b1cf179b8d3859bfde6e88d3"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>app_work_dispatch</b> (<a class="el" href="_bluetooth_a2_d_p_common_8h.html#a9bee258e477be3c0e70d6029ed86a019">app_callback_t</a> p_cback, uint16_t event, void *p_params, int param_len)</td></tr>
<tr class="separator:aba7d5111b1cf179b8d3859bfde6e88d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a69518d11bb3efb446cd49d5c08337327"><td class="memItemLeft" align="right" valign="top"><a id="a69518d11bb3efb446cd49d5c08337327"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_work_dispatched</b> (<a class="el" href="structbt__app__msg__t.html">bt_app_msg_t</a> *msg)</td></tr>
<tr class="separator:a69518d11bb3efb446cd49d5c08337327"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2cf823459de7a757d94a4ced2f375a0c"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a2cf823459de7a757d94a4ced2f375a0c">audio_data_callback</a> (const uint8_t *data, uint32_t len)</td></tr>
<tr class="separator:a2cf823459de7a757d94a4ced2f375a0c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46f8aeee188b3d201f1d7779876664ef"><td class="memItemLeft" align="right" valign="top"><a id="a46f8aeee188b3d201f1d7779876664ef"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_a2d_evt</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:a46f8aeee188b3d201f1d7779876664ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa1e8675ff5ee96ed74d2491d3505c37"><td class="memItemLeft" align="right" valign="top"><a id="afa1e8675ff5ee96ed74d2491d3505c37"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_avrc_evt</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:afa1e8675ff5ee96ed74d2491d3505c37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade57e43b77bf7e4f837c9ae75f2d71ca"><td class="memItemLeft" align="right" valign="top"><a id="ade57e43b77bf7e4f837c9ae75f2d71ca"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_avrc_tg_evt</b> (uint16_t event, void *p_param) override</td></tr>
<tr class="separator:ade57e43b77bf7e4f837c9ae75f2d71ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8844251a2ff7011de918e9958643a6f9"><td class="memItemLeft" align="right" valign="top"><a id="a8844251a2ff7011de918e9958643a6f9"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_stack_evt</b> (uint16_t event, void *p_param) override</td></tr>
<tr class="separator:a8844251a2ff7011de918e9958643a6f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0cb7f66ff75328b932c0351027a742aa"><td class="memItemLeft" align="right" valign="top"><a id="a0cb7f66ff75328b932c0351027a742aa"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>av_new_track</b> ()</td></tr>
<tr class="separator:a0cb7f66ff75328b932c0351027a742aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d008f9d590d7389399f625ef487d426"><td class="memItemLeft" align="right" valign="top"><a id="a2d008f9d590d7389399f625ef487d426"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>av_notify_evt_handler</b> (uint8_t event_id, esp_avrc_rn_param_t *event_parameter)</td></tr>
<tr class="separator:a2d008f9d590d7389399f625ef487d426"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76d8d66d3a7487b7d63634d4fd0970c9"><td class="memItemLeft" align="right" valign="top"><a id="a76d8d66d3a7487b7d63634d4fd0970c9"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>av_play_pos_changed</b> ()</td></tr>
<tr class="separator:a76d8d66d3a7487b7d63634d4fd0970c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a2b42410fcc8b009576944768fad807"><td class="memItemLeft" align="right" valign="top"><a id="a5a2b42410fcc8b009576944768fad807"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>av_playback_changed</b> ()</td></tr>
<tr class="separator:a5a2b42410fcc8b009576944768fad807"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aecacd14836e61b3a08a0f415095d3f98"><td class="memItemLeft" align="right" valign="top"><a id="aecacd14836e61b3a08a0f415095d3f98"></a>
virtual esp_err_t&#160;</td><td class="memItemRight" valign="bottom"><b>bluedroid_init</b> ()</td></tr>
<tr class="separator:aecacd14836e61b3a08a0f415095d3f98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af467f11e174f150e1aff8df08f56119a"><td class="memItemLeft" align="right" valign="top"><a id="af467f11e174f150e1aff8df08f56119a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>bt_i2s_task_shut_down</b> (void)</td></tr>
<tr class="separator:af467f11e174f150e1aff8df08f56119a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a559fdf1996d0c1e99ed07dfe1f95d9a2"><td class="memItemLeft" align="right" valign="top"><a id="a559fdf1996d0c1e99ed07dfe1f95d9a2"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>bt_i2s_task_start_up</b> (void)</td></tr>
<tr class="separator:a559fdf1996d0c1e99ed07dfe1f95d9a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8be3cf8679b236293658c06cd1ed010b"><td class="memItemLeft" align="right" valign="top">virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a8be3cf8679b236293658c06cd1ed010b">bt_start</a> ()</td></tr>
<tr class="memdesc:a8be3cf8679b236293658c06cd1ed010b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Startup logic as implemented by Arduino.  <a href="class_bluetooth_a2_d_p_common.html#a8be3cf8679b236293658c06cd1ed010b">More...</a><br /></td></tr>
<tr class="separator:a8be3cf8679b236293658c06cd1ed010b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4cb5a9aa6654ad8e6f518a36620657e9"><td class="memItemLeft" align="right" valign="top"><a id="a4cb5a9aa6654ad8e6f518a36620657e9"></a>
esp_err_t&#160;</td><td class="memItemRight" valign="bottom"><b>esp_a2d_connect</b> (<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> peer) override</td></tr>
<tr class="separator:a4cb5a9aa6654ad8e6f518a36620657e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aebc005e6b28bd68fb086ba1341c68669"><td class="memItemLeft" align="right" valign="top"><a id="aebc005e6b28bd68fb086ba1341c68669"></a>
esp_err_t&#160;</td><td class="memItemRight" valign="bottom"><b>esp_a2d_disconnect</b> (<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> remote_bda) override</td></tr>
<tr class="separator:aebc005e6b28bd68fb086ba1341c68669"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adb1442dcf53b7e8ccfd099db82ca0e60"><td class="memItemLeft" align="right" valign="top"><a id="adb1442dcf53b7e8ccfd099db82ca0e60"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>execute_avrc_command</b> (int cmd)</td></tr>
<tr class="separator:adb1442dcf53b7e8ccfd099db82ca0e60"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d277d15f94823eea70f80a327344939"><td class="memItemLeft" align="right" valign="top"><a id="a2d277d15f94823eea70f80a327344939"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a2d277d15f94823eea70f80a327344939">get_connected_source_name</a> ()</td></tr>
<tr class="memdesc:a2d277d15f94823eea70f80a327344939"><td class="mdescLeft">&#160;</td><td class="mdescRight">Get the name of the connected source device (obsolete): use <a class="el" href="class_bluetooth_a2_d_p_sink.html#ac9adc3ca64e68fb3d6e816698a725dd5" title="Get the name of the connected source device.">get_peer_name()</a> <br /></td></tr>
<tr class="separator:a2d277d15f94823eea70f80a327344939"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3771c6d300c7709f2b76ab8847574cf4"><td class="memItemLeft" align="right" valign="top"><a id="a3771c6d300c7709f2b76ab8847574cf4"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>get_last_connection</b> ()</td></tr>
<tr class="separator:a3771c6d300c7709f2b76ab8847574cf4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a718c4723206c6fdcd1649cfe2342e91a"><td class="memItemLeft" align="right" valign="top"><a id="a718c4723206c6fdcd1649cfe2342e91a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>handle_audio_cfg</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:a718c4723206c6fdcd1649cfe2342e91a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1b547f25c7fa20a4a1b4e999708c258c"><td class="memItemLeft" align="right" valign="top"><a id="a1b547f25c7fa20a4a1b4e999708c258c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>handle_audio_state</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:a1b547f25c7fa20a4a1b4e999708c258c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4cf06602ede9a331bfd0fde75c7bdc25"><td class="memItemLeft" align="right" valign="top"><a id="a4cf06602ede9a331bfd0fde75c7bdc25"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>handle_avrc_connection_state</b> (bool connected)</td></tr>
<tr class="separator:a4cf06602ede9a331bfd0fde75c7bdc25"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad52be2adb0bf68e3a1484166c4502e3f"><td class="memItemLeft" align="right" valign="top"><a id="ad52be2adb0bf68e3a1484166c4502e3f"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>handle_connection_state</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:ad52be2adb0bf68e3a1484166c4502e3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f98f07ba4cf5962a7cdda50317b3112"><td class="memItemLeft" align="right" valign="top"><a id="a4f98f07ba4cf5962a7cdda50317b3112"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>has_last_connection</b> ()</td></tr>
<tr class="separator:a4f98f07ba4cf5962a7cdda50317b3112"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afdc2f08c0547393704fd6fb56bde204f"><td class="memItemLeft" align="right" valign="top"><a id="afdc2f08c0547393704fd6fb56bde204f"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#afdc2f08c0547393704fd6fb56bde204f">i2s_task_handler</a> (void *arg)</td></tr>
<tr class="memdesc:afdc2f08c0547393704fd6fb56bde204f"><td class="mdescLeft">&#160;</td><td class="mdescRight">dummy functions needed for <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html" title="The BluetoothA2DPSinkQueued is using a separate Task with an additinal Queue to write the I2S data....">BluetoothA2DPSinkQueued</a> <br /></td></tr>
<tr class="separator:afdc2f08c0547393704fd6fb56bde204f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab59e6c716d9b5aaed69272e7d2a3d12a"><td class="memItemLeft" align="right" valign="top"><a id="ab59e6c716d9b5aaed69272e7d2a3d12a"></a>
size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ab59e6c716d9b5aaed69272e7d2a3d12a">i2s_write_data</a> (const uint8_t *data, size_t item_size)</td></tr>
<tr class="memdesc:ab59e6c716d9b5aaed69272e7d2a3d12a"><td class="mdescLeft">&#160;</td><td class="mdescRight">writes the data to i2s <br /></td></tr>
<tr class="separator:ab59e6c716d9b5aaed69272e7d2a3d12a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7501000b48fb46f9d6ae5f376d523aac"><td class="memItemLeft" align="right" valign="top"><a id="a7501000b48fb46f9d6ae5f376d523aac"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><b>init_bluetooth</b> ()</td></tr>
<tr class="separator:a7501000b48fb46f9d6ae5f376d523aac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c67b61f98c7daf25f72512cf181afbc"><td class="memItemLeft" align="right" valign="top"><a id="a2c67b61f98c7daf25f72512cf181afbc"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>init_i2s</b> ()</td></tr>
<tr class="separator:a2c67b61f98c7daf25f72512cf181afbc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51e9ff42c269979cb0e11f484e883fab"><td class="memItemLeft" align="right" valign="top"><a id="a51e9ff42c269979cb0e11f484e883fab"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>init_nvs</b> ()</td></tr>
<tr class="separator:a51e9ff42c269979cb0e11f484e883fab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0ef94c2b1e035eec0162d91d72132165"><td class="memItemLeft" align="right" valign="top"><a id="a0ef94c2b1e035eec0162d91d72132165"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_reconnect</b> (esp_a2d_disc_rsn_t type)</td></tr>
<tr class="separator:a0ef94c2b1e035eec0162d91d72132165"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a494fb5c610349dd467e7bc6bb34ee839"><td class="memItemLeft" align="right" valign="top"><a id="a494fb5c610349dd467e7bc6bb34ee839"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>isSource</b> ()</td></tr>
<tr class="separator:a494fb5c610349dd467e7bc6bb34ee839"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e9b33f70cd118d78b9d5015529c0d53"><td class="memItemLeft" align="right" valign="top"><a id="a6e9b33f70cd118d78b9d5015529c0d53"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><b>last_bda_nvs_name</b> ()</td></tr>
<tr class="separator:a6e9b33f70cd118d78b9d5015529c0d53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace4f3d087602a6db592df68ef8a2ded5"><td class="memItemLeft" align="right" valign="top"><a id="ace4f3d087602a6db592df68ef8a2ded5"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>read_address</b> (const char *name, <a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> &amp;bda)</td></tr>
<tr class="separator:ace4f3d087602a6db592df68ef8a2ded5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac7011399650aaf55abe54e1deb2432c"><td class="memItemLeft" align="right" valign="top"><a id="aac7011399650aaf55abe54e1deb2432c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>set_i2s_active</b> (bool active)</td></tr>
<tr class="separator:aac7011399650aaf55abe54e1deb2432c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af3a2ee313a97da4e788ba3489847115c"><td class="memItemLeft" align="right" valign="top"><a id="af3a2ee313a97da4e788ba3489847115c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>set_last_connection</b> (<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> bda)</td></tr>
<tr class="separator:af3a2ee313a97da4e788ba3489847115c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af1e2f14ddbe9266b61f5e721095c3685"><td class="memItemLeft" align="right" valign="top"><a id="af1e2f14ddbe9266b61f5e721095c3685"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#af1e2f14ddbe9266b61f5e721095c3685">set_scan_mode_connectable</a> (bool connectable)</td></tr>
<tr class="memdesc:af1e2f14ddbe9266b61f5e721095c3685"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines if the bluetooth is connectable. <br /></td></tr>
<tr class="separator:af1e2f14ddbe9266b61f5e721095c3685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af33d9cfbc588f0e978778614e6862a17"><td class="memItemLeft" align="right" valign="top"><a id="af33d9cfbc588f0e978778614e6862a17"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_scan_mode_connectable_default</b> () override</td></tr>
<tr class="separator:af33d9cfbc588f0e978778614e6862a17"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6fec0cfd3d0d9017b7ffcf82630ab89a"><td class="memItemLeft" align="right" valign="top"><a id="a6fec0cfd3d0d9017b7ffcf82630ab89a"></a>
virtual <a class="el" href="class_a2_d_p_volume_control.html">A2DPVolumeControl</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a6fec0cfd3d0d9017b7ffcf82630ab89a">volume_control</a> ()</td></tr>
<tr class="memdesc:a6fec0cfd3d0d9017b7ffcf82630ab89a"><td class="mdescLeft">&#160;</td><td class="mdescRight">provides access to the VolumeControl object <br /></td></tr>
<tr class="separator:a6fec0cfd3d0d9017b7ffcf82630ab89a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0ed754bc5f01492be905cc8d0bb12567"><td class="memItemLeft" align="right" valign="top"><a id="a0ed754bc5f01492be905cc8d0bb12567"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>volume_set_by_controller</b> (uint8_t volume)</td></tr>
<tr class="separator:a0ed754bc5f01492be905cc8d0bb12567"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abae7601e39f1b343fda2958fbefd83e4"><td class="memItemLeft" align="right" valign="top"><a id="abae7601e39f1b343fda2958fbefd83e4"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>volume_set_by_local_host</b> (uint8_t volume)</td></tr>
<tr class="separator:abae7601e39f1b343fda2958fbefd83e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aadd7d6f698bc8b97b3833c210eb38025"><td class="memItemLeft" align="right" valign="top"><a id="aadd7d6f698bc8b97b3833c210eb38025"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>write_address</b> (const char *name, <a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> bda)</td></tr>
<tr class="separator:aadd7d6f698bc8b97b3833c210eb38025"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa211fefd101a639938a20dc3478b48ae"><td class="memItemLeft" align="right" valign="top"><a id="aa211fefd101a639938a20dc3478b48ae"></a>
virtual size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aa211fefd101a639938a20dc3478b48ae">write_audio</a> (const uint8_t *data, size_t size)</td></tr>
<tr class="memdesc:aa211fefd101a639938a20dc3478b48ae"><td class="mdescLeft">&#160;</td><td class="mdescRight">output audio data e.g. to i2s or to queue <br /></td></tr>
<tr class="separator:aa211fefd101a639938a20dc3478b48ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-attribs"></a>
Protected Attributes</h2></td></tr>
<tr class="memitem:ac331f893e190c7167a1754c40c5a6b77"><td class="memItemLeft" align="right" valign="top"><a id="ac331f893e190c7167a1754c40c5a6b77"></a>
bool(*&#160;</td><td class="memItemRight" valign="bottom"><b>address_validator</b> )(<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> remote_bda) = nullptr</td></tr>
<tr class="separator:ac331f893e190c7167a1754c40c5a6b77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6358be19cd16d24c31e538bbdf821fca"><td class="memItemLeft" align="right" valign="top"><a id="a6358be19cd16d24c31e538bbdf821fca"></a>
TaskHandle_t&#160;</td><td class="memItemRight" valign="bottom"><b>app_task_handle</b> = nullptr</td></tr>
<tr class="separator:a6358be19cd16d24c31e538bbdf821fca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d1d8b85c5a1ac1822496e7742fc4ed6"><td class="memItemLeft" align="right" valign="top"><a id="a8d1d8b85c5a1ac1822496e7742fc4ed6"></a>
QueueHandle_t&#160;</td><td class="memItemRight" valign="bottom"><b>app_task_queue</b> = nullptr</td></tr>
<tr class="separator:a8d1d8b85c5a1ac1822496e7742fc4ed6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d557628ca4cdb883fa005df2f494f32"><td class="memItemLeft" align="right" valign="top"><a id="a1d557628ca4cdb883fa005df2f494f32"></a>
<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>audio_state</b> = <a class="el" href="group__a2dp.html#gga49adfa87b1ad7420b0075a0ac03cc194a2cac45c7b95065ed02420c5a632ab538">ESP_A2D_AUDIO_STATE_STOPPED</a></td></tr>
<tr class="separator:a1d557628ca4cdb883fa005df2f494f32"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa227651f633af4d364515a7691d68782"><td class="memItemLeft" align="right" valign="top"><a id="aa227651f633af4d364515a7691d68782"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>audio_state_callback</b> )(<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state, void *obj) = nullptr</td></tr>
<tr class="separator:aa227651f633af4d364515a7691d68782"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a586a5e9db2ad916cdcb32a2ab91d9776"><td class="memItemLeft" align="right" valign="top"><a id="a586a5e9db2ad916cdcb32a2ab91d9776"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>audio_state_callback_post</b> )(<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state, void *obj) = nullptr</td></tr>
<tr class="separator:a586a5e9db2ad916cdcb32a2ab91d9776"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a430ec514ce2596d8d1f644defa2090b0"><td class="memItemLeft" align="right" valign="top"><a id="a430ec514ce2596d8d1f644defa2090b0"></a>
void *&#160;</td><td class="memItemRight" valign="bottom"><b>audio_state_obj</b> = nullptr</td></tr>
<tr class="separator:a430ec514ce2596d8d1f644defa2090b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7d6ebc612bc39d8c0d774b05b5fb5435"><td class="memItemLeft" align="right" valign="top"><a id="a7d6ebc612bc39d8c0d774b05b5fb5435"></a>
void *&#160;</td><td class="memItemRight" valign="bottom"><b>audio_state_obj_post</b> = nullptr</td></tr>
<tr class="separator:a7d6ebc612bc39d8c0d774b05b5fb5435"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a91cc0f7e05ce24e311be3aa6281f4322"><td class="memItemLeft" align="right" valign="top"><a id="a91cc0f7e05ce24e311be3aa6281f4322"></a>
esp_a2d_mct_t&#160;</td><td class="memItemRight" valign="bottom"><b>audio_type</b></td></tr>
<tr class="separator:a91cc0f7e05ce24e311be3aa6281f4322"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf4dea0e87bd46b46e2bee811d0f81e6"><td class="memItemLeft" align="right" valign="top"><a id="aaf4dea0e87bd46b46e2bee811d0f81e6"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>avrc_connection_state</b> = false</td></tr>
<tr class="separator:aaf4dea0e87bd46b46e2bee811d0f81e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2be2a2c67f53ae92c34324bca6031f91"><td class="memItemLeft" align="right" valign="top"><a id="a2be2a2c67f53ae92c34324bca6031f91"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>avrc_connection_state_callback</b> )(bool connected) = nullptr</td></tr>
<tr class="separator:a2be2a2c67f53ae92c34324bca6031f91"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae2dd652a4ae05689eec6edc1be4c0add"><td class="memItemLeft" align="right" valign="top"><a id="ae2dd652a4ae05689eec6edc1be4c0add"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>avrc_metadata_callback</b> )(uint8_t, const uint8_t *) = nullptr</td></tr>
<tr class="separator:ae2dd652a4ae05689eec6edc1be4c0add"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8edf3f614972dbee071afb8eb7b2779a"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><b>avrc_metadata_flags</b></td></tr>
<tr class="separator:a8edf3f614972dbee071afb8eb7b2779a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2eaf5d6672d7cfcac7622790254b1afd"><td class="memItemLeft" align="right" valign="top">std::vector&lt; <a class="el" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><b>avrc_rn_events</b></td></tr>
<tr class="separator:a2eaf5d6672d7cfcac7622790254b1afd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6f34cb9044bd3381d73775ec32e64351"><td class="memItemLeft" align="right" valign="top"><a id="a6f34cb9044bd3381d73775ec32e64351"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>avrc_rn_play_pos_callback</b> )(uint32_t) = nullptr</td></tr>
<tr class="separator:a6f34cb9044bd3381d73775ec32e64351"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a39b59108b8dfa1c63d239a098fa87a86"><td class="memItemLeft" align="right" valign="top"><a id="a39b59108b8dfa1c63d239a098fa87a86"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>avrc_rn_playstatus_callback</b> )(<a class="el" href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a>) = nullptr</td></tr>
<tr class="separator:a39b59108b8dfa1c63d239a098fa87a86"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5159afdc0fba569fc2ae7c373a0ad41e"><td class="memItemLeft" align="right" valign="top"><a id="a5159afdc0fba569fc2ae7c373a0ad41e"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>avrc_rn_track_change_callback</b> )(uint8_t *) = nullptr</td></tr>
<tr class="separator:a5159afdc0fba569fc2ae7c373a0ad41e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6f59a9d98e48f0181c800125fca62050"><td class="memItemLeft" align="right" valign="top"><a id="a6f59a9d98e48f0181c800125fca62050"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>avrc_rn_volchg_complete_callback</b> )(int) = nullptr</td></tr>
<tr class="separator:a6f59a9d98e48f0181c800125fca62050"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a729840e194330a64ffbccc52d853ac4a"><td class="memItemLeft" align="right" valign="top"><a id="a729840e194330a64ffbccc52d853ac4a"></a>
esp_bluedroid_config_t&#160;</td><td class="memItemRight" valign="bottom"><b>bluedroid_config</b> {.ssp_en = true}</td></tr>
<tr class="separator:a729840e194330a64ffbccc52d853ac4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a515acb1d23ccd8188067ee68c2539aff"><td class="memItemLeft" align="right" valign="top"><a id="a515acb1d23ccd8188067ee68c2539aff"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>bt_connected</b> )() = nullptr</td></tr>
<tr class="separator:a515acb1d23ccd8188067ee68c2539aff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6921ec780feb37367fa69af774291a7f"><td class="memItemLeft" align="right" valign="top"><a id="a6921ec780feb37367fa69af774291a7f"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>bt_dis_connected</b> )() = nullptr</td></tr>
<tr class="separator:a6921ec780feb37367fa69af774291a7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1cd750d4718e5f4053131668c9851b63"><td class="memItemLeft" align="right" valign="top"><a id="a1cd750d4718e5f4053131668c9851b63"></a>
<a class="el" href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">esp_bt_mode_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>bt_mode</b> = <a class="el" href="group__a2dp.html#gga9861ef3ac455a4b2875219d457073de4a9fab3664192991a2bd656ba090c1da85">ESP_BT_MODE_CLASSIC_BT</a></td></tr>
<tr class="separator:a1cd750d4718e5f4053131668c9851b63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e53210a1eeea015457cfc94df7883e1"><td class="memItemLeft" align="right" valign="top"><a id="a6e53210a1eeea015457cfc94df7883e1"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><b>bt_name</b> = {0}</td></tr>
<tr class="separator:a6e53210a1eeea015457cfc94df7883e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32e9c57e2ff3224b94dfdc69ea52ad60"><td class="memItemLeft" align="right" valign="top"><a id="a32e9c57e2ff3224b94dfdc69ea52ad60"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>bt_volumechange</b> )(int) = nullptr</td></tr>
<tr class="separator:a32e9c57e2ff3224b94dfdc69ea52ad60"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a24fb2e82758a45d60d820d8877153563"><td class="memItemLeft" align="right" valign="top"><a id="a24fb2e82758a45d60d820d8877153563"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>connection_rety_count</b> = 0</td></tr>
<tr class="separator:a24fb2e82758a45d60d820d8877153563"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac11aaa770b2754858223a4bcdae83b5b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>connection_state</b></td></tr>
<tr class="separator:ac11aaa770b2754858223a4bcdae83b5b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefac3515fd23a006c95a754c7ad9ee28"><td class="memItemLeft" align="right" valign="top"><a id="aefac3515fd23a006c95a754c7ad9ee28"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>connection_state_callback</b> )(<a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> state, void *obj) = nullptr</td></tr>
<tr class="separator:aefac3515fd23a006c95a754c7ad9ee28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76c7cf9a20791dbfa9ee0117e3d42b95"><td class="memItemLeft" align="right" valign="top"><a id="a76c7cf9a20791dbfa9ee0117e3d42b95"></a>
void *&#160;</td><td class="memItemRight" valign="bottom"><b>connection_state_obj</b> = nullptr</td></tr>
<tr class="separator:a76c7cf9a20791dbfa9ee0117e3d42b95"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa645d8bec02c53604e93b0dda6f34b15"><td class="memItemLeft" align="right" valign="top"><a id="aa645d8bec02c53604e93b0dda6f34b15"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>data_received</b> )() = nullptr</td></tr>
<tr class="separator:aa645d8bec02c53604e93b0dda6f34b15"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad04d173227dbd77d1956747cc10837c4"><td class="memItemLeft" align="right" valign="top"><a id="ad04d173227dbd77d1956747cc10837c4"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>debounce_ms</b> = 0</td></tr>
<tr class="separator:ad04d173227dbd77d1956747cc10837c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa84a9c336d81ce5f8a64ce9f3b03fdc6"><td class="memItemLeft" align="right" valign="top"><a id="aa84a9c336d81ce5f8a64ce9f3b03fdc6"></a>
unsigned int&#160;</td><td class="memItemRight" valign="bottom"><b>default_reconnect_timout</b> = 10000</td></tr>
<tr class="separator:aa84a9c336d81ce5f8a64ce9f3b03fdc6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7d15ed8662f01083e09b8966ced8234"><td class="memItemLeft" align="right" valign="top"><a id="af7d15ed8662f01083e09b8966ced8234"></a>
<a class="el" href="class_a2_d_p_default_volume_control.html">A2DPDefaultVolumeControl</a>&#160;</td><td class="memItemRight" valign="bottom"><b>default_volume_control</b></td></tr>
<tr class="separator:af7d15ed8662f01083e09b8966ced8234"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2ca23dacb41661dd91b202c0f7939f69"><td class="memItemLeft" align="right" valign="top"><a id="a2ca23dacb41661dd91b202c0f7939f69"></a>
<a class="el" href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">esp_bt_discovery_mode_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>discoverability</b> = <a class="el" href="group__a2dp.html#gga6562796046744d7333ad2c64d2c8557da4fa5e4d5a80a86974515a8a362989711">ESP_BT_GENERAL_DISCOVERABLE</a></td></tr>
<tr class="separator:a2ca23dacb41661dd91b202c0f7939f69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aafad89f653fffa69c9bdee68b37f3f48"><td class="memItemLeft" align="right" valign="top"><a id="aafad89f653fffa69c9bdee68b37f3f48"></a>
esp_spp_mode_t&#160;</td><td class="memItemRight" valign="bottom"><b>esp_spp_mode</b> = ESP_SPP_MODE_CB</td></tr>
<tr class="separator:aafad89f653fffa69c9bdee68b37f3f48"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6737dd3e78e2f979815615b143fcfda"><td class="memItemLeft" align="right" valign="top"><a id="ae6737dd3e78e2f979815615b143fcfda"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>event_queue_size</b> = 20</td></tr>
<tr class="separator:ae6737dd3e78e2f979815615b143fcfda"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abdc17e14de376b611b41974052b4dd2c"><td class="memItemLeft" align="right" valign="top"><a id="abdc17e14de376b611b41974052b4dd2c"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>event_stack_size</b> = 3072</td></tr>
<tr class="separator:abdc17e14de376b611b41974052b4dd2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad383ee1fca4929326b02b671aa03ee44"><td class="memItemLeft" align="right" valign="top"><a id="ad383ee1fca4929326b02b671aa03ee44"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_autoreconnect_allowed</b> = false</td></tr>
<tr class="separator:ad383ee1fca4929326b02b671aa03ee44"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a73abd7cf332f32dfb1d8ff1cc5d59e"><td class="memItemLeft" align="right" valign="top"><a id="a2a73abd7cf332f32dfb1d8ff1cc5d59e"></a>
volatile bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_i2s_active</b> = false</td></tr>
<tr class="separator:a2a73abd7cf332f32dfb1d8ff1cc5d59e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a87fa51e0d2c40d8e5496d7815cc3e981"><td class="memItemLeft" align="right" valign="top"><a id="a87fa51e0d2c40d8e5496d7815cc3e981"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_output</b> = true</td></tr>
<tr class="separator:a87fa51e0d2c40d8e5496d7815cc3e981"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6283bcc3f4609807f746d06ccbfa4fac"><td class="memItemLeft" align="right" valign="top"><a id="a6283bcc3f4609807f746d06ccbfa4fac"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_pin_code_active</b> = false</td></tr>
<tr class="separator:a6283bcc3f4609807f746d06ccbfa4fac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44c96686a0e2d7da22805a1c9fb7ab85"><td class="memItemLeft" align="right" valign="top"><a id="a44c96686a0e2d7da22805a1c9fb7ab85"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_start_disabled</b> = false</td></tr>
<tr class="separator:a44c96686a0e2d7da22805a1c9fb7ab85"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeafe33810da405203ac3a38de6b43086"><td class="memItemLeft" align="right" valign="top"><a id="aeafe33810da405203ac3a38de6b43086"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_target_status_active</b> = true</td></tr>
<tr class="separator:aeafe33810da405203ac3a38de6b43086"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3f1abcd092657807bd4a4cab205ba88"><td class="memItemLeft" align="right" valign="top"><a id="aa3f1abcd092657807bd4a4cab205ba88"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_volume_used</b> = false</td></tr>
<tr class="separator:aa3f1abcd092657807bd4a4cab205ba88"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add25164fa6c099827e04db70d7ab0f62"><td class="memItemLeft" align="right" valign="top"><a id="add25164fa6c099827e04db70d7ab0f62"></a>
<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>last_connection</b> = {0, 0, 0, 0, 0, 0}</td></tr>
<tr class="separator:add25164fa6c099827e04db70d7ab0f62"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd684815c84a3d29da78377615878fd6"><td class="memItemLeft" align="right" valign="top"><a id="acd684815c84a3d29da78377615878fd6"></a>
esp_bt_gap_cb_param_t::read_rssi_delta_param&#160;</td><td class="memItemRight" valign="bottom"><b>last_rssi_delta</b></td></tr>
<tr class="separator:acd684815c84a3d29da78377615878fd6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a25feb62133e76e60a1808a303713c973"><td class="memItemLeft" align="right" valign="top"><a id="a25feb62133e76e60a1808a303713c973"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><b>m_a2d_audio_state_str</b> [4] = {&quot;Suspended&quot;, &quot;Started&quot;, &quot;Suspended&quot;, &quot;Suspended&quot;}</td></tr>
<tr class="separator:a25feb62133e76e60a1808a303713c973"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8614127c04ef9b9ef6d0c9fa2f33d1ed"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><b>m_a2d_conn_state_str</b> [4]</td></tr>
<tr class="separator:a8614127c04ef9b9ef6d0c9fa2f33d1ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44aeeb4214776fea1bca6ecf2e8e8dc2"><td class="memItemLeft" align="right" valign="top">const char *&#160;</td><td class="memItemRight" valign="bottom"><b>m_avrc_playback_state_str</b> [5]</td></tr>
<tr class="separator:a44aeeb4214776fea1bca6ecf2e8e8dc2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad558d5c64a96982632b63103197d6927"><td class="memItemLeft" align="right" valign="top"><a id="ad558d5c64a96982632b63103197d6927"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_pkt_cnt</b> = 0</td></tr>
<tr class="separator:ad558d5c64a96982632b63103197d6927"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af4d9a208a3f1a9eeb469dd3cbf9015d3"><td class="memItemLeft" align="right" valign="top"><a id="af4d9a208a3f1a9eeb469dd3cbf9015d3"></a>
uint16_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_sample_rate</b> = 44100</td></tr>
<tr class="separator:af4d9a208a3f1a9eeb469dd3cbf9015d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a640bbc2b3b6e98bb8b0dcb8eec3700cd"><td class="memItemLeft" align="right" valign="top"><a id="a640bbc2b3b6e98bb8b0dcb8eec3700cd"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>notif_interval_s</b> = 10</td></tr>
<tr class="separator:a640bbc2b3b6e98bb8b0dcb8eec3700cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad4d906fb4e80c8b3ce2fbdf075062644"><td class="memItemLeft" align="right" valign="top"><a id="ad4d906fb4e80c8b3ce2fbdf075062644"></a>
<a class="el" href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a> *&#160;</td><td class="memItemRight" valign="bottom"><b>out</b> = &amp;out_default</td></tr>
<tr class="separator:ad4d906fb4e80c8b3ce2fbdf075062644"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a05d74dd2a83f4378a5f713cb26125e5e"><td class="memItemLeft" align="right" valign="top"><a id="a05d74dd2a83f4378a5f713cb26125e5e"></a>
<a class="el" href="class_bluetooth_a2_d_p_output_default.html">BluetoothA2DPOutputDefault</a>&#160;</td><td class="memItemRight" valign="bottom"><b>out_default</b></td></tr>
<tr class="separator:a05d74dd2a83f4378a5f713cb26125e5e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a83cff3bdeb407a6c19618ba208438604"><td class="memItemLeft" align="right" valign="top"><a id="a83cff3bdeb407a6c19618ba208438604"></a>
<a class="el" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a>&#160;</td><td class="memItemRight" valign="bottom"><b>peer_bd_addr</b></td></tr>
<tr class="separator:a83cff3bdeb407a6c19618ba208438604"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0b0afc8460617c2d2b23abf048fc6f0"><td class="memItemLeft" align="right" valign="top"><a id="ae0b0afc8460617c2d2b23abf048fc6f0"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>pin_code_int</b> = 0</td></tr>
<tr class="separator:ae0b0afc8460617c2d2b23abf048fc6f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a36f12322ad8e025fd100d395b687ed74"><td class="memItemLeft" align="right" valign="top"><a id="a36f12322ad8e025fd100d395b687ed74"></a>
PinCodeRequest&#160;</td><td class="memItemRight" valign="bottom"><b>pin_code_request</b> = Undefined</td></tr>
<tr class="separator:a36f12322ad8e025fd100d395b687ed74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0097f5adbcf09b2c857528afa568c53f"><td class="memItemLeft" align="right" valign="top"><a id="a0097f5adbcf09b2c857528afa568c53f"></a>
char&#160;</td><td class="memItemRight" valign="bottom"><b>pin_code_str</b> [20] = {0}</td></tr>
<tr class="separator:a0097f5adbcf09b2c857528afa568c53f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a89dd2612e77c42a858e6365fffd00b9b"><td class="memItemLeft" align="right" valign="top"><a id="a89dd2612e77c42a858e6365fffd00b9b"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>raw_stream_reader</b> )(const uint8_t *, uint32_t) = nullptr</td></tr>
<tr class="separator:a89dd2612e77c42a858e6365fffd00b9b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae2fdcad112e30ca363b03156cc500cf0"><td class="memItemLeft" align="right" valign="top"><a id="ae2fdcad112e30ca363b03156cc500cf0"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>reconnect_delay</b> = 1000</td></tr>
<tr class="separator:ae2fdcad112e30ca363b03156cc500cf0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0e0c2fae7d742947d47bae41a690a712"><td class="memItemLeft" align="right" valign="top"><a id="a0e0c2fae7d742947d47bae41a690a712"></a>
<a class="el" href="group__a2dp.html#ga28a6ac1cbaf47c9d341da5391e2e72b3">ReconnectStatus</a>&#160;</td><td class="memItemRight" valign="bottom"><b>reconnect_status</b> = NoReconnect</td></tr>
<tr class="separator:a0e0c2fae7d742947d47bae41a690a712"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa350c922076ae8584f26d1693edc0ba8"><td class="memItemLeft" align="right" valign="top"><a id="aa350c922076ae8584f26d1693edc0ba8"></a>
unsigned long&#160;</td><td class="memItemRight" valign="bottom"><b>reconnect_timout</b> = 0</td></tr>
<tr class="separator:aa350c922076ae8584f26d1693edc0ba8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3d8960ea54614b6995ae04c161a0ff82"><td class="memItemLeft" align="right" valign="top"><a id="a3d8960ea54614b6995ae04c161a0ff82"></a>
char&#160;</td><td class="memItemRight" valign="bottom"><b>remote_name</b> [ESP_BT_GAP_MAX_BDNAME_LEN+1]</td></tr>
<tr class="separator:a3d8960ea54614b6995ae04c161a0ff82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7746332ebc078d642b043bfd8be7e6b6"><td class="memItemLeft" align="right" valign="top"><a id="a7746332ebc078d642b043bfd8be7e6b6"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>rssi_active</b> = false</td></tr>
<tr class="separator:a7746332ebc078d642b043bfd8be7e6b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a281e8176841885f19fc0ee9f3a0f119b"><td class="memItemLeft" align="right" valign="top">void(*&#160;</td><td class="memItemRight" valign="bottom"><b>rssi_callbak</b> )(esp_bt_gap_cb_param_t::read_rssi_delta_param &amp;rssi)</td></tr>
<tr class="separator:a281e8176841885f19fc0ee9f3a0f119b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad0f5f42d8ee5ebd9cf79c91eb0d9918c"><td class="memItemLeft" align="right" valign="top"><a id="ad0f5f42d8ee5ebd9cf79c91eb0d9918c"></a>
esp_avrc_rn_evt_cap_mask_t&#160;</td><td class="memItemRight" valign="bottom"><b>s_avrc_peer_rn_cap</b> = {0}</td></tr>
<tr class="separator:ad0f5f42d8ee5ebd9cf79c91eb0d9918c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac450046d5182d0574059f7ddf4a5c611"><td class="memItemLeft" align="right" valign="top"><a id="ac450046d5182d0574059f7ddf4a5c611"></a>
uint8_t&#160;</td><td class="memItemRight" valign="bottom"><b>s_volume</b> = 0</td></tr>
<tr class="separator:ac450046d5182d0574059f7ddf4a5c611"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab99c6480299a2b88fa5b0e1ad01fee6"><td class="memItemLeft" align="right" valign="top"><a id="aab99c6480299a2b88fa5b0e1ad01fee6"></a>
_lock_t&#160;</td><td class="memItemRight" valign="bottom"><b>s_volume_lock</b></td></tr>
<tr class="separator:aab99c6480299a2b88fa5b0e1ad01fee6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a811dc6271aff552907551c78331eea8f"><td class="memItemLeft" align="right" valign="top"><a id="a811dc6271aff552907551c78331eea8f"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>s_volume_notify</b></td></tr>
<tr class="separator:a811dc6271aff552907551c78331eea8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa7b11e1571b9c1bf158166ee423b178"><td class="memItemLeft" align="right" valign="top"><a id="afa7b11e1571b9c1bf158166ee423b178"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>sample_rate_callback</b> )(uint16_t rate) = nullptr</td></tr>
<tr class="separator:afa7b11e1571b9c1bf158166ee423b178"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6af94c47c9c4a11c526bc47750faae52"><td class="memItemLeft" align="right" valign="top"><a id="a6af94c47c9c4a11c526bc47750faae52"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>spp_active</b> = false</td></tr>
<tr class="separator:a6af94c47c9c4a11c526bc47750faae52"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc8411ed83cf8a4b2a972b46eb84b7a8"><td class="memItemLeft" align="right" valign="top"><a id="adc8411ed83cf8a4b2a972b46eb84b7a8"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>stream_reader</b> )(const uint8_t *, uint32_t) = nullptr</td></tr>
<tr class="separator:adc8411ed83cf8a4b2a972b46eb84b7a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00954e585dd50ef40719d208a972adda"><td class="memItemLeft" align="right" valign="top"><a id="a00954e585dd50ef40719d208a972adda"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>swap_left_right</b> = false</td></tr>
<tr class="separator:a00954e585dd50ef40719d208a972adda"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5b0acd3b6c295785607d326128eaa52e"><td class="memItemLeft" align="right" valign="top"><a id="a5b0acd3b6c295785607d326128eaa52e"></a>
BaseType_t&#160;</td><td class="memItemRight" valign="bottom"><b>task_core</b> = 1</td></tr>
<tr class="separator:a5b0acd3b6c295785607d326128eaa52e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e218a2956baea1be621e8c31d2875c8"><td class="memItemLeft" align="right" valign="top"><a id="a2e218a2956baea1be621e8c31d2875c8"></a>
UBaseType_t&#160;</td><td class="memItemRight" valign="bottom"><b>task_priority</b> = configMAX_PRIORITIES - 10</td></tr>
<tr class="separator:a2e218a2956baea1be621e8c31d2875c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab90b3d60f915bab9c9d2d9f4b27e93fe"><td class="memItemLeft" align="right" valign="top"><a id="ab90b3d60f915bab9c9d2d9f4b27e93fe"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>try_reconnect_max_count</b> = AUTOCONNECT_TRY_NUM</td></tr>
<tr class="separator:ab90b3d60f915bab9c9d2d9f4b27e93fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1cb800c8abaaffe89b343b03f5fc77ef"><td class="memItemLeft" align="right" valign="top"><a id="a1cb800c8abaaffe89b343b03f5fc77ef"></a>
<a class="el" href="class_a2_d_p_volume_control.html">A2DPVolumeControl</a> *&#160;</td><td class="memItemRight" valign="bottom"><b>volume_control_ptr</b> = nullptr</td></tr>
<tr class="separator:a1cb800c8abaaffe89b343b03f5fc77ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf02ccde9ba626cea64870a255672dba"><td class="memItemLeft" align="right" valign="top"><a id="aaf02ccde9ba626cea64870a255672dba"></a>
uint8_t&#160;</td><td class="memItemRight" valign="bottom"><b>volume_value</b> = 0</td></tr>
<tr class="separator:aaf02ccde9ba626cea64870a255672dba"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="friends"></a>
Friends</h2></td></tr>
<tr class="memitem:ac0e3ebab2d32e289ec52a08bcfa2e978"><td class="memItemLeft" align="right" valign="top"><a id="ac0e3ebab2d32e289ec52a08bcfa2e978"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ac0e3ebab2d32e289ec52a08bcfa2e978">ccall_audio_data_callback</a> (const uint8_t *data, uint32_t len)</td></tr>
<tr class="memdesc:ac0e3ebab2d32e289ec52a08bcfa2e978"><td class="mdescLeft">&#160;</td><td class="mdescRight">Callback for music stream. <br /></td></tr>
<tr class="separator:ac0e3ebab2d32e289ec52a08bcfa2e978"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ab56fe60162fa9c07bf0ab8f523bfbf"><td class="memItemLeft" align="right" valign="top"><a id="a9ab56fe60162fa9c07bf0ab8f523bfbf"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a9ab56fe60162fa9c07bf0ab8f523bfbf">ccall_av_hdl_a2d_evt</a> (uint16_t event, void *p_param)</td></tr>
<tr class="memdesc:a9ab56fe60162fa9c07bf0ab8f523bfbf"><td class="mdescLeft">&#160;</td><td class="mdescRight">a2dp event handler <br /></td></tr>
<tr class="separator:a9ab56fe60162fa9c07bf0ab8f523bfbf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2f773677a4da51d582e6cadd5a2cc514"><td class="memItemLeft" align="right" valign="top"><a id="a2f773677a4da51d582e6cadd5a2cc514"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a2f773677a4da51d582e6cadd5a2cc514">ccall_av_hdl_avrc_evt</a> (uint16_t event, void *p_param)</td></tr>
<tr class="memdesc:a2f773677a4da51d582e6cadd5a2cc514"><td class="mdescLeft">&#160;</td><td class="mdescRight">avrc event handler <br /></td></tr>
<tr class="separator:a2f773677a4da51d582e6cadd5a2cc514"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac18463dacb2427d3687ef8b930cb9a8d"><td class="memItemLeft" align="right" valign="top"><a id="ac18463dacb2427d3687ef8b930cb9a8d"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ac18463dacb2427d3687ef8b930cb9a8d">ccall_i2s_task_handler</a> (void *arg)</td></tr>
<tr class="memdesc:ac18463dacb2427d3687ef8b930cb9a8d"><td class="mdescLeft">&#160;</td><td class="mdescRight">task hander for i2s <br /></td></tr>
<tr class="separator:ac18463dacb2427d3687ef8b930cb9a8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example <a href="https://github.com/espressif/esp-idf/tree/master/examples/bluetooth/bluedroid/classic_bt/a2dp_sink">https://github.com/espressif/esp-idf/tree/master/examples/bluetooth/bluedroid/classic_bt/a2dp_sink</a> was refactered into a C++ class. </p>
<dl class="section author"><dt>Author</dt><dd>Phil Schatzmann </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Apache License Version 2 </dd></dl>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a2a383635d7b050833f56ee79867716bd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2a383635d7b050833f56ee79867716bd">&#9670;&nbsp;</a></span>BluetoothA2DPSink()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">BluetoothA2DPSink::BluetoothA2DPSink </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Default Constructor: output via callback or Legacy I2S. </p>
<p>Constructor </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="a2cf823459de7a757d94a4ced2f375a0c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2cf823459de7a757d94a4ced2f375a0c">&#9670;&nbsp;</a></span>audio_data_callback()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPSink::audio_data_callback </td>
          <td>(</td>
          <td class="paramtype">const uint8_t *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>len</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Wrappbed methods called from callbacks </p>

</div>
</div>
<a id="a8be3cf8679b236293658c06cd1ed010b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8be3cf8679b236293658c06cd1ed010b">&#9670;&nbsp;</a></span>bt_start()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool BluetoothA2DPCommon::bt_start </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Startup logic as implemented by Arduino. </p>
<dl class="section return"><dt>Returns</dt><dd>true </dd>
<dd>
false </dd></dl>

</div>
</div>
<a id="aa6601d3c57e37f77bfdd03a3ef6231e2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa6601d3c57e37f77bfdd03a3ef6231e2">&#9670;&nbsp;</a></span>debounce()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPCommon::debounce </td>
          <td>(</td>
          <td class="paramtype">void(*)(void)&#160;</td>
          <td class="paramname"><em>cb</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>ms</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Prevents that the same method is executed multiple times within the indicated time limit </p>

</div>
</div>
<a id="a5a91e49987a2e39c09fc6c2a64feaed6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5a91e49987a2e39c09fc6c2a64feaed6">&#9670;&nbsp;</a></span>end()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPSink::end </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>release_memory</em> = <code>false</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>ends the I2S bluetooth sink with the indicated name - if you release the memory a future start is not possible </p>

<p>Reimplemented from <a class="el" href="class_bluetooth_a2_d_p_common.html#a76e329bf0587ebf41792871acc69188b">BluetoothA2DPCommon</a>.</p>

</div>
</div>
<a id="af5d6399876738c8fa0766ea247476b3f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af5d6399876738c8fa0766ea247476b3f">&#9670;&nbsp;</a></span>is_avrc_peer_rn_cap()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool BluetoothA2DPSink::is_avrc_peer_rn_cap </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a>&#160;</td>
          <td class="paramname"><em>cmd</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Provides the result of the last result for the esp_avrc_tg_get_rn_evt_cap() callback (Available from ESP_IDF_4) </p>

</div>
</div>
<a id="a537d576b12d1158eb0681a6195b258de"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a537d576b12d1158eb0681a6195b258de">&#9670;&nbsp;</a></span>set_auto_reconnect() <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPCommon::set_auto_reconnect </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>active</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>activate / deactivate the automatic reconnection to the last address (per default this is on) </p>

</div>
</div>
<a id="af7d10cfe632a3c2f95409f6a23daecdd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af7d10cfe632a3c2f95409f6a23daecdd">&#9670;&nbsp;</a></span>set_auto_reconnect() <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void BluetoothA2DPSink::set_auto_reconnect </td>
          <td>(</td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>reconnect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>count</em> = <code>AUTOCONNECT_TRY_NUM</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Defines the number of times that the system tries to automatically reconnect to the last system </p>

</div>
</div>
<a id="a8281af353148544a0612f8f7c4d511b1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8281af353148544a0612f8f7c4d511b1">&#9670;&nbsp;</a></span>set_avrc_metadata_attribute_mask()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void BluetoothA2DPSink::set_avrc_metadata_attribute_mask </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>flags</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>defines the requested metadata: eg. ESP_AVRC_MD_ATTR_TITLE | ESP_AVRC_MD_ATTR_ARTIST | ESP_AVRC_MD_ATTR_ALBUM | ESP_AVRC_MD_ATTR_TRACK_NUM | ESP_AVRC_MD_ATTR_NUM_TRACKS | ESP_AVRC_MD_ATTR_GENRE | ESP_AVRC_MD_ATTR_PLAYING_TIME </p>

</div>
</div>
<a id="a17013c6f40042c68821548cab9ddb5eb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a17013c6f40042c68821548cab9ddb5eb">&#9670;&nbsp;</a></span>set_avrc_rn_events()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void BluetoothA2DPCommon::set_avrc_rn_events </td>
          <td>(</td>
          <td class="paramtype">std::vector&lt; <a class="el" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a> &gt;&#160;</td>
          <td class="paramname"><em>events</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Define the vector of esp_avrc_rn_event_ids_t with e.g. ESP_AVRC_RN_PLAY_STATUS_CHANGE | ESP_AVRC_RN_TRACK_CHANGE | ESP_AVRC_RN_TRACK_REACHED_END | ESP_AVRC_RN_TRACK_REACHED_START | ESP_AVRC_RN_PLAY_POS_CHANGED | ESP_AVRC_RN_BATTERY_STATUS_CHANGE | ESP_AVRC_RN_SYSTEM_STATUS_CHANGE | ESP_AVRC_RN_APP_SETTING_CHANGE | ESP_AVRC_RN_NOW_PLAYING_CHANGE | ESP_AVRC_RN_AVAILABLE_PLAYERS_CHANGE | ESP_AVRC_RN_ADDRESSED_PLAYER_CHANGE | ESP_AVRC_RN_UIDS_CHANGE|ESP_AVRC_RN_VOLUME_CHANGE </p>

</div>
</div>
<a id="a5dd8ed8e61d6bb6d0c1e05d5c17e45e7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5dd8ed8e61d6bb6d0c1e05d5c17e45e7">&#9670;&nbsp;</a></span>set_avrc_rn_play_pos_callback()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void BluetoothA2DPSink::set_avrc_rn_play_pos_callback </td>
          <td>(</td>
          <td class="paramtype">void(*)(uint32_t play_pos)&#160;</td>
          <td class="paramname"><em>callback</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&#160;</td>
          <td class="paramname"><em>notif_interval</em> = <code>10</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Define a callback method which provides esp_avrc_rn_param_t play position notifications, at a modifiable interval over 1s </p>

</div>
</div>
<a id="a5e4806bad4ed634493643c5925ecf67f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5e4806bad4ed634493643c5925ecf67f">&#9670;&nbsp;</a></span>set_avrc_rn_playstatus_callback()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void BluetoothA2DPSink::set_avrc_rn_playstatus_callback </td>
          <td>(</td>
          <td class="paramtype">void(*)(<a class="el" href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a> playback)&#160;</td>
          <td class="paramname"><em>callback</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Define a callback method which provides esp_avrc_playback_stat_t playback status notifications </p>

</div>
</div>
<a id="ae56f6a99a5c38e0bdd402a79faba6dd5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae56f6a99a5c38e0bdd402a79faba6dd5">&#9670;&nbsp;</a></span>set_avrc_rn_track_change_callback()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void BluetoothA2DPSink::set_avrc_rn_track_change_callback </td>
          <td>(</td>
          <td class="paramtype">void(*)(uint8_t *id)&#160;</td>
          <td class="paramname"><em>callback</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Define a callback method which provides an 8bit array for track change notifications Typically the last bit is 1 when there is a track change (so can be cast to a uint8_t) </p>

</div>
</div>
<a id="a9b16f9fec1e74eb3bb30f6cf572d21e9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9b16f9fec1e74eb3bb30f6cf572d21e9">&#9670;&nbsp;</a></span>set_avrc_rn_volumechange_completed()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPSink::set_avrc_rn_volumechange_completed </td>
          <td>(</td>
          <td class="paramtype">void(*)(int)&#160;</td>
          <td class="paramname"><em>callBack</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>set the callback that the local volume change is notification is received and complete </p>

</div>
</div>
<a id="a41ab8453d4f7f88d68d6cdb1a866532b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a41ab8453d4f7f88d68d6cdb1a866532b">&#9670;&nbsp;</a></span>set_default_bt_mode()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">virtual void BluetoothA2DPCommon::set_default_bt_mode </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__a2dp.html#ga9861ef3ac455a4b2875219d457073de4">esp_bt_mode_t</a>&#160;</td>
          <td class="paramname"><em>mode</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Defines the default bt mode. The default is ESP_BT_MODE_CLASSIC_BT: use this e.g. to set to ESP_BT_MODE_BTDM </p>

</div>
</div>
<a id="a8e53adc58f665113c9ac6a5521e58814"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e53adc58f665113c9ac6a5521e58814">&#9670;&nbsp;</a></span>set_discoverability()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPCommon::set_discoverability </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__a2dp.html#ga6562796046744d7333ad2c64d2c8557d">esp_bt_discovery_mode_t</a>&#160;</td>
          <td class="paramname"><em>d</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Bluetooth discoverability. </p>
<p>Defines if the bluetooth is discoverable. </p>

</div>
</div>
<a id="a5f13ecf541393c21a5a489235bad27fb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5f13ecf541393c21a5a489235bad27fb">&#9670;&nbsp;</a></span>set_on_audio_state_changed()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPCommon::set_on_audio_state_changed </td>
          <td>(</td>
          <td class="paramtype">void(*)(<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state, void *)&#160;</td>
          <td class="paramname"><em>callBack</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>obj</em> = <code>nullptr</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set the callback that is called when the audio state is changed. </p>
<p>Set the callback that is called when the audio state is changed This callback is called before the I2S bus is changed. </p>

</div>
</div>
<a id="a169e9b94cbbfb7311a8722cc6d436e95"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a169e9b94cbbfb7311a8722cc6d436e95">&#9670;&nbsp;</a></span>set_on_audio_state_changed_post()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPCommon::set_on_audio_state_changed_post </td>
          <td>(</td>
          <td class="paramtype">void(*)(<a class="el" href="group__a2dp.html#ga49adfa87b1ad7420b0075a0ac03cc194">esp_a2d_audio_state_t</a> state, void *)&#160;</td>
          <td class="paramname"><em>callBack</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>obj</em> = <code>nullptr</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set the callback that is called after the audio state has changed. This callback is called after the I2S bus has changed. </p>

</div>
</div>
<a id="aa79cff78c075c9273ea2b5c03f052fcd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa79cff78c075c9273ea2b5c03f052fcd">&#9670;&nbsp;</a></span>set_on_connection_state_changed()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPCommon::set_on_connection_state_changed </td>
          <td>(</td>
          <td class="paramtype">void(*)(<a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> state, void *)&#160;</td>
          <td class="paramname"><em>callBack</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>obj</em> = <code>nullptr</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Set the callback that is called when the connection state is changed. </p>
<p>Set the callback that is called when the connection state is changed This callback is called before the I2S bus is changed. </p>

</div>
</div>
<a id="ac245103d3d5c47b0414c2de21c0d52a7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac245103d3d5c47b0414c2de21c0d52a7">&#9670;&nbsp;</a></span>set_on_volumechange()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPSink::set_on_volumechange </td>
          <td>(</td>
          <td class="paramtype">void(*)(int)&#160;</td>
          <td class="paramname"><em>callBack</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set the callback that is called when they change the volume (kept for compatibility) </p>

</div>
</div>
<a id="a035b0e2534970acd2c35b65842374e51"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a035b0e2534970acd2c35b65842374e51">&#9670;&nbsp;</a></span>set_raw_stream_reader()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPSink::set_raw_stream_reader </td>
          <td>(</td>
          <td class="paramtype">void(*)(const uint8_t *, uint32_t)&#160;</td>
          <td class="paramname"><em>callBack</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Define a callback that is called before the volume changes: this callback provides access to the data </p>

</div>
</div>
<a id="a96b4fabd27e7952fc3ea5edca3b95cbb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a96b4fabd27e7952fc3ea5edca3b95cbb">&#9670;&nbsp;</a></span>set_reconnect_delay()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPSink::set_reconnect_delay </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>delay</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Defines the delay that is added to delay the startup when we automatically reconnect </p>

</div>
</div>
<a id="a4f94426ff4899c437d31623e013cf7a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4f94426ff4899c437d31623e013cf7a5">&#9670;&nbsp;</a></span>set_stream_reader()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPSink::set_stream_reader </td>
          <td>(</td>
          <td class="paramtype">void(*)(const uint8_t *, uint32_t)&#160;</td>
          <td class="paramname"><em>callBack</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>i2s_output</em> = <code>true</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Define callback which is called when we receive data: This callback provides access to the data </p>

</div>
</div>
<a id="a694940fad2a2d498875cfbdf52eea58b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a694940fad2a2d498875cfbdf52eea58b">&#9670;&nbsp;</a></span>set_task_core()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPCommon::set_task_core </td>
          <td>(</td>
          <td class="paramtype">BaseType_t&#160;</td>
          <td class="paramname"><em>core</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Defines the core which is used to start the tasks (to process the events and audio queue) </p>

</div>
</div>
<a id="a189424ab5dc8c44f00b461e9392a2ce8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a189424ab5dc8c44f00b461e9392a2ce8">&#9670;&nbsp;</a></span>start()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPSink::start </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>name</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>starts the I2S bluetooth sink with the inidicated name </p>
<p>Main function to start the Bluetooth Processing </p>

</div>
</div>
<a id="a7896335b8f2cc324da86e16efb1544c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7896335b8f2cc324da86e16efb1544c9">&#9670;&nbsp;</a></span>to_str()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char * BluetoothA2DPCommon::to_str </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a>&#160;</td>
          <td class="paramname"><em>state</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>converts esp_avrc_playback_stat_t to a string </p>
<p>converts a esp_a2d_audio_state_t to a string </p>

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a id="a8edf3f614972dbee071afb8eb7b2779a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8edf3f614972dbee071afb8eb7b2779a">&#9670;&nbsp;</a></span>avrc_metadata_flags</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int BluetoothA2DPSink::avrc_metadata_flags</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">=</div>
<div class="line">      ESP_AVRC_MD_ATTR_TITLE | ESP_AVRC_MD_ATTR_ARTIST |</div>
<div class="line">      ESP_AVRC_MD_ATTR_ALBUM | ESP_AVRC_MD_ATTR_TRACK_NUM |</div>
<div class="line">      ESP_AVRC_MD_ATTR_NUM_TRACKS | ESP_AVRC_MD_ATTR_GENRE</div>
</div><!-- fragment -->
</div>
</div>
<a id="a2eaf5d6672d7cfcac7622790254b1afd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2eaf5d6672d7cfcac7622790254b1afd">&#9670;&nbsp;</a></span>avrc_rn_events</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">std::vector&lt;<a class="el" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a>&gt; BluetoothA2DPCommon::avrc_rn_events</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= {</div>
<div class="line">      ESP_AVRC_RN_VOLUME_CHANGE}</div>
</div><!-- fragment -->
</div>
</div>
<a id="ac11aaa770b2754858223a4bcdae83b5b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac11aaa770b2754858223a4bcdae83b5b">&#9670;&nbsp;</a></span>connection_state</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="group__a2dp.html#ga52caa2d1e1c9d880c9651d52ff78a590">esp_a2d_connection_state_t</a> BluetoothA2DPCommon::connection_state</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">=</div>
<div class="line">      <a class="code" href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410">ESP_A2D_CONNECTION_STATE_DISCONNECTED</a></div>
<div class="ttc" id="agroup__a2dp_html_gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410"><div class="ttname"><a href="group__a2dp.html#gga52caa2d1e1c9d880c9651d52ff78a590ab87fa9d0f4c2fa39a931532f0e02e410">ESP_A2D_CONNECTION_STATE_DISCONNECTED</a></div><div class="ttdeci">@ ESP_A2D_CONNECTION_STATE_DISCONNECTED</div><div class="ttdef"><b>Definition:</b> external_lists.h:17</div></div>
</div><!-- fragment -->
</div>
</div>
<a id="a8614127c04ef9b9ef6d0c9fa2f33d1ed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8614127c04ef9b9ef6d0c9fa2f33d1ed">&#9670;&nbsp;</a></span>m_a2d_conn_state_str</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* BluetoothA2DPCommon::m_a2d_conn_state_str[4]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= {<span class="stringliteral">&quot;Disconnected&quot;</span>, <span class="stringliteral">&quot;Connecting&quot;</span>,</div>
<div class="line">                                         <span class="stringliteral">&quot;Connected&quot;</span>, <span class="stringliteral">&quot;Disconnecting&quot;</span>}</div>
</div><!-- fragment -->
</div>
</div>
<a id="a44aeeb4214776fea1bca6ecf2e8e8dc2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44aeeb4214776fea1bca6ecf2e8e8dc2">&#9670;&nbsp;</a></span>m_avrc_playback_state_str</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const char* BluetoothA2DPCommon::m_avrc_playback_state_str[5]</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= {<span class="stringliteral">&quot;stopped&quot;</span>, <span class="stringliteral">&quot;playing&quot;</span>, <span class="stringliteral">&quot;paused&quot;</span>,</div>
<div class="line">                                              <span class="stringliteral">&quot;forward seek&quot;</span>, <span class="stringliteral">&quot;reverse seek&quot;</span>}</div>
</div><!-- fragment -->
</div>
</div>
<a id="a281e8176841885f19fc0ee9f3a0f119b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a281e8176841885f19fc0ee9f3a0f119b">&#9670;&nbsp;</a></span>rssi_callbak</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void(* BluetoothA2DPSink::rssi_callbak) (esp_bt_gap_cb_param_t::read_rssi_delta_param &amp;rssi)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">=</div>
<div class="line">      <span class="keyword">nullptr</span></div>
</div><!-- fragment -->
</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>src/<a class="el" href="_bluetooth_a2_d_p_sink_8h_source.html">BluetoothA2DPSink.h</a></li>
<li>src/BluetoothA2DPSink.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
