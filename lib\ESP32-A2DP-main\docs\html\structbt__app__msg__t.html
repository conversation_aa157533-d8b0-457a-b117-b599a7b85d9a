<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: bt_app_msg_t Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structbt__app__msg__t-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">bt_app_msg_t Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Internal message to be sent for <a class="el" href="class_bluetooth_a2_d_p_sink.html" title="A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example https://github....">BluetoothA2DPSink</a> and <a class="el" href="class_bluetooth_a2_d_p_source.html" title="A2DP Bluetooth Source.">BluetoothA2DPSource</a>.  
 <a href="structbt__app__msg__t.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_bluetooth_a2_d_p_common_8h_source.html">BluetoothA2DPCommon.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a64ce3190ec86ce6a75f6b421319ed8f7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_bluetooth_a2_d_p_common_8h.html#a9bee258e477be3c0e70d6029ed86a019">app_callback_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structbt__app__msg__t.html#a64ce3190ec86ce6a75f6b421319ed8f7">cb</a></td></tr>
<tr class="separator:a64ce3190ec86ce6a75f6b421319ed8f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74f4561abb15d9ae98c3eefdd68de7c4"><td class="memItemLeft" align="right" valign="top">uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structbt__app__msg__t.html#a74f4561abb15d9ae98c3eefdd68de7c4">event</a></td></tr>
<tr class="separator:a74f4561abb15d9ae98c3eefdd68de7c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae06d9a8a215b9ae4b5a3827f5e5e7a7"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structbt__app__msg__t.html#aae06d9a8a215b9ae4b5a3827f5e5e7a7">param</a></td></tr>
<tr class="separator:aae06d9a8a215b9ae4b5a3827f5e5e7a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3d8f18211a5ce29fecbebc5546e7a17e"><td class="memItemLeft" align="right" valign="top">uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structbt__app__msg__t.html#a3d8f18211a5ce29fecbebc5546e7a17e">sig</a></td></tr>
<tr class="separator:a3d8f18211a5ce29fecbebc5546e7a17e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Internal message to be sent for <a class="el" href="class_bluetooth_a2_d_p_sink.html" title="A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example https://github....">BluetoothA2DPSink</a> and <a class="el" href="class_bluetooth_a2_d_p_source.html" title="A2DP Bluetooth Source.">BluetoothA2DPSource</a>. </p>
</div><h2 class="groupheader">Member Data Documentation</h2>
<a id="a64ce3190ec86ce6a75f6b421319ed8f7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a64ce3190ec86ce6a75f6b421319ed8f7">&#9670;&nbsp;</a></span>cb</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_bluetooth_a2_d_p_common_8h.html#a9bee258e477be3c0e70d6029ed86a019">app_callback_t</a> bt_app_msg_t::cb</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>context switch callback </p>

</div>
</div>
<a id="a74f4561abb15d9ae98c3eefdd68de7c4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a74f4561abb15d9ae98c3eefdd68de7c4">&#9670;&nbsp;</a></span>event</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t bt_app_msg_t::event</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>message event id </p>

</div>
</div>
<a id="aae06d9a8a215b9ae4b5a3827f5e5e7a7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aae06d9a8a215b9ae4b5a3827f5e5e7a7">&#9670;&nbsp;</a></span>param</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* bt_app_msg_t::param</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>parameter area needs to be last </p>

</div>
</div>
<a id="a3d8f18211a5ce29fecbebc5546e7a17e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3d8f18211a5ce29fecbebc5546e7a17e">&#9670;&nbsp;</a></span>sig</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t bt_app_msg_t::sig</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>signal to app_task </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>src/<a class="el" href="_bluetooth_a2_d_p_common_8h_source.html">BluetoothA2DPCommon.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
