<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: BluetoothA2DPOutputAudioTools Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-attribs">Protected Attributes</a> &#124;
<a href="class_bluetooth_a2_d_p_output_audio_tools-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">BluetoothA2DPOutputAudioTools Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Output Class using AudioTools library: <a href="https://github.com/pschatzmann/arduino-audio-tools">https://github.com/pschatzmann/arduino-audio-tools</a>.  
 <a href="class_bluetooth_a2_d_p_output_audio_tools.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_bluetooth_a2_d_p_output_8h_source.html">BluetoothA2DPOutput.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for BluetoothA2DPOutputAudioTools:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_bluetooth_a2_d_p_output_audio_tools.png" usemap="#BluetoothA2DPOutputAudioTools_map" alt=""/>
  <map id="BluetoothA2DPOutputAudioTools_map" name="BluetoothA2DPOutputAudioTools_map">
<area href="class_bluetooth_a2_d_p_output.html" title="Abstract Output Class." alt="BluetoothA2DPOutput" shape="rect" coords="0,0,197,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:af0c449c0c616dcef72dcbe40ea60c4c5"><td class="memItemLeft" align="right" valign="top"><a id="af0c449c0c616dcef72dcbe40ea60c4c5"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>begin</b> () override</td></tr>
<tr class="separator:af0c449c0c616dcef72dcbe40ea60c4c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a644a7b2b885e177b9b0d22954dbd4f0e"><td class="memItemLeft" align="right" valign="top"><a id="a644a7b2b885e177b9b0d22954dbd4f0e"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>end</b> () override</td></tr>
<tr class="separator:a644a7b2b885e177b9b0d22954dbd4f0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0a390900833400961f241907b2104bdb"><td class="memItemLeft" align="right" valign="top"><a id="a0a390900833400961f241907b2104bdb"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>operator bool</b> ()</td></tr>
<tr class="separator:a0a390900833400961f241907b2104bdb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7304b027857f382bfb1838b4b36df7f3"><td class="memItemLeft" align="right" valign="top"><a id="a7304b027857f382bfb1838b4b36df7f3"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_output_audio_tools.html#a7304b027857f382bfb1838b4b36df7f3">set_output</a> (audio_tools::AudioOutput &amp;output)</td></tr>
<tr class="memdesc:a7304b027857f382bfb1838b4b36df7f3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output AudioStream using AudioTools library. <br /></td></tr>
<tr class="separator:a7304b027857f382bfb1838b4b36df7f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf9ef0cf6b3e6ef45b03109605c66ccd"><td class="memItemLeft" align="right" valign="top"><a id="adf9ef0cf6b3e6ef45b03109605c66ccd"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_output_audio_tools.html#adf9ef0cf6b3e6ef45b03109605c66ccd">set_output</a> (audio_tools::AudioStream &amp;output)</td></tr>
<tr class="memdesc:adf9ef0cf6b3e6ef45b03109605c66ccd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output AudioStream using AudioTools library. <br /></td></tr>
<tr class="separator:adf9ef0cf6b3e6ef45b03109605c66ccd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3c360cc7bf000d4e8bc241007e34f7d9"><td class="memItemLeft" align="right" valign="top"><a id="a3c360cc7bf000d4e8bc241007e34f7d9"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_output_audio_tools.html#a3c360cc7bf000d4e8bc241007e34f7d9">set_output</a> (Print &amp;output)</td></tr>
<tr class="memdesc:a3c360cc7bf000d4e8bc241007e34f7d9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output to Arduino Print. <br /></td></tr>
<tr class="separator:a3c360cc7bf000d4e8bc241007e34f7d9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8aff51707bd51cb3ba53979fd9823a46"><td class="memItemLeft" align="right" valign="top"><a id="a8aff51707bd51cb3ba53979fd9823a46"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_output_active</b> (bool active) override</td></tr>
<tr class="separator:a8aff51707bd51cb3ba53979fd9823a46"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd8f78674c9d2eda24ec29af797bf31d"><td class="memItemLeft" align="right" valign="top"><a id="abd8f78674c9d2eda24ec29af797bf31d"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_sample_rate</b> (int rate) override</td></tr>
<tr class="separator:abd8f78674c9d2eda24ec29af797bf31d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a688985f4ff19fe4d5eeea9ab6c535e93"><td class="memItemLeft" align="right" valign="top"><a id="a688985f4ff19fe4d5eeea9ab6c535e93"></a>
size_t&#160;</td><td class="memItemRight" valign="bottom"><b>write</b> (const uint8_t *data, size_t len) override</td></tr>
<tr class="separator:a688985f4ff19fe4d5eeea9ab6c535e93"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-attribs"></a>
Protected Attributes</h2></td></tr>
<tr class="memitem:a9b57391e0bc30184bce9ad5e661c75ac"><td class="memItemLeft" align="right" valign="top"><a id="a9b57391e0bc30184bce9ad5e661c75ac"></a>
audio_tools::AudioOutput *&#160;</td><td class="memItemRight" valign="bottom"><b>p_audio_print</b> = nullptr</td></tr>
<tr class="separator:a9b57391e0bc30184bce9ad5e661c75ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42b059a9259db2060ed59c6bebdd9989"><td class="memItemLeft" align="right" valign="top"><a id="a42b059a9259db2060ed59c6bebdd9989"></a>
Print *&#160;</td><td class="memItemRight" valign="bottom"><b>p_print</b> = nullptr</td></tr>
<tr class="separator:a42b059a9259db2060ed59c6bebdd9989"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Output Class using AudioTools library: <a href="https://github.com/pschatzmann/arduino-audio-tools">https://github.com/pschatzmann/arduino-audio-tools</a>. </p>
<dl class="section author"><dt>Author</dt><dd>Phil Schatzmann </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Apache License Version 2 </dd></dl>
</div><hr/>The documentation for this class was generated from the following files:<ul>
<li>src/<a class="el" href="_bluetooth_a2_d_p_output_8h_source.html">BluetoothA2DPOutput.h</a></li>
<li>src/BluetoothA2DPOutput.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
