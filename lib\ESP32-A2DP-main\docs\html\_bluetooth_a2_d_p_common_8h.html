<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: src/BluetoothA2DPCommon.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#enum-members">Enumerations</a> &#124;
<a href="#func-members">Functions</a> &#124;
<a href="#var-members">Variables</a>  </div>
  <div class="headertitle">
<div class="title">BluetoothA2DPCommon.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &quot;config.h&quot;</code><br />
<code>#include &lt;math.h&gt;</code><br />
<code>#include &lt;stdbool.h&gt;</code><br />
<code>#include &lt;stdint.h&gt;</code><br />
<code>#include &lt;stdio.h&gt;</code><br />
<code>#include &lt;stdlib.h&gt;</code><br />
<code>#include &lt;string.h&gt;</code><br />
<code>#include &lt;unistd.h&gt;</code><br />
<code>#include &lt;vector&gt;</code><br />
<code>#include &quot;esp_idf_version.h&quot;</code><br />
<code>#include &quot;freertos/FreeRTOS.h&quot;</code><br />
<code>#include &quot;freertos/FreeRTOSConfig.h&quot;</code><br />
<code>#include &quot;freertos/queue.h&quot;</code><br />
<code>#include &quot;freertos/task.h&quot;</code><br />
<code>#include &quot;freertos/timers.h&quot;</code><br />
<code>#include &quot;xtensa_api.h&quot;</code><br />
<code>#include &quot;A2DPVolumeControl.h&quot;</code><br />
<code>#include &quot;esp_a2dp_api.h&quot;</code><br />
<code>#include &quot;esp_avrc_api.h&quot;</code><br />
<code>#include &quot;esp_bt.h&quot;</code><br />
<code>#include &quot;esp_bt_device.h&quot;</code><br />
<code>#include &quot;esp_bt_main.h&quot;</code><br />
<code>#include &quot;esp_gap_bt_api.h&quot;</code><br />
<code>#include &quot;esp_spp_api.h&quot;</code><br />
<code>#include &quot;esp_task_wdt.h&quot;</code><br />
<code>#include &quot;esp_timer.h&quot;</code><br />
<code>#include &quot;nvs.h&quot;</code><br />
<code>#include &quot;nvs_flash.h&quot;</code><br />
<code>#include &quot;esp_log.h&quot;</code><br />
</div>
<p><a href="_bluetooth_a2_d_p_common_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">class &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Common Bluetooth A2DP functions.  <a href="class_bluetooth_a2_d_p_common.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structbt__app__msg__t.html">bt_app_msg_t</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Internal message to be sent for <a class="el" href="class_bluetooth_a2_d_p_sink.html" title="A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example https://github....">BluetoothA2DPSink</a> and <a class="el" href="class_bluetooth_a2_d_p_source.html" title="A2DP Bluetooth Source.">BluetoothA2DPSource</a>.  <a href="structbt__app__msg__t.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:adb68ab4b3a1950c74c1332eb7f64d707"><td class="memItemLeft" align="right" valign="top"><a id="adb68ab4b3a1950c74c1332eb7f64d707"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>A2DP_DEPRECATED</b>&#160;&#160;&#160;__attribute__((deprecated))</td></tr>
<tr class="separator:adb68ab4b3a1950c74c1332eb7f64d707"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd53be7e960fd8703890d394d8dda41d"><td class="memItemLeft" align="right" valign="top"><a id="afd53be7e960fd8703890d394d8dda41d"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>APP_RC_CT_TL_GET_CAPS</b>&#160;&#160;&#160;(0)</td></tr>
<tr class="separator:afd53be7e960fd8703890d394d8dda41d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62ba9a3b2042befda829c741c29d6bc1"><td class="memItemLeft" align="right" valign="top"><a id="a62ba9a3b2042befda829c741c29d6bc1"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>APP_RC_CT_TL_GET_META_DATA</b>&#160;&#160;&#160;(1)</td></tr>
<tr class="separator:a62ba9a3b2042befda829c741c29d6bc1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae1d078c796ab2ba702d38b72f882784a"><td class="memItemLeft" align="right" valign="top"><a id="ae1d078c796ab2ba702d38b72f882784a"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>APP_RC_CT_TL_RN_PLAY_POS_CHANGE</b>&#160;&#160;&#160;(4)</td></tr>
<tr class="separator:ae1d078c796ab2ba702d38b72f882784a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a39915d96e3ccaedd461897f72e50d237"><td class="memItemLeft" align="right" valign="top"><a id="a39915d96e3ccaedd461897f72e50d237"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>APP_RC_CT_TL_RN_PLAYBACK_CHANGE</b>&#160;&#160;&#160;(3)</td></tr>
<tr class="separator:a39915d96e3ccaedd461897f72e50d237"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8537f6338dbd1e9ffe44147a3a134eaf"><td class="memItemLeft" align="right" valign="top"><a id="a8537f6338dbd1e9ffe44147a3a134eaf"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>APP_RC_CT_TL_RN_TRACK_CHANGE</b>&#160;&#160;&#160;(2)</td></tr>
<tr class="separator:a8537f6338dbd1e9ffe44147a3a134eaf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2a67368759c5f1d7a1ba512b038692f9"><td class="memItemLeft" align="right" valign="top"><a id="a2a67368759c5f1d7a1ba512b038692f9"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>BT_APP_SIG_WORK_DISPATCH</b>&#160;&#160;&#160;(0x01)</td></tr>
<tr class="separator:a2a67368759c5f1d7a1ba512b038692f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afdb3a069ade7c8551e6ddfa31a7fc19a"><td class="memItemLeft" align="right" valign="top"><a id="afdb3a069ade7c8551e6ddfa31a7fc19a"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>BT_APP_TAG</b>&#160;&#160;&#160;&quot;BT_API&quot;</td></tr>
<tr class="separator:afdb3a069ade7c8551e6ddfa31a7fc19a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6f966f5c35766566f8815bec494fbdda"><td class="memItemLeft" align="right" valign="top"><a id="a6f966f5c35766566f8815bec494fbdda"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>BT_AV_TAG</b>&#160;&#160;&#160;&quot;BT_AV&quot;</td></tr>
<tr class="separator:a6f966f5c35766566f8815bec494fbdda"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa87c1f9c93e7fd1b5cfaec76ff011afb"><td class="memItemLeft" align="right" valign="top"><a id="aa87c1f9c93e7fd1b5cfaec76ff011afb"></a>
#define&#160;</td><td class="memItemRight" valign="bottom"><b>BT_RC_CT_TAG</b>&#160;&#160;&#160;&quot;RCCT&quot;</td></tr>
<tr class="separator:aa87c1f9c93e7fd1b5cfaec76ff011afb"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:a9bee258e477be3c0e70d6029ed86a019"><td class="memItemLeft" align="right" valign="top"><a id="a9bee258e477be3c0e70d6029ed86a019"></a>
typedef void(*&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="_bluetooth_a2_d_p_common_8h.html#a9bee258e477be3c0e70d6029ed86a019">app_callback_t</a>) (uint16_t event, void *param)</td></tr>
<tr class="memdesc:a9bee258e477be3c0e70d6029ed86a019"><td class="mdescLeft">&#160;</td><td class="mdescRight">handler for the dispatched work <br /></td></tr>
<tr class="separator:a9bee258e477be3c0e70d6029ed86a019"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="enum-members"></a>
Enumerations</h2></td></tr>
<tr class="memitem:ga28a6ac1cbaf47c9d341da5391e2e72b3"><td class="memItemLeft" align="right" valign="top"><a id="ga28a6ac1cbaf47c9d341da5391e2e72b3"></a>enum &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__a2dp.html#ga28a6ac1cbaf47c9d341da5391e2e72b3">ReconnectStatus</a> { <b>NoReconnect</b>
, <b>AutoReconnect</b>
, <b>IsReconnecting</b>
 }</td></tr>
<tr class="memdesc:ga28a6ac1cbaf47c9d341da5391e2e72b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Buetooth A2DP Reconnect Status. <br /></td></tr>
<tr class="separator:ga28a6ac1cbaf47c9d341da5391e2e72b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:a4321b5374a1413fafafaec82f896eb1c"><td class="memItemLeft" align="right" valign="top"><a id="a4321b5374a1413fafafaec82f896eb1c"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>ccall_app_a2d_callback</b> (esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param)</td></tr>
<tr class="separator:a4321b5374a1413fafafaec82f896eb1c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad11e4bda6ef98605d8df00e510e2703f"><td class="memItemLeft" align="right" valign="top"><a id="ad11e4bda6ef98605d8df00e510e2703f"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>ccall_app_gap_callback</b> (esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t *param)</td></tr>
<tr class="separator:ad11e4bda6ef98605d8df00e510e2703f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88292248eabbb77aee7c6d390bd23f62"><td class="memItemLeft" align="right" valign="top"><a id="a88292248eabbb77aee7c6d390bd23f62"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>ccall_app_rc_ct_callback</b> (esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param)</td></tr>
<tr class="separator:a88292248eabbb77aee7c6d390bd23f62"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac82ed712dca89857181a7d1835cced7c"><td class="memItemLeft" align="right" valign="top"><a id="ac82ed712dca89857181a7d1835cced7c"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>ccall_app_rc_tg_callback</b> (esp_avrc_tg_cb_event_t event, esp_avrc_tg_cb_param_t *param)</td></tr>
<tr class="separator:ac82ed712dca89857181a7d1835cced7c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0260114b8032359247b14f8e7f613af6"><td class="memItemLeft" align="right" valign="top"><a id="a0260114b8032359247b14f8e7f613af6"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>ccall_av_hdl_avrc_tg_evt</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:a0260114b8032359247b14f8e7f613af6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38e69e5d70cecace49c90db414b97970"><td class="memItemLeft" align="right" valign="top"><a id="a38e69e5d70cecace49c90db414b97970"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>ccall_av_hdl_stack_evt</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:a38e69e5d70cecace49c90db414b97970"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6c7e4fb41d19a7d79bce115bd1502649"><td class="memItemLeft" align="right" valign="top"><a id="a6c7e4fb41d19a7d79bce115bd1502649"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>ccall_bt_app_task_handler</b> (void *arg)</td></tr>
<tr class="separator:a6c7e4fb41d19a7d79bce115bd1502649"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="var-members"></a>
Variables</h2></td></tr>
<tr class="memitem:a8a26ad8fa8bfa4ffb3d5f6ecf9d41419"><td class="memItemLeft" align="right" valign="top"><a id="a8a26ad8fa8bfa4ffb3d5f6ecf9d41419"></a>
<a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a> *&#160;</td><td class="memItemRight" valign="bottom"><b>actual_bluetooth_a2dp_common</b></td></tr>
<tr class="separator:a8a26ad8fa8bfa4ffb3d5f6ecf9d41419"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><dl class="section author"><dt>Author</dt><dd>Phil Schatzmann </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>GPLv3 </dd></dl>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
