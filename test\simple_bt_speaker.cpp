#include <Arduino.h>
#include "BluetoothA2DPSink.h"

BluetoothA2DPSink a2dp_sink;

void setup() {
    Serial.begin(115200);
    
    // Configure I2S pins directly using the legacy API
    i2s_pin_config_t pin_config = {
        .bck_io_num = 26,    // BCK pin
        .ws_io_num = 25,     // LRCK pin
        .data_out_num = 27,  // DATA pin
        .data_in_num = -1    // Not used
    };
    
    a2dp_sink.set_pin_config(pin_config);
    a2dp_sink.start("MyMusic");
}
void loop() {}
