<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_a"></a>- a -</h3><ul>
<li>activate_pin_code()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a22d52952a8ac8c78a483a53c2006a387">BluetoothA2DPSink</a>
</li>
<li>app_a2d_callback()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a6aaac4480b57cbdbef5e07ca619eb330">BluetoothA2DPCommon</a>
, <a class="el" href="class_bluetooth_a2_d_p_sink.html#a9892ecf2f81b99d861f2767f7b705188">BluetoothA2DPSink</a>
, <a class="el" href="class_bluetooth_a2_d_p_source.html#aa44bdbc77afd851d305a5412e3cc92e1">BluetoothA2DPSource</a>
</li>
<li>app_rc_ct_callback()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#ac5b64dc4ea62522eee0694454a393b2d">BluetoothA2DPCommon</a>
, <a class="el" href="class_bluetooth_a2_d_p_sink.html#a7f8680e010057c3fea392c75c85c9f23">BluetoothA2DPSink</a>
, <a class="el" href="class_bluetooth_a2_d_p_source.html#a1e2a51edb571273cbd349fbed8874cc1">BluetoothA2DPSource</a>
</li>
<li>audio_data_callback()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a2cf823459de7a757d94a4ced2f375a0c">BluetoothA2DPSink</a>
</li>
</ul>


<h3><a id="index_b"></a>- b -</h3><ul>
<li>BluetoothA2DPCommon()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a7cbfe59ac018d6886622c24139742ebe">BluetoothA2DPCommon</a>
</li>
<li>BluetoothA2DPSink()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a2a383635d7b050833f56ee79867716bd">BluetoothA2DPSink</a>
</li>
<li>BluetoothA2DPSinkQueued()
: <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#a2af5c1af762db64e77815000fedeec01">BluetoothA2DPSinkQueued</a>
</li>
<li>BluetoothA2DPSource()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a545a8b10ab474d787744f85fe784d49a">BluetoothA2DPSource</a>
</li>
<li>bt_app_av_sm_hdlr()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#ad3bb1aaddd9dcbd9da6a37c5aded8727">BluetoothA2DPSource</a>
</li>
<li>bt_app_av_state_connecting_hdlr()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#ac7131b626b43a516ae4ae9df6a7ec366">BluetoothA2DPSource</a>
</li>
<li>bt_app_av_state_unconnected_hdlr()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a025e54e7d0e8a4e07d5a125273fcb875">BluetoothA2DPSource</a>
</li>
<li>bt_av_hdl_avrc_ct_evt()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#ae9f14078c1d5dd00c93049fa8b2e283e">BluetoothA2DPSource</a>
</li>
<li>bt_start()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a8be3cf8679b236293658c06cd1ed010b">BluetoothA2DPCommon</a>
</li>
</ul>


<h3><a id="index_c"></a>- c -</h3><ul>
<li>clean_last_connection()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a962cc9aef396b06c7eb6f56462a743ac">BluetoothA2DPCommon</a>
</li>
<li>confirm_pin_code()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a5d4707195d0d6e79b65bef4ed48a57c2">BluetoothA2DPSink</a>
</li>
<li>connect_to()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a788d81fe538021f912d737de72ed6be6">BluetoothA2DPCommon</a>
</li>
</ul>


<h3><a id="index_d"></a>- d -</h3><ul>
<li>debounce()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#aa6601d3c57e37f77bfdd03a3ef6231e2">BluetoothA2DPCommon</a>
</li>
<li>delay_ms()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a2302fff324e703c3906835f759e87307">BluetoothA2DPCommon</a>
</li>
<li>disconnect()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#ab63e627832d6377be32dd700130bf0d8">BluetoothA2DPCommon</a>
</li>
</ul>


<h3><a id="index_e"></a>- e -</h3><ul>
<li>end()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a76e329bf0587ebf41792871acc69188b">BluetoothA2DPCommon</a>
, <a class="el" href="class_bluetooth_a2_d_p_sink.html#a5a91e49987a2e39c09fc6c2a64feaed6">BluetoothA2DPSink</a>
, <a class="el" href="class_bluetooth_a2_d_p_source.html#aef98c427f8e590be0a4dfaa28a5cb4fd">BluetoothA2DPSource</a>
</li>
</ul>


<h3><a id="index_f"></a>- f -</h3><ul>
<li>fast_forward()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a42e01689353026b1ef9883fd5d32f00c">BluetoothA2DPSink</a>
</li>
</ul>


<h3><a id="index_g"></a>- g -</h3><ul>
<li>get_audio_data()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a2c3a7aa140cf42a1f324e7669a65e5cc">BluetoothA2DPSource</a>
</li>
<li>get_audio_data_volume()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a03d1c23eb8a98bccdd15970f9d35db8c">BluetoothA2DPSource</a>
</li>
<li>get_audio_state()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a74eadbd69b5c7adf1b190c7e41b75b10">BluetoothA2DPCommon</a>
</li>
<li>get_audio_type()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a77600cb1e36b7814eb9b4126cdec62d4">BluetoothA2DPSink</a>
</li>
<li>get_connected_source_name()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a2d277d15f94823eea70f80a327344939">BluetoothA2DPSink</a>
</li>
<li>get_connection_state()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a513b32676d8fc248bb481180f832ef97">BluetoothA2DPCommon</a>
</li>
<li>get_current_peer_address()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#ac08fff859e0ccfbb12cbb6b119dba438">BluetoothA2DPSink</a>
</li>
<li>get_last_peer_address()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#ac21e1dbd2f5f475da871a7e778ba1a40">BluetoothA2DPCommon</a>
</li>
<li>get_last_rssi()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a5a770be98d977a8df916d8cc044b310c">BluetoothA2DPSink</a>
</li>
<li>get_millis()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a688bfd727bfed94f255b63c16a6b1b3c">BluetoothA2DPCommon</a>
</li>
<li>get_name()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a726f45e4d405f5c5f5b259f11aaf8246">BluetoothA2DPCommon</a>
</li>
<li>get_output()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a1c88745461503e5b95f26e41f409e428">BluetoothA2DPSink</a>
</li>
<li>get_peer_name()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#ac9adc3ca64e68fb3d6e816698a725dd5">BluetoothA2DPSink</a>
</li>
<li>get_volume()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a0e570c2c2f9db40873286e0571f0d93a">BluetoothA2DPCommon</a>
, <a class="el" href="class_bluetooth_a2_d_p_sink.html#aca1119f20d2321fb950ae859000cce7b">BluetoothA2DPSink</a>
</li>
</ul>


<h3><a id="index_i"></a>- i -</h3><ul>
<li>i2s_task_handler()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#afdc2f08c0547393704fd6fb56bde204f">BluetoothA2DPSink</a>
, <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#a914815f36b15b259d328da372b6c3d08">BluetoothA2DPSinkQueued</a>
</li>
<li>i2s_write_data()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#ab59e6c716d9b5aaed69272e7d2a3d12a">BluetoothA2DPSink</a>
</li>
<li>is_avrc_connected()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a458dc625cbceb5e534b07094136f6533">BluetoothA2DPSink</a>
</li>
<li>is_avrc_peer_rn_cap()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#af5d6399876738c8fa0766ea247476b3f">BluetoothA2DPSink</a>
</li>
<li>is_avrc_peer_rn_cap_available()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a8281ab4339dd58c49d1d651dd74f5711">BluetoothA2DPSink</a>
</li>
<li>is_connected()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a5e76412770515732e3f54275decf02f0">BluetoothA2DPCommon</a>
</li>
<li>is_discovery_active()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#af29d19e53e3585446fc294a3213a06af">BluetoothA2DPSource</a>
</li>
<li>is_output_active()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a6eb9f31e643607224fa784ff20654924">BluetoothA2DPSink</a>
</li>
<li>is_valid_cod_service()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a51f93bebf73f8bf9b98fa3c5fc4fcb18">BluetoothA2DPSource</a>
</li>
</ul>


<h3><a id="index_l"></a>- l -</h3><ul>
<li>log_free_heap()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a791432e5c800e75fb11b858071cff651">BluetoothA2DPCommon</a>
</li>
</ul>


<h3><a id="index_n"></a>- n -</h3><ul>
<li>next()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a296fb7aaf8d8e78991d9d505353de94f">BluetoothA2DPSink</a>
</li>
</ul>


<h3><a id="index_p"></a>- p -</h3><ul>
<li>pause()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#aa6967e9329939596c62f16e8686cac13">BluetoothA2DPSink</a>
</li>
<li>pin_code()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a3719138f63afaeed06b63cc48ea79335">BluetoothA2DPSink</a>
</li>
<li>play()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#aafd2afad1960db8ab73d7c6977aeb686">BluetoothA2DPSink</a>
</li>
<li>previous()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a341024c18eabdb06c734c2242d5ba505">BluetoothA2DPSink</a>
</li>
</ul>


<h3><a id="index_r"></a>- r -</h3><ul>
<li>reconnect()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#ac795a023f85438355a1b00644f2b040f">BluetoothA2DPCommon</a>
</li>
<li>reset_last_connection()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a190c59464f53e2d4c3f121afbb7a3c21">BluetoothA2DPSource</a>
</li>
<li>rewind()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a9ee01e6d11ee3c6c546a510029a23a12">BluetoothA2DPSink</a>
</li>
</ul>


<h3><a id="index_s"></a>- s -</h3><ul>
<li>sample_rate()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a90cee550d061836992616161d4215355">BluetoothA2DPSink</a>
</li>
<li>set_address_validator()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#ad25155f02bad11da6c130aae00c8ab9c">BluetoothA2DPSink</a>
</li>
<li>set_auto_reconnect()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a537d576b12d1158eb0681a6195b258de">BluetoothA2DPCommon</a>
, <a class="el" href="class_bluetooth_a2_d_p_sink.html#af7d10cfe632a3c2f95409f6a23daecdd">BluetoothA2DPSink</a>
, <a class="el" href="class_bluetooth_a2_d_p_source.html#a65ac6f2b0777c97874ee358119de3790">BluetoothA2DPSource</a>
</li>
<li>set_avrc_connection_state_callback()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#aedd80fe4cf8bd887224efa2716ad9d69">BluetoothA2DPSink</a>
</li>
<li>set_avrc_metadata_attribute_mask()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a8281af353148544a0612f8f7c4d511b1">BluetoothA2DPSink</a>
</li>
<li>set_avrc_metadata_callback()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#aac9074521c80d7574a855f30b8301d13">BluetoothA2DPSink</a>
</li>
<li>set_avrc_passthru_command_callback()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a72999af828301ef39f1f388fd7356b3b">BluetoothA2DPSource</a>
</li>
<li>set_avrc_rn_events()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a17013c6f40042c68821548cab9ddb5eb">BluetoothA2DPCommon</a>
</li>
<li>set_avrc_rn_play_pos_callback()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a5dd8ed8e61d6bb6d0c1e05d5c17e45e7">BluetoothA2DPSink</a>
</li>
<li>set_avrc_rn_playstatus_callback()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a5e4806bad4ed634493643c5925ecf67f">BluetoothA2DPSink</a>
</li>
<li>set_avrc_rn_track_change_callback()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#ae56f6a99a5c38e0bdd402a79faba6dd5">BluetoothA2DPSink</a>
</li>
<li>set_avrc_rn_volumechange()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#acb34360759c6a94028f74df35009b033">BluetoothA2DPSink</a>
</li>
<li>set_avrc_rn_volumechange_completed()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a9b16f9fec1e74eb3bb30f6cf572d21e9">BluetoothA2DPSink</a>
</li>
<li>set_bluedroid_config_t()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a145c20271b53d4329e0e2c7fa36692b0">BluetoothA2DPCommon</a>
</li>
<li>set_connectable()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a8b37f48a6b0bca33fb21b2a9ae9dab7c">BluetoothA2DPCommon</a>
</li>
<li>set_connected()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a0a8a860a325348cdf210637e8d1159e6">BluetoothA2DPCommon</a>
</li>
<li>set_data_callback()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a9eb67e480675059a96014f2c1b84b0c3">BluetoothA2DPSource</a>
</li>
<li>set_data_callback_in_frames()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#ad42fbe4ec00846ab22157545b3885db9">BluetoothA2DPSource</a>
</li>
<li>set_data_source()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a75f389f93441fe0735c3bae5f68043ae">BluetoothA2DPSource</a>
</li>
<li>set_data_source_callback()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a90f7e0445fffbc5ff4ac2f0d50f0b51e">BluetoothA2DPSource</a>
</li>
<li>set_default_bt_mode()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a41ab8453d4f7f88d68d6cdb1a866532b">BluetoothA2DPCommon</a>
</li>
<li>set_discoverability()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a8e53adc58f665113c9ac6a5521e58814">BluetoothA2DPCommon</a>
</li>
<li>set_discovery_mode_callback()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a73eea280b254473cab7c3b1e528b030f">BluetoothA2DPSource</a>
</li>
<li>set_event_queue_size()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#ae5e96c34428c50873a0ca7423a6b5402">BluetoothA2DPCommon</a>
</li>
<li>set_event_stack_size()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a04bc52a4279a503203084492fe20c32e">BluetoothA2DPCommon</a>
</li>
<li>set_i2s_ringbuffer_prefetch_percent()
: <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#a54861eac37a7f5902f6afb1a03200085">BluetoothA2DPSinkQueued</a>
</li>
<li>set_i2s_ringbuffer_size()
: <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#acc0410c71c7278cd3a858d037fdc1edc">BluetoothA2DPSinkQueued</a>
</li>
<li>set_i2s_stack_size()
: <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#a8450f09c7af817cacc78ae9a3544ee28">BluetoothA2DPSinkQueued</a>
</li>
<li>set_i2s_task_priority()
: <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#a430f730deb72b7edf5601e740c9b8563">BluetoothA2DPSinkQueued</a>
</li>
<li>set_local_name()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#acb9c5182525261c53b803719b3027014">BluetoothA2DPSource</a>
</li>
<li>set_mono_downmix()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a624040cce89a4a2f66495f57db6c1457">BluetoothA2DPSink</a>
</li>
<li>set_on_audio_state_changed()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a5f13ecf541393c21a5a489235bad27fb">BluetoothA2DPCommon</a>
</li>
<li>set_on_audio_state_changed_post()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a169e9b94cbbfb7311a8722cc6d436e95">BluetoothA2DPCommon</a>
</li>
<li>set_on_connection_state_changed()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#aa79cff78c075c9273ea2b5c03f052fcd">BluetoothA2DPCommon</a>
</li>
<li>set_on_data_received()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#af65219e635fadbbc90f4663b33abd3e0">BluetoothA2DPSink</a>
</li>
<li>set_on_volumechange()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#ac245103d3d5c47b0414c2de21c0d52a7">BluetoothA2DPSink</a>
</li>
<li>set_output()
: <a class="el" href="class_bluetooth_a2_d_p_output.html#a67fc2cf760ae2a48ac6bfa6ed235f8b8">BluetoothA2DPOutput</a>
, <a class="el" href="class_bluetooth_a2_d_p_output_audio_tools.html#a7304b027857f382bfb1838b4b36df7f3">BluetoothA2DPOutputAudioTools</a>
, <a class="el" href="class_bluetooth_a2_d_p_output_default.html#a69efd35a5b96a8c4ea45286ec02cb550">BluetoothA2DPOutputDefault</a>
, <a class="el" href="class_bluetooth_a2_d_p_output_print.html#aa9e40b46faaba5afdbbf6dbf403c627e">BluetoothA2DPOutputPrint</a>
, <a class="el" href="class_bluetooth_a2_d_p_sink.html#a2076eebe22254b2238ba097e8749d24d">BluetoothA2DPSink</a>
</li>
<li>set_output_active()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a68d88c33b8ec218fe3bd45f61c39f754">BluetoothA2DPSink</a>
</li>
<li>set_pin_code()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a098d9fdb6cfe044406025e89725c449d">BluetoothA2DPSource</a>
</li>
<li>set_raw_stream_reader()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a035b0e2534970acd2c35b65842374e51">BluetoothA2DPSink</a>
</li>
<li>set_reconnect_delay()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a96b4fabd27e7952fc3ea5edca3b95cbb">BluetoothA2DPSink</a>
</li>
<li>set_reset_ble()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a5efcc4c32c6ec8ce7eac2c1acb59f27c">BluetoothA2DPSource</a>
</li>
<li>set_rssi_active()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a452b509a46930ab1ed58188a4181c67b">BluetoothA2DPSink</a>
</li>
<li>set_rssi_callback()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a97c80237061c6e80d7c6e1e5e45773c8">BluetoothA2DPSink</a>
</li>
<li>set_sample_rate_callback()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a89d2694f880a2db22344b97b466c9a9d">BluetoothA2DPSink</a>
</li>
<li>set_scan_mode_connectable()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#af1e2f14ddbe9266b61f5e721095c3685">BluetoothA2DPCommon</a>
</li>
<li>set_spp_active()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#abe6004e2b95d120d64c10cf947fefb55">BluetoothA2DPSink</a>
</li>
<li>set_ssid_callback()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a6ba5496831ff64bdd515fc2ad811d76d">BluetoothA2DPSource</a>
</li>
<li>set_ssp_enabled()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a959ff7f30a6064f018dcdad5deb8e3d9">BluetoothA2DPSource</a>
</li>
<li>set_stream_reader()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a4f94426ff4899c437d31623e013cf7a5">BluetoothA2DPSink</a>
</li>
<li>set_swap_lr_channels()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a14d982730e2b9ea772fe9ede1563ed22">BluetoothA2DPSink</a>
</li>
<li>set_task_core()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a694940fad2a2d498875cfbdf52eea58b">BluetoothA2DPCommon</a>
</li>
<li>set_task_priority()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a68f9e168839f0faeb72705ccabbb6b7a">BluetoothA2DPCommon</a>
</li>
<li>set_valid_cod_service()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a2923c8e2a689f21fc5acf7895ad2f7a7">BluetoothA2DPSource</a>
</li>
<li>set_volume()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a0bab92d9317837ecaeacbfe26814e28c">BluetoothA2DPCommon</a>
, <a class="el" href="class_bluetooth_a2_d_p_sink.html#a507e30ececfdc4382af60a0319cdaf1b">BluetoothA2DPSink</a>
</li>
<li>set_volume_control()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a7757ddbf424aeb909dc952d7c40fc241">BluetoothA2DPCommon</a>
</li>
<li>start()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#af85e99324638d1c814e221ac4ba815dd">BluetoothA2DPSink</a>
, <a class="el" href="class_bluetooth_a2_d_p_source.html#a98c1eb3ad55af189fd3f1ddeac8f3636">BluetoothA2DPSource</a>
</li>
<li>start_raw()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#ad6aac053cc521667ea15a87277009574">BluetoothA2DPSource</a>
</li>
<li>stop()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a37dcbcd418b84310ccedf3330e44834f">BluetoothA2DPSink</a>
</li>
</ul>


<h3><a id="index_t"></a>- t -</h3><ul>
<li>to_state_str()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#ac7cd3655a7e2cfbd7e6c3474a5f2bc34">BluetoothA2DPSource</a>
</li>
<li>to_str()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a2b78346084e12feeea035d006e7cf07a">BluetoothA2DPCommon</a>
</li>
</ul>


<h3><a id="index_u"></a>- u -</h3><ul>
<li>update_rssi()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a3b43ee67031c300d07638f9c969fa4ef">BluetoothA2DPSink</a>
</li>
</ul>


<h3><a id="index_v"></a>- v -</h3><ul>
<li>volume_control()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a6fec0cfd3d0d9017b7ffcf82630ab89a">BluetoothA2DPCommon</a>
</li>
<li>volume_down()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#ae823f16ed3ee17cf9c6d1731b9d19a34">BluetoothA2DPSink</a>
</li>
<li>volume_up()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a42866994c045e27584e0438eb2d4cc79">BluetoothA2DPSink</a>
</li>
</ul>


<h3><a id="index_w"></a>- w -</h3><ul>
<li>write_audio()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#aa211fefd101a639938a20dc3478b48ae">BluetoothA2DPSink</a>
, <a class="el" href="class_bluetooth_a2_d_p_sink_queued.html#a1846088388d294f04469885b9bb560e4">BluetoothA2DPSinkQueued</a>
</li>
</ul>


<h3><a id="index__7E"></a>- ~ -</h3><ul>
<li>~BluetoothA2DPCommon()
: <a class="el" href="class_bluetooth_a2_d_p_common.html#a4bbbd1a2c9c85004afaa7c6dbad45322">BluetoothA2DPCommon</a>
</li>
<li>~BluetoothA2DPSink()
: <a class="el" href="class_bluetooth_a2_d_p_sink.html#a0f83dea1a97baeb360e4e1221c0aeaa9">BluetoothA2DPSink</a>
</li>
<li>~BluetoothA2DPSource()
: <a class="el" href="class_bluetooth_a2_d_p_source.html#a417e7ef0049364c22c92a29e6c4b4ed1">BluetoothA2DPSource</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
