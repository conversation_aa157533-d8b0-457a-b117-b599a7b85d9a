#include <Arduino.h>
#include <driver/i2s.h>

// I2S pin definitions
#define I2S_WS 15    // Left/Right clock
#define I2S_SD 13    // Serial Data (microphone output)
#define I2S_SCK 2   // Serial Clock
#define I2S_PORT I2S_NUM_0

void i2s_install();
void i2s_setpin();

void setup() {
  Serial.begin(115200);
  Serial.println("# INMP441 MEMS Microphone Test");
  Serial.println("# Open Serial Plotter to view waveform");
  delay(1000);
  
  i2s_install();
  i2s_setpin();
  i2s_start(I2S_PORT);
  
  Serial.println("I2S initialized successfully");
  delay(500);
}

void loop() {
  // Buffer for multiple samples to make reading more efficient
  int16_t samples[64];
  size_t bytesRead = 0;
  
  // Read multiple samples from I2S
  esp_err_t result = i2s_read(
    I2S_PORT, 
    (void*)samples, 
    sizeof(samples), 
    &bytesRead, 
    portMAX_DELAY
  );
  
  if (result == ESP_OK && bytesRead > 0) {
    // Calculate how many samples were read
    int samplesRead = bytesRead / sizeof(int16_t);
    
    // Print each sample to Serial Plotter
    for (int i = 0; i < samplesRead; i++) {
      Serial.println(samples[i]);
    }
  }
}

// I2S configuration and setup
void i2s_install() {
  const i2s_config_t i2s_config = {
    .mode = i2s_mode_t(I2S_MODE_MASTER | I2S_MODE_RX),
    .sample_rate = 44100,
    .bits_per_sample = I2S_BITS_PER_SAMPLE_16BIT,
    .channel_format = I2S_CHANNEL_FMT_ONLY_LEFT,
    .communication_format = i2s_comm_format_t(I2S_COMM_FORMAT_STAND_I2S),
    .intr_alloc_flags = ESP_INTR_FLAG_LEVEL1,
    .dma_buf_count = 8,
    .dma_buf_len = 64,
    .use_apll = false,
    .tx_desc_auto_clear = false,
    .fixed_mclk = 0
  };
  i2s_driver_install(I2S_PORT, &i2s_config, 0, NULL);
}

void i2s_setpin() {
  const i2s_pin_config_t pin_config = {
    .bck_io_num = I2S_SCK,
    .ws_io_num = I2S_WS,
    .data_out_num = I2S_PIN_NO_CHANGE,
    .data_in_num = I2S_SD
  };
  i2s_set_pin(I2S_PORT, &pin_config);
}
