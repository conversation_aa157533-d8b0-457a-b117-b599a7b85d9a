#include <Arduino.h>
#include <driver/i2s.h>

// I2S pin definitions (updated as requested)
#define I2S_WS 15    // Left/Right clock
#define I2S_SD 13    // Serial Data (microphone output)
#define I2S_SCK 2    // Serial Clock
#define I2S_PORT I2S_NUM_0

// Recording parameters - Optimized for 1kHz-3.5kHz chirp
#define SAMPLE_RATE 16000       // 16kHz sufficient for 3.5kHz max frequency
#define SHORT_DURATION_SEC 3    // For real-time transmission
#define LONG_DURATION_SEC 10    // For buffered transmission
#define SAMPLES_PER_SECOND SAMPLE_RATE

void i2s_install();
void i2s_setpin();
void record_audio_short();  // 3 seconds real-time
void record_audio_buffered(); // 10 seconds buffered

void setup() {
  Serial.begin(921600);  // Höhere Baudrate für schnellere Übertragung
  Serial.println("🎤 INMP441 Microphone Recording Test");
  Serial.println("📡 Send 'r' for 3s real-time, 'b' for 10s buffered recording");
  Serial.println("⚙️  Config: 16kHz, 16-bit, Mono (optimized for 1-3.5kHz chirp)");
  Serial.printf("📡 UART Baudrate: %d\n", 921600);
  delay(1000);

  i2s_install();
  i2s_setpin();
  i2s_start(I2S_PORT);

  Serial.println("✅ I2S initialized successfully");
  Serial.println("💡 Ready for commands...");
  delay(500);
}

void loop() {
  // Check for UART commands
  if (Serial.available() > 0) {
    char command = Serial.read();

    if (command == 'r' || command == 'R') {
      Serial.println("🎵 Starting 3-second real-time recording...");
      record_audio_short();
      Serial.println("✅ Recording complete!");
      Serial.println("💡 Send 'r' for 3s or 'b' for 10s recording");
    } else if (command == 'b' || command == 'B') {
      Serial.println("🎵 Starting 10-second buffered recording...");
      record_audio_buffered();
      Serial.println("✅ Recording complete!");
      Serial.println("💡 Send 'r' for 3s or 'b' for 10s recording");
    } else {
      Serial.println("❓ Unknown command. Send 'r' for 3s or 'b' for 10s recording.");
    }
  }

  delay(100); // Small delay to prevent excessive polling
}

// Record 3 seconds of audio in real-time
void record_audio_short() {
  const size_t total_samples = SAMPLE_RATE * SHORT_DURATION_SEC;
  Serial.printf("🎤 Starting %d-second real-time recording...\n", SHORT_DURATION_SEC);
  Serial.printf("🎯 Target: %d samples at %d Hz\n", total_samples, SAMPLE_RATE);
  Serial.println("--- AUDIO DATA START ---");

  const size_t buffer_size = 512; // Smaller buffer for real-time
  int16_t buffer[buffer_size];
  size_t samples_recorded = 0;
  unsigned long start_time = millis();

  while (samples_recorded < total_samples) {
    size_t bytes_read = 0;
    size_t samples_to_read = min(buffer_size, total_samples - samples_recorded);
    size_t bytes_to_read = samples_to_read * sizeof(int16_t);

    esp_err_t result = i2s_read(
      I2S_PORT,
      (void*)buffer,
      bytes_to_read,
      &bytes_read,
      portMAX_DELAY
    );

    if (result == ESP_OK && bytes_read > 0) {
      size_t samples_read = bytes_read / sizeof(int16_t);

      // Send samples immediately (real-time)
      for (size_t i = 0; i < samples_read; i++) {
        Serial.println(buffer[i]);
      }

      samples_recorded += samples_read;

      // Progress indicator every second (16000 samples at 16kHz)
      if (samples_recorded % SAMPLE_RATE == 0) {
        size_t seconds = samples_recorded / SAMPLE_RATE;
        unsigned long elapsed_ms = millis() - start_time;
        Serial.printf("📊 Progress: %d/%d seconds (actual: %d ms)\n",
                     seconds, SHORT_DURATION_SEC, elapsed_ms);
      }
    }
  }

  unsigned long total_time = millis() - start_time;
  Serial.println("--- AUDIO DATA END ---");
  Serial.printf("📈 Total samples recorded: %d\n", samples_recorded);
  Serial.printf("⏱️  Total recording time: %d ms\n", total_time);
  Serial.printf("📊 Actual sample rate: %.1f Hz\n", (float)samples_recorded * 1000.0 / total_time);
}

// Record 10 seconds buffered (collect first, then send)
void record_audio_buffered() {
  const size_t total_samples = SAMPLE_RATE * LONG_DURATION_SEC;
  Serial.printf("🎤 Starting %d-second buffered recording...\n", LONG_DURATION_SEC);
  Serial.printf("🎯 Target: %d samples at %d Hz\n", total_samples, SAMPLE_RATE);
  Serial.println("🔄 Recording to memory buffer...");

  // Allocate buffer for all samples (10 seconds = 480000 samples = 960KB)
  int16_t* audio_buffer = (int16_t*)malloc(total_samples * sizeof(int16_t));
  if (!audio_buffer) {
    Serial.println("❌ Failed to allocate memory buffer!");
    return;
  }

  const size_t read_buffer_size = 1024;
  int16_t read_buffer[read_buffer_size];
  size_t samples_recorded = 0;
  unsigned long start_time = millis();

  // Record to memory buffer
  while (samples_recorded < total_samples) {
    size_t bytes_read = 0;
    size_t samples_to_read = min(read_buffer_size, total_samples - samples_recorded);
    size_t bytes_to_read = samples_to_read * sizeof(int16_t);

    esp_err_t result = i2s_read(
      I2S_PORT,
      (void*)read_buffer,
      bytes_to_read,
      &bytes_read,
      portMAX_DELAY
    );

    if (result == ESP_OK && bytes_read > 0) {
      size_t samples_read = bytes_read / sizeof(int16_t);

      // Copy to main buffer
      memcpy(&audio_buffer[samples_recorded], read_buffer, samples_read * sizeof(int16_t));
      samples_recorded += samples_read;

      // Progress indicator every second
      if (samples_recorded % SAMPLE_RATE == 0) {
        size_t seconds = samples_recorded / SAMPLE_RATE;
        unsigned long elapsed_ms = millis() - start_time;
        Serial.printf("📊 Recording: %d/%d seconds (actual: %d ms)\n",
                     seconds, LONG_DURATION_SEC, elapsed_ms);
      }
    }
  }

  unsigned long record_time = millis() - start_time;
  Serial.printf("✅ Recording complete in %d ms\n", record_time);
  Serial.println("📤 Transmitting data...");
  Serial.println("--- AUDIO DATA START ---");

  // Send all data
  unsigned long send_start = millis();
  for (size_t i = 0; i < samples_recorded; i++) {
    Serial.println(audio_buffer[i]);

    // Progress every 48000 samples (1 second worth)
    if ((i + 1) % SAMPLE_RATE == 0) {
      size_t seconds = (i + 1) / SAMPLE_RATE;
      Serial.printf("📤 Sent: %d/%d seconds\n", seconds, LONG_DURATION_SEC);
    }
  }

  unsigned long send_time = millis() - send_start;
  Serial.println("--- AUDIO DATA END ---");
  Serial.printf("📈 Total samples: %d\n", samples_recorded);
  Serial.printf("⏱️  Record time: %d ms\n", record_time);
  Serial.printf("📤 Send time: %d ms\n", send_time);
  Serial.printf("📊 Record sample rate: %.1f Hz\n", (float)samples_recorded * 1000.0 / record_time);

  free(audio_buffer);
}

// I2S configuration and setup
void i2s_install() {
  const i2s_config_t i2s_config = {
    .mode = i2s_mode_t(I2S_MODE_MASTER | I2S_MODE_RX),
    .sample_rate = SAMPLE_RATE,  // 48000 Hz
    .bits_per_sample = I2S_BITS_PER_SAMPLE_16BIT,
    .channel_format = I2S_CHANNEL_FMT_ONLY_LEFT,  // Mono
    .communication_format = i2s_comm_format_t(I2S_COMM_FORMAT_STAND_I2S),
    .intr_alloc_flags = ESP_INTR_FLAG_LEVEL1,
    .dma_buf_count = 8,
    .dma_buf_len = 128,  // Larger buffer for better performance
    .use_apll = false,
    .tx_desc_auto_clear = false,
    .fixed_mclk = 0
  };
  i2s_driver_install(I2S_PORT, &i2s_config, 0, NULL);
}

void i2s_setpin() {
  const i2s_pin_config_t pin_config = {
    .bck_io_num = I2S_SCK,
    .ws_io_num = I2S_WS,
    .data_out_num = I2S_PIN_NO_CHANGE,
    .data_in_num = I2S_SD
  };
  i2s_set_pin(I2S_PORT, &pin_config);
}
