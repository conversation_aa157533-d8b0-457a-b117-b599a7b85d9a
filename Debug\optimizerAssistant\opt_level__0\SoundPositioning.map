******************************************************************************
                  TI ARM Linker PC v20.2.4                     
******************************************************************************
>> Linked Mon Feb 22 19:52:06 2021

OUTPUT FILE NAME:   <SoundPositioning.out>
ENTRY POINT SYMBOL: "resetISR"  address: 00004781


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00058000  00004dbe  00053242  R  X
  GPRAM                 11000000   00002000  00000000  00002000  RW X
  SRAM                  20000000   00014000  00004ee2  0000f11e  RW X
  LOG_DATA              90000000   00040000  00000000  00040000  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004c80   00004c80    r-x
  00000000    00000000    000000d8   000000d8    r-- .intvecs
  000000d8    000000d8    0000490e   0000490e    r-x .text
  000049e8    000049e8    00000298   00000298    r-- .const
00004c80    00004c80    00000008   00000008    rw-
  00004c80    00004c80    00000008   00000008    rw- .args
00004c88    00004c88    000000e0   000000e0    r--
  00004c88    00004c88    000000e0   000000e0    r-- .cinit
00057fa8    00057fa8    00000058   00000058    r--
  00057fa8    00057fa8    00000058   00000058    r-- .ccfg
20000000    20000000    000009ea   00000000    rw-
  20000000    20000000    00000604   00000000    rw- .bss
  20000604    20000604    000003e6   00000000    rw- .data
20000a00    20000a00    000000d8   00000000    rw-
  20000a00    20000a00    000000d8   00000000    rw- .vtable_ram
20001870    20001870    00000010   00000000    rw-
  20001870    20001870    00000010   00000000    rw- .TI.bound:dmaADCPriControlTableEntry
20001a70    20001a70    00004010   00000000    rw-
  20001a70    20001a70    00000010   00000000    rw- .TI.bound:dmaADCAltControlTableEntry
  20001a80    20001a80    00004000   00000000    rw- .sysmem
20013c00    20013c00    00000400   00000000    rw-
  20013c00    20013c00    00000400   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000d8     
                  00000000    000000d8     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.intvecs)

.text      0    000000d8    0000490e     
                  000000d8    000001e0     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_init)
                  000002b8    00000198                      : PowerCC26X2.oem4f (.text:Power_sleep)
                  00000450    00000190                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf1)
                  000005e0    0000018c                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:runCalibrateFsm)
                  0000076c    00000174                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_open)
                  000008e0    00000174                      : PowerCC26X2.oem4f (.text:Power_init)
                  00000a54    00000164                      : UARTCC26XX.oem4f (.text:UARTCC26XX_open)
                  00000bb8    00000154                      : UARTCC26XX.oem4f (.text:UARTCC26XX_hwiIntFxn)
                  00000d0c    0000013a                      : UARTCC26XX.oem4f (.text:UARTCC26XX_read)
                  00000e46    00000002     ti_drivers_config.obj (.text:Board_initHook)
                  00000e48    00000120     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureXOSCHF)
                  00000f68    00000114                      : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfg)
                  0000107c    00000110                      : GPIOCC26XX.oem4f (.text:GPIO_setConfig)
                  0000118c    00000100                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convert)
                  0000128c    00000100     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:aligned_alloc)
                  0000138c    000000f0     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlStandby)
                  0000147c    000000e4     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free)
                  00001560    000000e0     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_standbyPolicy)
                  00001640    000000e0     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_write)
                  00001720    000000d8     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_construct)
                  000017f8    000000c4     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_init)
                  000018bc    000000c0                      : PowerCC26X2.oem4f (.text:Power_releaseDependency)
                  0000197c    000000c0                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initIO)
                  00001a3c    000000bc     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_construct)
                  00001af8    000000b8     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_setDependency)
                  00001bb0    000000b8                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initHw)
                  00001c68    000000b4     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_startup)
                  00001d1c    000000b0     SoundTX.obj (.text:InitTimer2)
                  00001dcc    000000ac     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_workFuncDynamic)
                  00001e78    000000ac     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_open)
                  00001f24    000000ac     driverlib.lib : setup.obj (.text:TrimAfterColdResetWakeupFromShutDown)
                  00001fd0    000000a0     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_pend)
                  00002070    0000009c                     : SwiP_nortos.oem4f (.text:SwiP_dispatch)
                  0000210c    0000009c     SoundTX.obj (.text:Timer2AInterruptHandler)
                  000021a8    0000009c     rtsv7M4_T_le_v4SPD16_eabi.lib : memcpy_t2.asm.obj (.text)
                  00002244    00000098     driverlib.lib : setup.obj (.text:NOROM_SetupTrimDevice)
                  000022dc    00000098     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeTxFifoFlush)
                  00002374    00000096     rtsv7M4_T_le_v4SPD16_eabi.lib : ull_div_t2.asm.obj (.text)
                  0000240a    00000092     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_writeCancel)
                  0000249c    00000088                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_open)
                  00002524    00000088     driverlib.lib : osc.obj (.text:NOROM_OSCHF_AttemptToSwitchToXosc)
                  000025ac    00000088     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf2)
                  00002634    00000088                      : PowerCC26X2.oem4f (.text:disableLFClockQualifiers)
                  000026bc    00000088                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:updateSubSecInc)
                  00002744    00000084                      : UARTCC26XX.oem4f (.text:UARTCC26XX_close)
                  000027c8    00000080                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_hwiFxn)
                  00002848    00000080     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_start)
                  000028c8    00000080                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_construct)
                  00002948    00000080     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlSetRechargeBeforePowerDown)
                  000029c8    0000007a     rtsv7M4_T_le_v4SPD16_eabi.lib : memset_t2.asm.obj (.text)
                  00002a42    00000002     driverlib.lib : interrupt.obj (.text:IntDefaultHandler)
                  00002a44    00000078     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_walkQueueDynamic)
                  00002abc    00000074     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_resetHw)
                  00002b30    00000074                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_auxISR)
                  00002ba4    00000070                      : PINCC26XX.oem4f (.text:PIN_add)
                  00002c14    00000070     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:split)
                  00002c84    0000006c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipType)
                  00002cf0    00000068     ti_drivers_config.obj (.text:Board_sendExtFlashByte)
                  00002d58    00000068     driverlib.lib : osc.obj (.text:NOROM_OSCHF_SwitchToRcOscTurnOffXosc)
                  00002dc0    00000068     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_post)
                  00002e28    00000068                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_start)
                  00002e90    00000068     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_lzss.c.obj (.text:decompress:lzss:__TI_decompress_lzss)
                  00002ef8    00000064     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_control)
                  00002f5c    00000062                      : UARTCC26XX.oem4f (.text:UARTCC26XX_swiIntFxn)
                  00002fbe    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_destruct)
                  00002fc0    00000060     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_write)
                  00003020    0000005c                      : PINCC26XX.oem4f (.text:PIN_remove)
                  0000307c    0000005c     mainNew.obj (.text:main)
                  000030d8    0000005c     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:startTxFifoEmptyClk)
                  00003134    00000058                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_loadDMAControlTableEntry)
                  0000318c    00000058                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_stop)
                  000031e4    00000058                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_initiateCalibration)
                  0000323c    00000054                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_adjustRawValues)
                  00003290    00000054     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOff)
                  000032e4    00000050     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_construct)
                  00003334    0000004e     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_close)
                  00003382    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.text:_nop)
                  00003384    0000004c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_cleanADC)
                  000033d0    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_initHw)
                  0000341c    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_start)
                  00003468    0000004c     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOn)
                  000034b4    0000004c     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readCancel)
                  00003500    0000004c                      : UDMACC26XX.oem4f (.text:UDMACC26XX_open)
                  0000354c    0000004a                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_control)
                  00003596    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:clkFxn)
                  00003598    00000048     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertAdjustedToMicroVolts)
                  000035e0    00000048     ti_drivers_config.obj (.text:Board_wakeUpExtFlash)
                  00003628    00000048     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_construct)
                  00003670    00000048     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputEnable)
                  000036b8    00000048                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_RCOSC_clockFunc)
                  00003700    00000048                      : PowerCC26X2.oem4f (.text:switchXOSCHF)
                  00003748    00000048                      : UARTCC26XX.oem4f (.text:writeFinishedDoCallback)
                  00003790    00000046                      : RingBuf.oem4f (.text:RingBuf_put)
                  000037d6    00000002                      : PowerCC26X2.oem4f (.text:emptyClockFunc)
                  000037d8    00000044                      : ADCBuf.oem4f (.text:ADCBuf_init)
                  0000381c    00000044     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetHwRevision)
                  00003860    00000044     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_swi)
                  000038a4    00000044     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_post)
                  000038e8    00000044                     : SwiP_nortos.oem4f (.text:SwiP_restore)
                  0000392c    00000044     drivers_cc13x2.a : UART.oem4f (.text:UART_init)
                  00003970    00000044     rtsv7M4_T_le_v4SPD16_eabi.lib : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit:__TI_auto_init_nobinit_nopinit)
                  000039b4    00000042     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_swiFxn)
                  000039f6    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:faultISR)
                  000039f8    00000040     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertCancel)
                  00003a38    00000040     driverlib.lib : osc.obj (.text:NOROM_OSC_HPOSCRelativeFrequencyOffsetGet)
                  00003a78    00000040                   : timer.obj (.text:NOROM_TimerIntRegister)
                  00003ab8    00000040     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_get)
                  00003af8    00000040     SoundTX.obj (.text:SoundTransmit)
                  00003b38    00000040     drivers_cc13x2.a : UART.oem4f (.text:UART_open)
                  00003b78    0000003c     mainNew.obj (.text:InitUart)
                  00003bb4    0000003c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputValue)
                  00003bf0    00000038                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_configDMA)
                  00003c28    00000038     ti_drivers_config.obj (.text:Board_shutDownExtFlash)
                  00003c60    00000038     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setConfig)
                  00003c98    00000038     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCurrentTick)
                  00003cd0    00000038                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_initDevice)
                  00003d08    00000038     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:notify)
                  00003d40    00000038                      : UARTCC26XX.oem4f (.text:readData)
                  00003d78    00000038                      : UARTCC26XX.oem4f (.text:writeData)
                  00003db0    00000036     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setNextTick)
                  00003de6    00000036     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_close)
                  00003e1c    00000034     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicksUntilInterrupt)
                  00003e50    00000034     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XXSetLoadMatch)
                  00003e84    00000034                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_configureDebugStall)
                  00003eb8    00000034     driverlib.lib : aux_sysif.obj (.text:NOROM_AUXSYSIFOpModeChange)
                  00003eec    00000034                   : interrupt.obj (.text:NOROM_IntRegister)
                  00003f20    00000034                   : sys_ctrl.obj (.text:NOROM_SysCtrlIdle)
                  00003f54    00000034     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_destruct)
                  00003f88    00000034     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:readData2RingBuf)
                  00003fbc    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_hwiIntFxn)
                  00003fec    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_setCallback)
                  0000401c    00000030                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_close)
                  0000404c    00000030                      : PINCC26XX.oem4f (.text:PINCC26XX_setMux)
                  0000407c    00000030                      : PowerCC26X2.oem4f (.text:Power_releaseConstraint)
                  000040ac    00000030                      : PowerCC26X2.oem4f (.text:Power_setConstraint)
                  000040dc    00000030     driverlib.lib : timer.obj (.text:TimerIntNumberGet)
                  0000410c    00000030     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.text:_c_int00:_c_int00)
                  0000413c    0000002c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_acquireADCSemaphore)
                  00004168    0000002c                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_releaseADCSemaphore)
                  00004194    0000002c     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setThreshold)
                  000041c0    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_registerNotify)
                  000041e8    00000028     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_insert)
                  00004210    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:oscillatorISR)
                  00004238    00000026                      : List.oem4f (.text:List_put)
                  0000425e    00000026                      : List.oem4f (.text:List_remove)
                  00004284    00000024     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicks)
                  000042a8    00000024                     : ClockPTimer_nortos.oem4f (.text:ClockP_scheduleNextTick)
                  000042cc    00000024                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_dispatch)
                  000042f0    00000024     driverlib.lib : osc.obj (.text:NOROM_OSCHF_TurnOnXosc)
                  00004314    00000024     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getMaxTicks)
                  00004338    00000024     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:getTdcSemaphore)
                  0000435c    00000020     ti_drivers_config.obj (.text:Board_init)
                  0000437c    00000020     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_add)
                  0000439c    00000020     driverlib.lib : aux_adc.obj (.text:NOROM_AUXADCEnableSync)
                  000043bc    00000020     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfgMux)
                  000043dc    00000020                      : PowerCC26X2.oem4f (.text:Power_unregisterNotify)
                  000043fc    00000020     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_put)
                  0000441c    00000020                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_dynamicStub)
                  0000443c    00000020     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:threadSafeStdbyDisRelease)
                  0000445c    00000020                      : UARTCC26XX.oem4f (.text:threadSafeStdbyDisSet)
                  0000447c    0000001e                      : PINCC26XX.oem4f (.text:PIN_close)
                  0000449a    0000001e                      : PowerCC26X2.oem4f (.text:Power_getTransitionLatency)
                  000044b8    0000001e     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_constructBinary)
                  000044d6    0000001e                     : SwiP_nortos.oem4f (.text:SwiP_or)
                  000044f4    0000001c                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_destruct)
                  00004510    0000001c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipFamily)
                  0000452c    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_hwi)
                  00004548    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_disable)
                  00004564    0000001c     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_initHw)
                  00004580    0000001c     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_remove)
                  0000459c    0000001a     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:PowerCC26XX_calibrate)
                  000045b6    0000001a     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_get)
                  000045d0    0000001a                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_createBinary)
                  000045ea    00000018                     : ClockPTimer_nortos.oem4f (.text:ClockP_destruct)
                  00004602    00000002                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:intDefaultHandler)
                  00004604    00000018                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_inISR)
                  0000461c    00000018     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetPackageType)
                  00004634    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_idleFunc)
                  0000464c    00000018     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_startup)
                  00004664    00000018     drivers_cc13x2.a : UART.oem4f (.text:UART_Params_init)
                  0000467c    00000018     rtsv7M4_T_le_v4SPD16_eabi.lib : args_main.c.obj (.text:_args_main)
                  00004694    00000018                                   : ll_mul_t2.asm.obj (.text)
                  000046ac    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:switchToTCXO)
                  000046c4    00000016     driverlib.lib : chipinfo.obj (.text:NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated)
                  000046da    00000016     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_create)
                  000046f0    00000016                     : SwiP_nortos.oem4f (.text:maxbit)
                  00004706    00000014     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_freqToCounts)
                  0000471a    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:nmiISR)
                  0000471c    00000014     driverlib.lib : interrupt.obj (.text:NOROM_IntUnregister)
                  00004730    00000014     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_calibrate)
                  00004744    00000014                      : PowerCC26X2.oem4f (.text:Power_disablePolicy)
                  00004758    00000014     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_handleHwi)
                  0000476c    00000014                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCount64)
                  00004780    00000014                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:resetISR)
                  00004794    00000012     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_Params_init)
                  000047a6    00000012     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_Params_init)
                  000047b8    00000012     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_construct)
                  000047ca    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : div0.asm.obj (.text)
                  000047cc    00000010     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_Params_init)
                  000047dc    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_getCpuFreq)
                  000047ec    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_clearInterrupt)
                  000047fc    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_disable)
                  0000480c    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_post)
                  0000481c    00000010     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_getPinCount)
                  0000482c    00000010     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerDisable)
                  0000483c    00000010     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_enablePolicy)
                  0000484c    00000010     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_Params_init)
                  0000485c    00000010                     : SwiP_nortos.oem4f (.text:SwiP_Params_init)
                  0000486c    00000010                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_Params_init)
                  0000487c    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:uartPostNotify)
                  0000488c    0000000e     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_empty)
                  0000489a    0000000e                     : QueueP_nortos.oem4f (.text:QueueP_remove)
                  000048a8    0000000e     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_none.c.obj (.text:decompress:none:__TI_decompress_none)
                  000048b6    00000002     --HOLE-- [fill = 0]
                  000048b8    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_doTick)
                  000048c4    0000000c                     : ClockPTimer_nortos.oem4f (.text:ClockP_getSystemTickPeriod)
                  000048d0    0000000c     driverlib.lib : cpu.obj (.text:NOROM_CPUcpsid)
                  000048dc    0000000c                   : cpu.obj (.text:NOROM_CPUcpsie)
                  000048e8    0000000c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_registerIntCb)
                  000048f4    0000000c     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerRestore)
                  00004900    0000000c     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_getConstraintMask)
                  0000490c    0000000c                      : PowerCC26X2.oem4f (.text:Power_getDependencyCount)
                  00004918    0000000c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_getTrigger)
                  00004924    0000000c                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getFreq)
                  00004930    0000000c     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init)
                  0000493c    0000000a     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_convertCancel)
                  00004946    0000000a                      : UDMACC26XX.oem4f (.text:UDMACC26XX_hwiIntFxn)
                  00004950    0000000a                      : UARTCC26XX.oem4f (.text:readSemCallback)
                  0000495a    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_conversionCallback)
                  00004962    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_init)
                  0000496a    00000008                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_setLoadValue)
                  00004972    00000008     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_restore)
                  0000497a    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_init)
                  00004982    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:malloc)
                  0000498a    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeSemCallback)
                  00004992    00000006     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_stop)
                  00004998    00000006     driverlib.lib : cpu.obj (.text:NOROM_CPUdelay)
                  0000499e    00000006     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_init)
                  000049a4    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readPolling)
                  000049aa    00000006                      : UARTCC26XX.oem4f (.text:UARTCC26XX_writePolling)
                  000049b0    00000006                      : PowerCC26X2.oem4f (.text:delayUs)
                  000049b6    00000004                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_getResolution)
                  000049ba    00000004     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:CPUwfi)
                  000049be    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_isActive)
                  000049c2    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_setTimeout)
                  000049c6    00000004                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_enable)
                  000049ca    00000004                     : QueueP_nortos.oem4f (.text:QueueP_head)
                  000049ce    00000004                     : QueueP_nortos.oem4f (.text:QueueP_next)
                  000049d2    00000004                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_delete)
                  000049d6    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : pre_init.c.obj (.text:_system_pre_init)
                  000049da    00000004                                   : exit.c.obj (.text:abort:abort)
                  000049de    00000004     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureRFCoreClocks)
                  000049e2    00000004                      : PowerCC26X2.oem4f (.text:nopResourceHandler)

.const     0    000049e8    00000298     
                  000049e8    00000054     drivers_cc13x2.a : PowerCC26X2.oem4f (.const:resourceDB)
                  00004a3c    00000028     ti_drivers_config.obj (.const:BoardGpioInitTable)
                  00004a64    00000028     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:UARTCC26XX_fxnTable)
                  00004a8c    00000028     ti_drivers_config.obj (.const:uartCC26XXHWAttrs)
                  00004ab4    00000024     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.const:ADCBufCC26X2_fxnTable)
                  00004ad8    00000024                      : UART.oem4f (.const:UART_defaultParams)
                  00004afc    0000001c                      : GPTimerCC26XX.oem4f (.const:GPT_LUT)
                  00004b18    00000018     ti_drivers_config.obj (.const:GPTimerCC26XX_config)
                  00004b30    00000018     ti_drivers_config.obj (.const:gptimerCC26XXHWAttrs)
                  00004b48    00000018     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:interruptType)
                  00004b60    00000014     ti_drivers_config.obj (.const:$P$T1$2)
                  00004b74    00000014     ti_drivers_config.obj (.const:GPIOCC26XX_config)
                  00004b88    00000014     ti_drivers_config.obj (.const:PowerCC26X2_config)
                  00004b9c    00000014     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:parityType)
                  00004bb0    00000010     nortos_cc13x2.a : SwiP_nortos.oem4f (.const:SwiP_defaultParams)
                  00004bc0    00000010     ti_drivers_config.obj (.const:adcbufCC26XXHWAttrs)
                  00004bd0    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:dataLength)
                  00004be0    00000010                      : GPIOCC26XX.oem4f (.const:outPinTypes)
                  00004bf0    0000000e     ti_drivers_config.obj (.const)
                  00004bfe    00000002     --HOLE-- [fill = 0]
                  00004c00    0000000c     ti_drivers_config.obj (.const:ADCBuf_config)
                  00004c0c    0000000c     ti_drivers_config.obj (.const:UART_config)
                  00004c18    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:inPinTypes)
                  00004c24    0000000c                      : GPIOCC26XX.oem4f (.const:outPinStrengths)
                  00004c30    00000008     driverlib.lib : aux_sysif.obj (.const:$O1$$)
                  00004c38    00000008     ti_drivers_config.obj (.const:$P$T0$1)
                  00004c40    00000008     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.const:GPT_DefaultParams)
                  00004c48    00000008     ti_drivers_config.obj (.const:PINCC26XX_hwAttrs)
                  00004c50    00000008     ti_drivers_config.obj (.const:UDMACC26XX_config)
                  00004c58    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:stopBits)
                  00004c60    00000008     ti_drivers_config.obj (.const:udmaCC26XXHWAttrs)
                  00004c68    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:.string:rxFifoBytes)
                  00004c6e    00000006                      : UARTCC26XX.oem4f (.const:.string:rxFifoThreshold)
                  00004c74    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoBytes)
                  00004c7a    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoThreshold)

.binit     0    00000000    00000000     

.cinit     0    00004c88    000000e0     
                  00004c88    00000088     (.cinit..data.load) [load image, compression = lzss]
                  00004d10    0000000c     (__TI_handler_table)
                  00004d1c    00000004     --HOLE-- [fill = 0]
                  00004d20    00000008     (.cinit..TI.bound:dmaADCAltControlTableEntry.load) [load image, compression = zero_init]
                  00004d28    00000008     (.cinit..TI.bound:dmaADCPriControlTableEntry.load) [load image, compression = zero_init]
                  00004d30    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004d38    00000008     (.cinit..vtable_ram.load) [load image, compression = zero_init]
                  00004d40    00000028     (__TI_cinit_table)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.ccfg      0    00057fa8    00000058     
                  00057fa8    00000058     ti_devices_config.obj (.ccfg:retain)

.bss       0    20000000    00000604     UNINITIALIZED
                  20000000    000001b4     (.common:uartCC26XXObjects)
                  200001b4    000000dc     (.common:adcbufCC26XXbjects)
                  20000290    000000a0     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:ClockP_module)
                  20000330    00000098     (.common:gptimerCC26XXObjects)
                  200003c8    0000007c     (.common:pinHandleTable)
                  20000444    00000050     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinSemaphore)
                  20000494    00000034                      : PINCC26XX.oem4f (.bss:pinSwi)
                  200004c8    00000020     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_readyList)
                  200004e8    00000020     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioCallbackInfo)
                  20000508    00000020                      : PINCC26XX.oem4f (.bss:pinGpioConfigTable)
                  20000528    00000020     ti_drivers_config.obj (.bss:uartCC26XXRingBuffer0)
                  20000548    00000020     (.common:udmaCC26XXObject)
                  20000568    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_hwiStruct)
                  20000584    0000001c                     : TimerPCC26XX_nortos.oem4f (.bss:TimerP_hwiStruct)
                  200005a0    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinHwi)
                  200005bc    0000000d     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss)
                  200005c9    00000001     (.common:driverlib_release_0_59848)
                  200005ca    00000002     --HOLE--
                  200005cc    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioPinState)
                  200005d8    00000008                      : GPIOCC26XX.oem4f (.bss)
                  200005e0    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:timerFreq)
                  200005e8    00000004     drivers_cc13x2.a : PINCC26XX.oem4f (.bss)
                  200005ec    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss)
                  200005f0    00000004                     : PowerCC26X2_nortos.oem4f (.bss)
                  200005f4    00000004                     : TimerPCC26XX_nortos.oem4f (.bss)
                  200005f8    00000004     (.common:i)
                  200005fc    00000004     (.common:j)
                  20000600    00000004     (.common:uart)

.vtable_ram 
*          0    20000a00    000000d8     UNINITIALIZED
                  20000a00    000000d8     driverlib.lib : interrupt.obj (.vtable_ram)

.data      0    20000604    000003e6     UNINITIALIZED
                  20000604    00000170     drivers_cc13x2.a : PowerCC26X2.oem4f (.data:PowerCC26X2_module)
                  20000774    000000d8     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data:HwiP_dispatchTable)
                  2000084c    0000007c     SoundTX.obj (.data:ChirpDelay)
                  200008c8    0000007c     SoundTX.obj (.data:ChirpState)
                  20000944    00000024     driverlib.lib : osc.obj (.data:$O1$$)
                  20000968    00000010     ti_drivers_config.obj (.data:gpioCallbackFunctions)
                  20000978    00000010     ti_drivers_config.obj (.data:gpioPinConfigs)
                  20000988    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data:ClockP_defaultParams)
                  20000994    0000000c                     : TimerPCC26XX_nortos.oem4f (.data:TimerP_defaultParams)
                  200009a0    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.data:$O1$$)
                  200009a8    00000008                                   : memory.c.obj (.data:$O1$$)
                  200009b0    00000008     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data:SemaphoreP_defaultParams)
                  200009b8    00000008     SoundTX.obj (.data)
                  200009c0    00000008     drivers_cc13x2.a : PINCC26XX.oem4f (.data)
                  200009c8    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data)
                  200009d0    00000006     ti_drivers_config.obj (.data:adcBufCC26XXChannelLut0)
                  200009d6    00000001     drivers_cc13x2.a : ADCBuf.oem4f (.data)
                  200009d7    00000001                      : UART.oem4f (.data)
                  200009d8    00000005                      : GPIOCC26XX.oem4f (.data)
                  200009dd    00000003     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data)
                  200009e0    00000004     drivers_cc13x2.a : GPIOCC26XX.oem4f (.data:gpioPinTable)
                  200009e4    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data)
                  200009e8    00000001                     : SwiP_nortos.oem4f (.data)
                  200009e9    00000001                     : TimerPCC26XX_nortos.oem4f (.data)

.sysmem    0    20001a80    00004000     UNINITIALIZED
                  20001a80    00000010     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.sysmem)
                  20001a90    00003ff0     --HOLE--

.stack     0    20013c00    00000400     UNINITIALIZED
                  20013c00    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.stack)
                  20013c04    000003fc     --HOLE--

.log_data 
*          0    90000000    00000000     COPY SECTION

__llvm_prf_cnts 
*          0    20000000    00000000     UNINITIALIZED

.TI.bound:dmaADCPriControlTableEntry 
*          0    20001870    00000010     UNINITIALIZED
                  20001870    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCPriControlTableEntry)

.TI.bound:dmaADCAltControlTableEntry 
*          0    20001a70    00000010     UNINITIALIZED
                  20001a70    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCAltControlTableEntry)

.args      0    00004c80    00000008     
                  00004c80    00000008     --HOLE-- [fill = 0]

MODULE SUMMARY

       Module                             code    ro data   rw data
       ------                             ----    -------   -------
    .\
       SoundTX.obj                        396     0         256    
       mainNew.obj                        152     0         12     
    +--+----------------------------------+-------+---------+---------+
       Total:                             548     0         268    
                                                                   
    .\syscfg\
       ti_drivers_config.obj              266     274       910    
       ti_devices_config.obj              0       88        0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             266     362       910    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos/lib/ccs/m4f/nortos_cc13x2.a
       ClockPTimer_nortos.oem4f           922     0         192    
       SwiP_nortos.oem4f                  724     16        74     
       TimerPCC26XX_nortos.oem4f          642     0         45     
       HwiPCC26XX_nortos.oem4f            294     0         220    
       SemaphoreP_nortos.oem4f            410     0         11     
       PowerCC26X2_nortos.oem4f           256     0         4      
       startup_cc13x2_cc26x2_ccs.oem4f    26      216       0      
       QueueP_nortos.oem4f                100     0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             3374    232       546    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/cc13x2_cc26x2/driverlib/bin/ccs/driverlib.lib
       sys_ctrl.obj                       420     0         0      
       osc.obj                            340     0         36     
       setup.obj                          324     0         0      
       interrupt.obj                      74      0         216    
       chipinfo.obj                       250     0         0      
       prcm.obj                           160     0         0      
       timer.obj                          112     0         0      
       aux_sysif.obj                      52      8         0      
       aux_adc.obj                        32      0         0      
       cpu.obj                            30      0         0      
       driverlib_release.obj              0       0         1      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1794    8         253    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/lib/ccs/m4f/drivers_cc13x2.a
       UARTCC26XX.oem4f                   2760    108       0      
       PowerCC26X2.oem4f                  2096    84        368    
       PINCC26XX.oem4f                    1554    0         328    
       ADCBufCC26X2.oem4f                 1542    36        32     
       PowerCC26X2_calibrateRCOSC.oem4f   1400    0         0      
       GPIOCC26XX.oem4f                   660     64        61     
       GPTimerCC26XX.oem4f                670     36        0      
       UART.oem4f                         156     36        1      
       UDMACC26XX.oem4f                   168     0         0      
       RingBuf.oem4f                      152     0         0      
       ADCBuf.oem4f                       78      0         1      
       List.oem4f                         76      0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             11312   364       791    
                                                                   
    C:\ti\ccs1011\ccs\tools\compiler\ti-cgt-arm_20.2.4.LTS\lib\rtsv7M4_T_le_v4SPD16_eabi.lib
       memory.c.obj                       672     0         8      
       memcpy_t2.asm.obj                  156     0         0      
       ull_div_t2.asm.obj                 150     0         0      
       memset_t2.asm.obj                  122     0         0      
       copy_decompress_lzss.c.obj         104     0         0      
       autoinit.c.obj                     68      0         0      
       boot_cortex_m.c.obj                48      0         0      
       args_main.c.obj                    24      0         0      
       ll_mul_t2.asm.obj                  24      0         0      
       copy_decompress_none.c.obj         14      0         0      
       copy_zero_init.c.obj               12      0         0      
       _lock.c.obj                        2       0         8      
       exit.c.obj                         4       0         0      
       pre_init.c.obj                     4       0         0      
       div0.asm.obj                       2       0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1406    0         16     
                                                                   
       Heap:                              0       0         16384  
       Stack:                             0       0         1024   
       Linker Generated:                  0       220       0      
    +--+----------------------------------+-------+---------+---------+
       Grand Total:                       18700   1186      20192  


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004d40 records: 5, size/record: 8, table size: 40
	.data: load addr=00004c88, load size=00000088 bytes, run addr=20000604, run size=000003e6 bytes, compression=lzss
	.TI.bound:dmaADCAltControlTableEntry: load addr=00004d20, load size=00000008 bytes, run addr=20001a70, run size=00000010 bytes, compression=zero_init
	.TI.bound:dmaADCPriControlTableEntry: load addr=00004d28, load size=00000008 bytes, run addr=20001870, run size=00000010 bytes, compression=zero_init
	.bss: load addr=00004d30, load size=00000008 bytes, run addr=20000000, run size=00000604 bytes, compression=zero_init
	.vtable_ram: load addr=00004d38, load size=00000008 bytes, run addr=20000a00, run size=000000d8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004d10 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_lzss
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                                               
-------   ----                                                               
00004bf3  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004bf0  ADCBUF_CONST                                                       
00004bf1  ADCBUF_SOUND_CONST                                                 
00004bf2  ADCBUF_TEMPERATURE_CONST                                           
0000323d  ADCBufCC26X2_adjustRawValues                                       
00003335  ADCBufCC26X2_close                                                 
0000354d  ADCBufCC26X2_control                                               
0000118d  ADCBufCC26X2_convert                                               
00003599  ADCBufCC26X2_convertAdjustedToMicroVolts                           
000039f9  ADCBufCC26X2_convertCancel                                         
00004ab4  ADCBufCC26X2_fxnTable                                              
000049b7  ADCBufCC26X2_getResolution                                         
00004963  ADCBufCC26X2_init                                                  
0000076d  ADCBufCC26X2_open                                                  
00004c00  ADCBuf_config                                                      
0000493d  ADCBuf_convertCancel                                               
00004bf4  ADCBuf_count                                                       
000037d9  ADCBuf_init                                                        
00004a3c  BoardGpioInitTable                                                 
0000435d  Board_init                                                         
00000e47  Board_initHook                                                     
00002cf1  Board_sendExtFlashByte                                             
00003c29  Board_shutDownExtFlash                                             
000035e1  Board_wakeUpExtFlash                                               
000049db  C$$EXIT                                                            
00004bfb  CONFIG_GPTIMER_0_CONST                                             
00004bfc  CONFIG_GPTIMER_1_CONST                                             
2000084c  ChirpDelay                                                         
200009bc  ChirpIndex                                                         
200009b8  ChirpSize                                                          
200008c8  ChirpState                                                         
000047cd  ClockP_Params_init                                                 
0000437d  ClockP_add                                                         
00003629  ClockP_construct                                                   
000045eb  ClockP_destruct                                                    
000048b9  ClockP_doTick                                                      
000047dd  ClockP_getCpuFreq                                                  
000048c5  ClockP_getSystemTickPeriod                                         
00004285  ClockP_getTicks                                                    
00003e1d  ClockP_getTicksUntilInterrupt                                      
000049bf  ClockP_isActive                                                    
000042a9  ClockP_scheduleNextTick                                            
000049c3  ClockP_setTimeout                                                  
00002849  ClockP_start                                                       
00001c69  ClockP_startup                                                     
00004993  ClockP_stop                                                        
200009cc  ClockP_tickPeriod                                                  
00002a45  ClockP_walkQueueDynamic                                            
00001dcd  ClockP_workFuncDynamic                                             
00004b74  GPIOCC26XX_config                                                  
00003fbd  GPIO_hwiIntFxn                                                     
000017f9  GPIO_init                                                          
00003fed  GPIO_setCallback                                                   
0000107d  GPIO_setConfig                                                     
00002fc1  GPIO_write                                                         
00004795  GPTimerCC26XX_Params_init                                          
0000401d  GPTimerCC26XX_close                                                
00004b18  GPTimerCC26XX_config                                               
00003e85  GPTimerCC26XX_configureDebugStall                                  
0000249d  GPTimerCC26XX_open                                                 
0000496b  GPTimerCC26XX_setLoadValue                                         
0000341d  GPTimerCC26XX_start                                                
0000318d  GPTimerCC26XX_stop                                                 
00004bfd  GPTimer_count                                                      
000047a7  HwiP_Params_init                                                   
000047ed  HwiP_clearInterrupt                                                
000028c9  HwiP_construct                                                     
000044f5  HwiP_destruct                                                      
000047fd  HwiP_disable                                                       
000049c7  HwiP_enable                                                        
00004605  HwiP_inISR                                                         
0000480d  HwiP_post                                                          
00004973  HwiP_restore                                                       
200009e4  HwiP_swiPIntNum                                                    
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
000037d9  InitADC                                                            
00001d1d  InitTimer2                                                         
00003b79  InitUart                                                           
00004239  List_put                                                           
0000425f  List_remove                                                        
0000439d  NOROM_AUXADCEnableSync                                             
00003eb9  NOROM_AUXSYSIFOpModeChange                                         
000048d1  NOROM_CPUcpsid                                                     
000048dd  NOROM_CPUcpsie                                                     
00004999  NOROM_CPUdelay                                                     
00004511  NOROM_ChipInfo_GetChipFamily                                       
00002c85  NOROM_ChipInfo_GetChipType                                         
0000381d  NOROM_ChipInfo_GetHwRevision                                       
0000461d  NOROM_ChipInfo_GetPackageType                                      
00003eed  NOROM_IntRegister                                                  
0000471d  NOROM_IntUnregister                                                
00002525  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002d59  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
000042f1  NOROM_OSCHF_TurnOnXosc                                             
00003a39  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00003291  NOROM_PRCMPowerDomainsAllOff                                       
00003469  NOROM_PRCMPowerDomainsAllOn                                        
00002245  NOROM_SetupTrimDevice                                              
00003f21  NOROM_SysCtrlIdle                                                  
00002949  NOROM_SysCtrlSetRechargeBeforePowerDown                            
0000138d  NOROM_SysCtrlStandby                                               
000046c5  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00003a79  NOROM_TimerIntRegister                                             
000049c7  NoRTOS_start                                                       
0000481d  PINCC26XX_getPinCount                                              
00004c48  PINCC26XX_hwAttrs                                                  
0000404d  PINCC26XX_setMux                                                   
00004bf7  PIN_DRIVE_SPEAKER_A_CONST                                          
00004bf8  PIN_DRIVE_SPEAKER_B_CONST                                          
00004bf5  PIN_TEST1_CONST                                                    
00004bf6  PIN_TEST2_CONST                                                    
00002ba5  PIN_add                                                            
0000447d  PIN_close                                                          
000000d9  PIN_init                                                           
00001e79  PIN_open                                                           
000048e9  PIN_registerIntCb                                                  
00003021  PIN_remove                                                         
00003c61  PIN_setConfig                                                      
00003671  PIN_setOutputEnable                                                
00003bb5  PIN_setOutputValue                                                 
000036b9  PowerCC26X2_RCOSC_clockFunc                                        
00002b31  PowerCC26X2_auxISR                                                 
00004731  PowerCC26X2_calibrate                                              
00004b88  PowerCC26X2_config                                                 
000031e5  PowerCC26X2_initiateCalibration                                    
20000604  PowerCC26X2_module                                                 
0000459d  PowerCC26XX_calibrate                                              
0000482d  PowerCC26XX_schedulerDisable                                       
000048f5  PowerCC26XX_schedulerRestore                                       
00001561  PowerCC26XX_standbyPolicy                                          
00004745  Power_disablePolicy                                                
0000483d  Power_enablePolicy                                                 
00004901  Power_getConstraintMask                                            
0000490d  Power_getDependencyCount                                           
0000449b  Power_getTransitionLatency                                         
00004635  Power_idleFunc                                                     
000008e1  Power_init                                                         
000041c1  Power_registerNotify                                               
0000407d  Power_releaseConstraint                                            
000018bd  Power_releaseDependency                                            
000040ad  Power_setConstraint                                                
00001af9  Power_setDependency                                                
000002b9  Power_sleep                                                        
000043dd  Power_unregisterNotify                                             
0000488d  QueueP_empty                                                       
000045b7  QueueP_get                                                         
000049cb  QueueP_head                                                        
0000499f  QueueP_init                                                        
000049cf  QueueP_next                                                        
000043fd  QueueP_put                                                         
0000489b  QueueP_remove                                                      
000047b9  RingBuf_construct                                                  
00003ab9  RingBuf_get                                                        
00003791  RingBuf_put                                                        
0000484d  SemaphoreP_Params_init                                             
000032e5  SemaphoreP_construct                                               
000044b9  SemaphoreP_constructBinary                                         
000046db  SemaphoreP_create                                                  
000045d1  SemaphoreP_createBinary                                            
200009b0  SemaphoreP_defaultParams                                           
000049d3  SemaphoreP_delete                                                  
00002fbf  SemaphoreP_destruct                                                
00001fd1  SemaphoreP_pend                                                    
000038a5  SemaphoreP_post                                                    
00003af9  SoundTransmit                                                      
0000485d  SwiP_Params_init                                                   
00001721  SwiP_construct                                                     
00003f55  SwiP_destruct                                                      
00004549  SwiP_disable                                                       
00002071  SwiP_dispatch                                                      
00004919  SwiP_getTrigger                                                    
000044d7  SwiP_or                                                            
00002dc1  SwiP_post                                                          
000038e9  SwiP_restore                                                       
0000210d  Timer2AInterruptHandler                                            
0000486d  TimerP_Params_init                                                 
00001a3d  TimerP_construct                                                   
0000441d  TimerP_dynamicStub                                                 
0000476d  TimerP_getCount64                                                  
00003c99  TimerP_getCurrentTick                                              
00004925  TimerP_getFreq                                                     
00004315  TimerP_getMaxTicks                                                 
00003cd1  TimerP_initDevice                                                  
00003db1  TimerP_setNextTick                                                 
00004195  TimerP_setThreshold                                                
00002e29  TimerP_start                                                       
0000464d  TimerP_startup                                                     
00002745  UARTCC26XX_close                                                   
00002ef9  UARTCC26XX_control                                                 
00004a64  UARTCC26XX_fxnTable                                                
00000bb9  UARTCC26XX_hwiIntFxn                                               
0000497b  UARTCC26XX_init                                                    
00000a55  UARTCC26XX_open                                                    
00000d0d  UARTCC26XX_read                                                    
000034b5  UARTCC26XX_readCancel                                              
000049a5  UARTCC26XX_readPolling                                             
00002f5d  UARTCC26XX_swiIntFxn                                               
00001641  UARTCC26XX_write                                                   
0000240b  UARTCC26XX_writeCancel                                             
000049ab  UARTCC26XX_writePolling                                            
00004bf9  UART_0_CONST                                                       
00004665  UART_Params_init                                                   
00004c0c  UART_config                                                        
00004bfa  UART_count                                                         
00004ad8  UART_defaultParams                                                 
0000392d  UART_init                                                          
00003b39  UART_open                                                          
00003de7  UDMACC26XX_close                                                   
00004c50  UDMACC26XX_config                                                  
00004947  UDMACC26XX_hwiIntFxn                                               
00003501  UDMACC26XX_open                                                    
20014000  __STACK_END                                                        
00000400  __STACK_SIZE                                                       
00004000  __SYSMEM_SIZE                                                      
00004d40  __TI_CINIT_Base                                                    
00004d68  __TI_CINIT_Limit                                                   
00004d10  __TI_Handler_Table_Base                                            
00004d1c  __TI_Handler_Table_Limit                                           
00003971  __TI_auto_init_nobinit_nopinit                                     
00002e91  __TI_decompress_lzss                                               
000048a9  __TI_decompress_none                                               
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
00000000  __TI_static_base__                                                 
00004931  __TI_zero_init                                                     
000047cb  __aeabi_idiv0                                                      
000047cb  __aeabi_ldiv0                                                      
00004695  __aeabi_lmul                                                       
000029c9  __aeabi_memclr                                                     
000029c9  __aeabi_memclr4                                                    
000029c9  __aeabi_memclr8                                                    
000021a9  __aeabi_memcpy                                                     
000021a9  __aeabi_memcpy4                                                    
000021a9  __aeabi_memcpy8                                                    
000029cb  __aeabi_memset                                                     
000029cb  __aeabi_memset4                                                    
000029cb  __aeabi_memset8                                                    
00002375  __aeabi_uldivmod                                                   
ffffffff  __binit__                                                          
00004c80  __c_args__                                                         
00057fa8  __ccfg                                                             
UNDEFED   __mpu_init                                                         
20013c00  __stack                                                            
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
0000467d  _args_main                                                         
0000410d  _c_int00                                                           
20000944  _hposcCoeffs                                                       
200009a0  _lock                                                              
00003383  _nop                                                               
20001a80  _sys_memory                                                        
UNDEFED   _system_post_cinit                                                 
000049d7  _system_pre_init                                                   
200009a4  _unlock                                                            
000049db  abort                                                              
200009d0  adcBufCC26XXChannelLut0                                            
00004bc0  adcbufCC26XXHWAttrs                                                
200001b4  adcbufCC26XXbjects                                                 
0000128d  aligned_alloc                                                      
ffffffff  binit                                                              
00003597  clkFxn                                                             
200005c9  driverlib_release_0_59848                                          
0000147d  free                                                               
20000a00  g_pfnRAMVectors                                                    
00000000  g_pfnVectors                                                       
20000968  gpioCallbackFunctions                                              
20000978  gpioPinConfigs                                                     
00004b30  gptimerCC26XXHWAttrs                                               
20000330  gptimerCC26XXObjects                                               
200005f8  i                                                                  
00004c18  inPinTypes                                                         
200005fc  j                                                                  
0000307d  main                                                               
00004983  malloc                                                             
0000128d  memalign                                                           
000021a9  memcpy                                                             
000029d1  memset                                                             
00004c24  outPinStrengths                                                    
00004be0  outPinTypes                                                        
200003c8  pinHandleTable                                                     
200009c4  pinLowerBound                                                      
200009c0  pinUpperBound                                                      
00004781  resetISR                                                           
000049e8  resourceDB                                                         
20000600  uart                                                               
20000000  uartCC26XXObjects                                                  
00004c60  udmaCC26XXHWAttrs                                                  
20000548  udmaCC26XXObject                                                   


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                                               
-------   ----                                                               
00000000  __TI_static_base__                                                 
00000000  g_pfnVectors                                                       
000000d9  PIN_init                                                           
000002b9  Power_sleep                                                        
00000400  __STACK_SIZE                                                       
0000076d  ADCBufCC26X2_open                                                  
000008e1  Power_init                                                         
00000a55  UARTCC26XX_open                                                    
00000bb9  UARTCC26XX_hwiIntFxn                                               
00000d0d  UARTCC26XX_read                                                    
00000e47  Board_initHook                                                     
0000107d  GPIO_setConfig                                                     
0000118d  ADCBufCC26X2_convert                                               
0000128d  aligned_alloc                                                      
0000128d  memalign                                                           
0000138d  NOROM_SysCtrlStandby                                               
0000147d  free                                                               
00001561  PowerCC26XX_standbyPolicy                                          
00001641  UARTCC26XX_write                                                   
00001721  SwiP_construct                                                     
000017f9  GPIO_init                                                          
000018bd  Power_releaseDependency                                            
00001a3d  TimerP_construct                                                   
00001af9  Power_setDependency                                                
00001c69  ClockP_startup                                                     
00001d1d  InitTimer2                                                         
00001dcd  ClockP_workFuncDynamic                                             
00001e79  PIN_open                                                           
00001fd1  SemaphoreP_pend                                                    
00002071  SwiP_dispatch                                                      
0000210d  Timer2AInterruptHandler                                            
000021a9  __aeabi_memcpy                                                     
000021a9  __aeabi_memcpy4                                                    
000021a9  __aeabi_memcpy8                                                    
000021a9  memcpy                                                             
00002245  NOROM_SetupTrimDevice                                              
00002375  __aeabi_uldivmod                                                   
0000240b  UARTCC26XX_writeCancel                                             
0000249d  GPTimerCC26XX_open                                                 
00002525  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002745  UARTCC26XX_close                                                   
00002849  ClockP_start                                                       
000028c9  HwiP_construct                                                     
00002949  NOROM_SysCtrlSetRechargeBeforePowerDown                            
000029c9  __aeabi_memclr                                                     
000029c9  __aeabi_memclr4                                                    
000029c9  __aeabi_memclr8                                                    
000029cb  __aeabi_memset                                                     
000029cb  __aeabi_memset4                                                    
000029cb  __aeabi_memset8                                                    
000029d1  memset                                                             
00002a45  ClockP_walkQueueDynamic                                            
00002b31  PowerCC26X2_auxISR                                                 
00002ba5  PIN_add                                                            
00002c85  NOROM_ChipInfo_GetChipType                                         
00002cf1  Board_sendExtFlashByte                                             
00002d59  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
00002dc1  SwiP_post                                                          
00002e29  TimerP_start                                                       
00002e91  __TI_decompress_lzss                                               
00002ef9  UARTCC26XX_control                                                 
00002f5d  UARTCC26XX_swiIntFxn                                               
00002fbf  SemaphoreP_destruct                                                
00002fc1  GPIO_write                                                         
00003021  PIN_remove                                                         
0000307d  main                                                               
0000318d  GPTimerCC26XX_stop                                                 
000031e5  PowerCC26X2_initiateCalibration                                    
0000323d  ADCBufCC26X2_adjustRawValues                                       
00003291  NOROM_PRCMPowerDomainsAllOff                                       
000032e5  SemaphoreP_construct                                               
00003335  ADCBufCC26X2_close                                                 
00003383  _nop                                                               
0000341d  GPTimerCC26XX_start                                                
00003469  NOROM_PRCMPowerDomainsAllOn                                        
000034b5  UARTCC26XX_readCancel                                              
00003501  UDMACC26XX_open                                                    
0000354d  ADCBufCC26X2_control                                               
00003597  clkFxn                                                             
00003599  ADCBufCC26X2_convertAdjustedToMicroVolts                           
000035e1  Board_wakeUpExtFlash                                               
00003629  ClockP_construct                                                   
00003671  PIN_setOutputEnable                                                
000036b9  PowerCC26X2_RCOSC_clockFunc                                        
00003791  RingBuf_put                                                        
000037d9  ADCBuf_init                                                        
000037d9  InitADC                                                            
0000381d  NOROM_ChipInfo_GetHwRevision                                       
000038a5  SemaphoreP_post                                                    
000038e9  SwiP_restore                                                       
0000392d  UART_init                                                          
00003971  __TI_auto_init_nobinit_nopinit                                     
000039f9  ADCBufCC26X2_convertCancel                                         
00003a39  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00003a79  NOROM_TimerIntRegister                                             
00003ab9  RingBuf_get                                                        
00003af9  SoundTransmit                                                      
00003b39  UART_open                                                          
00003b79  InitUart                                                           
00003bb5  PIN_setOutputValue                                                 
00003c29  Board_shutDownExtFlash                                             
00003c61  PIN_setConfig                                                      
00003c99  TimerP_getCurrentTick                                              
00003cd1  TimerP_initDevice                                                  
00003db1  TimerP_setNextTick                                                 
00003de7  UDMACC26XX_close                                                   
00003e1d  ClockP_getTicksUntilInterrupt                                      
00003e85  GPTimerCC26XX_configureDebugStall                                  
00003eb9  NOROM_AUXSYSIFOpModeChange                                         
00003eed  NOROM_IntRegister                                                  
00003f21  NOROM_SysCtrlIdle                                                  
00003f55  SwiP_destruct                                                      
00003fbd  GPIO_hwiIntFxn                                                     
00003fed  GPIO_setCallback                                                   
00004000  __SYSMEM_SIZE                                                      
0000401d  GPTimerCC26XX_close                                                
0000404d  PINCC26XX_setMux                                                   
0000407d  Power_releaseConstraint                                            
000040ad  Power_setConstraint                                                
0000410d  _c_int00                                                           
00004195  TimerP_setThreshold                                                
000041c1  Power_registerNotify                                               
00004239  List_put                                                           
0000425f  List_remove                                                        
00004285  ClockP_getTicks                                                    
000042a9  ClockP_scheduleNextTick                                            
000042f1  NOROM_OSCHF_TurnOnXosc                                             
00004315  TimerP_getMaxTicks                                                 
0000435d  Board_init                                                         
0000437d  ClockP_add                                                         
0000439d  NOROM_AUXADCEnableSync                                             
000043dd  Power_unregisterNotify                                             
000043fd  QueueP_put                                                         
0000441d  TimerP_dynamicStub                                                 
0000447d  PIN_close                                                          
0000449b  Power_getTransitionLatency                                         
000044b9  SemaphoreP_constructBinary                                         
000044d7  SwiP_or                                                            
000044f5  HwiP_destruct                                                      
00004511  NOROM_ChipInfo_GetChipFamily                                       
00004549  SwiP_disable                                                       
0000459d  PowerCC26XX_calibrate                                              
000045b7  QueueP_get                                                         
000045d1  SemaphoreP_createBinary                                            
000045eb  ClockP_destruct                                                    
00004605  HwiP_inISR                                                         
0000461d  NOROM_ChipInfo_GetPackageType                                      
00004635  Power_idleFunc                                                     
0000464d  TimerP_startup                                                     
00004665  UART_Params_init                                                   
0000467d  _args_main                                                         
00004695  __aeabi_lmul                                                       
000046c5  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
000046db  SemaphoreP_create                                                  
0000471d  NOROM_IntUnregister                                                
00004731  PowerCC26X2_calibrate                                              
00004745  Power_disablePolicy                                                
0000476d  TimerP_getCount64                                                  
00004781  resetISR                                                           
00004795  GPTimerCC26XX_Params_init                                          
000047a7  HwiP_Params_init                                                   
000047b9  RingBuf_construct                                                  
000047cb  __aeabi_idiv0                                                      
000047cb  __aeabi_ldiv0                                                      
000047cd  ClockP_Params_init                                                 
000047dd  ClockP_getCpuFreq                                                  
000047ed  HwiP_clearInterrupt                                                
000047fd  HwiP_disable                                                       
0000480d  HwiP_post                                                          
0000481d  PINCC26XX_getPinCount                                              
0000482d  PowerCC26XX_schedulerDisable                                       
0000483d  Power_enablePolicy                                                 
0000484d  SemaphoreP_Params_init                                             
0000485d  SwiP_Params_init                                                   
0000486d  TimerP_Params_init                                                 
0000488d  QueueP_empty                                                       
0000489b  QueueP_remove                                                      
000048a9  __TI_decompress_none                                               
000048b9  ClockP_doTick                                                      
000048c5  ClockP_getSystemTickPeriod                                         
000048d1  NOROM_CPUcpsid                                                     
000048dd  NOROM_CPUcpsie                                                     
000048e9  PIN_registerIntCb                                                  
000048f5  PowerCC26XX_schedulerRestore                                       
00004901  Power_getConstraintMask                                            
0000490d  Power_getDependencyCount                                           
00004919  SwiP_getTrigger                                                    
00004925  TimerP_getFreq                                                     
00004931  __TI_zero_init                                                     
0000493d  ADCBuf_convertCancel                                               
00004947  UDMACC26XX_hwiIntFxn                                               
00004963  ADCBufCC26X2_init                                                  
0000496b  GPTimerCC26XX_setLoadValue                                         
00004973  HwiP_restore                                                       
0000497b  UARTCC26XX_init                                                    
00004983  malloc                                                             
00004993  ClockP_stop                                                        
00004999  NOROM_CPUdelay                                                     
0000499f  QueueP_init                                                        
000049a5  UARTCC26XX_readPolling                                             
000049ab  UARTCC26XX_writePolling                                            
000049b7  ADCBufCC26X2_getResolution                                         
000049bf  ClockP_isActive                                                    
000049c3  ClockP_setTimeout                                                  
000049c7  HwiP_enable                                                        
000049c7  NoRTOS_start                                                       
000049cb  QueueP_head                                                        
000049cf  QueueP_next                                                        
000049d3  SemaphoreP_delete                                                  
000049d7  _system_pre_init                                                   
000049db  C$$EXIT                                                            
000049db  abort                                                              
000049e8  resourceDB                                                         
00004a3c  BoardGpioInitTable                                                 
00004a64  UARTCC26XX_fxnTable                                                
00004ab4  ADCBufCC26X2_fxnTable                                              
00004ad8  UART_defaultParams                                                 
00004b18  GPTimerCC26XX_config                                               
00004b30  gptimerCC26XXHWAttrs                                               
00004b74  GPIOCC26XX_config                                                  
00004b88  PowerCC26X2_config                                                 
00004bc0  adcbufCC26XXHWAttrs                                                
00004be0  outPinTypes                                                        
00004bf0  ADCBUF_CONST                                                       
00004bf1  ADCBUF_SOUND_CONST                                                 
00004bf2  ADCBUF_TEMPERATURE_CONST                                           
00004bf3  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004bf4  ADCBuf_count                                                       
00004bf5  PIN_TEST1_CONST                                                    
00004bf6  PIN_TEST2_CONST                                                    
00004bf7  PIN_DRIVE_SPEAKER_A_CONST                                          
00004bf8  PIN_DRIVE_SPEAKER_B_CONST                                          
00004bf9  UART_0_CONST                                                       
00004bfa  UART_count                                                         
00004bfb  CONFIG_GPTIMER_0_CONST                                             
00004bfc  CONFIG_GPTIMER_1_CONST                                             
00004bfd  GPTimer_count                                                      
00004c00  ADCBuf_config                                                      
00004c0c  UART_config                                                        
00004c18  inPinTypes                                                         
00004c24  outPinStrengths                                                    
00004c48  PINCC26XX_hwAttrs                                                  
00004c50  UDMACC26XX_config                                                  
00004c60  udmaCC26XXHWAttrs                                                  
00004c80  __c_args__                                                         
00004d10  __TI_Handler_Table_Base                                            
00004d1c  __TI_Handler_Table_Limit                                           
00004d40  __TI_CINIT_Base                                                    
00004d68  __TI_CINIT_Limit                                                   
00057fa8  __ccfg                                                             
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
20000000  uartCC26XXObjects                                                  
200001b4  adcbufCC26XXbjects                                                 
20000330  gptimerCC26XXObjects                                               
200003c8  pinHandleTable                                                     
20000548  udmaCC26XXObject                                                   
200005c9  driverlib_release_0_59848                                          
200005f8  i                                                                  
200005fc  j                                                                  
20000600  uart                                                               
20000604  PowerCC26X2_module                                                 
2000084c  ChirpDelay                                                         
200008c8  ChirpState                                                         
20000944  _hposcCoeffs                                                       
20000968  gpioCallbackFunctions                                              
20000978  gpioPinConfigs                                                     
200009a0  _lock                                                              
200009a4  _unlock                                                            
200009b0  SemaphoreP_defaultParams                                           
200009b8  ChirpSize                                                          
200009bc  ChirpIndex                                                         
200009c0  pinUpperBound                                                      
200009c4  pinLowerBound                                                      
200009cc  ClockP_tickPeriod                                                  
200009d0  adcBufCC26XXChannelLut0                                            
200009e4  HwiP_swiPIntNum                                                    
20000a00  g_pfnRAMVectors                                                    
20001a80  _sys_memory                                                        
20013c00  __stack                                                            
20014000  __STACK_END                                                        
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
ffffffff  __binit__                                                          
ffffffff  binit                                                              
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
UNDEFED   __mpu_init                                                         
UNDEFED   _system_post_cinit                                                 

[290 symbols]
