import numpy as np
import soundfile as sf

# === 1. Daten laden (jede <PERSON> = 1 Sample) ===
with open("chirp.txt", "r") as f:
    lines = f.readlines()

# Nur Zahlen extrahieren
samples = [int(line.strip()) for line in lines if line.strip().lstrip('-').isdigit()]
signal = np.array(samples, dtype=np.int16)

# === 2. WAV speichern ===
sample_rate = 48000  # muss zu deinem CHIRP passen!
sf.write("chirp_from_esp32.wav", signal, samplerate=sample_rate, subtype='PCM_16')

print("✅ WAV-Datei gespeichert: chirp_from_esp32.wav")
