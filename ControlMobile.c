#include <ControlMobile.h>
#include <Devicetype.h>
#include <stdint.h>
#include <stddef.h>
#include <stdio.h>
#include <math.h>
#include <Serial.h>
#include "time.h"
#include <SoundTransmit.h>
#include <RFCommunication.h>
#include "ti_drivers_config.h"
#include "SoundReceive.h"
#include "Temperature.h"
#include "Batteryvoltage.h"
#include <stdbool.h>

#define TimeOutPacket 500
#define TimeOutReMeasureBaseLine 60000
#define TimeOutReadSamplesRF 6000
typedef enum {eNoError=0,eFailedX0=1,eFailedMX=2,eFailedM0=3,eLargeDifference2Directions=4,eX0tosmall=5,eYNotExist=6,eXnegative=7} MeasureErrors;

bool WaituntilRFPacketReceived(RxPacketType Packet);
bool MeasureDistance(DeviceTypes deviceA, DeviceTypes deviceB,  uint16_t *DistanceAB, uint16_t *DistanceBA);
bool RequestResultsByRF(DeviceTypes Device);
bool WaituntilRFPacketReceived(RxPacketType Packet);
void CalculatePosition (MeasureErrors *Error,float *Xmeter, float *Ymeter, float *X0basemeter, uint16_t DistanceMto0,uint16_t Distance0toM,uint16_t DistanceMtoX,uint16_t DistanceXtoM,uint16_t DistanceXto0,uint16_t Distance0toX  );
bool RequestResultsByRF(DeviceTypes Device);
float SpeedOfSound(float TemperatureCelcius);
float ConvertDistanceInSamplesToMeter ( uint16_t distanceSamples, uint16_t TemperatureDeciDegrees);
float CalculateAverageDistanceMeter( MeasureErrors *Error, uint16_t distance1, uint16_t distance2, uint16_t TemperatureDeciDegrees);
void Mobile_execute_MeasureAll(void);
void TransmitSamplesToSerial(DeviceTypes From);

//*********************************************************************************************************
// Mobile device controller. Acts on received serial command.
//*********************************************************************************************************
void ControlMobile_Run(void)
{
uint16_t d1,d2;
while (1)
{
    switch (Serial_CheckCommand())
    {
    case eSCMeasureMX :    { MeasureDistance(eDevMobile, eDevX ,&d1, &d2); break; }
    case eSCMeasureM0 :    { MeasureDistance(eDevMobile, eDev0 ,&d1, &d2); break; }
    case eSCMeasureX0 :    { MeasureDistance(eDevX, eDev0, &d1 ,&d2); break; }
    case eSCReadSamplesX : { TransmitSamplesToSerial(eDevX); break; }
    case eSCReadSamples0 : { TransmitSamplesToSerial(eDev0); break; }
    case eSCReadSamplesM : { TransmitSamplesToSerial(eDevMobile); break; }
    case eSCMeasureAll:    { Mobile_execute_MeasureAll(); break; }
    default : { break; }
    }
}
}

//*********************************************************************************************************
// measure the distance from deviceA->deviceB and reverse.
// only X<>0 , M<>0 and M<>X supported
// return true if successful
//*********************************************************************************************************
bool MeasureDistance(DeviceTypes deviceA, DeviceTypes deviceB,  uint16_t *DistanceAB, uint16_t *DistanceBA)
{
 bool ok=false;
 uint8_t Type;
 if ((deviceA==eDevX) && (deviceB==eDev0))   //  X<>0
  {
    RFCommunication_TXAskXToMeasureXto0();          // mobile asks X to coordinate measuring
    if (WaituntilRFPacketReceived(eRFMeasurement))  //  wait until X send results
      {
        *DistanceBA = RFCommunication_GetDistanceFromReceivedMeasurementPacket();
        BatteryVoltage_Store(eDevX,RFCommunication_GetBatteryFromReceivedMeasurementPacket());
        if (RequestResultsByRF(eDev0) )  // ask 0 for results
           {
             *DistanceAB=RFCommunication_GetDistanceFromReceivedMeasurementPacket();
             BatteryVoltage_Store(eDev0,RFCommunication_GetBatteryFromReceivedMeasurementPacket());
             Serial_PrintMeasurements(130,5, *DistanceAB,*DistanceBA,BatteryVoltage_GetStoredValue(eDevX),BatteryVoltage_GetStoredValue(eDev0),Temperature_GetValueDeciDegrees(),0,0);
             ok=true;
           }
     }
  } else  if (deviceA==eDevMobile) //  M<>0   or M<>X
  {
    RFCommunication_TXListenToSound(deviceB,eDevMobile);  // generate sound and tell 0 or X to listen
    SoundTransmit_GenerateSound(eChirp);
    if (deviceB==eDevX) ok=WaituntilRFPacketReceived(eRFListenMtoX);
    if (deviceB==eDev0) ok=WaituntilRFPacketReceived(eRFListenMto0);  // wait until 0 or X tells Mobile to listen
    if (ok)
     {
       SoundReceive_SampleSound();
       *DistanceBA=SoundReceive_CalculateDistance();
       if (RequestResultsByRF(deviceB))    // ask 0 or X for results
           {
             *DistanceAB =RFCommunication_GetDistanceFromReceivedMeasurementPacket();
             BatteryVoltage_Store(deviceB,RFCommunication_GetBatteryFromReceivedMeasurementPacket());
             if (deviceB==eDevX) Type=128; else Type=129;
             Serial_PrintMeasurements(Type,4, *DistanceAB,*DistanceBA,BatteryVoltage_GetStoredValue(deviceB),Temperature_GetValueDeciDegrees(),0,0,0);
             ok=true;
           }
     }
  }
 return(ok);
 }

//*********************************************************************************************************
// do a complete measurement cycle to calculate X,Y and optional baseline distance
//*********************************************************************************************************
void Mobile_execute_MeasureAll(void)
{
 static uint16_t DistanceMto0,Distance0toM,DistanceMtoX,DistanceXtoM,DistanceXto0,Distance0toX;
 MeasureErrors Error=eNoError;
 float X=1.0, Y=2.0, Base=3.0;
 static bool BaselineMeasured=false;

 // only measure baseline if it is not available or if it was too long ago.
 if (Time_IsSoftTimerExpired(eTimerMeasureBase,TimeOutReMeasureBaseLine) || !BaselineMeasured )
 {
     if (MeasureDistance(eDevX, eDev0,  &DistanceXto0, &Distance0toX)) {  BaselineMeasured=true; Time_StartSoftTimer(eTimerMeasureBase); }
                                                                  else {  BaselineMeasured=false; Error=eFailedX0;  }
 }
 if (BaselineMeasured)
 {
    if (MeasureDistance(eDevMobile, eDev0,  &DistanceMto0, &Distance0toM))
    {
       if (MeasureDistance(eDevMobile, eDevX,  &DistanceMtoX, &DistanceXtoM))
             {
             // all three measurements successful
             CalculatePosition (&Error,&X,&Y,&Base,DistanceMto0,Distance0toM,DistanceMtoX,DistanceXtoM,DistanceXto0,Distance0toX  );
             }  else  Error=eFailedMX;
    } else Error=eFailedM0;
 }
 Serial_PrintMeasurements(131,7, Error,(uint16_t)(X*1000),(uint16_t)(Y*1000),(uint16_t)(Base*1000),BatteryVoltage_GetStoredValue(eDev0),BatteryVoltage_GetStoredValue(eDevX),Temperature_GetValueDeciDegrees());
}

//SUPPORT FUNCTIONS *********************************************************************************************************

//*********************************************************************************************************
// ask Device to send measurements by RF and wait for them. Return 1 if successful.
//*********************************************************************************************************
bool RequestResultsByRF(DeviceTypes Device)
{
    RFCommunication_TXReturnResults(Device);
    return (WaituntilRFPacketReceived(eRFMeasurement));
}

//*********************************************************************************************************
// wait until specified packet is received. Return 1 if successful, 0 if timeout
//*********************************************************************************************************
bool WaituntilRFPacketReceived(RxPacketType Packet)
{
  Time_StartSoftTimer(eTimerGeneral);
  while ( (RFCommunication_CheckReceivedPacket()!=Packet) && (!Time_IsSoftTimerExpired(eTimerGeneral,TimeOutPacket)) );
  return (!Time_IsSoftTimerExpired(eTimerGeneral,TimeOutPacket));
}

//*********************************************************************************************************
// calculate relative position to baseline(0->X line) . Report X,Y,baseline distance
//*********************************************************************************************************
void CalculatePosition (MeasureErrors *Error,float *Xmeter, float *Ymeter, float *X0basemeter, uint16_t DistanceMto0,uint16_t Distance0toM,uint16_t DistanceMtoX,uint16_t DistanceXtoM,uint16_t DistanceXto0,uint16_t Distance0toX  )
{
 uint16_t T = Temperature_GetValueDeciDegrees();
 float distanceM0 = CalculateAverageDistanceMeter ( Error,DistanceMto0 ,Distance0toM,T);
 float distanceMX = CalculateAverageDistanceMeter ( Error,DistanceMtoX ,DistanceXtoM,T);
 float distanceX0 = CalculateAverageDistanceMeter ( Error,DistanceXto0 ,Distance0toX,T);
 if (distanceX0 < 1) *Error=eX0tosmall;
 if (*Error == eNoError)
 {
     *Xmeter =   0.5 *(  ( distanceM0*distanceM0 - distanceMX*distanceMX)/ distanceX0  + distanceX0) ;
     if (*Xmeter<0)  *Error=eXnegative;
     if ( distanceM0 > abs( *Xmeter) ) (*Ymeter) = sqrt ( distanceM0*distanceM0 - (*Xmeter)*(*Xmeter)); else *Error=eYNotExist;
     *X0basemeter = distanceX0;
 }
}

//*********************************************************************************************************
// return current speed of sound in m/s
//*********************************************************************************************************
float SpeedOfSound(float TemperatureCelcius)
{
 return (20.05 * sqrt(273.15 + TemperatureCelcius));
}

//*********************************************************************************************************
 //Convert distance in samples to distance in meters
//*********************************************************************************************************
float ConvertDistanceInSamplesToMeter ( uint16_t distanceSamples, uint16_t TemperatureDeciDegrees)
{
float speed = SpeedOfSound ( (float)TemperatureDeciDegrees/10.0);
return ( ((float)distanceSamples * speed )/(float)SoundSampleFrequency);
}

//*********************************************************************************************************
// Calculate the average of distance A->B and B->A . Set error if difference is too large
//*********************************************************************************************************
float CalculateAverageDistanceMeter( MeasureErrors *Error, uint16_t distance1, uint16_t distance2, uint16_t TemperatureDeciDegrees)
{
    float d1 = ConvertDistanceInSamplesToMeter(distance1,TemperatureDeciDegrees );
    float d2 = ConvertDistanceInSamplesToMeter(distance2, TemperatureDeciDegrees );
    if (abs(d1-d2)>1) *Error=eLargeDifference2Directions;
    return ( (d1+d2)/2.0 );
}

//*********************************************************************************************************
// Copy audio samples to serial.
//*********************************************************************************************************
void TransmitSamplesToSerial(DeviceTypes From)
{
   if ( (From==eDev0) || (From==eDevX) )
   {

    RFCommunication_TXReadSamples(From);
    Time_StartSoftTimer(eTimerGeneral);
    while(!Time_IsSoftTimerExpired(eTimerGeneral,TimeOutReadSamplesRF))
               {
                 if (RFCommunication_CheckReceivedPacket()==eRFArraySection) Serial_CopyReceivedArraySectionToSerial();
               }
   } else
   if (From==eDevMobile)
   {
       uint16_t Section;
       for (Section=0;Section<NumberOfSampleSections;Section++)
           {
           Serial_CopyArraySectionToSerial(Section,SoundReceive_GetSoundSampleArray() );
           }
   }

}
