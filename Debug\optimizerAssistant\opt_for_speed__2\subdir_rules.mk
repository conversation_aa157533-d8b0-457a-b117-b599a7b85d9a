################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
%.obj: ../%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/bin/armcl" -mv7M4 --code_state=16 --float_support=FPv4SPD16 -me --opt_for_speed=2 --include_path="D:/_ArchiefTemse/CodeComposerRepos/SoundPositioning" --include_path="D:/_ArchiefTemse/CodeComposerRepos/SoundPositioning/Debug__opt_for_speed__2" --include_path="C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source" --include_path="C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos" --include_path="C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos/posix" --include_path="C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include" --define=DeviceFamily_CC13X2 -g --diag_warning=225 --diag_warning=255 --diag_wrap=off --display_error_number --gen_func_subsections=on --preproc_with_compile --preproc_dependency="$(basename $(<F)).d_raw" --include_path="D:/_ArchiefTemse/CodeComposerRepos/SoundPositioning/Debug__opt_for_speed__2/syscfg" $(GEN_OPTS__FLAG) "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

build-2040949441: ../gpiointerrupt.syscfg
	@echo 'Building file: "$<"'
	@echo 'Invoking: SysConfig'
	"C:/ti/ccs1011/ccs/utils/sysconfig_1.7.0/sysconfig_cli.bat" -s "C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/.metadata/product.json" -o "syscfg" --compiler ccs "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

syscfg/ti_devices_config.c: build-2040949441 ../gpiointerrupt.syscfg
syscfg/ti_drivers_config.c: build-2040949441
syscfg/ti_drivers_config.h: build-2040949441
syscfg/ti_utils_build_linker.cmd.genlibs: build-2040949441
syscfg/syscfg_c.rov.xs: build-2040949441
syscfg/ti_utils_runtime_model.gv: build-2040949441
syscfg/ti_utils_runtime_Makefile: build-2040949441
syscfg/: build-2040949441

syscfg/%.obj: ./syscfg/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/bin/armcl" -mv7M4 --code_state=16 --float_support=FPv4SPD16 -me --opt_for_speed=2 --include_path="D:/_ArchiefTemse/CodeComposerRepos/SoundPositioning" --include_path="D:/_ArchiefTemse/CodeComposerRepos/SoundPositioning/Debug__opt_for_speed__2" --include_path="C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source" --include_path="C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos" --include_path="C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos/posix" --include_path="C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include" --define=DeviceFamily_CC13X2 -g --diag_warning=225 --diag_warning=255 --diag_wrap=off --display_error_number --gen_func_subsections=on --preproc_with_compile --preproc_dependency="syscfg/$(basename $(<F)).d_raw" --include_path="D:/_ArchiefTemse/CodeComposerRepos/SoundPositioning/Debug__opt_for_speed__2/syscfg" --obj_directory="syscfg" $(GEN_OPTS__FLAG) "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


