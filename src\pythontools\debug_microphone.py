import serial
import time

def debug_microphone():
    """Debug script to see what ESP32 is sending"""
    
    print("🔍 ESP32 Microphone Debug Tool")
    print("=" * 40)
    
    # Connect to ESP32 on COM4 with higher baudrate
    print("📡 Connecting to ESP32 on COM4 (921600 baud)...")
    try:
        ser = serial.Serial('COM4', 921600, timeout=2)
        time.sleep(2)
        print("✅ Connected successfully!")
    except Exception as e:
        print(f"❌ Failed to connect to COM4: {e}")
        print("💡 Make sure ESP32 is using 921600 baudrate")
        return
    
    print("📤 Sending 'r' command...")
    ser.write(b'r\n')
    ser.flush()
    
    print("👂 Listening to ESP32 output...")
    print("⏹️  Press Ctrl+C to stop")
    print("-" * 40)
    
    line_count = 0
    sample_count = 0
    start_time = time.time()
    data_start_time = None

    try:
        while True:
            line = ser.readline().decode(errors="ignore").strip()

            if line:
                line_count += 1
                elapsed = time.time() - start_time

                # Check for special markers first
                if "--- AUDIO DATA START ---" in line:
                    print("🎵 AUDIO DATA STARTED!")
                    data_start_time = time.time()
                    sample_count = 0
                    continue

                elif "--- AUDIO DATA END ---" in line:
                    print("🏁 AUDIO DATA ENDED!")
                    if data_start_time:
                        data_duration = time.time() - data_start_time
                        print(f"📊 Data samples: {sample_count}")
                        print(f"⏱️  Data duration: {data_duration:.1f} seconds")
                        print(f"📈 Sample rate: {sample_count/data_duration:.0f} samples/sec")
                        print(f"🎯 Expected: 48000 samples/sec")
                    break

                elif "Progress:" in line:
                    print(f"📊 {line}")
                    continue

                # Count numeric samples
                if data_start_time and line.strip().lstrip('-').isdigit():
                    sample_count += 1

                    # Show sample rate every 10000 samples
                    if sample_count % 10000 == 0:
                        data_elapsed = time.time() - data_start_time
                        current_rate = sample_count / data_elapsed if data_elapsed > 0 else 0
                        print(f"📈 {sample_count} samples in {data_elapsed:.1f}s = {current_rate:.0f} samples/sec")

                # Show first 20 lines, then every 5000th line
                if line_count <= 20 or line_count % 5000 == 0:
                    print(f"[{line_count:6d}] ({elapsed:6.1f}s) {line[:80]}")

            # Safety timeout
            if time.time() - start_time > 35:
                print("⏰ 35 second timeout reached!")
                break
                
    except KeyboardInterrupt:
        print("\n⏹️  Stopped by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        ser.close()
        print("🔌 Serial connection closed")
        print(f"📈 Final stats: {line_count} lines in {time.time() - start_time:.1f} seconds")

if __name__ == "__main__":
    debug_microphone()
