<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: OneChannel8BitSoundData Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="class_one_channel8_bit_sound_data-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">OneChannel8BitSoundData Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>1 Channel data is provided as signed int8 values.  
 <a href="class_one_channel8_bit_sound_data.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_sound_data_8h_source.html">SoundData.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for OneChannel8BitSoundData:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_one_channel8_bit_sound_data.png" usemap="#OneChannel8BitSoundData_map" alt=""/>
  <map id="OneChannel8BitSoundData_map" name="OneChannel8BitSoundData_map">
<area href="class_sound_data.html" title="Sound data as byte stream. We support TwoChannelSoundData (uint16_t + uint16_t) and OneChannelSoundDa..." alt="SoundData" shape="rect" coords="0,0,166,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a7111fd6a931722bbdfe4b815597fc711"><td class="memItemLeft" align="right" valign="top"><a id="a7111fd6a931722bbdfe4b815597fc711"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>OneChannel8BitSoundData</b> (bool loop=false, <a class="el" href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">ChannelInfo</a> channelInfo=<a class="el" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0aedf69634e61e7ec5d006874d299bc0d4">Both</a>)</td></tr>
<tr class="separator:a7111fd6a931722bbdfe4b815597fc711"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a056d8a555b92a29189b64d609450dd4d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_one_channel8_bit_sound_data.html#a056d8a555b92a29189b64d609450dd4d">OneChannel8BitSoundData</a> (int8_t *data, int32_t len, bool loop=false, <a class="el" href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">ChannelInfo</a> channelInfo=<a class="el" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0aedf69634e61e7ec5d006874d299bc0d4">Both</a>)</td></tr>
<tr class="separator:a056d8a555b92a29189b64d609450dd4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac79933ed3379cf5ef58d5675aa4bf12e"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_sound_data.html#ac79933ed3379cf5ef58d5675aa4bf12e">doLoop</a> ()</td></tr>
<tr class="separator:ac79933ed3379cf5ef58d5675aa4bf12e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d7a1fdf7cc8aee06c158b7dc72d491d"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_one_channel8_bit_sound_data.html#a0d7a1fdf7cc8aee06c158b7dc72d491d">get2ChannelData</a> (int32_t pos, int32_t len, uint8_t *data)</td></tr>
<tr class="separator:a0d7a1fdf7cc8aee06c158b7dc72d491d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac9d8ab1ab3b628e97cb3c434ddbc0d0b"><td class="memItemLeft" align="right" valign="top"><a id="ac9d8ab1ab3b628e97cb3c434ddbc0d0b"></a>
int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>getData</b> (int32_t pos, Frame &amp;frame)</td></tr>
<tr class="separator:ac9d8ab1ab3b628e97cb3c434ddbc0d0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a62b38cc1404563dd87de3dab47534433"><td class="memItemLeft" align="right" valign="top"><a id="a62b38cc1404563dd87de3dab47534433"></a>
int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>getData</b> (int32_t pos, int32_t len, int8_t *data)</td></tr>
<tr class="separator:a62b38cc1404563dd87de3dab47534433"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8746bc715037bb4f1e8f20b9b299729a"><td class="memItemLeft" align="right" valign="top"><a id="a8746bc715037bb4f1e8f20b9b299729a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>setData</b> (int8_t *data, int32_t len)</td></tr>
<tr class="separator:a8746bc715037bb4f1e8f20b9b299729a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad93c5eee6bea36fc2a9f32c79cc5d805"><td class="memItemLeft" align="right" valign="top"><a id="ad93c5eee6bea36fc2a9f32c79cc5d805"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>setDataRaw</b> (uint8_t *data, int32_t len)</td></tr>
<tr class="separator:ad93c5eee6bea36fc2a9f32c79cc5d805"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed157f28a14d6fd8ef1571db60afd003"><td class="memItemLeft" align="right" valign="top"><a id="aed157f28a14d6fd8ef1571db60afd003"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>setLoop</b> (bool loop)</td></tr>
<tr class="separator:aed157f28a14d6fd8ef1571db60afd003"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>1 Channel data is provided as signed int8 values. </p>
<dl class="section author"><dt>Author</dt><dd>Phil Schatzmann </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Apache License Version 2 </dd></dl>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a056d8a555b92a29189b64d609450dd4d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a056d8a555b92a29189b64d609450dd4d">&#9670;&nbsp;</a></span>OneChannel8BitSoundData()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">OneChannel8BitSoundData::OneChannel8BitSoundData </td>
          <td>(</td>
          <td class="paramtype">int8_t *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>len</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>loop</em> = <code>false</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">ChannelInfo</a>&#160;</td>
          <td class="paramname"><em>channelInfo</em> = <code><a class="el" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0aedf69634e61e7ec5d006874d299bc0d4">Both</a></code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Constructor for data conisting only of one Channel </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="ac79933ed3379cf5ef58d5675aa4bf12e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac79933ed3379cf5ef58d5675aa4bf12e">&#9670;&nbsp;</a></span>doLoop()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool SoundData::doLoop </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Automatic restart playing on end </p>

</div>
</div>
<a id="a0d7a1fdf7cc8aee06c158b7dc72d491d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0d7a1fdf7cc8aee06c158b7dc72d491d">&#9670;&nbsp;</a></span>get2ChannelData()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int32_t OneChannel8BitSoundData::get2ChannelData </td>
          <td>(</td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>pos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>len</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *&#160;</td>
          <td class="paramname"><em>data</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Data is stored in one channel with int16_t data. However we need to provide 2 channels. pos, len and result are in bytes. </p>

<p>Implements <a class="el" href="class_sound_data.html">SoundData</a>.</p>

</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>src/<a class="el" href="_sound_data_8h_source.html">SoundData.h</a></li>
<li>src/SoundData.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
