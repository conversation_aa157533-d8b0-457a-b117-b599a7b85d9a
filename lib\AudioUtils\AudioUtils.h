#pragma once
#include <cstdint>

constexpr uint32_t SAMPLE_RATE = 48000;  // Changed to 48 kHz
constexpr float CHIRP_SEC = 0.030f;      // 30 ms chirp duration
constexpr uint16_t CHIRP_DURATION_MS = static_cast<uint16_t>(CHIRP_SEC * 1000);
constexpr uint16_t RECORD_DURATION_MS = 50;

// Chirp parameters for sine sweep
constexpr float F_START = 1000.0f;       // Start frequency in Hz
constexpr float F_END = 8000.0f;         // End frequency in Hz
constexpr uint32_t CHIRP_TOTAL = static_cast<uint32_t>(SAMPLE_RATE * CHIRP_SEC);  // Total samples in chirp
constexpr float CHIRP_K = (F_END - F_START) / CHIRP_TOTAL;  // Phase slope

constexpr uint32_t calcSamples(uint32_t ms) {
    return (SAMPLE_RATE * ms) / 1000;
}
