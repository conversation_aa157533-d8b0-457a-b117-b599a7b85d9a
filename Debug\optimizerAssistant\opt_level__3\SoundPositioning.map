******************************************************************************
                  TI ARM Linker PC v20.2.4                     
******************************************************************************
>> Linked Mon Feb 22 19:52:15 2021

OUTPUT FILE NAME:   <SoundPositioning.out>
ENTRY POINT SYMBOL: "resetISR"  address: 00004705


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00058000  00004d42  000532be  R  X
  GPRAM                 11000000   00002000  00000000  00002000  RW X
  SRAM                  20000000   00014000  00004f46  0000f0ba  RW X
  LOG_DATA              90000000   00040000  00000000  00040000  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004c04   00004c04    r-x
  00000000    00000000    000000d8   000000d8    r-- .intvecs
  000000d8    000000d8    00004892   00004892    r-x .text
  0000496c    0000496c    00000298   00000298    r-- .const
00004c04    00004c04    00000008   00000008    rw-
  00004c04    00004c04    00000008   00000008    rw- .args
00004c10    00004c10    000000e0   000000e0    r--
  00004c10    00004c10    000000e0   000000e0    r-- .cinit
00057fa8    00057fa8    00000058   00000058    r--
  00057fa8    00057fa8    00000058   00000058    r-- .ccfg
20000000    20000000    00000a4e   00000000    rw-
  20000000    20000000    00000668   00000000    rw- .bss
  20000668    20000668    000003e6   00000000    rw- .data
20000b00    20000b00    000000d8   00000000    rw-
  20000b00    20000b00    000000d8   00000000    rw- .vtable_ram
20001870    20001870    00000010   00000000    rw-
  20001870    20001870    00000010   00000000    rw- .TI.bound:dmaADCPriControlTableEntry
20001a70    20001a70    00004010   00000000    rw-
  20001a70    20001a70    00000010   00000000    rw- .TI.bound:dmaADCAltControlTableEntry
  20001a80    20001a80    00004000   00000000    rw- .sysmem
20013c00    20013c00    00000400   00000000    rw-
  20013c00    20013c00    00000400   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000d8     
                  00000000    000000d8     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.intvecs)

.text      0    000000d8    00004892     
                  000000d8    000001e0     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_init)
                  000002b8    00000198                      : PowerCC26X2.oem4f (.text:Power_sleep)
                  00000450    00000190                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf1)
                  000005e0    0000018c                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:runCalibrateFsm)
                  0000076c    00000174                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_open)
                  000008e0    00000174                      : PowerCC26X2.oem4f (.text:Power_init)
                  00000a54    00000164                      : UARTCC26XX.oem4f (.text:UARTCC26XX_open)
                  00000bb8    00000154                      : UARTCC26XX.oem4f (.text:UARTCC26XX_hwiIntFxn)
                  00000d0c    0000013a                      : UARTCC26XX.oem4f (.text:UARTCC26XX_read)
                  00000e46    00000002     ti_drivers_config.obj (.text:Board_initHook)
                  00000e48    00000120     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureXOSCHF)
                  00000f68    00000114                      : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfg)
                  0000107c    00000110                      : GPIOCC26XX.oem4f (.text:GPIO_setConfig)
                  0000118c    00000100                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convert)
                  0000128c    00000100     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:aligned_alloc)
                  0000138c    000000f0     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlStandby)
                  0000147c    000000e4     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free)
                  00001560    000000e0     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_standbyPolicy)
                  00001640    000000e0     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_write)
                  00001720    000000d8     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_construct)
                  000017f8    000000c4     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_init)
                  000018bc    000000c0                      : PowerCC26X2.oem4f (.text:Power_releaseDependency)
                  0000197c    000000c0                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initIO)
                  00001a3c    000000bc     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_construct)
                  00001af8    000000b8     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_setDependency)
                  00001bb0    000000b8                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initHw)
                  00001c68    000000b4     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_startup)
                  00001d1c    000000ac                     : ClockPTimer_nortos.oem4f (.text:ClockP_workFuncDynamic)
                  00001dc8    000000ac     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_open)
                  00001e74    000000ac     driverlib.lib : setup.obj (.text:TrimAfterColdResetWakeupFromShutDown)
                  00001f20    000000a0     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_pend)
                  00001fc0    0000009c                     : SwiP_nortos.oem4f (.text:SwiP_dispatch)
                  0000205c    0000009c     rtsv7M4_T_le_v4SPD16_eabi.lib : memcpy_t2.asm.obj (.text)
                  000020f8    00000098     driverlib.lib : setup.obj (.text:NOROM_SetupTrimDevice)
                  00002190    00000098     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeTxFifoFlush)
                  00002228    00000096     rtsv7M4_T_le_v4SPD16_eabi.lib : ull_div_t2.asm.obj (.text)
                  000022be    00000092     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_writeCancel)
                  00002350    00000090     SoundTX.obj (.text:InitTimer2)
                  000023e0    00000088     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_open)
                  00002468    00000088     driverlib.lib : osc.obj (.text:NOROM_OSCHF_AttemptToSwitchToXosc)
                  000024f0    00000088     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf2)
                  00002578    00000088                      : PowerCC26X2.oem4f (.text:disableLFClockQualifiers)
                  00002600    00000088                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:updateSubSecInc)
                  00002688    00000084                      : UARTCC26XX.oem4f (.text:UARTCC26XX_close)
                  0000270c    00000080                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_hwiFxn)
                  0000278c    00000080     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_start)
                  0000280c    00000080                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_construct)
                  0000288c    00000080     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlSetRechargeBeforePowerDown)
                  0000290c    0000007a     rtsv7M4_T_le_v4SPD16_eabi.lib : memset_t2.asm.obj (.text)
                  00002986    00000002     driverlib.lib : interrupt.obj (.text:IntDefaultHandler)
                  00002988    00000078     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_walkQueueDynamic)
                  00002a00    00000074     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_resetHw)
                  00002a74    00000074                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_auxISR)
                  00002ae8    00000070                      : PINCC26XX.oem4f (.text:PIN_add)
                  00002b58    00000070     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:split)
                  00002bc8    0000006c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipType)
                  00002c34    00000068                   : osc.obj (.text:NOROM_OSCHF_SwitchToRcOscTurnOffXosc)
                  00002c9c    00000068     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_post)
                  00002d04    00000068     SoundTX.obj (.text:Timer2AInterruptHandler)
                  00002d6c    00000068     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_start)
                  00002dd4    00000068     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_lzss.c.obj (.text:decompress:lzss:__TI_decompress_lzss)
                  00002e3c    00000064     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_control)
                  00002ea0    00000062     ti_drivers_config.obj (.text:Board_sendExtFlashByte)
                  00002f02    00000062     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_swiIntFxn)
                  00002f64    00000060                      : GPIOCC26XX.oem4f (.text:GPIO_write)
                  00002fc4    0000005c                      : PINCC26XX.oem4f (.text:PIN_remove)
                  00003020    0000005c                      : UARTCC26XX.oem4f (.text:startTxFifoEmptyClk)
                  0000307c    00000058                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_loadDMAControlTableEntry)
                  000030d4    00000058                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_stop)
                  0000312c    00000058                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_initiateCalibration)
                  00003184    00000054                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_adjustRawValues)
                  000031d8    00000054     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOff)
                  0000322c    00000050     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_construct)
                  0000327c    0000004e     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_close)
                  000032ca    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_destruct)
                  000032cc    0000004c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_cleanADC)
                  00003318    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_initHw)
                  00003364    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_start)
                  000033b0    0000004c     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOn)
                  000033fc    0000004c     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readCancel)
                  00003448    0000004c                      : UDMACC26XX.oem4f (.text:UDMACC26XX_open)
                  00003494    0000004c     mainNew.obj (.text:main)
                  000034e0    0000004a     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_control)
                  0000352a    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.text:_nop)
                  0000352c    00000048     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertAdjustedToMicroVolts)
                  00003574    00000048     ti_drivers_config.obj (.text:Board_wakeUpExtFlash)
                  000035bc    00000048     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_construct)
                  00003604    00000048     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputEnable)
                  0000364c    00000048                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_RCOSC_clockFunc)
                  00003694    00000048                      : PowerCC26X2.oem4f (.text:switchXOSCHF)
                  000036dc    00000048                      : UARTCC26XX.oem4f (.text:writeFinishedDoCallback)
                  00003724    00000046                      : RingBuf.oem4f (.text:RingBuf_put)
                  0000376a    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:clkFxn)
                  0000376c    00000044     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_init)
                  000037b0    00000044     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetHwRevision)
                  000037f4    00000044     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_swi)
                  00003838    00000044     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_post)
                  0000387c    00000044                     : SwiP_nortos.oem4f (.text:SwiP_restore)
                  000038c0    00000044     drivers_cc13x2.a : UART.oem4f (.text:UART_init)
                  00003904    00000044     rtsv7M4_T_le_v4SPD16_eabi.lib : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit:__TI_auto_init_nobinit_nopinit)
                  00003948    00000042     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_swiFxn)
                  0000398a    00000002                      : PowerCC26X2.oem4f (.text:emptyClockFunc)
                  0000398c    00000040                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertCancel)
                  000039cc    00000040     driverlib.lib : osc.obj (.text:NOROM_OSC_HPOSCRelativeFrequencyOffsetGet)
                  00003a0c    00000040                   : timer.obj (.text:NOROM_TimerIntRegister)
                  00003a4c    00000040     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_get)
                  00003a8c    00000040                      : UART.oem4f (.text:UART_open)
                  00003acc    0000003c     mainNew.obj (.text:InitUart)
                  00003b08    0000003c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputValue)
                  00003b44    00000038                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_configDMA)
                  00003b7c    00000038     ti_drivers_config.obj (.text:Board_shutDownExtFlash)
                  00003bb4    00000038     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setConfig)
                  00003bec    00000038     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCurrentTick)
                  00003c24    00000038                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_initDevice)
                  00003c5c    00000038     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:notify)
                  00003c94    00000038                      : UARTCC26XX.oem4f (.text:readData)
                  00003ccc    00000038                      : UARTCC26XX.oem4f (.text:writeData)
                  00003d04    00000036     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setNextTick)
                  00003d3a    00000036     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_close)
                  00003d70    00000034     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicksUntilInterrupt)
                  00003da4    00000034     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XXSetLoadMatch)
                  00003dd8    00000034                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_configureDebugStall)
                  00003e0c    00000034     driverlib.lib : aux_sysif.obj (.text:NOROM_AUXSYSIFOpModeChange)
                  00003e40    00000034                   : interrupt.obj (.text:NOROM_IntRegister)
                  00003e74    00000034                   : sys_ctrl.obj (.text:NOROM_SysCtrlIdle)
                  00003ea8    00000034     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_destruct)
                  00003edc    00000034     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:readData2RingBuf)
                  00003f10    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_hwiIntFxn)
                  00003f40    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_setCallback)
                  00003f70    00000030                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_close)
                  00003fa0    00000030                      : PINCC26XX.oem4f (.text:PINCC26XX_setMux)
                  00003fd0    00000030                      : PowerCC26X2.oem4f (.text:Power_releaseConstraint)
                  00004000    00000030                      : PowerCC26X2.oem4f (.text:Power_setConstraint)
                  00004030    00000030     SoundTX.obj (.text:SoundTransmit)
                  00004060    00000030     driverlib.lib : timer.obj (.text:TimerIntNumberGet)
                  00004090    00000030     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.text:_c_int00:_c_int00)
                  000040c0    0000002c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_acquireADCSemaphore)
                  000040ec    0000002c                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_releaseADCSemaphore)
                  00004118    0000002c     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setThreshold)
                  00004144    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_registerNotify)
                  0000416c    00000028     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_insert)
                  00004194    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:oscillatorISR)
                  000041bc    00000026                      : List.oem4f (.text:List_put)
                  000041e2    00000026                      : List.oem4f (.text:List_remove)
                  00004208    00000024     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicks)
                  0000422c    00000024                     : ClockPTimer_nortos.oem4f (.text:ClockP_scheduleNextTick)
                  00004250    00000024                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_dispatch)
                  00004274    00000024     driverlib.lib : osc.obj (.text:NOROM_OSCHF_TurnOnXosc)
                  00004298    00000024     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getMaxTicks)
                  000042bc    00000024     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:getTdcSemaphore)
                  000042e0    00000020     ti_drivers_config.obj (.text:Board_init)
                  00004300    00000020     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_add)
                  00004320    00000020     driverlib.lib : aux_adc.obj (.text:NOROM_AUXADCEnableSync)
                  00004340    00000020     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfgMux)
                  00004360    00000020                      : PowerCC26X2.oem4f (.text:Power_unregisterNotify)
                  00004380    00000020     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_put)
                  000043a0    00000020                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_dynamicStub)
                  000043c0    00000020     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:threadSafeStdbyDisRelease)
                  000043e0    00000020                      : UARTCC26XX.oem4f (.text:threadSafeStdbyDisSet)
                  00004400    0000001e                      : PINCC26XX.oem4f (.text:PIN_close)
                  0000441e    0000001e                      : PowerCC26X2.oem4f (.text:Power_getTransitionLatency)
                  0000443c    0000001e     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_constructBinary)
                  0000445a    0000001e                     : SwiP_nortos.oem4f (.text:SwiP_or)
                  00004478    0000001c                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_destruct)
                  00004494    0000001c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipFamily)
                  000044b0    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_hwi)
                  000044cc    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_disable)
                  000044e8    0000001c     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_initHw)
                  00004504    0000001c     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_remove)
                  00004520    0000001a     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:PowerCC26XX_calibrate)
                  0000453a    0000001a     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_get)
                  00004554    0000001a                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_createBinary)
                  0000456e    00000018                     : ClockPTimer_nortos.oem4f (.text:ClockP_destruct)
                  00004586    00000002                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:faultISR)
                  00004588    00000018                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_inISR)
                  000045a0    00000018     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetPackageType)
                  000045b8    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_idleFunc)
                  000045d0    00000018     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_startup)
                  000045e8    00000018     drivers_cc13x2.a : UART.oem4f (.text:UART_Params_init)
                  00004600    00000018     rtsv7M4_T_le_v4SPD16_eabi.lib : args_main.c.obj (.text:_args_main)
                  00004618    00000018                                   : ll_mul_t2.asm.obj (.text)
                  00004630    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:switchToTCXO)
                  00004648    00000016     driverlib.lib : chipinfo.obj (.text:NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated)
                  0000465e    00000016     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_create)
                  00004674    00000016                     : SwiP_nortos.oem4f (.text:maxbit)
                  0000468a    00000014     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_freqToCounts)
                  0000469e    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:intDefaultHandler)
                  000046a0    00000014     driverlib.lib : interrupt.obj (.text:NOROM_IntUnregister)
                  000046b4    00000014     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_calibrate)
                  000046c8    00000014                      : PowerCC26X2.oem4f (.text:Power_disablePolicy)
                  000046dc    00000014     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_handleHwi)
                  000046f0    00000014                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCount64)
                  00004704    00000014                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:resetISR)
                  00004718    00000012     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_Params_init)
                  0000472a    00000012     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_Params_init)
                  0000473c    00000012     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_construct)
                  0000474e    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:nmiISR)
                  00004750    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_Params_init)
                  00004760    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_getCpuFreq)
                  00004770    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_clearInterrupt)
                  00004780    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_disable)
                  00004790    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_post)
                  000047a0    00000010     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_getPinCount)
                  000047b0    00000010     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerDisable)
                  000047c0    00000010     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_enablePolicy)
                  000047d0    00000010     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_Params_init)
                  000047e0    00000010                     : SwiP_nortos.oem4f (.text:SwiP_Params_init)
                  000047f0    00000010                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_Params_init)
                  00004800    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:uartPostNotify)
                  00004810    0000000e     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_empty)
                  0000481e    0000000e                     : QueueP_nortos.oem4f (.text:QueueP_remove)
                  0000482c    0000000e     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_none.c.obj (.text:decompress:none:__TI_decompress_none)
                  0000483a    00000002                                   : div0.asm.obj (.text)
                  0000483c    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_doTick)
                  00004848    0000000c                     : ClockPTimer_nortos.oem4f (.text:ClockP_getSystemTickPeriod)
                  00004854    0000000c     driverlib.lib : cpu.obj (.text:NOROM_CPUcpsid)
                  00004860    0000000c                   : cpu.obj (.text:NOROM_CPUcpsie)
                  0000486c    0000000c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_registerIntCb)
                  00004878    0000000c     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerRestore)
                  00004884    0000000c     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_getConstraintMask)
                  00004890    0000000c                      : PowerCC26X2.oem4f (.text:Power_getDependencyCount)
                  0000489c    0000000c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_getTrigger)
                  000048a8    0000000c                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getFreq)
                  000048b4    0000000c     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init)
                  000048c0    0000000a     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_convertCancel)
                  000048ca    0000000a                      : UDMACC26XX.oem4f (.text:UDMACC26XX_hwiIntFxn)
                  000048d4    0000000a                      : UARTCC26XX.oem4f (.text:readSemCallback)
                  000048de    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_conversionCallback)
                  000048e6    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_init)
                  000048ee    00000008                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_setLoadValue)
                  000048f6    00000008     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_restore)
                  000048fe    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_init)
                  00004906    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:malloc)
                  0000490e    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeSemCallback)
                  00004916    00000006     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_stop)
                  0000491c    00000006     driverlib.lib : cpu.obj (.text:NOROM_CPUdelay)
                  00004922    00000006     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_init)
                  00004928    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readPolling)
                  0000492e    00000006                      : UARTCC26XX.oem4f (.text:UARTCC26XX_writePolling)
                  00004934    00000006                      : PowerCC26X2.oem4f (.text:delayUs)
                  0000493a    00000004                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_getResolution)
                  0000493e    00000004     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:CPUwfi)
                  00004942    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_isActive)
                  00004946    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_setTimeout)
                  0000494a    00000004                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_enable)
                  0000494e    00000004                     : QueueP_nortos.oem4f (.text:QueueP_head)
                  00004952    00000004                     : QueueP_nortos.oem4f (.text:QueueP_next)
                  00004956    00000004                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_delete)
                  0000495a    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : pre_init.c.obj (.text:_system_pre_init)
                  0000495e    00000004                                   : exit.c.obj (.text:abort:abort)
                  00004962    00000004     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureRFCoreClocks)
                  00004966    00000004                      : PowerCC26X2.oem4f (.text:nopResourceHandler)

.const     0    0000496c    00000298     
                  0000496c    00000054     drivers_cc13x2.a : PowerCC26X2.oem4f (.const:resourceDB)
                  000049c0    00000044     ti_drivers_config.obj (.const:$O1$$)
                  00004a04    00000028     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:UARTCC26XX_fxnTable)
                  00004a2c    00000028     ti_drivers_config.obj (.const:uartCC26XXHWAttrs)
                  00004a54    00000024     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.const:ADCBufCC26X2_fxnTable)
                  00004a78    00000024                      : UART.oem4f (.const:UART_defaultParams)
                  00004a9c    0000001c                      : GPTimerCC26XX.oem4f (.const:GPT_LUT)
                  00004ab8    00000018     ti_drivers_config.obj (.const:GPTimerCC26XX_config)
                  00004ad0    00000018     ti_drivers_config.obj (.const:gptimerCC26XXHWAttrs)
                  00004ae8    00000018     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:interruptType)
                  00004b00    00000014     ti_drivers_config.obj (.const:GPIOCC26XX_config)
                  00004b14    00000014     ti_drivers_config.obj (.const:PowerCC26X2_config)
                  00004b28    00000014     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:parityType)
                  00004b3c    00000010     nortos_cc13x2.a : SwiP_nortos.oem4f (.const:SwiP_defaultParams)
                  00004b4c    00000010     ti_drivers_config.obj (.const:adcbufCC26XXHWAttrs)
                  00004b5c    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:dataLength)
                  00004b6c    00000010                      : GPIOCC26XX.oem4f (.const:outPinTypes)
                  00004b7c    0000000e     ti_drivers_config.obj (.const)
                  00004b8a    00000002     --HOLE-- [fill = 0]
                  00004b8c    0000000c     ti_drivers_config.obj (.const:ADCBuf_config)
                  00004b98    0000000c     ti_drivers_config.obj (.const:UART_config)
                  00004ba4    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:inPinTypes)
                  00004bb0    0000000c                      : GPIOCC26XX.oem4f (.const:outPinStrengths)
                  00004bbc    00000008     driverlib.lib : aux_sysif.obj (.const:$O1$$)
                  00004bc4    00000008     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.const:GPT_DefaultParams)
                  00004bcc    00000008     ti_drivers_config.obj (.const:PINCC26XX_hwAttrs)
                  00004bd4    00000008     ti_drivers_config.obj (.const:UDMACC26XX_config)
                  00004bdc    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:stopBits)
                  00004be4    00000008     ti_drivers_config.obj (.const:udmaCC26XXHWAttrs)
                  00004bec    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:.string:rxFifoBytes)
                  00004bf2    00000006                      : UARTCC26XX.oem4f (.const:.string:rxFifoThreshold)
                  00004bf8    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoBytes)
                  00004bfe    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoThreshold)

.binit     0    00000000    00000000     

.cinit     0    00004c10    000000e0     
                  00004c10    00000086     (.cinit..data.load) [load image, compression = lzss]
                  00004c96    00000002     --HOLE-- [fill = 0]
                  00004c98    0000000c     (__TI_handler_table)
                  00004ca4    00000004     --HOLE-- [fill = 0]
                  00004ca8    00000008     (.cinit..TI.bound:dmaADCAltControlTableEntry.load) [load image, compression = zero_init]
                  00004cb0    00000008     (.cinit..TI.bound:dmaADCPriControlTableEntry.load) [load image, compression = zero_init]
                  00004cb8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004cc0    00000008     (.cinit..vtable_ram.load) [load image, compression = zero_init]
                  00004cc8    00000028     (__TI_cinit_table)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.ccfg      0    00057fa8    00000058     
                  00057fa8    00000058     ti_devices_config.obj (.ccfg:retain)

.bss       0    20000000    00000668     UNINITIALIZED
                  20000000    000001b4     (.common:uartCC26XXObjects)
                  200001b4    000000dc     (.common:adcbufCC26XXbjects)
                  20000290    000000a0     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:ClockP_module)
                  20000330    00000098     (.common:gptimerCC26XXObjects)
                  200003c8    0000007c     (.common:pinHandleTable)
                  20000444    00000070     mainNew.obj (.bss:$O1$$)
                  200004b4    00000050     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinSemaphore)
                  20000504    00000034                      : PINCC26XX.oem4f (.bss:pinSwi)
                  20000538    00000020     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_readyList)
                  20000558    00000020     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioCallbackInfo)
                  20000578    00000020                      : PINCC26XX.oem4f (.bss:pinGpioConfigTable)
                  20000598    00000020     ti_drivers_config.obj (.bss:uartCC26XXRingBuffer0)
                  200005b8    00000020     (.common:udmaCC26XXObject)
                  200005d8    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_hwiStruct)
                  200005f4    0000001c                     : TimerPCC26XX_nortos.oem4f (.bss:TimerP_hwiStruct)
                  20000610    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinHwi)
                  2000062c    0000000d     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss)
                  20000639    00000001     (.common:driverlib_release_0_59848)
                  2000063a    00000002     --HOLE--
                  2000063c    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioPinState)
                  20000648    00000008                      : GPIOCC26XX.oem4f (.bss)
                  20000650    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:timerFreq)
                  20000658    00000004     drivers_cc13x2.a : PINCC26XX.oem4f (.bss)
                  2000065c    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss)
                  20000660    00000004                     : PowerCC26X2_nortos.oem4f (.bss)
                  20000664    00000004                     : TimerPCC26XX_nortos.oem4f (.bss)

.vtable_ram 
*          0    20000b00    000000d8     UNINITIALIZED
                  20000b00    000000d8     driverlib.lib : interrupt.obj (.vtable_ram)

.data      0    20000668    000003e6     UNINITIALIZED
                  20000668    00000170     drivers_cc13x2.a : PowerCC26X2.oem4f (.data:PowerCC26X2_module)
                  200007d8    00000100     SoundTX.obj (.data:$O1$$)
                  200008d8    000000d8     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data:HwiP_dispatchTable)
                  200009b0    00000024     driverlib.lib : osc.obj (.data:$O1$$)
                  200009d4    00000010     ti_drivers_config.obj (.data:gpioCallbackFunctions)
                  200009e4    00000010     ti_drivers_config.obj (.data:gpioPinConfigs)
                  200009f4    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data:ClockP_defaultParams)
                  20000a00    0000000c                     : TimerPCC26XX_nortos.oem4f (.data:TimerP_defaultParams)
                  20000a0c    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.data:$O1$$)
                  20000a14    00000008                                   : memory.c.obj (.data:$O1$$)
                  20000a1c    00000008     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data:SemaphoreP_defaultParams)
                  20000a24    00000008     drivers_cc13x2.a : PINCC26XX.oem4f (.data)
                  20000a2c    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data)
                  20000a34    00000006     ti_drivers_config.obj (.data:adcBufCC26XXChannelLut0)
                  20000a3a    00000001     drivers_cc13x2.a : ADCBuf.oem4f (.data)
                  20000a3b    00000001                      : UART.oem4f (.data)
                  20000a3c    00000005                      : GPIOCC26XX.oem4f (.data)
                  20000a41    00000003     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data)
                  20000a44    00000004     drivers_cc13x2.a : GPIOCC26XX.oem4f (.data:gpioPinTable)
                  20000a48    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data)
                  20000a4c    00000001                     : SwiP_nortos.oem4f (.data)
                  20000a4d    00000001                     : TimerPCC26XX_nortos.oem4f (.data)

.sysmem    0    20001a80    00004000     UNINITIALIZED
                  20001a80    00000010     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.sysmem)
                  20001a90    00003ff0     --HOLE--

.stack     0    20013c00    00000400     UNINITIALIZED
                  20013c00    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.stack)
                  20013c04    000003fc     --HOLE--

.log_data 
*          0    90000000    00000000     COPY SECTION

__llvm_prf_cnts 
*          0    20000000    00000000     UNINITIALIZED

.TI.bound:dmaADCPriControlTableEntry 
*          0    20001870    00000010     UNINITIALIZED
                  20001870    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCPriControlTableEntry)

.TI.bound:dmaADCAltControlTableEntry 
*          0    20001a70    00000010     UNINITIALIZED
                  20001a70    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCAltControlTableEntry)

.args      0    00004c04    00000008     
                  00004c04    00000008     --HOLE-- [fill = 0]

MODULE SUMMARY

       Module                             code    ro data   rw data
       ------                             ----    -------   -------
    .\
       SoundTX.obj                        296     0         256    
       mainNew.obj                        136     0         112    
    +--+----------------------------------+-------+---------+---------+
       Total:                             432     0         368    
                                                                   
    .\syscfg\
       ti_drivers_config.obj              260     274       910    
       ti_devices_config.obj              0       88        0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             260     362       910    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos/lib/ccs/m4f/nortos_cc13x2.a
       ClockPTimer_nortos.oem4f           922     0         192    
       SwiP_nortos.oem4f                  724     16        74     
       TimerPCC26XX_nortos.oem4f          642     0         45     
       HwiPCC26XX_nortos.oem4f            294     0         220    
       SemaphoreP_nortos.oem4f            410     0         11     
       PowerCC26X2_nortos.oem4f           256     0         4      
       startup_cc13x2_cc26x2_ccs.oem4f    26      216       0      
       QueueP_nortos.oem4f                100     0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             3374    232       546    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/cc13x2_cc26x2/driverlib/bin/ccs/driverlib.lib
       sys_ctrl.obj                       420     0         0      
       osc.obj                            340     0         36     
       setup.obj                          324     0         0      
       interrupt.obj                      74      0         216    
       chipinfo.obj                       250     0         0      
       prcm.obj                           160     0         0      
       timer.obj                          112     0         0      
       aux_sysif.obj                      52      8         0      
       aux_adc.obj                        32      0         0      
       cpu.obj                            30      0         0      
       driverlib_release.obj              0       0         1      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1794    8         253    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/lib/ccs/m4f/drivers_cc13x2.a
       UARTCC26XX.oem4f                   2760    108       0      
       PowerCC26X2.oem4f                  2096    84        368    
       PINCC26XX.oem4f                    1554    0         328    
       ADCBufCC26X2.oem4f                 1542    36        32     
       PowerCC26X2_calibrateRCOSC.oem4f   1400    0         0      
       GPIOCC26XX.oem4f                   660     64        61     
       GPTimerCC26XX.oem4f                670     36        0      
       UART.oem4f                         156     36        1      
       UDMACC26XX.oem4f                   168     0         0      
       RingBuf.oem4f                      152     0         0      
       ADCBuf.oem4f                       78      0         1      
       List.oem4f                         76      0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             11312   364       791    
                                                                   
    C:\ti\ccs1011\ccs\tools\compiler\ti-cgt-arm_20.2.4.LTS\lib\rtsv7M4_T_le_v4SPD16_eabi.lib
       memory.c.obj                       672     0         8      
       memcpy_t2.asm.obj                  156     0         0      
       ull_div_t2.asm.obj                 150     0         0      
       memset_t2.asm.obj                  122     0         0      
       copy_decompress_lzss.c.obj         104     0         0      
       autoinit.c.obj                     68      0         0      
       boot_cortex_m.c.obj                48      0         0      
       args_main.c.obj                    24      0         0      
       ll_mul_t2.asm.obj                  24      0         0      
       copy_decompress_none.c.obj         14      0         0      
       copy_zero_init.c.obj               12      0         0      
       _lock.c.obj                        2       0         8      
       exit.c.obj                         4       0         0      
       pre_init.c.obj                     4       0         0      
       div0.asm.obj                       2       0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1406    0         16     
                                                                   
       Heap:                              0       0         16384  
       Stack:                             0       0         1024   
       Linker Generated:                  0       218       0      
    +--+----------------------------------+-------+---------+---------+
       Grand Total:                       18578   1184      20292  


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004cc8 records: 5, size/record: 8, table size: 40
	.data: load addr=00004c10, load size=00000086 bytes, run addr=20000668, run size=000003e6 bytes, compression=lzss
	.TI.bound:dmaADCAltControlTableEntry: load addr=00004ca8, load size=00000008 bytes, run addr=20001a70, run size=00000010 bytes, compression=zero_init
	.TI.bound:dmaADCPriControlTableEntry: load addr=00004cb0, load size=00000008 bytes, run addr=20001870, run size=00000010 bytes, compression=zero_init
	.bss: load addr=00004cb8, load size=00000008 bytes, run addr=20000000, run size=00000668 bytes, compression=zero_init
	.vtable_ram: load addr=00004cc0, load size=00000008 bytes, run addr=20000b00, run size=000000d8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004c98 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_lzss
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                                               
-------   ----                                                               
00004b7f  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004b7c  ADCBUF_CONST                                                       
00004b7d  ADCBUF_SOUND_CONST                                                 
00004b7e  ADCBUF_TEMPERATURE_CONST                                           
00003185  ADCBufCC26X2_adjustRawValues                                       
0000327d  ADCBufCC26X2_close                                                 
000034e1  ADCBufCC26X2_control                                               
0000118d  ADCBufCC26X2_convert                                               
0000352d  ADCBufCC26X2_convertAdjustedToMicroVolts                           
0000398d  ADCBufCC26X2_convertCancel                                         
00004a54  ADCBufCC26X2_fxnTable                                              
0000493b  ADCBufCC26X2_getResolution                                         
000048e7  ADCBufCC26X2_init                                                  
0000076d  ADCBufCC26X2_open                                                  
00004b8c  ADCBuf_config                                                      
000048c1  ADCBuf_convertCancel                                               
00004b80  ADCBuf_count                                                       
0000376d  ADCBuf_init                                                        
000049dc  BoardGpioInitTable                                                 
000042e1  Board_init                                                         
00000e47  Board_initHook                                                     
00002ea1  Board_sendExtFlashByte                                             
00003b7d  Board_shutDownExtFlash                                             
00003575  Board_wakeUpExtFlash                                               
0000495f  C$$EXIT                                                            
00004b87  CONFIG_GPTIMER_0_CONST                                             
00004b88  CONFIG_GPTIMER_1_CONST                                             
200007e0  ChirpDelay                                                         
200007dc  ChirpIndex                                                         
200007d8  ChirpSize                                                          
2000085c  ChirpState                                                         
00004751  ClockP_Params_init                                                 
00004301  ClockP_add                                                         
000035bd  ClockP_construct                                                   
0000456f  ClockP_destruct                                                    
0000483d  ClockP_doTick                                                      
00004761  ClockP_getCpuFreq                                                  
00004849  ClockP_getSystemTickPeriod                                         
00004209  ClockP_getTicks                                                    
00003d71  ClockP_getTicksUntilInterrupt                                      
00004943  ClockP_isActive                                                    
0000422d  ClockP_scheduleNextTick                                            
00004947  ClockP_setTimeout                                                  
0000278d  ClockP_start                                                       
00001c69  ClockP_startup                                                     
00004917  ClockP_stop                                                        
20000a30  ClockP_tickPeriod                                                  
00002989  ClockP_walkQueueDynamic                                            
00001d1d  ClockP_workFuncDynamic                                             
00004b00  GPIOCC26XX_config                                                  
00003f11  GPIO_hwiIntFxn                                                     
000017f9  GPIO_init                                                          
00003f41  GPIO_setCallback                                                   
0000107d  GPIO_setConfig                                                     
00002f65  GPIO_write                                                         
00004719  GPTimerCC26XX_Params_init                                          
00003f71  GPTimerCC26XX_close                                                
00004ab8  GPTimerCC26XX_config                                               
00003dd9  GPTimerCC26XX_configureDebugStall                                  
000023e1  GPTimerCC26XX_open                                                 
000048ef  GPTimerCC26XX_setLoadValue                                         
00003365  GPTimerCC26XX_start                                                
000030d5  GPTimerCC26XX_stop                                                 
00004b89  GPTimer_count                                                      
0000472b  HwiP_Params_init                                                   
00004771  HwiP_clearInterrupt                                                
0000280d  HwiP_construct                                                     
00004479  HwiP_destruct                                                      
00004781  HwiP_disable                                                       
0000494b  HwiP_enable                                                        
00004589  HwiP_inISR                                                         
00004791  HwiP_post                                                          
000048f7  HwiP_restore                                                       
20000a48  HwiP_swiPIntNum                                                    
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
0000376d  InitADC                                                            
00002351  InitTimer2                                                         
00003acd  InitUart                                                           
000041bd  List_put                                                           
000041e3  List_remove                                                        
00004321  NOROM_AUXADCEnableSync                                             
00003e0d  NOROM_AUXSYSIFOpModeChange                                         
00004855  NOROM_CPUcpsid                                                     
00004861  NOROM_CPUcpsie                                                     
0000491d  NOROM_CPUdelay                                                     
00004495  NOROM_ChipInfo_GetChipFamily                                       
00002bc9  NOROM_ChipInfo_GetChipType                                         
000037b1  NOROM_ChipInfo_GetHwRevision                                       
000045a1  NOROM_ChipInfo_GetPackageType                                      
00003e41  NOROM_IntRegister                                                  
000046a1  NOROM_IntUnregister                                                
00002469  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002c35  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
00004275  NOROM_OSCHF_TurnOnXosc                                             
000039cd  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
000031d9  NOROM_PRCMPowerDomainsAllOff                                       
000033b1  NOROM_PRCMPowerDomainsAllOn                                        
000020f9  NOROM_SetupTrimDevice                                              
00003e75  NOROM_SysCtrlIdle                                                  
0000288d  NOROM_SysCtrlSetRechargeBeforePowerDown                            
0000138d  NOROM_SysCtrlStandby                                               
00004649  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00003a0d  NOROM_TimerIntRegister                                             
0000494b  NoRTOS_start                                                       
000047a1  PINCC26XX_getPinCount                                              
00004bcc  PINCC26XX_hwAttrs                                                  
00003fa1  PINCC26XX_setMux                                                   
00004b83  PIN_DRIVE_SPEAKER_A_CONST                                          
00004b84  PIN_DRIVE_SPEAKER_B_CONST                                          
00004b81  PIN_TEST1_CONST                                                    
00004b82  PIN_TEST2_CONST                                                    
00002ae9  PIN_add                                                            
00004401  PIN_close                                                          
000000d9  PIN_init                                                           
00001dc9  PIN_open                                                           
0000486d  PIN_registerIntCb                                                  
00002fc5  PIN_remove                                                         
00003bb5  PIN_setConfig                                                      
00003605  PIN_setOutputEnable                                                
00003b09  PIN_setOutputValue                                                 
0000364d  PowerCC26X2_RCOSC_clockFunc                                        
00002a75  PowerCC26X2_auxISR                                                 
000046b5  PowerCC26X2_calibrate                                              
00004b14  PowerCC26X2_config                                                 
0000312d  PowerCC26X2_initiateCalibration                                    
20000668  PowerCC26X2_module                                                 
00004521  PowerCC26XX_calibrate                                              
000047b1  PowerCC26XX_schedulerDisable                                       
00004879  PowerCC26XX_schedulerRestore                                       
00001561  PowerCC26XX_standbyPolicy                                          
000046c9  Power_disablePolicy                                                
000047c1  Power_enablePolicy                                                 
00004885  Power_getConstraintMask                                            
00004891  Power_getDependencyCount                                           
0000441f  Power_getTransitionLatency                                         
000045b9  Power_idleFunc                                                     
000008e1  Power_init                                                         
00004145  Power_registerNotify                                               
00003fd1  Power_releaseConstraint                                            
000018bd  Power_releaseDependency                                            
00004001  Power_setConstraint                                                
00001af9  Power_setDependency                                                
000002b9  Power_sleep                                                        
00004361  Power_unregisterNotify                                             
00004811  QueueP_empty                                                       
0000453b  QueueP_get                                                         
0000494f  QueueP_head                                                        
00004923  QueueP_init                                                        
00004953  QueueP_next                                                        
00004381  QueueP_put                                                         
0000481f  QueueP_remove                                                      
0000473d  RingBuf_construct                                                  
00003a4d  RingBuf_get                                                        
00003725  RingBuf_put                                                        
000047d1  SemaphoreP_Params_init                                             
0000322d  SemaphoreP_construct                                               
0000443d  SemaphoreP_constructBinary                                         
0000465f  SemaphoreP_create                                                  
00004555  SemaphoreP_createBinary                                            
20000a1c  SemaphoreP_defaultParams                                           
00004957  SemaphoreP_delete                                                  
000032cb  SemaphoreP_destruct                                                
00001f21  SemaphoreP_pend                                                    
00003839  SemaphoreP_post                                                    
00004031  SoundTransmit                                                      
000047e1  SwiP_Params_init                                                   
00001721  SwiP_construct                                                     
00003ea9  SwiP_destruct                                                      
000044cd  SwiP_disable                                                       
00001fc1  SwiP_dispatch                                                      
0000489d  SwiP_getTrigger                                                    
0000445b  SwiP_or                                                            
00002c9d  SwiP_post                                                          
0000387d  SwiP_restore                                                       
00002d05  Timer2AInterruptHandler                                            
000047f1  TimerP_Params_init                                                 
00001a3d  TimerP_construct                                                   
000043a1  TimerP_dynamicStub                                                 
000046f1  TimerP_getCount64                                                  
00003bed  TimerP_getCurrentTick                                              
000048a9  TimerP_getFreq                                                     
00004299  TimerP_getMaxTicks                                                 
00003c25  TimerP_initDevice                                                  
00003d05  TimerP_setNextTick                                                 
00004119  TimerP_setThreshold                                                
00002d6d  TimerP_start                                                       
000045d1  TimerP_startup                                                     
00002689  UARTCC26XX_close                                                   
00002e3d  UARTCC26XX_control                                                 
00004a04  UARTCC26XX_fxnTable                                                
00000bb9  UARTCC26XX_hwiIntFxn                                               
000048ff  UARTCC26XX_init                                                    
00000a55  UARTCC26XX_open                                                    
00000d0d  UARTCC26XX_read                                                    
000033fd  UARTCC26XX_readCancel                                              
00004929  UARTCC26XX_readPolling                                             
00002f03  UARTCC26XX_swiIntFxn                                               
00001641  UARTCC26XX_write                                                   
000022bf  UARTCC26XX_writeCancel                                             
0000492f  UARTCC26XX_writePolling                                            
00004b85  UART_0_CONST                                                       
000045e9  UART_Params_init                                                   
00004b98  UART_config                                                        
00004b86  UART_count                                                         
00004a78  UART_defaultParams                                                 
000038c1  UART_init                                                          
00003a8d  UART_open                                                          
20000450  UARTbuffer                                                         
00003d3b  UDMACC26XX_close                                                   
00004bd4  UDMACC26XX_config                                                  
000048cb  UDMACC26XX_hwiIntFxn                                               
00003449  UDMACC26XX_open                                                    
20014000  __STACK_END                                                        
00000400  __STACK_SIZE                                                       
00004000  __SYSMEM_SIZE                                                      
00004cc8  __TI_CINIT_Base                                                    
00004cf0  __TI_CINIT_Limit                                                   
00004c98  __TI_Handler_Table_Base                                            
00004ca4  __TI_Handler_Table_Limit                                           
00003905  __TI_auto_init_nobinit_nopinit                                     
00002dd5  __TI_decompress_lzss                                               
0000482d  __TI_decompress_none                                               
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
00000000  __TI_static_base__                                                 
000048b5  __TI_zero_init                                                     
0000483b  __aeabi_idiv0                                                      
0000483b  __aeabi_ldiv0                                                      
00004619  __aeabi_lmul                                                       
0000290d  __aeabi_memclr                                                     
0000290d  __aeabi_memclr4                                                    
0000290d  __aeabi_memclr8                                                    
0000205d  __aeabi_memcpy                                                     
0000205d  __aeabi_memcpy4                                                    
0000205d  __aeabi_memcpy8                                                    
0000290f  __aeabi_memset                                                     
0000290f  __aeabi_memset4                                                    
0000290f  __aeabi_memset8                                                    
00002229  __aeabi_uldivmod                                                   
ffffffff  __binit__                                                          
00004c04  __c_args__                                                         
00057fa8  __ccfg                                                             
UNDEFED   __mpu_init                                                         
20013c00  __stack                                                            
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
00004601  _args_main                                                         
00004091  _c_int00                                                           
200009b0  _hposcCoeffs                                                       
20000a0c  _lock                                                              
0000352b  _nop                                                               
20001a80  _sys_memory                                                        
UNDEFED   _system_post_cinit                                                 
0000495b  _system_pre_init                                                   
20000a10  _unlock                                                            
0000495f  abort                                                              
20000a34  adcBufCC26XXChannelLut0                                            
00004b4c  adcbufCC26XXHWAttrs                                                
200001b4  adcbufCC26XXbjects                                                 
0000128d  aligned_alloc                                                      
ffffffff  binit                                                              
0000376b  clkFxn                                                             
20000639  driverlib_release_0_59848                                          
0000147d  free                                                               
20000b00  g_pfnRAMVectors                                                    
00000000  g_pfnVectors                                                       
200009d4  gpioCallbackFunctions                                              
200009e4  gpioPinConfigs                                                     
00004ad0  gptimerCC26XXHWAttrs                                               
20000330  gptimerCC26XXObjects                                               
20000444  i                                                                  
00004ba4  inPinTypes                                                         
20000448  j                                                                  
00003495  main                                                               
00004907  malloc                                                             
0000128d  memalign                                                           
0000205d  memcpy                                                             
00002915  memset                                                             
00004bb0  outPinStrengths                                                    
00004b6c  outPinTypes                                                        
200003c8  pinHandleTable                                                     
20000a28  pinLowerBound                                                      
20000a24  pinUpperBound                                                      
00004705  resetISR                                                           
0000496c  resourceDB                                                         
2000044c  uart                                                               
20000000  uartCC26XXObjects                                                  
00004be4  udmaCC26XXHWAttrs                                                  
200005b8  udmaCC26XXObject                                                   


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                                               
-------   ----                                                               
00000000  __TI_static_base__                                                 
00000000  g_pfnVectors                                                       
000000d9  PIN_init                                                           
000002b9  Power_sleep                                                        
00000400  __STACK_SIZE                                                       
0000076d  ADCBufCC26X2_open                                                  
000008e1  Power_init                                                         
00000a55  UARTCC26XX_open                                                    
00000bb9  UARTCC26XX_hwiIntFxn                                               
00000d0d  UARTCC26XX_read                                                    
00000e47  Board_initHook                                                     
0000107d  GPIO_setConfig                                                     
0000118d  ADCBufCC26X2_convert                                               
0000128d  aligned_alloc                                                      
0000128d  memalign                                                           
0000138d  NOROM_SysCtrlStandby                                               
0000147d  free                                                               
00001561  PowerCC26XX_standbyPolicy                                          
00001641  UARTCC26XX_write                                                   
00001721  SwiP_construct                                                     
000017f9  GPIO_init                                                          
000018bd  Power_releaseDependency                                            
00001a3d  TimerP_construct                                                   
00001af9  Power_setDependency                                                
00001c69  ClockP_startup                                                     
00001d1d  ClockP_workFuncDynamic                                             
00001dc9  PIN_open                                                           
00001f21  SemaphoreP_pend                                                    
00001fc1  SwiP_dispatch                                                      
0000205d  __aeabi_memcpy                                                     
0000205d  __aeabi_memcpy4                                                    
0000205d  __aeabi_memcpy8                                                    
0000205d  memcpy                                                             
000020f9  NOROM_SetupTrimDevice                                              
00002229  __aeabi_uldivmod                                                   
000022bf  UARTCC26XX_writeCancel                                             
00002351  InitTimer2                                                         
000023e1  GPTimerCC26XX_open                                                 
00002469  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002689  UARTCC26XX_close                                                   
0000278d  ClockP_start                                                       
0000280d  HwiP_construct                                                     
0000288d  NOROM_SysCtrlSetRechargeBeforePowerDown                            
0000290d  __aeabi_memclr                                                     
0000290d  __aeabi_memclr4                                                    
0000290d  __aeabi_memclr8                                                    
0000290f  __aeabi_memset                                                     
0000290f  __aeabi_memset4                                                    
0000290f  __aeabi_memset8                                                    
00002915  memset                                                             
00002989  ClockP_walkQueueDynamic                                            
00002a75  PowerCC26X2_auxISR                                                 
00002ae9  PIN_add                                                            
00002bc9  NOROM_ChipInfo_GetChipType                                         
00002c35  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
00002c9d  SwiP_post                                                          
00002d05  Timer2AInterruptHandler                                            
00002d6d  TimerP_start                                                       
00002dd5  __TI_decompress_lzss                                               
00002e3d  UARTCC26XX_control                                                 
00002ea1  Board_sendExtFlashByte                                             
00002f03  UARTCC26XX_swiIntFxn                                               
00002f65  GPIO_write                                                         
00002fc5  PIN_remove                                                         
000030d5  GPTimerCC26XX_stop                                                 
0000312d  PowerCC26X2_initiateCalibration                                    
00003185  ADCBufCC26X2_adjustRawValues                                       
000031d9  NOROM_PRCMPowerDomainsAllOff                                       
0000322d  SemaphoreP_construct                                               
0000327d  ADCBufCC26X2_close                                                 
000032cb  SemaphoreP_destruct                                                
00003365  GPTimerCC26XX_start                                                
000033b1  NOROM_PRCMPowerDomainsAllOn                                        
000033fd  UARTCC26XX_readCancel                                              
00003449  UDMACC26XX_open                                                    
00003495  main                                                               
000034e1  ADCBufCC26X2_control                                               
0000352b  _nop                                                               
0000352d  ADCBufCC26X2_convertAdjustedToMicroVolts                           
00003575  Board_wakeUpExtFlash                                               
000035bd  ClockP_construct                                                   
00003605  PIN_setOutputEnable                                                
0000364d  PowerCC26X2_RCOSC_clockFunc                                        
00003725  RingBuf_put                                                        
0000376b  clkFxn                                                             
0000376d  ADCBuf_init                                                        
0000376d  InitADC                                                            
000037b1  NOROM_ChipInfo_GetHwRevision                                       
00003839  SemaphoreP_post                                                    
0000387d  SwiP_restore                                                       
000038c1  UART_init                                                          
00003905  __TI_auto_init_nobinit_nopinit                                     
0000398d  ADCBufCC26X2_convertCancel                                         
000039cd  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00003a0d  NOROM_TimerIntRegister                                             
00003a4d  RingBuf_get                                                        
00003a8d  UART_open                                                          
00003acd  InitUart                                                           
00003b09  PIN_setOutputValue                                                 
00003b7d  Board_shutDownExtFlash                                             
00003bb5  PIN_setConfig                                                      
00003bed  TimerP_getCurrentTick                                              
00003c25  TimerP_initDevice                                                  
00003d05  TimerP_setNextTick                                                 
00003d3b  UDMACC26XX_close                                                   
00003d71  ClockP_getTicksUntilInterrupt                                      
00003dd9  GPTimerCC26XX_configureDebugStall                                  
00003e0d  NOROM_AUXSYSIFOpModeChange                                         
00003e41  NOROM_IntRegister                                                  
00003e75  NOROM_SysCtrlIdle                                                  
00003ea9  SwiP_destruct                                                      
00003f11  GPIO_hwiIntFxn                                                     
00003f41  GPIO_setCallback                                                   
00003f71  GPTimerCC26XX_close                                                
00003fa1  PINCC26XX_setMux                                                   
00003fd1  Power_releaseConstraint                                            
00004000  __SYSMEM_SIZE                                                      
00004001  Power_setConstraint                                                
00004031  SoundTransmit                                                      
00004091  _c_int00                                                           
00004119  TimerP_setThreshold                                                
00004145  Power_registerNotify                                               
000041bd  List_put                                                           
000041e3  List_remove                                                        
00004209  ClockP_getTicks                                                    
0000422d  ClockP_scheduleNextTick                                            
00004275  NOROM_OSCHF_TurnOnXosc                                             
00004299  TimerP_getMaxTicks                                                 
000042e1  Board_init                                                         
00004301  ClockP_add                                                         
00004321  NOROM_AUXADCEnableSync                                             
00004361  Power_unregisterNotify                                             
00004381  QueueP_put                                                         
000043a1  TimerP_dynamicStub                                                 
00004401  PIN_close                                                          
0000441f  Power_getTransitionLatency                                         
0000443d  SemaphoreP_constructBinary                                         
0000445b  SwiP_or                                                            
00004479  HwiP_destruct                                                      
00004495  NOROM_ChipInfo_GetChipFamily                                       
000044cd  SwiP_disable                                                       
00004521  PowerCC26XX_calibrate                                              
0000453b  QueueP_get                                                         
00004555  SemaphoreP_createBinary                                            
0000456f  ClockP_destruct                                                    
00004589  HwiP_inISR                                                         
000045a1  NOROM_ChipInfo_GetPackageType                                      
000045b9  Power_idleFunc                                                     
000045d1  TimerP_startup                                                     
000045e9  UART_Params_init                                                   
00004601  _args_main                                                         
00004619  __aeabi_lmul                                                       
00004649  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
0000465f  SemaphoreP_create                                                  
000046a1  NOROM_IntUnregister                                                
000046b5  PowerCC26X2_calibrate                                              
000046c9  Power_disablePolicy                                                
000046f1  TimerP_getCount64                                                  
00004705  resetISR                                                           
00004719  GPTimerCC26XX_Params_init                                          
0000472b  HwiP_Params_init                                                   
0000473d  RingBuf_construct                                                  
00004751  ClockP_Params_init                                                 
00004761  ClockP_getCpuFreq                                                  
00004771  HwiP_clearInterrupt                                                
00004781  HwiP_disable                                                       
00004791  HwiP_post                                                          
000047a1  PINCC26XX_getPinCount                                              
000047b1  PowerCC26XX_schedulerDisable                                       
000047c1  Power_enablePolicy                                                 
000047d1  SemaphoreP_Params_init                                             
000047e1  SwiP_Params_init                                                   
000047f1  TimerP_Params_init                                                 
00004811  QueueP_empty                                                       
0000481f  QueueP_remove                                                      
0000482d  __TI_decompress_none                                               
0000483b  __aeabi_idiv0                                                      
0000483b  __aeabi_ldiv0                                                      
0000483d  ClockP_doTick                                                      
00004849  ClockP_getSystemTickPeriod                                         
00004855  NOROM_CPUcpsid                                                     
00004861  NOROM_CPUcpsie                                                     
0000486d  PIN_registerIntCb                                                  
00004879  PowerCC26XX_schedulerRestore                                       
00004885  Power_getConstraintMask                                            
00004891  Power_getDependencyCount                                           
0000489d  SwiP_getTrigger                                                    
000048a9  TimerP_getFreq                                                     
000048b5  __TI_zero_init                                                     
000048c1  ADCBuf_convertCancel                                               
000048cb  UDMACC26XX_hwiIntFxn                                               
000048e7  ADCBufCC26X2_init                                                  
000048ef  GPTimerCC26XX_setLoadValue                                         
000048f7  HwiP_restore                                                       
000048ff  UARTCC26XX_init                                                    
00004907  malloc                                                             
00004917  ClockP_stop                                                        
0000491d  NOROM_CPUdelay                                                     
00004923  QueueP_init                                                        
00004929  UARTCC26XX_readPolling                                             
0000492f  UARTCC26XX_writePolling                                            
0000493b  ADCBufCC26X2_getResolution                                         
00004943  ClockP_isActive                                                    
00004947  ClockP_setTimeout                                                  
0000494b  HwiP_enable                                                        
0000494b  NoRTOS_start                                                       
0000494f  QueueP_head                                                        
00004953  QueueP_next                                                        
00004957  SemaphoreP_delete                                                  
0000495b  _system_pre_init                                                   
0000495f  C$$EXIT                                                            
0000495f  abort                                                              
0000496c  resourceDB                                                         
000049dc  BoardGpioInitTable                                                 
00004a04  UARTCC26XX_fxnTable                                                
00004a54  ADCBufCC26X2_fxnTable                                              
00004a78  UART_defaultParams                                                 
00004ab8  GPTimerCC26XX_config                                               
00004ad0  gptimerCC26XXHWAttrs                                               
00004b00  GPIOCC26XX_config                                                  
00004b14  PowerCC26X2_config                                                 
00004b4c  adcbufCC26XXHWAttrs                                                
00004b6c  outPinTypes                                                        
00004b7c  ADCBUF_CONST                                                       
00004b7d  ADCBUF_SOUND_CONST                                                 
00004b7e  ADCBUF_TEMPERATURE_CONST                                           
00004b7f  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004b80  ADCBuf_count                                                       
00004b81  PIN_TEST1_CONST                                                    
00004b82  PIN_TEST2_CONST                                                    
00004b83  PIN_DRIVE_SPEAKER_A_CONST                                          
00004b84  PIN_DRIVE_SPEAKER_B_CONST                                          
00004b85  UART_0_CONST                                                       
00004b86  UART_count                                                         
00004b87  CONFIG_GPTIMER_0_CONST                                             
00004b88  CONFIG_GPTIMER_1_CONST                                             
00004b89  GPTimer_count                                                      
00004b8c  ADCBuf_config                                                      
00004b98  UART_config                                                        
00004ba4  inPinTypes                                                         
00004bb0  outPinStrengths                                                    
00004bcc  PINCC26XX_hwAttrs                                                  
00004bd4  UDMACC26XX_config                                                  
00004be4  udmaCC26XXHWAttrs                                                  
00004c04  __c_args__                                                         
00004c98  __TI_Handler_Table_Base                                            
00004ca4  __TI_Handler_Table_Limit                                           
00004cc8  __TI_CINIT_Base                                                    
00004cf0  __TI_CINIT_Limit                                                   
00057fa8  __ccfg                                                             
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
20000000  uartCC26XXObjects                                                  
200001b4  adcbufCC26XXbjects                                                 
20000330  gptimerCC26XXObjects                                               
200003c8  pinHandleTable                                                     
20000444  i                                                                  
20000448  j                                                                  
2000044c  uart                                                               
20000450  UARTbuffer                                                         
200005b8  udmaCC26XXObject                                                   
20000639  driverlib_release_0_59848                                          
20000668  PowerCC26X2_module                                                 
200007d8  ChirpSize                                                          
200007dc  ChirpIndex                                                         
200007e0  ChirpDelay                                                         
2000085c  ChirpState                                                         
200009b0  _hposcCoeffs                                                       
200009d4  gpioCallbackFunctions                                              
200009e4  gpioPinConfigs                                                     
20000a0c  _lock                                                              
20000a10  _unlock                                                            
20000a1c  SemaphoreP_defaultParams                                           
20000a24  pinUpperBound                                                      
20000a28  pinLowerBound                                                      
20000a30  ClockP_tickPeriod                                                  
20000a34  adcBufCC26XXChannelLut0                                            
20000a48  HwiP_swiPIntNum                                                    
20000b00  g_pfnRAMVectors                                                    
20001a80  _sys_memory                                                        
20013c00  __stack                                                            
20014000  __STACK_END                                                        
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
ffffffff  __binit__                                                          
ffffffff  binit                                                              
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
UNDEFED   __mpu_init                                                         
UNDEFED   _system_post_cinit                                                 

[291 symbols]
