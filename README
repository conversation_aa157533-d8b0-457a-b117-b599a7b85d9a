## MAC Addresses

- NodeA MAC Address: 08:3A:F2:B7:50:5C
- NodeB MAC Address: 50:02:91:98:FC:BC

## Project Structure

- `src/nodeA/` - Source code for the transmitter node
- `src/nodeB/` - Source code for the receiver node
- `lib/` - Shared libraries
  - `AudioUtils/` - Audio processing utilities
  - `ESPNowManager/` - ESP-NOW communication wrapper
- `test/` - Test applications
  - `espnow_comm_test.cpp` - ESP-NOW communication test

## Building and Uploading

### Using the PowerShell Script

This project includes a PowerShell script (`build.ps1`) to simplify building and uploading:

```powershell
# Build the firmware
.\build.ps1 build-a      # Build NodeA firmware
.\build.ps1 build-b      # Build NodeB firmware
.\build.ps1 build-all    # Build both nodes

# Upload the firmware
.\build.ps1 upload-a     # Upload to NodeA
.\build.ps1 upload-b     # Upload to NodeB
.\build.ps1 upload-all   # Upload to both nodes

# Upload test firmware
.\build.ps1 test-a       # Upload test to NodeA
.\build.ps1 test-b       # Upload test to NodeB
.\build.ps1 test-all     # Upload test to both nodes

# Monitor serial output
.\build.ps1 monitor-a    # Monitor NodeA
.\build.ps1 monitor-b    # Monitor NodeB