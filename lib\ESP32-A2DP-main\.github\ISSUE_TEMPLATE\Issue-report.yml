name: Bug report
description: Report only a bugs here! 
body:
  - type: markdown
    attributes:
      value: |
        * Before reporting a new bug please check and search the [list of existing issues](https://github.com/pschatzmann/ESP32-A2DP/issues?q=) 
        * Please check [the Readme](https://github.com/pschatzmann/ESP32-A2DP) and [Wiki](https://github.com/pschatzmann/ESP32-A2DP/wiki)
        * Don't forget to check [the discusions](https://github.com/pschatzmann/ESP32-A2DP/discussions)
        * If still experiencing the issue, please provide as many details as possible below about your hardware, computer setup and code.
  - type: textarea
    id: Description
    attributes:
      label: Problem Description
      description: Please describe your problem here and expected behaviour
      placeholder: ex. Can't connect/weird behaviour/wrong function/missing parameter..
    validations:
      required: true
  - type: textarea
    id: Board
    attributes:
      label: Device Description
      description: What development board are you using
      placeholder: e.g. ESP32 Wroom
    validations:
       required: true
  - type: textarea
    id: sketch
    attributes:
      label: Sketch
      description: Please provide full minimal sketch/code which can be run to reproduce your issue 
      placeholder: ex. Related part of the code to replicate the issue
      render: cpp
    validations:
     required: true
  - type: textarea
    id: other-remarks
    attributes:
      label: Other Steps to Reproduce 
      description: Is there any other information you can think of which will help us reproduce this problem? Any additional info can be added as well.
      placeholder: ex. I also tried on other OS, HW...it works correctly on that setup.

  - type: input
    id: esp32-version
    attributes:
      label: Provide your Version of the EP32 Arduino Core (or the IDF Version)
      description: This information can be found in the Boards Manager
      placeholder: e.g. 2.0.13
    validations:
     required: true

  - type: checkboxes
    id: confirmation
    attributes:
      label: I have checked existing issues, discussions and online documentation
      description: You agree to check all the resources above before opening a new issue.
      options:
        - label: I confirm I have checked existing issues, discussions and online documentation
          required: true  
