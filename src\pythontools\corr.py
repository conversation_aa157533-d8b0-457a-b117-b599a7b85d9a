import numpy as np
import soundfile as sf
import matplotlib.pyplot as plt
from scipy.signal import correlate
import json

# Try to import hann from different locations (compatibility with different scipy versions)
try:
    from scipy.signal.windows import hann
except ImportError:
    try:
        from scipy.signal import hann
    except ImportError:
        # Fallback: use numpy's hanning window
        def hann(M):
            return np.hanning(M)

# === Parameter aus deinem C++ Code ===
SAMPLE_RATE = 48000
CHIRP_DURATION = 0.050  # 50 ms
CHIRP_SAMPLES = int(SAMPLE_RATE * CHIRP_DURATION)
F_START = 800.0
F_END = 3500.0
CHIRP_K = (F_END - F_START) / CHIRP_SAMPLES
FADE_SAMPLES = 144

# === Chirp-Signal aus JSON laden ===
def load_chirp_from_json(json_path="i2s_data_list.json"):
    """
    <PERSON>ädt den Chirp aus der I2S JSON-Datei
    """
    try:
        with open(json_path, 'r') as f:
            data = json.load(f)

        # I2S-Daten als int16 laden
        i2s_samples = np.array(data['i2s_data_list'], dtype=np.int16)

        # Konvertiere von int16 zu float (-1.0 bis 1.0) für Korrelation
        chirp_float = i2s_samples.astype(np.float64) / 32767.0

        print(f"✅ Chirp aus JSON geladen: {json_path}")
        print(f"📊 Samples: {len(chirp_float)}")
        print(f"📈 Wertebereich: {np.min(i2s_samples)} bis {np.max(i2s_samples)} (int16)")
        print(f"🎵 Dauer: {len(chirp_float)/SAMPLE_RATE*1000:.2f} ms")

        return chirp_float

    except FileNotFoundError:
        print(f"❌ JSON-Datei nicht gefunden: {json_path}")
        print("💡 Fallback: Generiere synthetischen Chirp...")
        return generate_synthetic_chirp()
    except KeyError:
        print(f"❌ 'i2s_data_list' nicht in JSON gefunden: {json_path}")
        print("💡 Fallback: Generiere synthetischen Chirp...")
        return generate_synthetic_chirp()
    except Exception as e:
        print(f"❌ Fehler beim Laden der JSON: {e}")
        print("💡 Fallback: Generiere synthetischen Chirp...")
        return generate_synthetic_chirp()

# === Fallback: Synthetischen Chirp generieren ===
def generate_synthetic_chirp():
    """
    Fallback-Funktion: Generiert synthetischen Chirp falls JSON nicht verfügbar
    """
    n = np.arange(CHIRP_SAMPLES)
    t = n / SAMPLE_RATE
    phase = 2.0 * np.pi * (F_START * t + CHIRP_K * n * t / 2.0)
    chirp = np.sin(phase)

    # Hann-Fenster für Fade-in/out
    ramp = np.ones_like(chirp)
    hann_window = hann(2 * FADE_SAMPLES)
    ramp[:FADE_SAMPLES] = hann_window[:FADE_SAMPLES]
    ramp[-FADE_SAMPLES:] = hann_window[-FADE_SAMPLES:]
    chirp *= ramp

    print("🔧 Synthetischer Chirp generiert (Fallback)")
    return chirp



# === WAV-Datei laden ===
def load_wav(filepath):
    signal, fs = sf.read(filepath)
    if fs != SAMPLE_RATE:
        raise ValueError(f"Sample rate mismatch! Expected {SAMPLE_RATE}, got {fs}")
    if signal.ndim > 1:
        signal = signal[:, 0]  # nur erster Kanal
    return signal

# === Korrelation durchführen ===
def correlate_with_chirp(recorded, chirp):
    correlation = correlate(recorded, chirp, mode='valid')
    return correlation

# === Hauptfunktion ===
def main():
    import os

    # Pfade zu den WAV-Dateien
    wav_paths = [
        "aufnahme.wav",  # Pfad zur aufgenommenen Datei
        "noise.wav"      # Pfad zur Noise-Datei
    ]

    print("🎵 Correlation Analysis Tool")
    print("=" * 40)

    # Check if any WAV files exist
    existing_files = [path for path in wav_paths if os.path.exists(path)]

    if not existing_files:
        print("❌ No WAV files found!")
        print("Expected files:")
        for path in wav_paths:
            print(f"  - {path}")
        print("\nPlease ensure WAV files are in the src/pythontools/ directory.")
        return

    print(f"Found {len(existing_files)} WAV file(s):")
    for path in existing_files:
        print(f"  ✅ {path}")

    print("\n🔧 Loading reference chirp from I2S data...")
    chirp = load_chirp_from_json()

    # Verarbeite nur existierende Dateien
    for wav_path in existing_files:
        try:
            print(f"\n=== Verarbeitung: {wav_path} ===")
            recorded = load_wav(wav_path)

            # Berechne Aufnahme-Dauer
            recording_duration_ms = len(recorded) / SAMPLE_RATE * 1000
            recording_duration_s = recording_duration_ms / 1000

            print(f"📊 Aufnahme-Info:")
            print(f"   📏 Aufnahme-Dauer: {recording_duration_ms:.1f} ms ({recording_duration_s:.3f} s)")
            print(f"   📊 Anzahl Samples: {len(recorded)}")
            print(f"   🎵 Sample Rate: {SAMPLE_RATE} Hz")

            corr = correlate_with_chirp(recorded, chirp)

            # === Visualisieren ===
            plt.figure(figsize=(12, 8))

            # Subplot 1: Original Signal
            plt.subplot(2, 1, 1)
            time_axis = np.arange(len(recorded)) / SAMPLE_RATE * 1000  # in ms
            plt.plot(time_axis, recorded)
            plt.title(f"Original Signal: {wav_path}")
            plt.xlabel("Zeit (ms)")
            plt.ylabel("Amplitude")
            plt.grid()

            # Subplot 2: Korrelation mit Peak-Markierung
            plt.subplot(2, 1, 2)
            corr_time = np.arange(len(corr)) / SAMPLE_RATE * 1000  # in ms
            plt.plot(corr_time, corr, 'b-', linewidth=1, label='Korrelation')

            # Markiere den Korrelations-Peak
            max_idx = np.argmax(corr)
            max_value = corr[max_idx]
            peak_time_ms = max_idx / SAMPLE_RATE * 1000

            # Berechne tatsächlichen Chirp-Start
            chirp_start_sample_plot = max_idx - len(chirp) + 1
            chirp_start_time_plot = chirp_start_sample_plot / SAMPLE_RATE * 1000

            # Peak als roter Punkt markieren
            plt.plot(peak_time_ms, max_value, 'ro', markersize=10, label=f'Korr-Peak: {peak_time_ms:.2f} ms')

            # Chirp-Start als grüner Punkt markieren
            plt.axvline(x=chirp_start_time_plot, color='green', linestyle='-', alpha=0.8, linewidth=3,
                       label=f'Chirp-Start: {chirp_start_time_plot:.2f} ms')

            # Vertikale Linie am Peak
            plt.axvline(x=peak_time_ms, color='red', linestyle='--', alpha=0.7, linewidth=2)

            # Text mit korrigierter Chirp-Start Information
            plt.text(chirp_start_time_plot, max_value * 1.1,
                    f'Chirp-Start: {chirp_start_time_plot:.2f} ms\nPeak: {peak_time_ms:.2f} ms\nKorr: {max_value:.4f}',
                    ha='center', va='bottom', fontsize=11, fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.8))

            # Finde zusätzliche Peaks für Markierung
            from scipy.signal import find_peaks
            threshold = max_value * 0.5
            peaks, _ = find_peaks(corr, height=threshold, distance=len(chirp)//2)

            # Markiere weitere Peaks falls vorhanden
            if len(peaks) > 1:
                for i, peak_idx in enumerate(peaks[1:], 2):
                    peak_t = peak_idx / SAMPLE_RATE * 1000
                    peak_val = corr[peak_idx]
                    plt.plot(peak_t, peak_val, 'go', markersize=8, label=f'Peak #{i}: {peak_t:.2f} ms')
                    plt.axvline(x=peak_t, color='green', linestyle=':', alpha=0.5)

            plt.title(f"Korrelation mit Chirp: {wav_path}")
            plt.xlabel("Zeit (ms)")
            plt.ylabel("Korrelationswert")
            plt.legend()
            plt.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.show()

            # Maxima ausgeben und Chirp-Start berechnen
            max_idx = np.argmax(corr)
            max_value = corr[max_idx]

            # Berechne wann der Chirp in der Aufnahme startet
            # Der Korrelations-Peak zeigt das Ende des Chirps an
            # Also: Chirp-Start = Peak-Position - Chirp-Länge
            chirp_start_sample = max_idx - len(chirp) + 1
            chirp_end_sample = max_idx + 1
            chirp_start_time_ms = chirp_start_sample / SAMPLE_RATE * 1000
            chirp_end_time_ms = chirp_end_sample / SAMPLE_RATE * 1000

            print(f"\n📊 Korrelations-Analyse für {wav_path}:")
            print("=" * 60)
            print(f"🎯 PEAK GEFUNDEN!")
            print(f"   ⏰ PEAK-ZEIT: {max_idx / SAMPLE_RATE * 1000:.2f} ms")
            print(f"   📍 PEAK-SAMPLE: {max_idx}")
            print(f"   📈 KORRELATIONSWERT: {max_value:.6f}")
            print("-" * 60)
            print(f"📏 Aufnahme-Dauer: {recording_duration_ms:.1f} ms ({recording_duration_s:.3f} s)")
            print(f"📍 Chirp-Start in Aufnahme: {chirp_start_time_ms:.2f} ms (Sample {chirp_start_sample})")
            print(f"📍 Chirp-Ende in Aufnahme:  {chirp_end_time_ms:.2f} ms (Sample {chirp_end_sample})")
            print(f"⏱️  Chirp-Dauer: {CHIRP_DURATION * 1000:.1f} ms ({len(chirp)} Samples)")

            # Berechne relative Position des Chirps in der Aufnahme
            chirp_position_percent = (chirp_start_time_ms / recording_duration_ms) * 100
            print(f"📊 Chirp-Position: {chirp_position_percent:.1f}% der Aufnahme")

            # Zusätzliche Analyse: Signal-zu-Rausch-Verhältnis schätzen
            # Berechne RMS des Signals um den Peak herum
            peak_window = 200  # Samples um den Peak
            start_win = max(0, max_idx - peak_window)
            end_win = min(len(corr), max_idx + peak_window)
            peak_region_rms = np.sqrt(np.mean(corr[start_win:end_win]**2))

            # Berechne RMS des restlichen Signals (Rauschen)
            noise_region = np.concatenate([corr[:start_win], corr[end_win:]])
            if len(noise_region) > 0:
                noise_rms = np.sqrt(np.mean(noise_region**2))
                snr_db = 20 * np.log10(peak_region_rms / noise_rms) if noise_rms > 0 else float('inf')
                print(f"   📈 Geschätztes SNR: {snr_db:.1f} dB")

            # Finde alle signifikanten Peaks (für mehrere Chirps)
            from scipy.signal import find_peaks
            # Finde Peaks die mindestens 50% des Hauptpeaks sind
            threshold = max_value * 0.5
            peaks, _ = find_peaks(corr, height=threshold, distance=len(chirp)//2)

            if len(peaks) > 1:
                print(f"   🔍 Weitere Chirps gefunden: {len(peaks)-1}")
                for i, peak_idx in enumerate(peaks[1:], 2):  # Skip first peak (already reported)
                    peak_start_time = peak_idx / SAMPLE_RATE * 1000
                    peak_value = corr[peak_idx]
                    print(f"      Chirp #{i}: {peak_start_time:.2f} ms (Korr: {peak_value:.6f})")
            else:
                print(f"   ✅ Einzelner Chirp erkannt")

        except FileNotFoundError:
            print(f"❌ Datei nicht gefunden: {wav_path}")
        except Exception as e:
            print(f"❌ Fehler bei {wav_path}: {e}")

if __name__ == "__main__":
    main()
