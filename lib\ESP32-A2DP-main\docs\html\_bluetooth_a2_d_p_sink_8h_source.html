<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: src/BluetoothA2DPSink.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">BluetoothA2DPSink.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">// Licensed under the Apache License, Version 2.0 (the &quot;License&quot;);</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment">// you may not use this file except in compliance with the License.</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment">// You may obtain a copy of the License at</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160; </div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">//     http://www.apache.org/licenses/LICENSE-2.0</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment">// Unless required by applicable law or agreed to in writing, software</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment">// distributed under the License is distributed on an &quot;AS IS&quot; BASIS,</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment">// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment">// See the License for the specific language governing permissions and</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment">// limitations under the License.</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment">// Copyright 2020 Phil Schatzmann</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment">// Copyright 2015-2016 Espressif Systems (Shanghai) PTE LTD</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160; </div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="_bluetooth_a2_d_p_common_8h.html">BluetoothA2DPCommon.h</a>&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;BluetoothA2DPOutput.h&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;freertos/ringbuf.h&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160; </div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment">// Comment out next line to deactivate warnings</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#ifndef A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#warning &quot;AudioTools library is not included first or installed&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160; </div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#define APP_SIG_WORK_DISPATCH (0x01)</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160; </div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#ifndef BT_AV_TAG</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#define BT_AV_TAG &quot;BT_AV&quot;</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160; </div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="comment">/* @brief event for handler &quot;bt_av_hdl_stack_up */</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="keyword">enum</span> {</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;  BT_APP_EVT_STACK_UP = 0,</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;};</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160; </div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> <span class="keywordtype">void</span> ccall_i2s_task_handler(<span class="keywordtype">void</span> *arg);</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> <span class="keywordtype">void</span> ccall_audio_data_callback(<span class="keyword">const</span> uint8_t *data, uint32_t len);</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> <span class="keywordtype">void</span> ccall_av_hdl_a2d_evt(uint16_t event, <span class="keywordtype">void</span> *p_param);</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="keyword">extern</span> <span class="stringliteral">&quot;C&quot;</span> <span class="keywordtype">void</span> ccall_av_hdl_avrc_evt(uint16_t event, <span class="keywordtype">void</span> *p_param);</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160; </div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="comment">// defines the mechanism to confirm a pin request</span></div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="keyword">enum</span> PinCodeRequest { Undefined, Confirm, Reply };</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160; </div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="comment">// provide global ref for callbacks</span></div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="keyword">class </span><a class="code" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="keyword">extern</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a> *actual_bluetooth_a2dp_sink;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160; </div>
<div class="line"><a name="l00059"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html">   59</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a> : <span class="keyword">public</span> <a class="code" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a> {</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  <span class="keyword">friend</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#ac18463dacb2427d3687ef8b930cb9a8d">ccall_i2s_task_handler</a>(<span class="keywordtype">void</span> *arg);</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  <span class="keyword">friend</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#ac0e3ebab2d32e289ec52a08bcfa2e978">ccall_audio_data_callback</a>(<span class="keyword">const</span> uint8_t *data, uint32_t len);</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  <span class="keyword">friend</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a9ab56fe60162fa9c07bf0ab8f523bfbf">ccall_av_hdl_a2d_evt</a>(uint16_t event, <span class="keywordtype">void</span> *p_param);</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  <span class="keyword">friend</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a2f773677a4da51d582e6cadd5a2cc514">ccall_av_hdl_avrc_evt</a>(uint16_t event, <span class="keywordtype">void</span> *p_param);</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160; </div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160; <span class="keyword">public</span>:</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_sink.html#a2a383635d7b050833f56ee79867716bd">BluetoothA2DPSink</a>();</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160; </div>
<div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#aadfb815b992649822db74d3e2780d4c8">   74</a></span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_sink.html#aadfb815b992649822db74d3e2780d4c8">BluetoothA2DPSink</a>(<a class="code" href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a> &amp;out) : <a class="code" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>() { </div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_sink.html#a2076eebe22254b2238ba097e8749d24d">set_output</a>(out); </div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  }</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160; </div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;<span class="preprocessor">#if A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a9c47257f9af0d64008fd46e201fb9063">   80</a></span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_sink.html#a9c47257f9af0d64008fd46e201fb9063">BluetoothA2DPSink</a>(audio_tools::AudioOutput &amp;output) : <a class="code" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>() {</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_sink.html#a2076eebe22254b2238ba097e8749d24d">set_output</a>(output);</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  }</div>
<div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a62e0cb5c7a6552581ad43fdd98c4a022">   84</a></span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_sink.html#a62e0cb5c7a6552581ad43fdd98c4a022">BluetoothA2DPSink</a>(audio_tools::AudioStream &amp;output) : <a class="code" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>(){</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_sink.html#a2076eebe22254b2238ba097e8749d24d">set_output</a>(output);</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  }</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160; </div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00091"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a7acf882642fea0df0c36847be134ec6f">   91</a></span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_sink.html#a7acf882642fea0df0c36847be134ec6f">BluetoothA2DPSink</a>(Print &amp;output) : <a class="code" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>() {</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_sink.html#a2076eebe22254b2238ba097e8749d24d">set_output</a>(output);</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;  }</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160; </div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;  <span class="keyword">virtual</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a0f83dea1a97baeb360e4e1221c0aeaa9">~BluetoothA2DPSink</a>();</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160; </div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;<span class="preprocessor">#if A2DP_LEGACY_I2S_SUPPORT</span></div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_pin_config(i2s_pin_config_t pin_config) {</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;    out-&gt;set_pin_config(pin_config);</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;  }</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160; </div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_port(i2s_port_t i2s_num) { out-&gt;set_i2s_port(i2s_num); }</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160; </div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_config(i2s_config_t i2s_config) {</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;    out-&gt;set_i2s_config(i2s_config);</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;  }</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160; </div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_channels(i2s_channel_t channels) {</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_sink.html#a624040cce89a4a2f66495f57db6c1457">set_mono_downmix</a>(channels == I2S_CHANNEL_MONO);</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;  }</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160; </div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_bits_per_sample(<span class="keywordtype">int</span> bps) { out-&gt;set_bits_per_sample(bps); }</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160; </div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &lt; ESP_IDF_VERSION_VAL(5, 1, 1)</span></div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;  <span class="keyword">virtual</span> esp_err_t i2s_mclk_pin_select(<span class="keyword">const</span> uint8_t pin) {</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    <span class="keywordflow">return</span> out-&gt;i2s_mclk_pin_select(pin);</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;  }</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160; </div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160; </div>
<div class="line"><a name="l00132"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a2076eebe22254b2238ba097e8749d24d">  132</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a2076eebe22254b2238ba097e8749d24d">set_output</a>(<a class="code" href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a> &amp;output) { out = &amp;output; }</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160; </div>
<div class="line"><a name="l00135"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a1c88745461503e5b95f26e41f409e428">  135</a></span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a> *<a class="code" href="class_bluetooth_a2_d_p_sink.html#a1c88745461503e5b95f26e41f409e428">get_output</a>() { <span class="keywordflow">return</span> out; }</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160; </div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;<span class="preprocessor">#ifdef ARDUINO</span></div>
<div class="line"><a name="l00139"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#af87666dc2c00c5917db709e2edaf1dab">  139</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#af87666dc2c00c5917db709e2edaf1dab">set_output</a>(Print &amp;output) {</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;    <span class="keyword">static</span> <a class="code" href="class_bluetooth_a2_d_p_output_print.html">BluetoothA2DPOutputPrint</a> s_out;</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    out = &amp;s_out;</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;    out-&gt;<a class="code" href="class_bluetooth_a2_d_p_output.html#a67fc2cf760ae2a48ac6bfa6ed235f8b8">set_output</a>(output);</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;  }</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160; </div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;<span class="preprocessor">#if A2DP_I2S_AUDIOTOOLS</span></div>
<div class="line"><a name="l00148"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a5bdae239eb76ad435a7999362a8019fc">  148</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a5bdae239eb76ad435a7999362a8019fc">set_output</a>(audio_tools::AudioOutput &amp;output) { out-&gt;<a class="code" href="class_bluetooth_a2_d_p_output.html#a67fc2cf760ae2a48ac6bfa6ed235f8b8">set_output</a>(output); }</div>
<div class="line"><a name="l00150"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a664eafe67a1f66b415159b84c3fe851f">  150</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a664eafe67a1f66b415159b84c3fe851f">set_output</a>(audio_tools::AudioStream &amp;output) { out-&gt;<a class="code" href="class_bluetooth_a2_d_p_output.html#a67fc2cf760ae2a48ac6bfa6ed235f8b8">set_output</a>(output); }</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160; </div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#af85e99324638d1c814e221ac4ba815dd">start</a>(<span class="keyword">const</span> <span class="keywordtype">char</span> *name, <span class="keywordtype">bool</span> auto_reconect);</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160; </div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#af85e99324638d1c814e221ac4ba815dd">start</a>(<span class="keyword">const</span> <span class="keywordtype">char</span> *name);</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160; </div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a5a91e49987a2e39c09fc6c2a64feaed6">end</a>(<span class="keywordtype">bool</span> release_memory = <span class="keyword">false</span>);</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160; </div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;  <span class="keyword">virtual</span> esp_a2d_mct_t <a class="code" href="class_bluetooth_a2_d_p_sink.html#a77600cb1e36b7814eb9b4126cdec62d4">get_audio_type</a>();</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160; </div>
<div class="line"><a name="l00167"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#aedd80fe4cf8bd887224efa2716ad9d69">  167</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#aedd80fe4cf8bd887224efa2716ad9d69">set_avrc_connection_state_callback</a>(<span class="keywordtype">void</span> (*callback)(<span class="keywordtype">bool</span>)) {</div>
<div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;    this-&gt;avrc_connection_state_callback = callback;</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;  }</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160; </div>
<div class="line"><a name="l00172"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#aac9074521c80d7574a855f30b8301d13">  172</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#aac9074521c80d7574a855f30b8301d13">set_avrc_metadata_callback</a>(<span class="keywordtype">void</span> (*callback)(uint8_t,</div>
<div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;                                                           <span class="keyword">const</span> uint8_t *)) {</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;    this-&gt;avrc_metadata_callback = callback;</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;  }</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160; </div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &gt;= ESP_IDF_VERSION_VAL(4, 0, 0)</span></div>
<div class="line"><a name="l00180"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a5e4806bad4ed634493643c5925ecf67f">  180</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a5e4806bad4ed634493643c5925ecf67f">set_avrc_rn_playstatus_callback</a>(</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;      <span class="keywordtype">void</span> (*callback)(<a class="code" href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a> playback)) {</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;    this-&gt;avrc_rn_playstatus_callback = callback;</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;  }</div>
<div class="line"><a name="l00186"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a5dd8ed8e61d6bb6d0c1e05d5c17e45e7">  186</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a5dd8ed8e61d6bb6d0c1e05d5c17e45e7">set_avrc_rn_play_pos_callback</a>(</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;      <span class="keywordtype">void</span> (*callback)(uint32_t play_pos), uint32_t notif_interval = 10) {</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;    this-&gt;avrc_rn_play_pos_callback = callback;</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;    this-&gt;notif_interval_s = std::max(notif_interval, (uint32_t)1);</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;  }</div>
<div class="line"><a name="l00194"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#ae56f6a99a5c38e0bdd402a79faba6dd5">  194</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#ae56f6a99a5c38e0bdd402a79faba6dd5">set_avrc_rn_track_change_callback</a>(</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;      <span class="keywordtype">void</span> (*callback)(uint8_t *<span class="keywordtype">id</span>)) {</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;    this-&gt;avrc_rn_track_change_callback = callback;</div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;  }</div>
<div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160; </div>
<div class="line"><a name="l00201"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a89d2694f880a2db22344b97b466c9a9d">  201</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a89d2694f880a2db22344b97b466c9a9d">set_sample_rate_callback</a>(<span class="keywordtype">void</span> (*callback)(uint16_t rate)) {</div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;    this-&gt;sample_rate_callback = callback;</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;  }</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160; </div>
<div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a4f94426ff4899c437d31623e013cf7a5">set_stream_reader</a>(<span class="keywordtype">void</span> (*callBack)(<span class="keyword">const</span> uint8_t *, uint32_t),</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;                                 <span class="keywordtype">bool</span> i2s_output = <span class="keyword">true</span>);</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160; </div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a035b0e2534970acd2c35b65842374e51">set_raw_stream_reader</a>(<span class="keywordtype">void</span> (*callBack)(<span class="keyword">const</span> uint8_t *,</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;                                                      uint32_t));</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160; </div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#af65219e635fadbbc90f4663b33abd3e0">set_on_data_received</a>(<span class="keywordtype">void</span> (*callBack)());</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160; </div>
<div class="line"><a name="l00219"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#ad25155f02bad11da6c130aae00c8ab9c">  219</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#ad25155f02bad11da6c130aae00c8ab9c">set_address_validator</a>(</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;      <span class="keywordtype">bool</span> (*callBack)(<a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> remote_bda)) {</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;    address_validator = callBack;</div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;  }</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160; </div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a458dc625cbceb5e534b07094136f6533">is_avrc_connected</a>();</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160; </div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a507e30ececfdc4382af60a0319cdaf1b">set_volume</a>(uint8_t volume);</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160; </div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">int</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#aca1119f20d2321fb950ae859000cce7b">get_volume</a>();</div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160; </div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#ac245103d3d5c47b0414c2de21c0d52a7">set_on_volumechange</a>(<span class="keywordtype">void</span> (*callBack)(<span class="keywordtype">int</span>));</div>
<div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160; </div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#acb34360759c6a94028f74df35009b033">set_avrc_rn_volumechange</a>(<span class="keywordtype">void</span> (*callBack)(<span class="keywordtype">int</span>));</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160; </div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a9b16f9fec1e74eb3bb30f6cf572d21e9">set_avrc_rn_volumechange_completed</a>(<span class="keywordtype">void</span> (*callBack)(<span class="keywordtype">int</span>));</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160; </div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#aafd2afad1960db8ab73d7c6977aeb686">play</a>();</div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#aa6967e9329939596c62f16e8686cac13">pause</a>();</div>
<div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a37dcbcd418b84310ccedf3330e44834f">stop</a>();</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a296fb7aaf8d8e78991d9d505353de94f">next</a>();</div>
<div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a341024c18eabdb06c734c2242d5ba505">previous</a>();</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a42e01689353026b1ef9883fd5d32f00c">fast_forward</a>();</div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a9ee01e6d11ee3c6c546a510029a23a12">rewind</a>();</div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a42866994c045e27584e0438eb2d4cc79">volume_up</a>();</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#ae823f16ed3ee17cf9c6d1731b9d19a34">volume_down</a>();</div>
<div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160; </div>
<div class="line"><a name="l00264"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a624040cce89a4a2f66495f57db6c1457">  264</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a624040cce89a4a2f66495f57db6c1457">set_mono_downmix</a>(<span class="keywordtype">bool</span> enabled) {</div>
<div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_common.html#a6fec0cfd3d0d9017b7ffcf82630ab89a">volume_control</a>()-&gt;set_mono_downmix(enabled);</div>
<div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;  }</div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160; </div>
<div class="line"><a name="l00269"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a90cee550d061836992616161d4215355">  269</a></span>&#160;  <span class="keyword">virtual</span> uint16_t <a class="code" href="class_bluetooth_a2_d_p_sink.html#a90cee550d061836992616161d4215355">sample_rate</a>() { <span class="keywordflow">return</span> m_sample_rate; }</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160; </div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a22d52952a8ac8c78a483a53c2006a387">activate_pin_code</a>(<span class="keywordtype">bool</span> active);</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160; </div>
<div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a5d4707195d0d6e79b65bef4ed48a57c2">confirm_pin_code</a>();</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160; </div>
<div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a5d4707195d0d6e79b65bef4ed48a57c2">confirm_pin_code</a>(<span class="keywordtype">int</span> code);</div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160; </div>
<div class="line"><a name="l00281"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a3719138f63afaeed06b63cc48ea79335">  281</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">int</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a3719138f63afaeed06b63cc48ea79335">pin_code</a>() { <span class="keywordflow">return</span> pin_code_int; }</div>
<div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160; </div>
<div class="line"><a name="l00287"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a8281af353148544a0612f8f7c4d511b1">  287</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a8281af353148544a0612f8f7c4d511b1">set_avrc_metadata_attribute_mask</a>(<span class="keywordtype">int</span> flags) {</div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;    avrc_metadata_flags = flags;</div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;  }</div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160; </div>
<div class="line"><a name="l00292"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a14d982730e2b9ea772fe9ede1563ed22">  292</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a14d982730e2b9ea772fe9ede1563ed22">set_swap_lr_channels</a>(<span class="keywordtype">bool</span> swap) { swap_left_right = swap; }</div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160; </div>
<div class="line"><a name="l00296"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#af7d10cfe632a3c2f95409f6a23daecdd">  296</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#af7d10cfe632a3c2f95409f6a23daecdd">set_auto_reconnect</a>(<span class="keywordtype">bool</span> <a class="code" href="class_bluetooth_a2_d_p_common.html#ac795a023f85438355a1b00644f2b040f">reconnect</a>,</div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;                                  <span class="keywordtype">int</span> count = AUTOCONNECT_TRY_NUM) {</div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;    reconnect_status = <a class="code" href="class_bluetooth_a2_d_p_common.html#ac795a023f85438355a1b00644f2b040f">reconnect</a> ? AutoReconnect : NoReconnect;</div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160;    try_reconnect_max_count = count;</div>
<div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;  }</div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160; </div>
<div class="line"><a name="l00303"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#ac08fff859e0ccfbb12cbb6b119dba438">  303</a></span>&#160;  <span class="keyword">virtual</span> <a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> *<a class="code" href="class_bluetooth_a2_d_p_sink.html#ac08fff859e0ccfbb12cbb6b119dba438">get_current_peer_address</a>() { <span class="keywordflow">return</span> &amp;peer_bd_addr; }</div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160; </div>
<div class="line"><a name="l00306"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a452b509a46930ab1ed58188a4181c67b">  306</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a452b509a46930ab1ed58188a4181c67b">set_rssi_active</a>(<span class="keywordtype">bool</span> active) { rssi_active = active; }</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160; </div>
<div class="line"><a name="l00309"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a3b43ee67031c300d07638f9c969fa4ef">  309</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a3b43ee67031c300d07638f9c969fa4ef">update_rssi</a>() {</div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;    <span class="keywordflow">if</span> (!rssi_active) <span class="keywordflow">return</span> <span class="keyword">false</span>;</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;    <span class="keywordflow">return</span> esp_bt_gap_read_rssi_delta(*<a class="code" href="class_bluetooth_a2_d_p_sink.html#ac08fff859e0ccfbb12cbb6b119dba438">get_current_peer_address</a>()) == ESP_OK;</div>
<div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;  }</div>
<div class="line"><a name="l00313"></a><span class="lineno">  313</span>&#160; </div>
<div class="line"><a name="l00315"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a5a770be98d977a8df916d8cc044b310c">  315</a></span>&#160;  esp_bt_gap_cb_param_t::read_rssi_delta_param <a class="code" href="class_bluetooth_a2_d_p_sink.html#a5a770be98d977a8df916d8cc044b310c">get_last_rssi</a>() {</div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;    <span class="keywordflow">return</span> last_rssi_delta;</div>
<div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;  }</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160; </div>
<div class="line"><a name="l00320"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a97c80237061c6e80d7c6e1e5e45773c8">  320</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a97c80237061c6e80d7c6e1e5e45773c8">set_rssi_callback</a>(</div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;      <span class="keywordtype">void</span> (*callback)(esp_bt_gap_cb_param_t::read_rssi_delta_param &amp;rssi)) {</div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;    rssi_callbak = callback;</div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;  }</div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160; </div>
<div class="line"><a name="l00327"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a96b4fabd27e7952fc3ea5edca3b95cbb">  327</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a96b4fabd27e7952fc3ea5edca3b95cbb">set_reconnect_delay</a>(<span class="keywordtype">int</span> delay) { reconnect_delay = delay; }</div>
<div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160; </div>
<div class="line"><a name="l00330"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#abe6004e2b95d120d64c10cf947fefb55">  330</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#abe6004e2b95d120d64c10cf947fefb55">set_spp_active</a>(<span class="keywordtype">bool</span> flag) { spp_active = flag; }</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160; </div>
<div class="line"><a name="l00333"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a68d88c33b8ec218fe3bd45f61c39f754">  333</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a68d88c33b8ec218fe3bd45f61c39f754">set_output_active</a>(<span class="keywordtype">bool</span> flag) { is_i2s_active = flag; }</div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160; </div>
<div class="line"><a name="l00336"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a6eb9f31e643607224fa784ff20654924">  336</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a6eb9f31e643607224fa784ff20654924">is_output_active</a>() { <span class="keywordflow">return</span> is_i2s_active; }</div>
<div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160; </div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &gt;= ESP_IDF_VERSION_VAL(4, 0, 0)</span></div>
<div class="line"><a name="l00341"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#af5d6399876738c8fa0766ea247476b3f">  341</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#af5d6399876738c8fa0766ea247476b3f">is_avrc_peer_rn_cap</a>(<a class="code" href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a> cmd) {</div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;    <span class="keywordflow">return</span> esp_avrc_rn_evt_bit_mask_operation(ESP_AVRC_BIT_MASK_OP_TEST,</div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;                                              &amp;s_avrc_peer_rn_cap, cmd);</div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;  }</div>
<div class="line"><a name="l00346"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#a8281ab4339dd58c49d1d651dd74f5711">  346</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a8281ab4339dd58c49d1d651dd74f5711">is_avrc_peer_rn_cap_available</a>() { <span class="keywordflow">return</span> s_avrc_peer_rn_cap.bits != 0; }</div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160; </div>
<div class="line"><a name="l00349"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#ac9adc3ca64e68fb3d6e816698a725dd5">  349</a></span>&#160;  <span class="keyword">virtual</span> <span class="keyword">const</span> <span class="keywordtype">char</span> *<a class="code" href="class_bluetooth_a2_d_p_sink.html#ac9adc3ca64e68fb3d6e816698a725dd5">get_peer_name</a>() { <span class="keywordflow">return</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a2d277d15f94823eea70f80a327344939">get_connected_source_name</a>(); }</div>
<div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160; </div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160; </div>
<div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160; <span class="keyword">protected</span>:</div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_output_default.html">BluetoothA2DPOutputDefault</a> out_default;</div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;  <a class="code" href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a> *out = &amp;out_default;</div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160; </div>
<div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;  <span class="keyword">volatile</span> <span class="keywordtype">bool</span> is_i2s_active = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;  <span class="comment">// activate output via BluetoothA2DPOutput</span></div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;  <span class="keywordtype">bool</span> is_output = <span class="keyword">true</span>;</div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;  uint16_t m_sample_rate = 44100;  <span class="comment">// set default rate</span></div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;  uint32_t m_pkt_cnt = 0;</div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;  <span class="comment">// esp_a2d_audio_state_t m_audio_state = ESP_A2D_AUDIO_STATE_STOPPED;</span></div>
<div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;  esp_a2d_mct_t audio_type;</div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;  <span class="keywordtype">char</span> pin_code_str[20] = {0};</div>
<div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;  <span class="keywordtype">int</span> connection_rety_count = 0;</div>
<div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;  <span class="keywordtype">bool</span> spp_active = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;  esp_spp_mode_t esp_spp_mode = ESP_SPP_MODE_CB;</div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;  _lock_t s_volume_lock;</div>
<div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;  uint8_t s_volume = 0;</div>
<div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;  <span class="keywordtype">bool</span> s_volume_notify;</div>
<div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;  <span class="keywordtype">int</span> pin_code_int = 0;</div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;  PinCodeRequest pin_code_request = Undefined;</div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;  <span class="keywordtype">bool</span> is_pin_code_active = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;  <span class="keywordtype">bool</span> avrc_connection_state = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;  <span class="keywordtype">int</span> avrc_metadata_flags =</div>
<div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;      ESP_AVRC_MD_ATTR_TITLE | ESP_AVRC_MD_ATTR_ARTIST |</div>
<div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;      ESP_AVRC_MD_ATTR_ALBUM | ESP_AVRC_MD_ATTR_TRACK_NUM |</div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;      ESP_AVRC_MD_ATTR_NUM_TRACKS | ESP_AVRC_MD_ATTR_GENRE;</div>
<div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;  void (*bt_volumechange)(int) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;  void (*bt_dis_connected)() = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;  void (*bt_connected)() = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;  void (*data_received)() = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;  void (*stream_reader)(<span class="keyword">const</span> uint8_t *, uint32_t) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;  void (*raw_stream_reader)(<span class="keyword">const</span> uint8_t *, uint32_t) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;  void (*avrc_connection_state_callback)(<span class="keywordtype">bool</span> connected) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;  void (*avrc_metadata_callback)(uint8_t, <span class="keyword">const</span> uint8_t *) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &gt;= ESP_IDF_VERSION_VAL(4, 0, 0)</span></div>
<div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;  void (*avrc_rn_playstatus_callback)(<a class="code" href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a>) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;  void (*avrc_rn_track_change_callback)(uint8_t *) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;  void (*avrc_rn_play_pos_callback)(uint32_t) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;  uint32_t notif_interval_s = 10;</div>
<div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;  void (*avrc_rn_volchg_complete_callback)(int) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;  bool (*address_validator)(<a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> remote_bda) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;  void (*sample_rate_callback)(uint16_t rate) = <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;  <span class="keywordtype">bool</span> swap_left_right = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;  <span class="keywordtype">int</span> try_reconnect_max_count = AUTOCONNECT_TRY_NUM;</div>
<div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160; </div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;  <span class="comment">// RSSI support</span></div>
<div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160;  esp_bt_gap_cb_param_t::read_rssi_delta_param last_rssi_delta;</div>
<div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;  <span class="keywordtype">bool</span> rssi_active = <span class="keyword">false</span>;</div>
<div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;  void (*rssi_callbak)(esp_bt_gap_cb_param_t::read_rssi_delta_param &amp;rssi) =</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;      <span class="keyword">nullptr</span>;</div>
<div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;  <span class="keywordtype">int</span> reconnect_delay = 1000;</div>
<div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160; </div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &gt;= ESP_IDF_VERSION_VAL(4, 0, 0)</span></div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;  esp_avrc_rn_evt_cap_mask_t s_avrc_peer_rn_cap = {0};</div>
<div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;  <span class="keywordtype">char</span> remote_name[ESP_BT_GAP_MAX_BDNAME_LEN + 1];</div>
<div class="line"><a name="l00409"></a><span class="lineno">  409</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;  <span class="keywordtype">void</span> app_gap_callback(esp_bt_gap_cb_event_t event,</div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;                        esp_bt_gap_cb_param_t *param) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a7f8680e010057c3fea392c75c85c9f23">app_rc_ct_callback</a>(esp_avrc_ct_cb_event_t event,</div>
<div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;                          esp_avrc_ct_cb_param_t *param) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00414"></a><span class="lineno">  414</span>&#160;  <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a9892ecf2f81b99d861f2767f7b705188">app_a2d_callback</a>(esp_a2d_cb_event_t event,</div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;                        esp_a2d_cb_param_t *param) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;  <span class="keywordtype">void</span> av_hdl_stack_evt(uint16_t event, <span class="keywordtype">void</span> *p_param) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160; </div>
<div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">int</span> init_bluetooth();</div>
<div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> app_work_dispatch(<a class="code" href="_bluetooth_a2_d_p_common_8h.html#a9bee258e477be3c0e70d6029ed86a019">app_callback_t</a> p_cback, uint16_t event,</div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;                                 <span class="keywordtype">void</span> *p_params, <span class="keywordtype">int</span> param_len);</div>
<div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> app_alloc_meta_buffer(esp_avrc_ct_cb_param_t *param);</div>
<div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> av_new_track();</div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> av_playback_changed();</div>
<div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> av_play_pos_changed();</div>
<div class="line"><a name="l00425"></a><span class="lineno">  425</span>&#160;  <span class="comment">// execute AVRC command</span></div>
<div class="line"><a name="l00426"></a><span class="lineno">  426</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> execute_avrc_command(<span class="keywordtype">int</span> cmd);</div>
<div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160; </div>
<div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;  <span class="keyword">virtual</span> <span class="keyword">const</span> <span class="keywordtype">char</span> *last_bda_nvs_name() { <span class="keywordflow">return</span> <span class="stringliteral">&quot;last_bda&quot;</span>; }</div>
<div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160; </div>
<div class="line"><a name="l00430"></a><span class="lineno">  430</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> is_reconnect(esp_a2d_disc_rsn_t type) {</div>
<div class="line"><a name="l00431"></a><span class="lineno">  431</span>&#160;    <span class="keywordtype">bool</span> result = is_autoreconnect_allowed &amp;&amp;</div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;                  (reconnect_status == AutoReconnect ||</div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;                   reconnect_status == IsReconnecting) &amp;&amp;</div>
<div class="line"><a name="l00434"></a><span class="lineno">  434</span>&#160;                  has_last_connection();</div>
<div class="line"><a name="l00435"></a><span class="lineno">  435</span>&#160;    ESP_LOGI(BT_AV_TAG, <span class="stringliteral">&quot;is_reconnect: %s&quot;</span>, result ? <span class="stringliteral">&quot;true&quot;</span> : <span class="stringliteral">&quot;false&quot;</span>);</div>
<div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;    <span class="keywordflow">return</span> result;</div>
<div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;  }</div>
<div class="line"><a name="l00438"></a><span class="lineno">  438</span>&#160; </div>
<div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160;  <span class="comment">// Callback for music stream</span></div>
<div class="line"><a name="l00443"></a><span class="lineno">  443</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#a2cf823459de7a757d94a4ced2f375a0c">audio_data_callback</a>(<span class="keyword">const</span> uint8_t *data, uint32_t len);</div>
<div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;  <span class="comment">// a2dp event handler</span></div>
<div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> av_hdl_a2d_evt(uint16_t event, <span class="keywordtype">void</span> *p_param);</div>
<div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;  <span class="comment">// avrc event handler</span></div>
<div class="line"><a name="l00447"></a><span class="lineno">  447</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> av_hdl_avrc_evt(uint16_t event, <span class="keywordtype">void</span> *p_param);</div>
<div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160; </div>
<div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;  <span class="comment">// split up long handlers</span></div>
<div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> handle_connection_state(uint16_t event, <span class="keywordtype">void</span> *p_param);</div>
<div class="line"><a name="l00451"></a><span class="lineno">  451</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> handle_audio_state(uint16_t event, <span class="keywordtype">void</span> *p_param);</div>
<div class="line"><a name="l00452"></a><span class="lineno">  452</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> handle_audio_cfg(uint16_t event, <span class="keywordtype">void</span> *p_param);</div>
<div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> handle_avrc_connection_state(<span class="keywordtype">bool</span> connected);</div>
<div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160; </div>
<div class="line"><a name="l00455"></a><span class="lineno">  455</span>&#160;<span class="preprocessor">#if ESP_IDF_VERSION &gt;= ESP_IDF_VERSION_VAL(4, 0, 0)</span></div>
<div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;  <span class="keyword">virtual</span> <span class="keyword">const</span> <span class="keywordtype">char</span> *<a class="code" href="class_bluetooth_a2_d_p_sink.html#a2d277d15f94823eea70f80a327344939">get_connected_source_name</a>();</div>
<div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> volume_set_by_local_host(uint8_t volume);</div>
<div class="line"><a name="l00459"></a><span class="lineno">  459</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> volume_set_by_controller(uint8_t volume);</div>
<div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> av_notify_evt_handler(uint8_t event_id,</div>
<div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;                                     esp_avrc_rn_param_t *event_parameter);</div>
<div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> app_rc_tg_callback(esp_avrc_tg_cb_event_t event,</div>
<div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160;                                  esp_avrc_tg_cb_param_t *param) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00464"></a><span class="lineno">  464</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> av_hdl_avrc_tg_evt(uint16_t event, <span class="keywordtype">void</span> *p_param) <span class="keyword">override</span>;</div>
<div class="line"><a name="l00465"></a><span class="lineno">  465</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> av_notify_evt_handler(uint8_t event_id,</div>
<div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;                                     uint32_t event_parameter);</div>
<div class="line"><a name="l00468"></a><span class="lineno">  468</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00469"></a><span class="lineno">  469</span>&#160; </div>
<div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> init_i2s();</div>
<div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160; </div>
<div class="line"><a name="l00473"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#aa211fefd101a639938a20dc3478b48ae">  473</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">size_t</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#aa211fefd101a639938a20dc3478b48ae">write_audio</a>(<span class="keyword">const</span> uint8_t *data, <span class="keywordtype">size_t</span> size) {</div>
<div class="line"><a name="l00474"></a><span class="lineno">  474</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#ab59e6c716d9b5aaed69272e7d2a3d12a">i2s_write_data</a>(data, size);</div>
<div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;  }</div>
<div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160; </div>
<div class="line"><a name="l00478"></a><span class="lineno">  478</span>&#160;  <span class="keywordtype">size_t</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#ab59e6c716d9b5aaed69272e7d2a3d12a">i2s_write_data</a>(<span class="keyword">const</span> uint8_t *data, <span class="keywordtype">size_t</span> item_size);</div>
<div class="line"><a name="l00479"></a><span class="lineno">  479</span>&#160; </div>
<div class="line"><a name="l00481"></a><span class="lineno"><a class="line" href="class_bluetooth_a2_d_p_sink.html#afdc2f08c0547393704fd6fb56bde204f">  481</a></span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="class_bluetooth_a2_d_p_sink.html#afdc2f08c0547393704fd6fb56bde204f">i2s_task_handler</a>(<span class="keywordtype">void</span> *arg) {}</div>
<div class="line"><a name="l00482"></a><span class="lineno">  482</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> bt_i2s_task_start_up(<span class="keywordtype">void</span>) {}</div>
<div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> bt_i2s_task_shut_down(<span class="keywordtype">void</span>) {}</div>
<div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160; </div>
<div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;  esp_err_t esp_a2d_connect(<a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> peer)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160;    <span class="keywordflow">return</span> esp_a2d_sink_connect(peer);</div>
<div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;  }</div>
<div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;  esp_err_t esp_a2d_disconnect(<a class="code" href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a> remote_bda)<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00489"></a><span class="lineno">  489</span>&#160;    <span class="keywordflow">return</span> esp_a2d_sink_disconnect(remote_bda);</div>
<div class="line"><a name="l00490"></a><span class="lineno">  490</span>&#160;  }</div>
<div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160; </div>
<div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;  <span class="keywordtype">void</span> set_scan_mode_connectable_default()<span class="keyword"> override </span>{</div>
<div class="line"><a name="l00493"></a><span class="lineno">  493</span>&#160;    <a class="code" href="class_bluetooth_a2_d_p_common.html#af1e2f14ddbe9266b61f5e721095c3685">set_scan_mode_connectable</a>(<span class="keyword">true</span>);</div>
<div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160;  }</div>
<div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160; </div>
<div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">void</span> set_i2s_active(<span class="keywordtype">bool</span> active);</div>
<div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160; </div>
<div class="line"><a name="l00498"></a><span class="lineno">  498</span>&#160;  <span class="keyword">virtual</span> <span class="keywordtype">bool</span> isSource() { <span class="keywordflow">return</span> <span class="keyword">false</span>; }</div>
<div class="line"><a name="l00499"></a><span class="lineno">  499</span>&#160;};</div>
<div class="ttc" id="a_bluetooth_a2_d_p_common_8h_html"><div class="ttname"><a href="_bluetooth_a2_d_p_common_8h.html">BluetoothA2DPCommon.h</a></div></div>
<div class="ttc" id="a_bluetooth_a2_d_p_common_8h_html_a9bee258e477be3c0e70d6029ed86a019"><div class="ttname"><a href="_bluetooth_a2_d_p_common_8h.html#a9bee258e477be3c0e70d6029ed86a019">app_callback_t</a></div><div class="ttdeci">void(* app_callback_t)(uint16_t event, void *param)</div><div class="ttdoc">handler for the dispatched work</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:120</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></div><div class="ttdoc">Common Bluetooth A2DP functions.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:169</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_a6fec0cfd3d0d9017b7ffcf82630ab89a"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#a6fec0cfd3d0d9017b7ffcf82630ab89a">BluetoothA2DPCommon::volume_control</a></div><div class="ttdeci">virtual A2DPVolumeControl * volume_control()</div><div class="ttdoc">provides access to the VolumeControl object</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.h:401</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_ac795a023f85438355a1b00644f2b040f"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#ac795a023f85438355a1b00644f2b040f">BluetoothA2DPCommon::reconnect</a></div><div class="ttdeci">virtual bool reconnect()</div><div class="ttdoc">Reconnects to the last device.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.cpp:84</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_common_html_af1e2f14ddbe9266b61f5e721095c3685"><div class="ttname"><a href="class_bluetooth_a2_d_p_common.html#af1e2f14ddbe9266b61f5e721095c3685">BluetoothA2DPCommon::set_scan_mode_connectable</a></div><div class="ttdeci">virtual void set_scan_mode_connectable(bool connectable)</div><div class="ttdoc">Defines if the bluetooth is connectable.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPCommon.cpp:537</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_default_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_output_default.html">BluetoothA2DPOutputDefault</a></div><div class="ttdoc">Default Output Class providing both the Legacy I2S and the AudioTools I2S functionality.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:203</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_output.html">BluetoothA2DPOutput</a></div><div class="ttdoc">Abstract Output Class.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:22</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_html_a67fc2cf760ae2a48ac6bfa6ed235f8b8"><div class="ttname"><a href="class_bluetooth_a2_d_p_output.html#a67fc2cf760ae2a48ac6bfa6ed235f8b8">BluetoothA2DPOutput::set_output</a></div><div class="ttdeci">virtual void set_output(audio_tools::AudioOutput &amp;output)</div><div class="ttdoc">Not implemented.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:32</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_output_print_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_output_print.html">BluetoothA2DPOutputPrint</a></div><div class="ttdoc">Output Class using Print API:</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPOutput.h:126</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></div><div class="ttdoc">A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example https://github....</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:59</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a035b0e2534970acd2c35b65842374e51"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a035b0e2534970acd2c35b65842374e51">BluetoothA2DPSink::set_raw_stream_reader</a></div><div class="ttdeci">virtual void set_raw_stream_reader(void(*callBack)(const uint8_t *, uint32_t))</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:85</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a0f83dea1a97baeb360e4e1221c0aeaa9"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a0f83dea1a97baeb360e4e1221c0aeaa9">BluetoothA2DPSink::~BluetoothA2DPSink</a></div><div class="ttdeci">virtual ~BluetoothA2DPSink()</div><div class="ttdoc">Destructor - stops the playback and releases all resources.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:60</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a14d982730e2b9ea772fe9ede1563ed22"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a14d982730e2b9ea772fe9ede1563ed22">BluetoothA2DPSink::set_swap_lr_channels</a></div><div class="ttdeci">virtual void set_swap_lr_channels(bool swap)</div><div class="ttdoc">swaps the left and right channel</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:292</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a1c88745461503e5b95f26e41f409e428"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a1c88745461503e5b95f26e41f409e428">BluetoothA2DPSink::get_output</a></div><div class="ttdeci">BluetoothA2DPOutput * get_output()</div><div class="ttdoc">Provides access to the output class.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:135</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a2076eebe22254b2238ba097e8749d24d"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a2076eebe22254b2238ba097e8749d24d">BluetoothA2DPSink::set_output</a></div><div class="ttdeci">void set_output(BluetoothA2DPOutput &amp;output)</div><div class="ttdoc">Defines the output class: by default we use BluetoothA2DPOutputDefault.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:132</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a22d52952a8ac8c78a483a53c2006a387"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a22d52952a8ac8c78a483a53c2006a387">BluetoothA2DPSink::activate_pin_code</a></div><div class="ttdeci">virtual void activate_pin_code(bool active)</div><div class="ttdoc">We need to confirm a new seesion by calling confirm_pin_code()</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:1064</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a296fb7aaf8d8e78991d9d505353de94f"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a296fb7aaf8d8e78991d9d505353de94f">BluetoothA2DPSink::next</a></div><div class="ttdeci">virtual void next()</div><div class="ttdoc">AVRC next.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:1026</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a2a383635d7b050833f56ee79867716bd"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a2a383635d7b050833f56ee79867716bd">BluetoothA2DPSink::BluetoothA2DPSink</a></div><div class="ttdeci">BluetoothA2DPSink()</div><div class="ttdoc">Default Constructor: output via callback or Legacy I2S.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:50</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a2cf823459de7a757d94a4ced2f375a0c"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a2cf823459de7a757d94a4ced2f375a0c">BluetoothA2DPSink::audio_data_callback</a></div><div class="ttdeci">virtual void audio_data_callback(const uint8_t *data, uint32_t len)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:955</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a2d277d15f94823eea70f80a327344939"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a2d277d15f94823eea70f80a327344939">BluetoothA2DPSink::get_connected_source_name</a></div><div class="ttdeci">virtual const char * get_connected_source_name()</div><div class="ttdoc">Get the name of the connected source device (obsolete): use get_peer_name()</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:197</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a2f773677a4da51d582e6cadd5a2cc514"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a2f773677a4da51d582e6cadd5a2cc514">BluetoothA2DPSink::ccall_av_hdl_avrc_evt</a></div><div class="ttdeci">friend void ccall_av_hdl_avrc_evt(uint16_t event, void *p_param)</div><div class="ttdoc">avrc event handler</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:33</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a341024c18eabdb06c734c2242d5ba505"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a341024c18eabdb06c734c2242d5ba505">BluetoothA2DPSink::previous</a></div><div class="ttdeci">virtual void previous()</div><div class="ttdoc">AVRC previous.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:1029</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a3719138f63afaeed06b63cc48ea79335"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a3719138f63afaeed06b63cc48ea79335">BluetoothA2DPSink::pin_code</a></div><div class="ttdeci">virtual int pin_code()</div><div class="ttdoc">provides the requested pin code (0 = undefined)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:281</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a37dcbcd418b84310ccedf3330e44834f"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a37dcbcd418b84310ccedf3330e44834f">BluetoothA2DPSink::stop</a></div><div class="ttdeci">virtual void stop()</div><div class="ttdoc">AVRC stop.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:1024</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a3b43ee67031c300d07638f9c969fa4ef"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a3b43ee67031c300d07638f9c969fa4ef">BluetoothA2DPSink::update_rssi</a></div><div class="ttdeci">bool update_rssi()</div><div class="ttdoc">Requests an update of the rssi delta value.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:309</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a42866994c045e27584e0438eb2d4cc79"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a42866994c045e27584e0438eb2d4cc79">BluetoothA2DPSink::volume_up</a></div><div class="ttdeci">virtual void volume_up()</div><div class="ttdoc">AVRC increase the volume.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:1039</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a42e01689353026b1ef9883fd5d32f00c"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a42e01689353026b1ef9883fd5d32f00c">BluetoothA2DPSink::fast_forward</a></div><div class="ttdeci">virtual void fast_forward()</div><div class="ttdoc">AVRC fast_forward.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:1032</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a452b509a46930ab1ed58188a4181c67b"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a452b509a46930ab1ed58188a4181c67b">BluetoothA2DPSink::set_rssi_active</a></div><div class="ttdeci">void set_rssi_active(bool active)</div><div class="ttdoc">Activates the rssi reporting.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:306</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a458dc625cbceb5e534b07094136f6533"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a458dc625cbceb5e534b07094136f6533">BluetoothA2DPSink::is_avrc_connected</a></div><div class="ttdeci">virtual bool is_avrc_connected()</div><div class="ttdoc">returns true if the avrc service is connected</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:996</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a4f94426ff4899c437d31623e013cf7a5"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a4f94426ff4899c437d31623e013cf7a5">BluetoothA2DPSink::set_stream_reader</a></div><div class="ttdeci">virtual void set_stream_reader(void(*callBack)(const uint8_t *, uint32_t), bool i2s_output=true)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:78</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a507e30ececfdc4382af60a0319cdaf1b"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a507e30ececfdc4382af60a0319cdaf1b">BluetoothA2DPSink::set_volume</a></div><div class="ttdeci">virtual void set_volume(uint8_t volume)</div><div class="ttdoc">Changes the volume.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:1047</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a5a770be98d977a8df916d8cc044b310c"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a5a770be98d977a8df916d8cc044b310c">BluetoothA2DPSink::get_last_rssi</a></div><div class="ttdeci">esp_bt_gap_cb_param_t::read_rssi_delta_param get_last_rssi()</div><div class="ttdoc">provides the last rssi parameters</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:315</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a5a91e49987a2e39c09fc6c2a64feaed6"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a5a91e49987a2e39c09fc6c2a64feaed6">BluetoothA2DPSink::end</a></div><div class="ttdeci">virtual void end(bool release_memory=false)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:66</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a5bdae239eb76ad435a7999362a8019fc"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a5bdae239eb76ad435a7999362a8019fc">BluetoothA2DPSink::set_output</a></div><div class="ttdeci">void set_output(audio_tools::AudioOutput &amp;output)</div><div class="ttdoc">Output AudioOutput using AudioTools library.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:148</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a5d4707195d0d6e79b65bef4ed48a57c2"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a5d4707195d0d6e79b65bef4ed48a57c2">BluetoothA2DPSink::confirm_pin_code</a></div><div class="ttdeci">virtual void confirm_pin_code()</div><div class="ttdoc">confirms the connection request by returning the receivedn pin code</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:1068</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a5dd8ed8e61d6bb6d0c1e05d5c17e45e7"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a5dd8ed8e61d6bb6d0c1e05d5c17e45e7">BluetoothA2DPSink::set_avrc_rn_play_pos_callback</a></div><div class="ttdeci">virtual void set_avrc_rn_play_pos_callback(void(*callback)(uint32_t play_pos), uint32_t notif_interval=10)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:186</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a5e4806bad4ed634493643c5925ecf67f"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a5e4806bad4ed634493643c5925ecf67f">BluetoothA2DPSink::set_avrc_rn_playstatus_callback</a></div><div class="ttdeci">virtual void set_avrc_rn_playstatus_callback(void(*callback)(esp_avrc_playback_stat_t playback))</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:180</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a624040cce89a4a2f66495f57db6c1457"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a624040cce89a4a2f66495f57db6c1457">BluetoothA2DPSink::set_mono_downmix</a></div><div class="ttdeci">virtual void set_mono_downmix(bool enabled)</div><div class="ttdoc">mix stereo into single mono signal</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:264</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a62e0cb5c7a6552581ad43fdd98c4a022"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a62e0cb5c7a6552581ad43fdd98c4a022">BluetoothA2DPSink::BluetoothA2DPSink</a></div><div class="ttdeci">BluetoothA2DPSink(audio_tools::AudioStream &amp;output)</div><div class="ttdoc">Output AudioStream using AudioTools library.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:84</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a664eafe67a1f66b415159b84c3fe851f"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a664eafe67a1f66b415159b84c3fe851f">BluetoothA2DPSink::set_output</a></div><div class="ttdeci">void set_output(audio_tools::AudioStream &amp;output)</div><div class="ttdoc">Output AudioStream using AudioTools library.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:150</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a68d88c33b8ec218fe3bd45f61c39f754"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a68d88c33b8ec218fe3bd45f61c39f754">BluetoothA2DPSink::set_output_active</a></div><div class="ttdeci">void set_output_active(bool flag)</div><div class="ttdoc">Activate/Deactivate output e.g. to I2S.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:333</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a6eb9f31e643607224fa784ff20654924"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a6eb9f31e643607224fa784ff20654924">BluetoothA2DPSink::is_output_active</a></div><div class="ttdeci">bool is_output_active()</div><div class="ttdoc">Checks if output is active.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:336</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a77600cb1e36b7814eb9b4126cdec62d4"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a77600cb1e36b7814eb9b4126cdec62d4">BluetoothA2DPSink::get_audio_type</a></div><div class="ttdeci">virtual esp_a2d_mct_t get_audio_type()</div><div class="ttdoc">Determine the actual audio type.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:194</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a7acf882642fea0df0c36847be134ec6f"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a7acf882642fea0df0c36847be134ec6f">BluetoothA2DPSink::BluetoothA2DPSink</a></div><div class="ttdeci">BluetoothA2DPSink(Print &amp;output)</div><div class="ttdoc">Output to Arduino Print.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:91</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a7f8680e010057c3fea392c75c85c9f23"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a7f8680e010057c3fea392c75c85c9f23">BluetoothA2DPSink::app_rc_ct_callback</a></div><div class="ttdeci">void app_rc_ct_callback(esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param) override</div><div class="ttdoc">callback function for AVRCP controller</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:371</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a8281ab4339dd58c49d1d651dd74f5711"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a8281ab4339dd58c49d1d651dd74f5711">BluetoothA2DPSink::is_avrc_peer_rn_cap_available</a></div><div class="ttdeci">bool is_avrc_peer_rn_cap_available()</div><div class="ttdoc">Returns true if the is_avrc_peer_rn_cap() method can be called.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:346</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a8281af353148544a0612f8f7c4d511b1"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a8281af353148544a0612f8f7c4d511b1">BluetoothA2DPSink::set_avrc_metadata_attribute_mask</a></div><div class="ttdeci">virtual void set_avrc_metadata_attribute_mask(int flags)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:287</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a89d2694f880a2db22344b97b466c9a9d"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a89d2694f880a2db22344b97b466c9a9d">BluetoothA2DPSink::set_sample_rate_callback</a></div><div class="ttdeci">virtual void set_sample_rate_callback(void(*callback)(uint16_t rate))</div><div class="ttdoc">Defines the method which will be called with the sample rate is updated.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:201</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a90cee550d061836992616161d4215355"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a90cee550d061836992616161d4215355">BluetoothA2DPSink::sample_rate</a></div><div class="ttdeci">virtual uint16_t sample_rate()</div><div class="ttdoc">Provides the actually set data rate (in samples per second)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:269</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a96b4fabd27e7952fc3ea5edca3b95cbb"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a96b4fabd27e7952fc3ea5edca3b95cbb">BluetoothA2DPSink::set_reconnect_delay</a></div><div class="ttdeci">void set_reconnect_delay(int delay)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:327</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a97c80237061c6e80d7c6e1e5e45773c8"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a97c80237061c6e80d7c6e1e5e45773c8">BluetoothA2DPSink::set_rssi_callback</a></div><div class="ttdeci">void set_rssi_callback(void(*callback)(esp_bt_gap_cb_param_t::read_rssi_delta_param &amp;rssi))</div><div class="ttdoc">Defines the callback that is called when we get an new rssi value.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:320</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a9892ecf2f81b99d861f2767f7b705188"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a9892ecf2f81b99d861f2767f7b705188">BluetoothA2DPSink::app_a2d_callback</a></div><div class="ttdeci">void app_a2d_callback(esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param) override</div><div class="ttdoc">callback function for A2DP source</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:917</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a9ab56fe60162fa9c07bf0ab8f523bfbf"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a9ab56fe60162fa9c07bf0ab8f523bfbf">BluetoothA2DPSink::ccall_av_hdl_a2d_evt</a></div><div class="ttdeci">friend void ccall_av_hdl_a2d_evt(uint16_t event, void *p_param)</div><div class="ttdoc">a2dp event handler</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:40</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a9b16f9fec1e74eb3bb30f6cf572d21e9"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a9b16f9fec1e74eb3bb30f6cf572d21e9">BluetoothA2DPSink::set_avrc_rn_volumechange_completed</a></div><div class="ttdeci">virtual void set_avrc_rn_volumechange_completed(void(*callBack)(int))</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:103</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a9c47257f9af0d64008fd46e201fb9063"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a9c47257f9af0d64008fd46e201fb9063">BluetoothA2DPSink::BluetoothA2DPSink</a></div><div class="ttdeci">BluetoothA2DPSink(audio_tools::AudioOutput &amp;output)</div><div class="ttdoc">Output AudioOutput using AudioTools library.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:80</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_a9ee01e6d11ee3c6c546a510029a23a12"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#a9ee01e6d11ee3c6c546a510029a23a12">BluetoothA2DPSink::rewind</a></div><div class="ttdeci">virtual void rewind()</div><div class="ttdoc">AVRC rewind.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:1035</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_aa211fefd101a639938a20dc3478b48ae"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#aa211fefd101a639938a20dc3478b48ae">BluetoothA2DPSink::write_audio</a></div><div class="ttdeci">virtual size_t write_audio(const uint8_t *data, size_t size)</div><div class="ttdoc">output audio data e.g. to i2s or to queue</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:473</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_aa6967e9329939596c62f16e8686cac13"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#aa6967e9329939596c62f16e8686cac13">BluetoothA2DPSink::pause</a></div><div class="ttdeci">virtual void pause()</div><div class="ttdoc">AVRC pause.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:1022</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_aac9074521c80d7574a855f30b8301d13"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#aac9074521c80d7574a855f30b8301d13">BluetoothA2DPSink::set_avrc_metadata_callback</a></div><div class="ttdeci">virtual void set_avrc_metadata_callback(void(*callback)(uint8_t, const uint8_t *))</div><div class="ttdoc">Define a callback method which provides the meta data.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:172</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_aadfb815b992649822db74d3e2780d4c8"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#aadfb815b992649822db74d3e2780d4c8">BluetoothA2DPSink::BluetoothA2DPSink</a></div><div class="ttdeci">BluetoothA2DPSink(BluetoothA2DPOutput &amp;out)</div><div class="ttdoc">Define output scenario class.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:74</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_aafd2afad1960db8ab73d7c6977aeb686"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#aafd2afad1960db8ab73d7c6977aeb686">BluetoothA2DPSink::play</a></div><div class="ttdeci">virtual void play()</div><div class="ttdoc">Starts to play music using AVRC.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:1020</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_ab59e6c716d9b5aaed69272e7d2a3d12a"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#ab59e6c716d9b5aaed69272e7d2a3d12a">BluetoothA2DPSink::i2s_write_data</a></div><div class="ttdeci">size_t i2s_write_data(const uint8_t *data, size_t item_size)</div><div class="ttdoc">writes the data to i2s</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:1098</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_abe6004e2b95d120d64c10cf947fefb55"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#abe6004e2b95d120d64c10cf947fefb55">BluetoothA2DPSink::set_spp_active</a></div><div class="ttdeci">void set_spp_active(bool flag)</div><div class="ttdoc">Activates SSP (Serial protocol)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:330</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_ac08fff859e0ccfbb12cbb6b119dba438"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#ac08fff859e0ccfbb12cbb6b119dba438">BluetoothA2DPSink::get_current_peer_address</a></div><div class="ttdeci">virtual esp_bd_addr_t * get_current_peer_address()</div><div class="ttdoc">Provides the address of the connected device.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:303</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_ac0e3ebab2d32e289ec52a08bcfa2e978"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#ac0e3ebab2d32e289ec52a08bcfa2e978">BluetoothA2DPSink::ccall_audio_data_callback</a></div><div class="ttdeci">friend void ccall_audio_data_callback(const uint8_t *data, uint32_t len)</div><div class="ttdoc">Callback for music stream.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:27</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_ac18463dacb2427d3687ef8b930cb9a8d"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#ac18463dacb2427d3687ef8b930cb9a8d">BluetoothA2DPSink::ccall_i2s_task_handler</a></div><div class="ttdeci">friend void ccall_i2s_task_handler(void *arg)</div><div class="ttdoc">task hander for i2s</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:21</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_ac245103d3d5c47b0414c2de21c0d52a7"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#ac245103d3d5c47b0414c2de21c0d52a7">BluetoothA2DPSink::set_on_volumechange</a></div><div class="ttdeci">virtual void set_on_volumechange(void(*callBack)(int))</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:95</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_ac9adc3ca64e68fb3d6e816698a725dd5"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#ac9adc3ca64e68fb3d6e816698a725dd5">BluetoothA2DPSink::get_peer_name</a></div><div class="ttdeci">virtual const char * get_peer_name()</div><div class="ttdoc">Get the name of the connected source device.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:349</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_aca1119f20d2321fb950ae859000cce7b"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#aca1119f20d2321fb950ae859000cce7b">BluetoothA2DPSink::get_volume</a></div><div class="ttdeci">virtual int get_volume()</div><div class="ttdoc">Determines the volume.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:1059</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_acb34360759c6a94028f74df35009b033"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#acb34360759c6a94028f74df35009b033">BluetoothA2DPSink::set_avrc_rn_volumechange</a></div><div class="ttdeci">virtual void set_avrc_rn_volumechange(void(*callBack)(int))</div><div class="ttdoc">Set the callback that is called when remote changes the volume.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:99</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_ad25155f02bad11da6c130aae00c8ab9c"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#ad25155f02bad11da6c130aae00c8ab9c">BluetoothA2DPSink::set_address_validator</a></div><div class="ttdeci">virtual void set_address_validator(bool(*callBack)(esp_bd_addr_t remote_bda))</div><div class="ttdoc">Allows you to reject unauthorized addresses.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:219</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_ae56f6a99a5c38e0bdd402a79faba6dd5"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#ae56f6a99a5c38e0bdd402a79faba6dd5">BluetoothA2DPSink::set_avrc_rn_track_change_callback</a></div><div class="ttdeci">virtual void set_avrc_rn_track_change_callback(void(*callback)(uint8_t *id))</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:194</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_ae823f16ed3ee17cf9c6d1731b9d19a34"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#ae823f16ed3ee17cf9c6d1731b9d19a34">BluetoothA2DPSink::volume_down</a></div><div class="ttdeci">virtual void volume_down()</div><div class="ttdoc">AVRC decrease the volume.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:1043</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_aedd80fe4cf8bd887224efa2716ad9d69"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#aedd80fe4cf8bd887224efa2716ad9d69">BluetoothA2DPSink::set_avrc_connection_state_callback</a></div><div class="ttdeci">virtual void set_avrc_connection_state_callback(void(*callback)(bool))</div><div class="ttdoc">Define a callback method which provides connection state of AVRC service.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:167</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_af5d6399876738c8fa0766ea247476b3f"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#af5d6399876738c8fa0766ea247476b3f">BluetoothA2DPSink::is_avrc_peer_rn_cap</a></div><div class="ttdeci">bool is_avrc_peer_rn_cap(esp_avrc_rn_event_ids_t cmd)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:341</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_af65219e635fadbbc90f4663b33abd3e0"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#af65219e635fadbbc90f4663b33abd3e0">BluetoothA2DPSink::set_on_data_received</a></div><div class="ttdeci">virtual void set_on_data_received(void(*callBack)())</div><div class="ttdoc">Define callback which is called when we receive data.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:90</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_af7d10cfe632a3c2f95409f6a23daecdd"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#af7d10cfe632a3c2f95409f6a23daecdd">BluetoothA2DPSink::set_auto_reconnect</a></div><div class="ttdeci">virtual void set_auto_reconnect(bool reconnect, int count=AUTOCONNECT_TRY_NUM)</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:296</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_af85e99324638d1c814e221ac4ba815dd"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#af85e99324638d1c814e221ac4ba815dd">BluetoothA2DPSink::start</a></div><div class="ttdeci">virtual void start(const char *name, bool auto_reconect)</div><div class="ttdoc">starts the I2S bluetooth sink with the inidicated name</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.cpp:108</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_af87666dc2c00c5917db709e2edaf1dab"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#af87666dc2c00c5917db709e2edaf1dab">BluetoothA2DPSink::set_output</a></div><div class="ttdeci">void set_output(Print &amp;output)</div><div class="ttdoc">Output to Arduino Print.</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:139</div></div>
<div class="ttc" id="aclass_bluetooth_a2_d_p_sink_html_afdc2f08c0547393704fd6fb56bde204f"><div class="ttname"><a href="class_bluetooth_a2_d_p_sink.html#afdc2f08c0547393704fd6fb56bde204f">BluetoothA2DPSink::i2s_task_handler</a></div><div class="ttdeci">virtual void i2s_task_handler(void *arg)</div><div class="ttdoc">dummy functions needed for BluetoothA2DPSinkQueued</div><div class="ttdef"><b>Definition:</b> BluetoothA2DPSink.h:481</div></div>
<div class="ttc" id="agroup__a2dp_html_ga0af05e9d744ec14ee33e345d678e8ade"><div class="ttname"><a href="group__a2dp.html#ga0af05e9d744ec14ee33e345d678e8ade">esp_avrc_rn_event_ids_t</a></div><div class="ttdeci">esp_avrc_rn_event_ids_t</div><div class="ttdoc">AVRC event notification ids.</div><div class="ttdef"><b>Definition:</b> external_lists.h:50</div></div>
<div class="ttc" id="agroup__a2dp_html_ga89fdf5fb26b1ea6f33d36cc0eebca4fb"><div class="ttname"><a href="group__a2dp.html#ga89fdf5fb26b1ea6f33d36cc0eebca4fb">esp_avrc_playback_stat_t</a></div><div class="ttdeci">esp_avrc_playback_stat_t</div><div class="ttdoc">AVRCP current status of playback.</div><div class="ttdef"><b>Definition:</b> external_lists.h:72</div></div>
<div class="ttc" id="agroup__a2dp_html_gae1f72542f04666cd97c26732366bf109"><div class="ttname"><a href="group__a2dp.html#gae1f72542f04666cd97c26732366bf109">esp_bd_addr_t</a></div><div class="ttdeci">uint8_t esp_bd_addr_t[ESP_BD_ADDR_LEN]</div><div class="ttdoc">Bluetooth address.</div><div class="ttdef"><b>Definition:</b> external_lists.h:107</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
