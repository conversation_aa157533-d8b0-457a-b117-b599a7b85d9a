<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: BluetoothA2DPSinkMinRAM Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="#pro-attribs">Protected Attributes</a> &#124;
<a href="#pro-static-attribs">Static Protected Attributes</a> &#124;
<a href="class_bluetooth_a2_d_p_sink_min_r_a_m-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">BluetoothA2DPSinkMinRAM Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="class_bluetooth_a2_d_p_sink_min_r_a_m.html" title="BluetoothA2DPSinkMinRAM. The BluetoothA2DPSink is using a separate Task with an additinal Queue to wr...">BluetoothA2DPSinkMinRAM</a>. The <a class="el" href="class_bluetooth_a2_d_p_sink.html" title="A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example https://github....">BluetoothA2DPSink</a> is using a separate Task with an additinal Queue to write the I2S data. This implementation is using the legacy logic which writes directly to I2S w/o task. This leaves more RAM available to the application.  
 <a href="class_bluetooth_a2_d_p_sink_min_r_a_m.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_bluetooth_a2_d_p_sink_8h_source.html">BluetoothA2DPSink.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for BluetoothA2DPSinkMinRAM:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_bluetooth_a2_d_p_sink_min_r_a_m.png" usemap="#BluetoothA2DPSinkMinRAM_map" alt=""/>
  <map id="BluetoothA2DPSinkMinRAM_map" name="BluetoothA2DPSinkMinRAM_map">
<area href="class_bluetooth_a2_d_p_sink.html" title="A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example https://github...." alt="BluetoothA2DPSink" shape="rect" coords="0,56,170,80"/>
<area href="class_bluetooth_a2_d_p_common.html" title="Common Bluetooth A2DP functions." alt="BluetoothA2DPCommon" shape="rect" coords="0,0,170,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a90a903dadcd5e123afe69ca5ac0fb2dc"><td class="memItemLeft" align="right" valign="top"><a id="a90a903dadcd5e123afe69ca5ac0fb2dc"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>bt_i2s_task_start_up</b> (void) override</td></tr>
<tr class="separator:a90a903dadcd5e123afe69ca5ac0fb2dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab35a7d3f7a810d5f139983f6ae7cc91d"><td class="memItemLeft" align="right" valign="top"><a id="ab35a7d3f7a810d5f139983f6ae7cc91d"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>bt_i2s_task_shut_down</b> (void) override</td></tr>
<tr class="separator:ab35a7d3f7a810d5f139983f6ae7cc91d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab434ca517e5ec9bc988d44d677ff5722"><td class="memItemLeft" align="right" valign="top"><a id="ab434ca517e5ec9bc988d44d677ff5722"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>i2s_task_handler</b> (void *arg) override</td></tr>
<tr class="separator:ab434ca517e5ec9bc988d44d677ff5722"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af584c3b4db5ccc746cf59b95c977be27"><td class="memItemLeft" align="right" valign="top"><a id="af584c3b4db5ccc746cf59b95c977be27"></a>
size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink_min_r_a_m.html#af584c3b4db5ccc746cf59b95c977be27">write_ringbuf</a> (const uint8_t *data, size_t size)</td></tr>
<tr class="memdesc:af584c3b4db5ccc746cf59b95c977be27"><td class="mdescLeft">&#160;</td><td class="mdescRight">i2s task with ringubffer <br /></td></tr>
<tr class="separator:af584c3b4db5ccc746cf59b95c977be27"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7ce8131af4f085e94eb81e3fcbfc4af"><td class="memItemLeft" align="right" valign="top"><a id="af7ce8131af4f085e94eb81e3fcbfc4af"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af7ce8131af4f085e94eb81e3fcbfc4af">set_pin_config</a> (i2s_pin_config_t pin_config)</td></tr>
<tr class="memdesc:af7ce8131af4f085e94eb81e3fcbfc4af"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define the pins. <br /></td></tr>
<tr class="separator:af7ce8131af4f085e94eb81e3fcbfc4af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab4e52dff7ef08f17cfce4e011cdd6542"><td class="memItemLeft" align="right" valign="top"><a id="ab4e52dff7ef08f17cfce4e011cdd6542"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ab4e52dff7ef08f17cfce4e011cdd6542">set_i2s_port</a> (i2s_port_t i2s_num)</td></tr>
<tr class="memdesc:ab4e52dff7ef08f17cfce4e011cdd6542"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define an alternative i2s port other then 0. <br /></td></tr>
<tr class="separator:ab4e52dff7ef08f17cfce4e011cdd6542"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a39329de792f43f90a16f9ab2ee62814f"><td class="memItemLeft" align="right" valign="top"><a id="a39329de792f43f90a16f9ab2ee62814f"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a39329de792f43f90a16f9ab2ee62814f">set_i2s_config</a> (i2s_config_t i2s_config)</td></tr>
<tr class="memdesc:a39329de792f43f90a16f9ab2ee62814f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define the i2s configuration. <br /></td></tr>
<tr class="separator:a39329de792f43f90a16f9ab2ee62814f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af85e99324638d1c814e221ac4ba815dd"><td class="memItemLeft" align="right" valign="top"><a id="af85e99324638d1c814e221ac4ba815dd"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af85e99324638d1c814e221ac4ba815dd">start</a> (const char *name, bool auto_reconect)</td></tr>
<tr class="memdesc:af85e99324638d1c814e221ac4ba815dd"><td class="mdescLeft">&#160;</td><td class="mdescRight">starts the I2S bluetooth sink with the inidicated name <br /></td></tr>
<tr class="separator:af85e99324638d1c814e221ac4ba815dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a189424ab5dc8c44f00b461e9392a2ce8"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a189424ab5dc8c44f00b461e9392a2ce8">start</a> (const char *name)</td></tr>
<tr class="memdesc:a189424ab5dc8c44f00b461e9392a2ce8"><td class="mdescLeft">&#160;</td><td class="mdescRight">starts the I2S bluetooth sink with the inidicated name  <a href="class_bluetooth_a2_d_p_sink.html#a189424ab5dc8c44f00b461e9392a2ce8">More...</a><br /></td></tr>
<tr class="separator:a189424ab5dc8c44f00b461e9392a2ce8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a91e49987a2e39c09fc6c2a64feaed6"><td class="memItemLeft" align="right" valign="top"><a id="a5a91e49987a2e39c09fc6c2a64feaed6"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5a91e49987a2e39c09fc6c2a64feaed6">end</a> (bool release_memory=false)</td></tr>
<tr class="memdesc:a5a91e49987a2e39c09fc6c2a64feaed6"><td class="mdescLeft">&#160;</td><td class="mdescRight">ends the I2S bluetooth sink with the indicated name - if you release the memory a future start is not possible <br /></td></tr>
<tr class="separator:a5a91e49987a2e39c09fc6c2a64feaed6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a19842c9bedcfd1d88e4f62f7a2523db7"><td class="memItemLeft" align="right" valign="top"><a id="a19842c9bedcfd1d88e4f62f7a2523db7"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a19842c9bedcfd1d88e4f62f7a2523db7">is_connected</a> ()</td></tr>
<tr class="memdesc:a19842c9bedcfd1d88e4f62f7a2523db7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if the state is connected. <br /></td></tr>
<tr class="separator:a19842c9bedcfd1d88e4f62f7a2523db7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77600cb1e36b7814eb9b4126cdec62d4"><td class="memItemLeft" align="right" valign="top"><a id="a77600cb1e36b7814eb9b4126cdec62d4"></a>
virtual esp_a2d_mct_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a77600cb1e36b7814eb9b4126cdec62d4">get_audio_type</a> ()</td></tr>
<tr class="memdesc:a77600cb1e36b7814eb9b4126cdec62d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Determine the actual audio type. <br /></td></tr>
<tr class="separator:a77600cb1e36b7814eb9b4126cdec62d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aac9074521c80d7574a855f30b8301d13"><td class="memItemLeft" align="right" valign="top"><a id="aac9074521c80d7574a855f30b8301d13"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aac9074521c80d7574a855f30b8301d13">set_avrc_metadata_callback</a> (void(*callback)(uint8_t, const uint8_t *))</td></tr>
<tr class="memdesc:aac9074521c80d7574a855f30b8301d13"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define a callback method which provides the meta data. <br /></td></tr>
<tr class="separator:aac9074521c80d7574a855f30b8301d13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a89d2694f880a2db22344b97b466c9a9d"><td class="memItemLeft" align="right" valign="top"><a id="a89d2694f880a2db22344b97b466c9a9d"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a89d2694f880a2db22344b97b466c9a9d">set_sample_rate_callback</a> (void(*callback)(uint16_t rate))</td></tr>
<tr class="memdesc:a89d2694f880a2db22344b97b466c9a9d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the method which will be called with the sample rate is updated. <br /></td></tr>
<tr class="separator:a89d2694f880a2db22344b97b466c9a9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f94426ff4899c437d31623e013cf7a5"><td class="memItemLeft" align="right" valign="top"><a id="a4f94426ff4899c437d31623e013cf7a5"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a4f94426ff4899c437d31623e013cf7a5">set_stream_reader</a> (void(*callBack)(const uint8_t *, uint32_t), bool i2s_output=true)</td></tr>
<tr class="memdesc:a4f94426ff4899c437d31623e013cf7a5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define callback which is called when we receive data: This callback provides access to the data. <br /></td></tr>
<tr class="separator:a4f94426ff4899c437d31623e013cf7a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af65219e635fadbbc90f4663b33abd3e0"><td class="memItemLeft" align="right" valign="top"><a id="af65219e635fadbbc90f4663b33abd3e0"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af65219e635fadbbc90f4663b33abd3e0">set_on_data_received</a> (void(*callBack)())</td></tr>
<tr class="memdesc:af65219e635fadbbc90f4663b33abd3e0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define callback which is called when we receive data. <br /></td></tr>
<tr class="separator:af65219e635fadbbc90f4663b33abd3e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad25155f02bad11da6c130aae00c8ab9c"><td class="memItemLeft" align="right" valign="top"><a id="ad25155f02bad11da6c130aae00c8ab9c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ad25155f02bad11da6c130aae00c8ab9c">set_address_validator</a> (bool(*callBack)(esp_bd_addr_t remote_bda))</td></tr>
<tr class="memdesc:ad25155f02bad11da6c130aae00c8ab9c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Allows you to reject unauthorized addresses. <br /></td></tr>
<tr class="separator:ad25155f02bad11da6c130aae00c8ab9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a507e30ececfdc4382af60a0319cdaf1b"><td class="memItemLeft" align="right" valign="top"><a id="a507e30ececfdc4382af60a0319cdaf1b"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a507e30ececfdc4382af60a0319cdaf1b">set_volume</a> (uint8_t volume)</td></tr>
<tr class="memdesc:a507e30ececfdc4382af60a0319cdaf1b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Changes the volume. <br /></td></tr>
<tr class="separator:a507e30ececfdc4382af60a0319cdaf1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aca1119f20d2321fb950ae859000cce7b"><td class="memItemLeft" align="right" valign="top"><a id="aca1119f20d2321fb950ae859000cce7b"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aca1119f20d2321fb950ae859000cce7b">get_volume</a> ()</td></tr>
<tr class="memdesc:aca1119f20d2321fb950ae859000cce7b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Determines the volume. <br /></td></tr>
<tr class="separator:aca1119f20d2321fb950ae859000cce7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac245103d3d5c47b0414c2de21c0d52a7"><td class="memItemLeft" align="right" valign="top"><a id="ac245103d3d5c47b0414c2de21c0d52a7"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ac245103d3d5c47b0414c2de21c0d52a7">set_on_volumechange</a> (void(*callBack)(int))</td></tr>
<tr class="memdesc:ac245103d3d5c47b0414c2de21c0d52a7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the callback that is called when they change the volume. <br /></td></tr>
<tr class="separator:ac245103d3d5c47b0414c2de21c0d52a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aafd2afad1960db8ab73d7c6977aeb686"><td class="memItemLeft" align="right" valign="top"><a id="aafd2afad1960db8ab73d7c6977aeb686"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aafd2afad1960db8ab73d7c6977aeb686">play</a> ()</td></tr>
<tr class="memdesc:aafd2afad1960db8ab73d7c6977aeb686"><td class="mdescLeft">&#160;</td><td class="mdescRight">Starts to play music using AVRC. <br /></td></tr>
<tr class="separator:aafd2afad1960db8ab73d7c6977aeb686"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6967e9329939596c62f16e8686cac13"><td class="memItemLeft" align="right" valign="top"><a id="aa6967e9329939596c62f16e8686cac13"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aa6967e9329939596c62f16e8686cac13">pause</a> ()</td></tr>
<tr class="memdesc:aa6967e9329939596c62f16e8686cac13"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC pause. <br /></td></tr>
<tr class="separator:aa6967e9329939596c62f16e8686cac13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37dcbcd418b84310ccedf3330e44834f"><td class="memItemLeft" align="right" valign="top"><a id="a37dcbcd418b84310ccedf3330e44834f"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a37dcbcd418b84310ccedf3330e44834f">stop</a> ()</td></tr>
<tr class="memdesc:a37dcbcd418b84310ccedf3330e44834f"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC stop. <br /></td></tr>
<tr class="separator:a37dcbcd418b84310ccedf3330e44834f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a296fb7aaf8d8e78991d9d505353de94f"><td class="memItemLeft" align="right" valign="top"><a id="a296fb7aaf8d8e78991d9d505353de94f"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a296fb7aaf8d8e78991d9d505353de94f">next</a> ()</td></tr>
<tr class="memdesc:a296fb7aaf8d8e78991d9d505353de94f"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC next. <br /></td></tr>
<tr class="separator:a296fb7aaf8d8e78991d9d505353de94f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a341024c18eabdb06c734c2242d5ba505"><td class="memItemLeft" align="right" valign="top"><a id="a341024c18eabdb06c734c2242d5ba505"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a341024c18eabdb06c734c2242d5ba505">previous</a> ()</td></tr>
<tr class="memdesc:a341024c18eabdb06c734c2242d5ba505"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC previous. <br /></td></tr>
<tr class="separator:a341024c18eabdb06c734c2242d5ba505"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42e01689353026b1ef9883fd5d32f00c"><td class="memItemLeft" align="right" valign="top"><a id="a42e01689353026b1ef9883fd5d32f00c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a42e01689353026b1ef9883fd5d32f00c">fast_forward</a> ()</td></tr>
<tr class="memdesc:a42e01689353026b1ef9883fd5d32f00c"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC fast_forward. <br /></td></tr>
<tr class="separator:a42e01689353026b1ef9883fd5d32f00c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ee01e6d11ee3c6c546a510029a23a12"><td class="memItemLeft" align="right" valign="top"><a id="a9ee01e6d11ee3c6c546a510029a23a12"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a9ee01e6d11ee3c6c546a510029a23a12">rewind</a> ()</td></tr>
<tr class="memdesc:a9ee01e6d11ee3c6c546a510029a23a12"><td class="mdescLeft">&#160;</td><td class="mdescRight">AVRC rewind. <br /></td></tr>
<tr class="separator:a9ee01e6d11ee3c6c546a510029a23a12"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc8a4564e135ace22d31c2231f1a0696"><td class="memItemLeft" align="right" valign="top"><a id="abc8a4564e135ace22d31c2231f1a0696"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#abc8a4564e135ace22d31c2231f1a0696">set_channels</a> (i2s_channel_t channels)</td></tr>
<tr class="memdesc:abc8a4564e135ace22d31c2231f1a0696"><td class="mdescLeft">&#160;</td><td class="mdescRight">set output to I2S_CHANNEL_STEREO (default) or I2S_CHANNEL_MONO <br /></td></tr>
<tr class="separator:abc8a4564e135ace22d31c2231f1a0696"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a624040cce89a4a2f66495f57db6c1457"><td class="memItemLeft" align="right" valign="top"><a id="a624040cce89a4a2f66495f57db6c1457"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a624040cce89a4a2f66495f57db6c1457">set_mono_downmix</a> (bool enabled)</td></tr>
<tr class="memdesc:a624040cce89a4a2f66495f57db6c1457"><td class="mdescLeft">&#160;</td><td class="mdescRight">mix stereo into single mono signal <br /></td></tr>
<tr class="separator:a624040cce89a4a2f66495f57db6c1457"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ebe4927600f29318133b5f11e0ab7f8"><td class="memItemLeft" align="right" valign="top"><a id="a9ebe4927600f29318133b5f11e0ab7f8"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a9ebe4927600f29318133b5f11e0ab7f8">set_bits_per_sample</a> (int bps)</td></tr>
<tr class="memdesc:a9ebe4927600f29318133b5f11e0ab7f8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the bits per sample for output (if &gt; 16 output will be expanded) <br /></td></tr>
<tr class="separator:a9ebe4927600f29318133b5f11e0ab7f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a09a8b269e2a936c5517bd9f88f666a1c"><td class="memItemLeft" align="right" valign="top"><a id="a09a8b269e2a936c5517bd9f88f666a1c"></a>
virtual uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a09a8b269e2a936c5517bd9f88f666a1c">sample_rate</a> ()</td></tr>
<tr class="memdesc:a09a8b269e2a936c5517bd9f88f666a1c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the actually set data rate (in samples per second) <br /></td></tr>
<tr class="separator:a09a8b269e2a936c5517bd9f88f666a1c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a90e76786cf62555379796e63f4499951"><td class="memItemLeft" align="right" valign="top"><a id="a90e76786cf62555379796e63f4499951"></a>
virtual esp_err_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a90e76786cf62555379796e63f4499951">i2s_mclk_pin_select</a> (const uint8_t pin)</td></tr>
<tr class="memdesc:a90e76786cf62555379796e63f4499951"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the pin for the master clock. <br /></td></tr>
<tr class="separator:a90e76786cf62555379796e63f4499951"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a22d52952a8ac8c78a483a53c2006a387"><td class="memItemLeft" align="right" valign="top"><a id="a22d52952a8ac8c78a483a53c2006a387"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a22d52952a8ac8c78a483a53c2006a387">activate_pin_code</a> (bool active)</td></tr>
<tr class="memdesc:a22d52952a8ac8c78a483a53c2006a387"><td class="mdescLeft">&#160;</td><td class="mdescRight">We need to confirm a new seesion by calling <a class="el" href="class_bluetooth_a2_d_p_sink.html#a5d4707195d0d6e79b65bef4ed48a57c2" title="confirms the connection request by returning the receivedn pin code">confirm_pin_code()</a> <br /></td></tr>
<tr class="separator:a22d52952a8ac8c78a483a53c2006a387"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d4707195d0d6e79b65bef4ed48a57c2"><td class="memItemLeft" align="right" valign="top"><a id="a5d4707195d0d6e79b65bef4ed48a57c2"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5d4707195d0d6e79b65bef4ed48a57c2">confirm_pin_code</a> ()</td></tr>
<tr class="memdesc:a5d4707195d0d6e79b65bef4ed48a57c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">confirms the connection request by returning the receivedn pin code <br /></td></tr>
<tr class="separator:a5d4707195d0d6e79b65bef4ed48a57c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a43369961e9858cf99798e9c1b6a634b9"><td class="memItemLeft" align="right" valign="top"><a id="a43369961e9858cf99798e9c1b6a634b9"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a43369961e9858cf99798e9c1b6a634b9">confirm_pin_code</a> (int code)</td></tr>
<tr class="memdesc:a43369961e9858cf99798e9c1b6a634b9"><td class="mdescLeft">&#160;</td><td class="mdescRight">confirms the connection request by returning the indicated pin code <br /></td></tr>
<tr class="separator:a43369961e9858cf99798e9c1b6a634b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3719138f63afaeed06b63cc48ea79335"><td class="memItemLeft" align="right" valign="top"><a id="a3719138f63afaeed06b63cc48ea79335"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a3719138f63afaeed06b63cc48ea79335">pin_code</a> ()</td></tr>
<tr class="memdesc:a3719138f63afaeed06b63cc48ea79335"><td class="mdescLeft">&#160;</td><td class="mdescRight">provides the requested pin code (0 = undefined) <br /></td></tr>
<tr class="separator:a3719138f63afaeed06b63cc48ea79335"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8281af353148544a0612f8f7c4d511b1"><td class="memItemLeft" align="right" valign="top"><a id="a8281af353148544a0612f8f7c4d511b1"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a8281af353148544a0612f8f7c4d511b1">set_avrc_metadata_attribute_mask</a> (int flags)</td></tr>
<tr class="memdesc:a8281af353148544a0612f8f7c4d511b1"><td class="mdescLeft">&#160;</td><td class="mdescRight">defines the requested metadata: eg. ESP_AVRC_MD_ATTR_TITLE | ESP_AVRC_MD_ATTR_ARTIST | ESP_AVRC_MD_ATTR_ALBUM | ESP_AVRC_MD_ATTR_TRACK_NUM | ESP_AVRC_MD_ATTR_NUM_TRACKS | ESP_AVRC_MD_ATTR_GENRE | ESP_AVRC_MD_ATTR_PLAYING_TIME <br /></td></tr>
<tr class="separator:a8281af353148544a0612f8f7c4d511b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a14d982730e2b9ea772fe9ede1563ed22"><td class="memItemLeft" align="right" valign="top"><a id="a14d982730e2b9ea772fe9ede1563ed22"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a14d982730e2b9ea772fe9ede1563ed22">set_swap_lr_channels</a> (bool swap)</td></tr>
<tr class="memdesc:a14d982730e2b9ea772fe9ede1563ed22"><td class="mdescLeft">&#160;</td><td class="mdescRight">swaps the left and right channel <br /></td></tr>
<tr class="separator:a14d982730e2b9ea772fe9ede1563ed22"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7ba90ebbed89f38e292de7a0ac1bdb0"><td class="memItemLeft" align="right" valign="top"><a id="ac7ba90ebbed89f38e292de7a0ac1bdb0"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ac7ba90ebbed89f38e292de7a0ac1bdb0">set_auto_reconnect</a> (bool <a class="el" href="class_bluetooth_a2_d_p_common.html#aca9eebdad8d5525cb3dc1406e2c455c2">reconnect</a>, bool afterNormalDisconnect=false, int count=AUTOCONNECT_TRY_NUM)</td></tr>
<tr class="memdesc:ac7ba90ebbed89f38e292de7a0ac1bdb0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the number of times that the system tries to automatically reconnect to the last system. <br /></td></tr>
<tr class="separator:ac7ba90ebbed89f38e292de7a0ac1bdb0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a537d576b12d1158eb0681a6195b258de"><td class="memItemLeft" align="right" valign="top"><a id="a537d576b12d1158eb0681a6195b258de"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a537d576b12d1158eb0681a6195b258de">set_auto_reconnect</a> (bool active)</td></tr>
<tr class="memdesc:a537d576b12d1158eb0681a6195b258de"><td class="mdescLeft">&#160;</td><td class="mdescRight">activate / deactivate the automatic reconnection to the last address (per default this is on) <br /></td></tr>
<tr class="separator:a537d576b12d1158eb0681a6195b258de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac08fff859e0ccfbb12cbb6b119dba438"><td class="memItemLeft" align="right" valign="top"><a id="ac08fff859e0ccfbb12cbb6b119dba438"></a>
virtual esp_bd_addr_t *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ac08fff859e0ccfbb12cbb6b119dba438">get_current_peer_address</a> ()</td></tr>
<tr class="memdesc:ac08fff859e0ccfbb12cbb6b119dba438"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the address of the connected device. <br /></td></tr>
<tr class="separator:ac08fff859e0ccfbb12cbb6b119dba438"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3061ae4cc5bd536094f5fa836fffc081"><td class="memItemLeft" align="right" valign="top"><a id="a3061ae4cc5bd536094f5fa836fffc081"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a3061ae4cc5bd536094f5fa836fffc081">set_event_queue_size</a> (int size)</td></tr>
<tr class="memdesc:a3061ae4cc5bd536094f5fa836fffc081"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the queue size of the event task. <br /></td></tr>
<tr class="separator:a3061ae4cc5bd536094f5fa836fffc081"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b32be65a83a744869c8a9565a170cf3"><td class="memItemLeft" align="right" valign="top"><a id="a6b32be65a83a744869c8a9565a170cf3"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a6b32be65a83a744869c8a9565a170cf3">set_event_stack_size</a> (int size)</td></tr>
<tr class="memdesc:a6b32be65a83a744869c8a9565a170cf3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the stack size of the event task (in bytes) <br /></td></tr>
<tr class="separator:a6b32be65a83a744869c8a9565a170cf3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73e1da11ce7d4ebc3bc3a3368feb5d7f"><td class="memItemLeft" align="right" valign="top"><a id="a73e1da11ce7d4ebc3bc3a3368feb5d7f"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a73e1da11ce7d4ebc3bc3a3368feb5d7f">set_i2s_stack_size</a> (int size)</td></tr>
<tr class="memdesc:a73e1da11ce7d4ebc3bc3a3368feb5d7f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the stack size of the i2s task (in bytes) <br /></td></tr>
<tr class="separator:a73e1da11ce7d4ebc3bc3a3368feb5d7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5e5d13f82d40279028631248b51e1382"><td class="memItemLeft" align="right" valign="top"><a id="a5e5d13f82d40279028631248b51e1382"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5e5d13f82d40279028631248b51e1382">set_i2s_ringbuffer_size</a> (int size)</td></tr>
<tr class="memdesc:a5e5d13f82d40279028631248b51e1382"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the ringbuffer size used by the i2s task (in bytes) <br /></td></tr>
<tr class="separator:a5e5d13f82d40279028631248b51e1382"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad9261a4a966cfc552ce31059029ed0de"><td class="memItemLeft" align="right" valign="top"><a id="ad9261a4a966cfc552ce31059029ed0de"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ad9261a4a966cfc552ce31059029ed0de">set_i2s_task_priority</a> (UBaseType_t prio)</td></tr>
<tr class="memdesc:ad9261a4a966cfc552ce31059029ed0de"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the priority of the I2S task. <br /></td></tr>
<tr class="separator:ad9261a4a966cfc552ce31059029ed0de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a452b509a46930ab1ed58188a4181c67b"><td class="memItemLeft" align="right" valign="top"><a id="a452b509a46930ab1ed58188a4181c67b"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a452b509a46930ab1ed58188a4181c67b">set_rssi_active</a> (bool active)</td></tr>
<tr class="memdesc:a452b509a46930ab1ed58188a4181c67b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Activates the rssi reporting. <br /></td></tr>
<tr class="separator:a452b509a46930ab1ed58188a4181c67b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a770be98d977a8df916d8cc044b310c"><td class="memItemLeft" align="right" valign="top"><a id="a5a770be98d977a8df916d8cc044b310c"></a>
esp_bt_gap_cb_param_t::read_rssi_delta_param&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5a770be98d977a8df916d8cc044b310c">get_last_rssi</a> ()</td></tr>
<tr class="memdesc:a5a770be98d977a8df916d8cc044b310c"><td class="mdescLeft">&#160;</td><td class="mdescRight">provides the last rssi parameters <br /></td></tr>
<tr class="separator:a5a770be98d977a8df916d8cc044b310c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a97c80237061c6e80d7c6e1e5e45773c8"><td class="memItemLeft" align="right" valign="top"><a id="a97c80237061c6e80d7c6e1e5e45773c8"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a97c80237061c6e80d7c6e1e5e45773c8">set_rssi_callback</a> (void(*callback)(esp_bt_gap_cb_param_t::read_rssi_delta_param &amp;rssi))</td></tr>
<tr class="memdesc:a97c80237061c6e80d7c6e1e5e45773c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines the callback that is called when we get an new rssi value. <br /></td></tr>
<tr class="separator:a97c80237061c6e80d7c6e1e5e45773c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab63e627832d6377be32dd700130bf0d8"><td class="memItemLeft" align="right" valign="top"><a id="ab63e627832d6377be32dd700130bf0d8"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#ab63e627832d6377be32dd700130bf0d8">disconnect</a> ()</td></tr>
<tr class="memdesc:ab63e627832d6377be32dd700130bf0d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Closes the connection. <br /></td></tr>
<tr class="separator:ab63e627832d6377be32dd700130bf0d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aca9eebdad8d5525cb3dc1406e2c455c2"><td class="memItemLeft" align="right" valign="top"><a id="aca9eebdad8d5525cb3dc1406e2c455c2"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#aca9eebdad8d5525cb3dc1406e2c455c2">reconnect</a> ()</td></tr>
<tr class="memdesc:aca9eebdad8d5525cb3dc1406e2c455c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Reconnects to the last device. <br /></td></tr>
<tr class="separator:aca9eebdad8d5525cb3dc1406e2c455c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9fdeaf51df336473114a5f8d593703b1"><td class="memItemLeft" align="right" valign="top"><a id="a9fdeaf51df336473114a5f8d593703b1"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a9fdeaf51df336473114a5f8d593703b1">set_connected</a> (bool active)</td></tr>
<tr class="memdesc:a9fdeaf51df336473114a5f8d593703b1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Calls disconnect or reconnect. <br /></td></tr>
<tr class="separator:a9fdeaf51df336473114a5f8d593703b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7757ddbf424aeb909dc952d7c40fc241"><td class="memItemLeft" align="right" valign="top"><a id="a7757ddbf424aeb909dc952d7c40fc241"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a7757ddbf424aeb909dc952d7c40fc241">set_volume_control</a> (<a class="el" href="class_a2_d_p_volume_control.html">A2DPVolumeControl</a> *ptr)</td></tr>
<tr class="memdesc:a7757ddbf424aeb909dc952d7c40fc241"><td class="mdescLeft">&#160;</td><td class="mdescRight">you can define a custom VolumeControl implementation <br /></td></tr>
<tr class="separator:a7757ddbf424aeb909dc952d7c40fc241"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74eadbd69b5c7adf1b190c7e41b75b10"><td class="memItemLeft" align="right" valign="top"><a id="a74eadbd69b5c7adf1b190c7e41b75b10"></a>
virtual esp_a2d_audio_state_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a74eadbd69b5c7adf1b190c7e41b75b10">get_audio_state</a> ()</td></tr>
<tr class="memdesc:a74eadbd69b5c7adf1b190c7e41b75b10"><td class="mdescLeft">&#160;</td><td class="mdescRight">Determine the actual audio state. <br /></td></tr>
<tr class="separator:a74eadbd69b5c7adf1b190c7e41b75b10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a513b32676d8fc248bb481180f832ef97"><td class="memItemLeft" align="right" valign="top"><a id="a513b32676d8fc248bb481180f832ef97"></a>
virtual esp_a2d_connection_state_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a513b32676d8fc248bb481180f832ef97">get_connection_state</a> ()</td></tr>
<tr class="memdesc:a513b32676d8fc248bb481180f832ef97"><td class="mdescLeft">&#160;</td><td class="mdescRight">Determine the connection state. <br /></td></tr>
<tr class="separator:a513b32676d8fc248bb481180f832ef97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa79cff78c075c9273ea2b5c03f052fcd"><td class="memItemLeft" align="right" valign="top"><a id="aa79cff78c075c9273ea2b5c03f052fcd"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#aa79cff78c075c9273ea2b5c03f052fcd">set_on_connection_state_changed</a> (void(*callBack)(esp_a2d_connection_state_t state, void *), void *obj=nullptr)</td></tr>
<tr class="memdesc:aa79cff78c075c9273ea2b5c03f052fcd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the callback that is called when the connection state is changed. <br /></td></tr>
<tr class="separator:aa79cff78c075c9273ea2b5c03f052fcd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f13ecf541393c21a5a489235bad27fb"><td class="memItemLeft" align="right" valign="top"><a id="a5f13ecf541393c21a5a489235bad27fb"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a5f13ecf541393c21a5a489235bad27fb">set_on_audio_state_changed</a> (void(*callBack)(esp_a2d_audio_state_t state, void *), void *obj=nullptr)</td></tr>
<tr class="memdesc:a5f13ecf541393c21a5a489235bad27fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set the callback that is called when the audio state is changed. <br /></td></tr>
<tr class="separator:a5f13ecf541393c21a5a489235bad27fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6601d3c57e37f77bfdd03a3ef6231e2"><td class="memItemLeft" align="right" valign="top"><a id="aa6601d3c57e37f77bfdd03a3ef6231e2"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#aa6601d3c57e37f77bfdd03a3ef6231e2">debounce</a> (void(*cb)(void), int ms)</td></tr>
<tr class="memdesc:aa6601d3c57e37f77bfdd03a3ef6231e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Prevents that the same method is executed multiple times within the indicated time limit. <br /></td></tr>
<tr class="separator:aa6601d3c57e37f77bfdd03a3ef6231e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a791432e5c800e75fb11b858071cff651"><td class="memItemLeft" align="right" valign="top"><a id="a791432e5c800e75fb11b858071cff651"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a791432e5c800e75fb11b858071cff651">log_free_heap</a> ()</td></tr>
<tr class="memdesc:a791432e5c800e75fb11b858071cff651"><td class="mdescLeft">&#160;</td><td class="mdescRight">Logs the free heap. <br /></td></tr>
<tr class="separator:a791432e5c800e75fb11b858071cff651"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2b78346084e12feeea035d006e7cf07a"><td class="memItemLeft" align="right" valign="top"><a id="a2b78346084e12feeea035d006e7cf07a"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a2b78346084e12feeea035d006e7cf07a">to_str</a> (esp_a2d_connection_state_t state)</td></tr>
<tr class="memdesc:a2b78346084e12feeea035d006e7cf07a"><td class="mdescLeft">&#160;</td><td class="mdescRight">converts esp_a2d_connection_state_t to a string <br /></td></tr>
<tr class="separator:a2b78346084e12feeea035d006e7cf07a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38d70707790dec91d63da2006f2ff17a"><td class="memItemLeft" align="right" valign="top"><a id="a38d70707790dec91d63da2006f2ff17a"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a38d70707790dec91d63da2006f2ff17a">to_str</a> (esp_a2d_audio_state_t state)</td></tr>
<tr class="memdesc:a38d70707790dec91d63da2006f2ff17a"><td class="mdescLeft">&#160;</td><td class="mdescRight">converts a esp_a2d_audio_state_t to a string <br /></td></tr>
<tr class="separator:a38d70707790dec91d63da2006f2ff17a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa76a15aa8922301e72a745b540b040c"><td class="memItemLeft" align="right" valign="top"><a id="afa76a15aa8922301e72a745b540b040c"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#afa76a15aa8922301e72a745b540b040c">to_str</a> (esp_bd_addr_t bda)</td></tr>
<tr class="memdesc:afa76a15aa8922301e72a745b540b040c"><td class="mdescLeft">&#160;</td><td class="mdescRight">converts a esp_bd_addr_t to a string - the string is 18 characters long! <br /></td></tr>
<tr class="separator:afa76a15aa8922301e72a745b540b040c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68f9e168839f0faeb72705ccabbb6b7a"><td class="memItemLeft" align="right" valign="top"><a id="a68f9e168839f0faeb72705ccabbb6b7a"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a68f9e168839f0faeb72705ccabbb6b7a">set_task_priority</a> (UBaseType_t priority)</td></tr>
<tr class="memdesc:a68f9e168839f0faeb72705ccabbb6b7a"><td class="mdescLeft">&#160;</td><td class="mdescRight">defines the task priority (the default value is configMAX_PRIORITIES - 3) <br /></td></tr>
<tr class="separator:a68f9e168839f0faeb72705ccabbb6b7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac21e1dbd2f5f475da871a7e778ba1a40"><td class="memItemLeft" align="right" valign="top"><a id="ac21e1dbd2f5f475da871a7e778ba1a40"></a>
virtual esp_bd_addr_t *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#ac21e1dbd2f5f475da871a7e778ba1a40">get_last_peer_address</a> ()</td></tr>
<tr class="memdesc:ac21e1dbd2f5f475da871a7e778ba1a40"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides the address of the last device. <br /></td></tr>
<tr class="separator:ac21e1dbd2f5f475da871a7e778ba1a40"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a7501000b48fb46f9d6ae5f376d523aac"><td class="memItemLeft" align="right" valign="top"><a id="a7501000b48fb46f9d6ae5f376d523aac"></a>
virtual int&#160;</td><td class="memItemRight" valign="bottom"><b>init_bluetooth</b> ()</td></tr>
<tr class="separator:a7501000b48fb46f9d6ae5f376d523aac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2c67b61f98c7daf25f72512cf181afbc"><td class="memItemLeft" align="right" valign="top"><a id="a2c67b61f98c7daf25f72512cf181afbc"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>init_i2s</b> ()</td></tr>
<tr class="separator:a2c67b61f98c7daf25f72512cf181afbc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a998c0a26e18e423dc83bf2ed01db78f8"><td class="memItemLeft" align="right" valign="top"><a id="a998c0a26e18e423dc83bf2ed01db78f8"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_task_start_up</b> (void)</td></tr>
<tr class="separator:a998c0a26e18e423dc83bf2ed01db78f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a870834f07671aedd52f829d8455893b8"><td class="memItemLeft" align="right" valign="top"><a id="a870834f07671aedd52f829d8455893b8"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_task_shut_down</b> (void)</td></tr>
<tr class="separator:a870834f07671aedd52f829d8455893b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1386a6655efa1a4ce1bf7da5d980cc13"><td class="memItemLeft" align="right" valign="top"><a id="a1386a6655efa1a4ce1bf7da5d980cc13"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>app_send_msg</b> (<a class="el" href="structapp__msg__t.html">app_msg_t</a> *msg)</td></tr>
<tr class="separator:a1386a6655efa1a4ce1bf7da5d980cc13"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba7d5111b1cf179b8d3859bfde6e88d3"><td class="memItemLeft" align="right" valign="top"><a id="aba7d5111b1cf179b8d3859bfde6e88d3"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>app_work_dispatch</b> (app_callback_t p_cback, uint16_t event, void *p_params, int param_len)</td></tr>
<tr class="separator:aba7d5111b1cf179b8d3859bfde6e88d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2b28c1fac6b2d3fac84ca51c57bc6813"><td class="memItemLeft" align="right" valign="top"><a id="a2b28c1fac6b2d3fac84ca51c57bc6813"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_work_dispatched</b> (<a class="el" href="structapp__msg__t.html">app_msg_t</a> *msg)</td></tr>
<tr class="separator:a2b28c1fac6b2d3fac84ca51c57bc6813"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3afdd55a114e11dbf0618f0accf928aa"><td class="memItemLeft" align="right" valign="top"><a id="a3afdd55a114e11dbf0618f0accf928aa"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_alloc_meta_buffer</b> (esp_avrc_ct_cb_param_t *param)</td></tr>
<tr class="separator:a3afdd55a114e11dbf0618f0accf928aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0cb7f66ff75328b932c0351027a742aa"><td class="memItemLeft" align="right" valign="top"><a id="a0cb7f66ff75328b932c0351027a742aa"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>av_new_track</b> ()</td></tr>
<tr class="separator:a0cb7f66ff75328b932c0351027a742aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a341fb3e044fa3f25f763c1efe1c6f138"><td class="memItemLeft" align="right" valign="top"><a id="a341fb3e044fa3f25f763c1efe1c6f138"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>init_nvs</b> ()</td></tr>
<tr class="separator:a341fb3e044fa3f25f763c1efe1c6f138"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adb1442dcf53b7e8ccfd099db82ca0e60"><td class="memItemLeft" align="right" valign="top"><a id="adb1442dcf53b7e8ccfd099db82ca0e60"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>execute_avrc_command</b> (int cmd)</td></tr>
<tr class="separator:adb1442dcf53b7e8ccfd099db82ca0e60"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e9b33f70cd118d78b9d5015529c0d53"><td class="memItemLeft" align="right" valign="top"><a id="a6e9b33f70cd118d78b9d5015529c0d53"></a>
virtual const char *&#160;</td><td class="memItemRight" valign="bottom"><b>last_bda_nvs_name</b> ()</td></tr>
<tr class="separator:a6e9b33f70cd118d78b9d5015529c0d53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0ef94c2b1e035eec0162d91d72132165"><td class="memItemLeft" align="right" valign="top"><a id="a0ef94c2b1e035eec0162d91d72132165"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_reconnect</b> (esp_a2d_disc_rsn_t type)</td></tr>
<tr class="separator:a0ef94c2b1e035eec0162d91d72132165"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a361f80944f06806b7e42302f95171675"><td class="memItemLeft" align="right" valign="top">virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a361f80944f06806b7e42302f95171675">app_task_handler</a> (void *arg)</td></tr>
<tr class="separator:a361f80944f06806b7e42302f95171675"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32d94525aad2b16e301e15e17816d8ab"><td class="memItemLeft" align="right" valign="top"><a id="a32d94525aad2b16e301e15e17816d8ab"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_a2d_callback</b> (esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param)</td></tr>
<tr class="separator:a32d94525aad2b16e301e15e17816d8ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b05028880120df3089b77aa9fa05558"><td class="memItemLeft" align="right" valign="top"><a id="a8b05028880120df3089b77aa9fa05558"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_gap_callback</b> (esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t *param)</td></tr>
<tr class="separator:a8b05028880120df3089b77aa9fa05558"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a15339f3f739d3cfae3612d3469c22940"><td class="memItemLeft" align="right" valign="top"><a id="a15339f3f739d3cfae3612d3469c22940"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>app_rc_ct_callback</b> (esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param)</td></tr>
<tr class="separator:a15339f3f739d3cfae3612d3469c22940"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2cf823459de7a757d94a4ced2f375a0c"><td class="memItemLeft" align="right" valign="top"><a id="a2cf823459de7a757d94a4ced2f375a0c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>audio_data_callback</b> (const uint8_t *data, uint32_t len)</td></tr>
<tr class="separator:a2cf823459de7a757d94a4ced2f375a0c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac28af7c0b719cc34c03454df94880439"><td class="memItemLeft" align="right" valign="top"><a id="ac28af7c0b719cc34c03454df94880439"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_stack_evt</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:ac28af7c0b719cc34c03454df94880439"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46f8aeee188b3d201f1d7779876664ef"><td class="memItemLeft" align="right" valign="top"><a id="a46f8aeee188b3d201f1d7779876664ef"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_a2d_evt</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:a46f8aeee188b3d201f1d7779876664ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa1e8675ff5ee96ed74d2491d3505c37"><td class="memItemLeft" align="right" valign="top"><a id="afa1e8675ff5ee96ed74d2491d3505c37"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>av_hdl_avrc_evt</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:afa1e8675ff5ee96ed74d2491d3505c37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad52be2adb0bf68e3a1484166c4502e3f"><td class="memItemLeft" align="right" valign="top"><a id="ad52be2adb0bf68e3a1484166c4502e3f"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>handle_connection_state</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:ad52be2adb0bf68e3a1484166c4502e3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1b547f25c7fa20a4a1b4e999708c258c"><td class="memItemLeft" align="right" valign="top"><a id="a1b547f25c7fa20a4a1b4e999708c258c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>handle_audio_state</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:a1b547f25c7fa20a4a1b4e999708c258c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a718c4723206c6fdcd1649cfe2342e91a"><td class="memItemLeft" align="right" valign="top"><a id="a718c4723206c6fdcd1649cfe2342e91a"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>handle_audio_cfg</b> (uint16_t event, void *p_param)</td></tr>
<tr class="separator:a718c4723206c6fdcd1649cfe2342e91a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2347a681512b9487691e80a4d975f699"><td class="memItemLeft" align="right" valign="top"><a id="a2347a681512b9487691e80a4d975f699"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>av_notify_evt_handler</b> (uint8_t event_id, uint32_t event_parameter)</td></tr>
<tr class="separator:a2347a681512b9487691e80a4d975f699"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab59e6c716d9b5aaed69272e7d2a3d12a"><td class="memItemLeft" align="right" valign="top"><a id="ab59e6c716d9b5aaed69272e7d2a3d12a"></a>
size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ab59e6c716d9b5aaed69272e7d2a3d12a">i2s_write_data</a> (const uint8_t *data, size_t item_size)</td></tr>
<tr class="memdesc:ab59e6c716d9b5aaed69272e7d2a3d12a"><td class="mdescLeft">&#160;</td><td class="mdescRight">writes the data to i2s <br /></td></tr>
<tr class="separator:ab59e6c716d9b5aaed69272e7d2a3d12a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3771c6d300c7709f2b76ab8847574cf4"><td class="memItemLeft" align="right" valign="top"><a id="a3771c6d300c7709f2b76ab8847574cf4"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>get_last_connection</b> ()</td></tr>
<tr class="separator:a3771c6d300c7709f2b76ab8847574cf4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af3a2ee313a97da4e788ba3489847115c"><td class="memItemLeft" align="right" valign="top"><a id="af3a2ee313a97da4e788ba3489847115c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>set_last_connection</b> (esp_bd_addr_t bda)</td></tr>
<tr class="separator:af3a2ee313a97da4e788ba3489847115c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a962cc9aef396b06c7eb6f56462a743ac"><td class="memItemLeft" align="right" valign="top"><a id="a962cc9aef396b06c7eb6f56462a743ac"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>clean_last_connection</b> ()</td></tr>
<tr class="separator:a962cc9aef396b06c7eb6f56462a743ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3d74fa80f9992e6f831bd0ba4c06339"><td class="memItemLeft" align="right" valign="top"><a id="aa3d74fa80f9992e6f831bd0ba4c06339"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>connect_to_last_device</b> ()</td></tr>
<tr class="separator:aa3d74fa80f9992e6f831bd0ba4c06339"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f98f07ba4cf5962a7cdda50317b3112"><td class="memItemLeft" align="right" valign="top"><a id="a4f98f07ba4cf5962a7cdda50317b3112"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>has_last_connection</b> ()</td></tr>
<tr class="separator:a4f98f07ba4cf5962a7cdda50317b3112"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af1e2f14ddbe9266b61f5e721095c3685"><td class="memItemLeft" align="right" valign="top"><a id="af1e2f14ddbe9266b61f5e721095c3685"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>set_scan_mode_connectable</b> (bool connectable)</td></tr>
<tr class="separator:af1e2f14ddbe9266b61f5e721095c3685"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6fec0cfd3d0d9017b7ffcf82630ab89a"><td class="memItemLeft" align="right" valign="top"><a id="a6fec0cfd3d0d9017b7ffcf82630ab89a"></a>
virtual <a class="el" href="class_a2_d_p_volume_control.html">A2DPVolumeControl</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_common.html#a6fec0cfd3d0d9017b7ffcf82630ab89a">volume_control</a> ()</td></tr>
<tr class="memdesc:a6fec0cfd3d0d9017b7ffcf82630ab89a"><td class="mdescLeft">&#160;</td><td class="mdescRight">provides access to the VolumeControl object <br /></td></tr>
<tr class="separator:a6fec0cfd3d0d9017b7ffcf82630ab89a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-attribs"></a>
Protected Attributes</h2></td></tr>
<tr class="memitem:ab659a79f8359e559bd8aefb79b0f7199"><td class="memItemLeft" align="right" valign="top"><a id="ab659a79f8359e559bd8aefb79b0f7199"></a>
xQueueHandle&#160;</td><td class="memItemRight" valign="bottom"><b>app_task_queue</b> = nullptr</td></tr>
<tr class="separator:ab659a79f8359e559bd8aefb79b0f7199"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc8f09d352db2d5cb39d8843728da2db"><td class="memItemLeft" align="right" valign="top"><a id="adc8f09d352db2d5cb39d8843728da2db"></a>
xTaskHandle&#160;</td><td class="memItemRight" valign="bottom"><b>app_task_handle</b> = nullptr</td></tr>
<tr class="separator:adc8f09d352db2d5cb39d8843728da2db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeb44844c8a4aec954e91adfc4e80af4d"><td class="memItemLeft" align="right" valign="top"><a id="aeb44844c8a4aec954e91adfc4e80af4d"></a>
xTaskHandle&#160;</td><td class="memItemRight" valign="bottom"><b>s_bt_i2s_task_handle</b> = nullptr</td></tr>
<tr class="separator:aeb44844c8a4aec954e91adfc4e80af4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad5d06a4736a2decd8c44970c581e51e1"><td class="memItemLeft" align="right" valign="top"><a id="ad5d06a4736a2decd8c44970c581e51e1"></a>
RingbufHandle_t&#160;</td><td class="memItemRight" valign="bottom"><b>s_ringbuf_i2s</b> = nullptr</td></tr>
<tr class="separator:ad5d06a4736a2decd8c44970c581e51e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade4543c13b6ca2a83f0385dd08e13b5c"><td class="memItemLeft" align="right" valign="top"><a id="ade4543c13b6ca2a83f0385dd08e13b5c"></a>
i2s_config_t&#160;</td><td class="memItemRight" valign="bottom"><b>i2s_config</b></td></tr>
<tr class="separator:ade4543c13b6ca2a83f0385dd08e13b5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aef204cf6b9fd3061778cc175001c9aac"><td class="memItemLeft" align="right" valign="top"><a id="aef204cf6b9fd3061778cc175001c9aac"></a>
i2s_pin_config_t&#160;</td><td class="memItemRight" valign="bottom"><b>pin_config</b></td></tr>
<tr class="separator:aef204cf6b9fd3061778cc175001c9aac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aca6abec56c06d0376cbd91ec2f4520e7"><td class="memItemLeft" align="right" valign="top"><a id="aca6abec56c06d0376cbd91ec2f4520e7"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><b>bt_name</b> = nullptr</td></tr>
<tr class="separator:aca6abec56c06d0376cbd91ec2f4520e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad558d5c64a96982632b63103197d6927"><td class="memItemLeft" align="right" valign="top"><a id="ad558d5c64a96982632b63103197d6927"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>m_pkt_cnt</b> = 0</td></tr>
<tr class="separator:ad558d5c64a96982632b63103197d6927"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a91cc0f7e05ce24e311be3aa6281f4322"><td class="memItemLeft" align="right" valign="top"><a id="a91cc0f7e05ce24e311be3aa6281f4322"></a>
esp_a2d_mct_t&#160;</td><td class="memItemRight" valign="bottom"><b>audio_type</b></td></tr>
<tr class="separator:a91cc0f7e05ce24e311be3aa6281f4322"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0097f5adbcf09b2c857528afa568c53f"><td class="memItemLeft" align="right" valign="top"><a id="a0097f5adbcf09b2c857528afa568c53f"></a>
char&#160;</td><td class="memItemRight" valign="bottom"><b>pin_code_str</b> [20] = {0}</td></tr>
<tr class="separator:a0097f5adbcf09b2c857528afa568c53f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3046175ecb053f03df6596775ccfa560"><td class="memItemLeft" align="right" valign="top"><a id="a3046175ecb053f03df6596775ccfa560"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_i2s_output</b> = true</td></tr>
<tr class="separator:a3046175ecb053f03df6596775ccfa560"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3589ffe4b88230d5e4d38c9eae6b67e4"><td class="memItemLeft" align="right" valign="top"><a id="a3589ffe4b88230d5e4d38c9eae6b67e4"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>player_init</b> = false</td></tr>
<tr class="separator:a3589ffe4b88230d5e4d38c9eae6b67e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a70cba1227c116caeb8e444ee1d9b9cfb"><td class="memItemLeft" align="right" valign="top"><a id="a70cba1227c116caeb8e444ee1d9b9cfb"></a>
i2s_channel_t&#160;</td><td class="memItemRight" valign="bottom"><b>i2s_channels</b> = I2S_CHANNEL_STEREO</td></tr>
<tr class="separator:a70cba1227c116caeb8e444ee1d9b9cfb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aef11accb488e670e1526abac8953ca4a"><td class="memItemLeft" align="right" valign="top"><a id="aef11accb488e670e1526abac8953ca4a"></a>
i2s_port_t&#160;</td><td class="memItemRight" valign="bottom"><b>i2s_port</b> = I2S_NUM_0</td></tr>
<tr class="separator:aef11accb488e670e1526abac8953ca4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a24fb2e82758a45d60d820d8877153563"><td class="memItemLeft" align="right" valign="top"><a id="a24fb2e82758a45d60d820d8877153563"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>connection_rety_count</b> = 0</td></tr>
<tr class="separator:a24fb2e82758a45d60d820d8877153563"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aab99c6480299a2b88fa5b0e1ad01fee6"><td class="memItemLeft" align="right" valign="top"><a id="aab99c6480299a2b88fa5b0e1ad01fee6"></a>
_lock_t&#160;</td><td class="memItemRight" valign="bottom"><b>s_volume_lock</b></td></tr>
<tr class="separator:aab99c6480299a2b88fa5b0e1ad01fee6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac450046d5182d0574059f7ddf4a5c611"><td class="memItemLeft" align="right" valign="top"><a id="ac450046d5182d0574059f7ddf4a5c611"></a>
uint8_t&#160;</td><td class="memItemRight" valign="bottom"><b>s_volume</b> = 0</td></tr>
<tr class="separator:ac450046d5182d0574059f7ddf4a5c611"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a811dc6271aff552907551c78331eea8f"><td class="memItemLeft" align="right" valign="top"><a id="a811dc6271aff552907551c78331eea8f"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>s_volume_notify</b></td></tr>
<tr class="separator:a811dc6271aff552907551c78331eea8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0b0afc8460617c2d2b23abf048fc6f0"><td class="memItemLeft" align="right" valign="top"><a id="ae0b0afc8460617c2d2b23abf048fc6f0"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>pin_code_int</b> = 0</td></tr>
<tr class="separator:ae0b0afc8460617c2d2b23abf048fc6f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a36f12322ad8e025fd100d395b687ed74"><td class="memItemLeft" align="right" valign="top"><a id="a36f12322ad8e025fd100d395b687ed74"></a>
PinCodeRequest&#160;</td><td class="memItemRight" valign="bottom"><b>pin_code_request</b> = Undefined</td></tr>
<tr class="separator:a36f12322ad8e025fd100d395b687ed74"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6283bcc3f4609807f746d06ccbfa4fac"><td class="memItemLeft" align="right" valign="top"><a id="a6283bcc3f4609807f746d06ccbfa4fac"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_pin_code_active</b> = false</td></tr>
<tr class="separator:a6283bcc3f4609807f746d06ccbfa4fac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8edf3f614972dbee071afb8eb7b2779a"><td class="memItemLeft" align="right" valign="top"><a id="a8edf3f614972dbee071afb8eb7b2779a"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>avrc_metadata_flags</b> = ESP_AVRC_MD_ATTR_TITLE | ESP_AVRC_MD_ATTR_ARTIST | ESP_AVRC_MD_ATTR_ALBUM | ESP_AVRC_MD_ATTR_TRACK_NUM | ESP_AVRC_MD_ATTR_NUM_TRACKS | ESP_AVRC_MD_ATTR_GENRE</td></tr>
<tr class="separator:a8edf3f614972dbee071afb8eb7b2779a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32e9c57e2ff3224b94dfdc69ea52ad60"><td class="memItemLeft" align="right" valign="top"><a id="a32e9c57e2ff3224b94dfdc69ea52ad60"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>bt_volumechange</b> )(int) = nullptr</td></tr>
<tr class="separator:a32e9c57e2ff3224b94dfdc69ea52ad60"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6921ec780feb37367fa69af774291a7f"><td class="memItemLeft" align="right" valign="top"><a id="a6921ec780feb37367fa69af774291a7f"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>bt_dis_connected</b> )() = nullptr</td></tr>
<tr class="separator:a6921ec780feb37367fa69af774291a7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a515acb1d23ccd8188067ee68c2539aff"><td class="memItemLeft" align="right" valign="top"><a id="a515acb1d23ccd8188067ee68c2539aff"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>bt_connected</b> )() = nullptr</td></tr>
<tr class="separator:a515acb1d23ccd8188067ee68c2539aff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa645d8bec02c53604e93b0dda6f34b15"><td class="memItemLeft" align="right" valign="top"><a id="aa645d8bec02c53604e93b0dda6f34b15"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>data_received</b> )() = nullptr</td></tr>
<tr class="separator:aa645d8bec02c53604e93b0dda6f34b15"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc8411ed83cf8a4b2a972b46eb84b7a8"><td class="memItemLeft" align="right" valign="top"><a id="adc8411ed83cf8a4b2a972b46eb84b7a8"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>stream_reader</b> )(const uint8_t *, uint32_t) = nullptr</td></tr>
<tr class="separator:adc8411ed83cf8a4b2a972b46eb84b7a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae2dd652a4ae05689eec6edc1be4c0add"><td class="memItemLeft" align="right" valign="top"><a id="ae2dd652a4ae05689eec6edc1be4c0add"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>avrc_metadata_callback</b> )(uint8_t, const uint8_t *) = nullptr</td></tr>
<tr class="separator:ae2dd652a4ae05689eec6edc1be4c0add"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac331f893e190c7167a1754c40c5a6b77"><td class="memItemLeft" align="right" valign="top"><a id="ac331f893e190c7167a1754c40c5a6b77"></a>
bool(*&#160;</td><td class="memItemRight" valign="bottom"><b>address_validator</b> )(esp_bd_addr_t remote_bda) = nullptr</td></tr>
<tr class="separator:ac331f893e190c7167a1754c40c5a6b77"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa7b11e1571b9c1bf158166ee423b178"><td class="memItemLeft" align="right" valign="top"><a id="afa7b11e1571b9c1bf158166ee423b178"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>sample_rate_callback</b> )(uint16_t rate) =nullptr</td></tr>
<tr class="separator:afa7b11e1571b9c1bf158166ee423b178"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00954e585dd50ef40719d208a972adda"><td class="memItemLeft" align="right" valign="top"><a id="a00954e585dd50ef40719d208a972adda"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>swap_left_right</b> = false</td></tr>
<tr class="separator:a00954e585dd50ef40719d208a972adda"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab90b3d60f915bab9c9d2d9f4b27e93fe"><td class="memItemLeft" align="right" valign="top"><a id="ab90b3d60f915bab9c9d2d9f4b27e93fe"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>try_reconnect_max_count</b> = AUTOCONNECT_TRY_NUM</td></tr>
<tr class="separator:ab90b3d60f915bab9c9d2d9f4b27e93fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a821a93584902ea463e3974ad594144b3"><td class="memItemLeft" align="right" valign="top"><a id="a821a93584902ea463e3974ad594144b3"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>reconnect_on_normal_disconnect</b> = false</td></tr>
<tr class="separator:a821a93584902ea463e3974ad594144b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a17ecf8ab2757f6fdab38bbae18f19941"><td class="memItemLeft" align="right" valign="top"><a id="a17ecf8ab2757f6fdab38bbae18f19941"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>end_in_progress</b> = false</td></tr>
<tr class="separator:a17ecf8ab2757f6fdab38bbae18f19941"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a77e4633ef21aecd693bb26da2fdb70"><td class="memItemLeft" align="right" valign="top"><a id="a5a77e4633ef21aecd693bb26da2fdb70"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>event_queue_size</b> = 20</td></tr>
<tr class="separator:a5a77e4633ef21aecd693bb26da2fdb70"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7c6c4fd7156175610f58ec80cc597613"><td class="memItemLeft" align="right" valign="top"><a id="a7c6c4fd7156175610f58ec80cc597613"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>event_stack_size</b> = 3072</td></tr>
<tr class="separator:a7c6c4fd7156175610f58ec80cc597613"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5a0329500ca2c846b46edaf38ff2c07a"><td class="memItemLeft" align="right" valign="top"><a id="a5a0329500ca2c846b46edaf38ff2c07a"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>i2s_stack_size</b> = 2048</td></tr>
<tr class="separator:a5a0329500ca2c846b46edaf38ff2c07a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae08f591ddf169cf7039eacf725f67a5c"><td class="memItemLeft" align="right" valign="top"><a id="ae08f591ddf169cf7039eacf725f67a5c"></a>
int&#160;</td><td class="memItemRight" valign="bottom"><b>i2s_ringbuffer_size</b> = 4 * 1024</td></tr>
<tr class="separator:ae08f591ddf169cf7039eacf725f67a5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1725eafed3ebe2412766d2cb86196133"><td class="memItemLeft" align="right" valign="top"><a id="a1725eafed3ebe2412766d2cb86196133"></a>
UBaseType_t&#160;</td><td class="memItemRight" valign="bottom"><b>i2s_task_priority</b> = configMAX_PRIORITIES - 3</td></tr>
<tr class="separator:a1725eafed3ebe2412766d2cb86196133"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acd684815c84a3d29da78377615878fd6"><td class="memItemLeft" align="right" valign="top"><a id="acd684815c84a3d29da78377615878fd6"></a>
esp_bt_gap_cb_param_t::read_rssi_delta_param&#160;</td><td class="memItemRight" valign="bottom"><b>last_rssi_delta</b></td></tr>
<tr class="separator:acd684815c84a3d29da78377615878fd6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7746332ebc078d642b043bfd8be7e6b6"><td class="memItemLeft" align="right" valign="top"><a id="a7746332ebc078d642b043bfd8be7e6b6"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>rssi_active</b> = false</td></tr>
<tr class="separator:a7746332ebc078d642b043bfd8be7e6b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a281e8176841885f19fc0ee9f3a0f119b"><td class="memItemLeft" align="right" valign="top"><a id="a281e8176841885f19fc0ee9f3a0f119b"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>rssi_callbak</b> )(esp_bt_gap_cb_param_t::read_rssi_delta_param &amp;rssi) = nullptr</td></tr>
<tr class="separator:a281e8176841885f19fc0ee9f3a0f119b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a83cff3bdeb407a6c19618ba208438604"><td class="memItemLeft" align="right" valign="top"><a id="a83cff3bdeb407a6c19618ba208438604"></a>
esp_bd_addr_t&#160;</td><td class="memItemRight" valign="bottom"><b>peer_bd_addr</b></td></tr>
<tr class="separator:a83cff3bdeb407a6c19618ba208438604"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae07c63cafc1692273d6fa3758bd37678"><td class="memItemLeft" align="right" valign="top"><a id="ae07c63cafc1692273d6fa3758bd37678"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_auto_reconnect</b> =true</td></tr>
<tr class="separator:ae07c63cafc1692273d6fa3758bd37678"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac59a58ee6e34bcf66d44b563350b01d7"><td class="memItemLeft" align="right" valign="top"><a id="ac59a58ee6e34bcf66d44b563350b01d7"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_connecting</b> = true</td></tr>
<tr class="separator:ac59a58ee6e34bcf66d44b563350b01d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad04d173227dbd77d1956747cc10837c4"><td class="memItemLeft" align="right" valign="top"><a id="ad04d173227dbd77d1956747cc10837c4"></a>
uint32_t&#160;</td><td class="memItemRight" valign="bottom"><b>debounce_ms</b> = 0</td></tr>
<tr class="separator:ad04d173227dbd77d1956747cc10837c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7d15ed8662f01083e09b8966ced8234"><td class="memItemLeft" align="right" valign="top"><a id="af7d15ed8662f01083e09b8966ced8234"></a>
<a class="el" href="class_a2_d_p_default_volume_control.html">A2DPDefaultVolumeControl</a>&#160;</td><td class="memItemRight" valign="bottom"><b>default_volume_control</b></td></tr>
<tr class="separator:af7d15ed8662f01083e09b8966ced8234"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1cb800c8abaaffe89b343b03f5fc77ef"><td class="memItemLeft" align="right" valign="top"><a id="a1cb800c8abaaffe89b343b03f5fc77ef"></a>
<a class="el" href="class_a2_d_p_volume_control.html">A2DPVolumeControl</a> *&#160;</td><td class="memItemRight" valign="bottom"><b>volume_control_ptr</b> = nullptr</td></tr>
<tr class="separator:a1cb800c8abaaffe89b343b03f5fc77ef"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add25164fa6c099827e04db70d7ab0f62"><td class="memItemLeft" align="right" valign="top"><a id="add25164fa6c099827e04db70d7ab0f62"></a>
esp_bd_addr_t&#160;</td><td class="memItemRight" valign="bottom"><b>last_connection</b> = {0,0,0,0,0,0}</td></tr>
<tr class="separator:add25164fa6c099827e04db70d7ab0f62"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44c96686a0e2d7da22805a1c9fb7ab85"><td class="memItemLeft" align="right" valign="top"><a id="a44c96686a0e2d7da22805a1c9fb7ab85"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_start_disabled</b> = false</td></tr>
<tr class="separator:a44c96686a0e2d7da22805a1c9fb7ab85"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefac3515fd23a006c95a754c7ad9ee28"><td class="memItemLeft" align="right" valign="top"><a id="aefac3515fd23a006c95a754c7ad9ee28"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>connection_state_callback</b> )(esp_a2d_connection_state_t state, void *obj) = nullptr</td></tr>
<tr class="separator:aefac3515fd23a006c95a754c7ad9ee28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa227651f633af4d364515a7691d68782"><td class="memItemLeft" align="right" valign="top"><a id="aa227651f633af4d364515a7691d68782"></a>
void(*&#160;</td><td class="memItemRight" valign="bottom"><b>audio_state_callback</b> )(esp_a2d_audio_state_t state, void *obj) = nullptr</td></tr>
<tr class="separator:aa227651f633af4d364515a7691d68782"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76c7cf9a20791dbfa9ee0117e3d42b95"><td class="memItemLeft" align="right" valign="top"><a id="a76c7cf9a20791dbfa9ee0117e3d42b95"></a>
void *&#160;</td><td class="memItemRight" valign="bottom"><b>connection_state_obj</b> = nullptr</td></tr>
<tr class="separator:a76c7cf9a20791dbfa9ee0117e3d42b95"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a430ec514ce2596d8d1f644defa2090b0"><td class="memItemLeft" align="right" valign="top"><a id="a430ec514ce2596d8d1f644defa2090b0"></a>
void *&#160;</td><td class="memItemRight" valign="bottom"><b>audio_state_obj</b> = nullptr</td></tr>
<tr class="separator:a430ec514ce2596d8d1f644defa2090b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8614127c04ef9b9ef6d0c9fa2f33d1ed"><td class="memItemLeft" align="right" valign="top"><a id="a8614127c04ef9b9ef6d0c9fa2f33d1ed"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><b>m_a2d_conn_state_str</b> [4] = {&quot;Disconnected&quot;, &quot;Connecting&quot;, &quot;Connected&quot;, &quot;Disconnecting&quot;}</td></tr>
<tr class="separator:a8614127c04ef9b9ef6d0c9fa2f33d1ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abbe69860108ab18edee3cd24fa64be53"><td class="memItemLeft" align="right" valign="top"><a id="abbe69860108ab18edee3cd24fa64be53"></a>
const char *&#160;</td><td class="memItemRight" valign="bottom"><b>m_a2d_audio_state_str</b> [3] = {&quot;Suspended&quot;, &quot;Stopped&quot;, &quot;Started&quot;}</td></tr>
<tr class="separator:abbe69860108ab18edee3cd24fa64be53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1d557628ca4cdb883fa005df2f494f32"><td class="memItemLeft" align="right" valign="top"><a id="a1d557628ca4cdb883fa005df2f494f32"></a>
esp_a2d_audio_state_t&#160;</td><td class="memItemRight" valign="bottom"><b>audio_state</b> = ESP_A2D_AUDIO_STATE_STOPPED</td></tr>
<tr class="separator:a1d557628ca4cdb883fa005df2f494f32"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac11aaa770b2754858223a4bcdae83b5b"><td class="memItemLeft" align="right" valign="top"><a id="ac11aaa770b2754858223a4bcdae83b5b"></a>
esp_a2d_connection_state_t&#160;</td><td class="memItemRight" valign="bottom"><b>connection_state</b> = ESP_A2D_CONNECTION_STATE_DISCONNECTED</td></tr>
<tr class="separator:ac11aaa770b2754858223a4bcdae83b5b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2e218a2956baea1be621e8c31d2875c8"><td class="memItemLeft" align="right" valign="top"><a id="a2e218a2956baea1be621e8c31d2875c8"></a>
UBaseType_t&#160;</td><td class="memItemRight" valign="bottom"><b>task_priority</b> = configMAX_PRIORITIES - 10</td></tr>
<tr class="separator:a2e218a2956baea1be621e8c31d2875c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf02ccde9ba626cea64870a255672dba"><td class="memItemLeft" align="right" valign="top"><a id="aaf02ccde9ba626cea64870a255672dba"></a>
uint8_t&#160;</td><td class="memItemRight" valign="bottom"><b>volume_value</b> = 0</td></tr>
<tr class="separator:aaf02ccde9ba626cea64870a255672dba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3f1abcd092657807bd4a4cab205ba88"><td class="memItemLeft" align="right" valign="top"><a id="aa3f1abcd092657807bd4a4cab205ba88"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_volume_used</b> = false</td></tr>
<tr class="separator:aa3f1abcd092657807bd4a4cab205ba88"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-static-attribs"></a>
Static Protected Attributes</h2></td></tr>
<tr class="memitem:a001de9410aa1158a8af4185af93fc75c"><td class="memItemLeft" align="right" valign="top"><a id="a001de9410aa1158a8af4185af93fc75c"></a>
static const esp_spp_mode_t&#160;</td><td class="memItemRight" valign="bottom"><b>esp_spp_mode</b> = ESP_SPP_MODE_CB</td></tr>
<tr class="separator:a001de9410aa1158a8af4185af93fc75c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="class_bluetooth_a2_d_p_sink_min_r_a_m.html" title="BluetoothA2DPSinkMinRAM. The BluetoothA2DPSink is using a separate Task with an additinal Queue to wr...">BluetoothA2DPSinkMinRAM</a>. The <a class="el" href="class_bluetooth_a2_d_p_sink.html" title="A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example https://github....">BluetoothA2DPSink</a> is using a separate Task with an additinal Queue to write the I2S data. This implementation is using the legacy logic which writes directly to I2S w/o task. This leaves more RAM available to the application. </p>
</div><h2 class="groupheader">Member Function Documentation</h2>
<a id="a361f80944f06806b7e42302f95171675"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a361f80944f06806b7e42302f95171675">&#9670;&nbsp;</a></span>app_task_handler()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPSink::app_task_handler </td>
          <td>(</td>
          <td class="paramtype">void *&#160;</td>
          <td class="paramname"><em>arg</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">protected</span><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Wrappbed methods called from callbacks </p>

</div>
</div>
<a id="a189424ab5dc8c44f00b461e9392a2ce8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a189424ab5dc8c44f00b461e9392a2ce8">&#9670;&nbsp;</a></span>start()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void BluetoothA2DPSink::start </td>
          <td>(</td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>name</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>starts the I2S bluetooth sink with the inidicated name </p>
<p>Main function to start the Bluetooth Processing </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>src/<a class="el" href="_bluetooth_a2_d_p_sink_8h_source.html">BluetoothA2DPSink.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
