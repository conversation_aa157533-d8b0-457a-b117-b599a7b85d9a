#include <ESPNowManager.h>
#include <Arduino.h>
#include <cassert>

// Test message
const char* TEST_MESSAGE = "hello esp, i from esp now";
const size_t MESSAGE_LEN = strlen(TEST_MESSAGE) + 1; // +1 for null terminator

// Test MAC addresses (use your actual device MACs)
const uint8_t NODE_A_MAC[] = {0x08, 0x3A, 0xF2, 0xB7, 0x50, 0x5C};
const uint8_t NODE_B_MAC[] = {0x50, 0x02, 0x91, 0x98, 0xFC, 0xBC};

// Flag to track message receipt
volatile bool messageReceived = false;
char receivedMessage[64] = {0};

// Callback for receiving data
void onDataReceived(const uint8_t* mac, const uint8_t* data, int len) {
    Serial.println("Data received!");
    Serial.printf("From MAC: %02X:%02X:%02X:%02X:%02X:%02X\n", 
                 mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
    Serial.printf("Data length: %d\n", len);
    
    if (len <= sizeof(receivedMessage) - 1) {
        memcpy(receivedMessage, data, len);
        receivedMessage[len] = '\0'; // Ensure null termination
        Serial.printf("Message: %s\n", receivedMessage);
        messageReceived = true;
    }
}

void testSender() {
    Serial.println("Starting sender test...");
    
    ESPNowManager espNow;
    espNow.init(true);
    
    // Print own MAC address
    uint8_t macAddr[6];
    WiFi.macAddress(macAddr);
    Serial.printf("My MAC: %02X:%02X:%02X:%02X:%02X:%02X\n", 
                 macAddr[0], macAddr[1], macAddr[2], macAddr[3], macAddr[4], macAddr[5]);
    
    // Add peer
    Serial.println("Adding peer...");
    bool peerAdded = espNow.addPeer(NODE_B_MAC);
    Serial.printf("Peer added: %s\n", peerAdded ? "true" : "false");
    
    // Send test message multiple times
    for (int i = 0; i < 5; i++) {
        Serial.printf("Sending message (attempt %d)...\n", i+1);
        bool success = espNow.send(NODE_B_MAC, 
                                  (const uint8_t*)TEST_MESSAGE, 
                                  MESSAGE_LEN);
        Serial.printf("Send result: %s\n", success ? "success" : "failed");
        delay(500);
    }
    
    Serial.println("Test message sent");
}

void testReceiver() {
    Serial.println("Starting receiver test...");
    
    // Print own MAC address
    uint8_t macAddr[6];
    WiFi.macAddress(macAddr);
    Serial.printf("My MAC: %02X:%02X:%02X:%02X:%02X:%02X\n", 
                 macAddr[0], macAddr[1], macAddr[2], macAddr[3], macAddr[4], macAddr[5]);
    
    ESPNowManager espNow;
    espNow.init(false, onDataReceived);
    
    Serial.println("Waiting for message...");
    
    // Wait for message (with timeout)
    unsigned long startTime = millis();
    while (!messageReceived && (millis() - startTime < 10000)) {
        delay(10);
        if (millis() % 1000 < 10) {
            Serial.println("Still waiting...");
        }
    }
    
    if (messageReceived) {
        Serial.println("Test message received correctly");
        Serial.printf("Received: '%s'\n", receivedMessage);
        Serial.printf("Expected: '%s'\n", TEST_MESSAGE);
        assert(strcmp(receivedMessage, TEST_MESSAGE) == 0);
    } else {
        Serial.println("No message received within timeout!");
        // Don't assert here, just report failure
    }
}

void setup() {
    Serial.begin(115200);
    delay(1000);
    Serial.println("\n\n--- ESP-NOW Communication Test ---");
    
    #ifdef IS_NODE_A
    testSender();
    #else
    testReceiver();
    #endif
}

void loop() {
    delay(1000);
}
