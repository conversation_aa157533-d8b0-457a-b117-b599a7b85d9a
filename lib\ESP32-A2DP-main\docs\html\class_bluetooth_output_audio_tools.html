<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: BluetoothOutputAudioTools Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-attribs">Protected Attributes</a> &#124;
<a href="class_bluetooth_output_audio_tools-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">BluetoothOutputAudioTools Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Output Class using AudioTools library: <a href="https://github.com/pschatzmann/arduino-audio-tools">https://github.com/pschatzmann/arduino-audio-tools</a>.  
 <a href="class_bluetooth_output_audio_tools.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_bluetooth_output_8h_source.html">BluetoothOutput.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for BluetoothOutputAudioTools:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_bluetooth_output_audio_tools.png" usemap="#BluetoothOutputAudioTools_map" alt=""/>
  <map id="BluetoothOutputAudioTools_map" name="BluetoothOutputAudioTools_map">
<area href="class_bluetooth_output.html" title="Abstract Output Class." alt="BluetoothOutput" shape="rect" coords="0,0,164,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a9384baafe5b3998c05a1202461847b93"><td class="memItemLeft" align="right" valign="top"><a id="a9384baafe5b3998c05a1202461847b93"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>begin</b> () override</td></tr>
<tr class="separator:a9384baafe5b3998c05a1202461847b93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab967150eecfa7a72a981f1c35035c0c1"><td class="memItemLeft" align="right" valign="top"><a id="ab967150eecfa7a72a981f1c35035c0c1"></a>
size_t&#160;</td><td class="memItemRight" valign="bottom"><b>write</b> (const uint8_t *data, size_t len) override</td></tr>
<tr class="separator:ab967150eecfa7a72a981f1c35035c0c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9b33ba22d082077f2789077f687e8f0b"><td class="memItemLeft" align="right" valign="top"><a id="a9b33ba22d082077f2789077f687e8f0b"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>end</b> () override</td></tr>
<tr class="separator:a9b33ba22d082077f2789077f687e8f0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab43925198b921262791efc1ca78901f3"><td class="memItemLeft" align="right" valign="top"><a id="ab43925198b921262791efc1ca78901f3"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_sample_rate</b> (int rate) override</td></tr>
<tr class="separator:ab43925198b921262791efc1ca78901f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c25ff803256b54692355ba127d2177e"><td class="memItemLeft" align="right" valign="top"><a id="a8c25ff803256b54692355ba127d2177e"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_output_active</b> (bool active) override</td></tr>
<tr class="separator:a8c25ff803256b54692355ba127d2177e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f20c3d9928b746ac108c97390c70b71"><td class="memItemLeft" align="right" valign="top"><a id="a9f20c3d9928b746ac108c97390c70b71"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>operator bool</b> ()</td></tr>
<tr class="separator:a9f20c3d9928b746ac108c97390c70b71"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e2bb1656e8e9c821678a3e0b208c88b"><td class="memItemLeft" align="right" valign="top"><a id="a6e2bb1656e8e9c821678a3e0b208c88b"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_output_audio_tools.html#a6e2bb1656e8e9c821678a3e0b208c88b">set_output</a> (AudioOutput &amp;output)</td></tr>
<tr class="memdesc:a6e2bb1656e8e9c821678a3e0b208c88b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output AudioStream using AudioTools library. <br /></td></tr>
<tr class="separator:a6e2bb1656e8e9c821678a3e0b208c88b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abd423c533f3ac76390d5360121833daf"><td class="memItemLeft" align="right" valign="top"><a id="abd423c533f3ac76390d5360121833daf"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_output_audio_tools.html#abd423c533f3ac76390d5360121833daf">set_output</a> (AudioStream &amp;output)</td></tr>
<tr class="memdesc:abd423c533f3ac76390d5360121833daf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output AudioStream using AudioTools library. <br /></td></tr>
<tr class="separator:abd423c533f3ac76390d5360121833daf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:affe43634c04afad72609ab8a158957b1"><td class="memItemLeft" align="right" valign="top"><a id="affe43634c04afad72609ab8a158957b1"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_output_audio_tools.html#affe43634c04afad72609ab8a158957b1">set_output</a> (Print &amp;output)</td></tr>
<tr class="memdesc:affe43634c04afad72609ab8a158957b1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Output to Arduino Print. <br /></td></tr>
<tr class="separator:affe43634c04afad72609ab8a158957b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-attribs"></a>
Protected Attributes</h2></td></tr>
<tr class="memitem:af22d8b854e3894bdca2a903b9c026dc0"><td class="memItemLeft" align="right" valign="top"><a id="af22d8b854e3894bdca2a903b9c026dc0"></a>
Print *&#160;</td><td class="memItemRight" valign="bottom"><b>p_print</b> = nullptr</td></tr>
<tr class="separator:af22d8b854e3894bdca2a903b9c026dc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adddcd86cee044b4fd905a2d81e8d0b40"><td class="memItemLeft" align="right" valign="top"><a id="adddcd86cee044b4fd905a2d81e8d0b40"></a>
AudioOutput *&#160;</td><td class="memItemRight" valign="bottom"><b>p_audio_print</b> = nullptr</td></tr>
<tr class="separator:adddcd86cee044b4fd905a2d81e8d0b40"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Output Class using AudioTools library: <a href="https://github.com/pschatzmann/arduino-audio-tools">https://github.com/pschatzmann/arduino-audio-tools</a>. </p>
<dl class="section author"><dt>Author</dt><dd>Phil Schatzmann </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Apache License Version 2 </dd></dl>
</div><hr/>The documentation for this class was generated from the following files:<ul>
<li>src/<a class="el" href="_bluetooth_output_8h_source.html">BluetoothOutput.h</a></li>
<li>src/BluetoothOutput.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
