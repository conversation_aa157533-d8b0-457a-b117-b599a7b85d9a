<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: src/VolumeControl.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VolumeControl.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160; </div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment">// Licensed under the Apache License, Version 2.0 (the &quot;License&quot;);</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment">// you may not use this file except in compliance with the License.</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">// You may obtain a copy of the License at</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160; </div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment">//     http://www.apache.org/licenses/LICENSE-2.0</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment">// Unless required by applicable law or agreed to in writing, software</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment">// distributed under the License is distributed on an &quot;AS IS&quot; BASIS,</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment">// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment">// See the License for the specific language governing permissions and</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment">// limitations under the License.</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment">// Copyright 2020 Phil Schatzmann</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Copyright 2015-2016 Espressif Systems (Shanghai) PTE LTD</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160; </div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;SoundData.h&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &quot;esp_log.h&quot;</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160; </div>
<div class="line"><a name="l00027"></a><span class="lineno"><a class="line" href="class_volume_control.html">   27</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_volume_control.html">VolumeControl</a> {</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;    <span class="keyword">public</span>:</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;        <span class="keyword">virtual</span> <span class="keywordtype">void</span> update_audio_data(Frame* data, uint16_t frameCount, uint8_t volume, <span class="keywordtype">bool</span> mono_downmix, <span class="keywordtype">bool</span> is_volume_used) {</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;            <span class="keywordflow">if</span> (data!=<span class="keyword">nullptr</span> &amp;&amp; frameCount&gt;0 &amp;&amp; ( mono_downmix || is_volume_used)) {</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;                ESP_LOGD(<span class="stringliteral">&quot;VolumeControl&quot;</span>, <span class="stringliteral">&quot;update_audio_data&quot;</span>);</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;                int32_t volumeFactor = get_volume_factor(volume);</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;                int32_t max = get_volume_factor_max();</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;                <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i=0;i&lt;frameCount;i++){</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;                    int32_t pcmLeft = data[i].channel1;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;                    int32_t pcmRight = data[i].channel2;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;                    <span class="comment">// if mono -&gt; we provide the same output on both channels</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;                    <span class="keywordflow">if</span> (mono_downmix) {</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;                        pcmRight = pcmLeft = (pcmLeft + pcmRight) / 2;</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;                    }</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;                    <span class="comment">// adjust the volume</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;                    <span class="keywordflow">if</span> (is_volume_used) {</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;                        pcmLeft = pcmLeft * volumeFactor / max; </div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;                        pcmRight = pcmRight * volumeFactor / max; </div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;                    }</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;                    data[i].channel1 = pcmLeft;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;                    data[i].channel2 = pcmRight;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;                }</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;            }</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        }</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160; </div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;        <span class="comment">// provides a factor in the range of 0 to 4096</span></div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;        <span class="keyword">virtual</span> int32_t get_volume_factor(uint8_t volume) = 0;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160; </div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;        <span class="comment">// provides the max factor value 4096</span></div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;        <span class="keyword">virtual</span> int32_t get_volume_factor_max() {</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;            <span class="keywordflow">return</span> 0x1000;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        }</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;};</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160; </div>
<div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="class_default_volume_control.html">   66</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_default_volume_control.html">DefaultVolumeControl</a> : <span class="keyword">public</span> <a class="code" href="class_volume_control.html">VolumeControl</a> {</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        <span class="comment">// provides a factor in the range of 0 to 4096</span></div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        <span class="keyword">virtual</span> int32_t get_volume_factor(uint8_t volume) {</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;            constexpr <span class="keywordtype">double</span> base = 1.4;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;            constexpr <span class="keywordtype">double</span> bits = 12;</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;            constexpr <span class="keywordtype">double</span> zero_ofs = pow(base, -bits);</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;            constexpr <span class="keywordtype">double</span> scale = pow(2.0, bits);</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;            <span class="keywordtype">double</span> volumeFactorFloat = (pow(base, volume * bits / 127.0 - bits) - zero_ofs) * scale / (1.0 - zero_ofs);</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;            int32_t volumeFactor = volumeFactorFloat;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;            <span class="keywordflow">if</span> (volumeFactor &gt; 0x1000) {</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;                volumeFactor = 0x1000;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;            }</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;            <span class="keywordflow">return</span> volumeFactor;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;        }</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160; </div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;};</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160; </div>
<div class="line"><a name="l00087"></a><span class="lineno"><a class="line" href="class_simple_exponential_volume_control.html">   87</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_simple_exponential_volume_control.html">SimpleExponentialVolumeControl</a> : <span class="keyword">public</span> <a class="code" href="class_volume_control.html">VolumeControl</a> {</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;        <span class="comment">// provides a factor in the range of 0 to 4096</span></div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;        <span class="keyword">virtual</span> int32_t get_volume_factor(uint8_t volume) {</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;            <span class="keywordtype">double</span> volumeFactorFloat = volume;</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;            volumeFactorFloat = pow(2.0, volumeFactorFloat * 12.0 / 127.0);</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;            int32_t volumeFactor = volumeFactorFloat - 1.0;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;            <span class="keywordflow">if</span> (volumeFactor &gt; 0xfff) {</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;                volumeFactor = 0xfff;</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;            }</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;            <span class="keywordflow">return</span> volumeFactor;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        }</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160; </div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;};</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160; </div>
<div class="line"><a name="l00106"></a><span class="lineno"><a class="line" href="class_linear_volume_control.html">  106</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_linear_volume_control.html">LinearVolumeControl</a> : <span class="keyword">public</span> <a class="code" href="class_volume_control.html">VolumeControl</a> {</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;        <span class="comment">// provides a factor in the range of 0 to 4096</span></div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;        <span class="keyword">virtual</span> int32_t get_volume_factor(uint8_t volume) {</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;            <span class="keywordflow">return</span> volume;</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        }</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;        <span class="keyword">virtual</span> int32_t get_volume_factor_max() {</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;            <span class="keywordflow">return</span> 128;</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;        }</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;};</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160; </div>
<div class="line"><a name="l00121"></a><span class="lineno"><a class="line" href="class_no_volume_control.html">  121</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_no_volume_control.html">NoVolumeControl</a> : <span class="keyword">public</span> <a class="code" href="class_volume_control.html">VolumeControl</a> {</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;    <span class="keyword">public</span>:</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;        <span class="keyword">virtual</span> <span class="keywordtype">void</span> update_audio_data(Frame* data, uint16_t frameCount, uint8_t volume, <span class="keywordtype">bool</span> mono_downmix, <span class="keywordtype">bool</span> ivolume_used) {</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;        }</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;};</div>
<div class="ttc" id="aclass_default_volume_control_html"><div class="ttname"><a href="class_default_volume_control.html">DefaultVolumeControl</a></div><div class="ttdoc">Default implementation for handling of the volume of the audio data.</div><div class="ttdef"><b>Definition:</b> VolumeControl.h:66</div></div>
<div class="ttc" id="aclass_linear_volume_control_html"><div class="ttname"><a href="class_linear_volume_control.html">LinearVolumeControl</a></div><div class="ttdoc">The simplest possible implementation of a VolumeControl.</div><div class="ttdef"><b>Definition:</b> VolumeControl.h:106</div></div>
<div class="ttc" id="aclass_no_volume_control_html"><div class="ttname"><a href="class_no_volume_control.html">NoVolumeControl</a></div><div class="ttdoc">Keeps the audio data as is -&gt; no volume control!</div><div class="ttdef"><b>Definition:</b> VolumeControl.h:121</div></div>
<div class="ttc" id="aclass_simple_exponential_volume_control_html"><div class="ttname"><a href="class_simple_exponential_volume_control.html">SimpleExponentialVolumeControl</a></div><div class="ttdoc">Exponentional volume control.</div><div class="ttdef"><b>Definition:</b> VolumeControl.h:87</div></div>
<div class="ttc" id="aclass_volume_control_html"><div class="ttname"><a href="class_volume_control.html">VolumeControl</a></div><div class="ttdoc">Abstract class for handling of the volume of the audio data.</div><div class="ttdef"><b>Definition:</b> VolumeControl.h:27</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
