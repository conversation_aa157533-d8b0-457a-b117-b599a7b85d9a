<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en-US">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.6"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: app_msg_t Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr id="projectrow">
  <td id="projectalign">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.6 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search/",'.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<div id="MSearchResults">
<div class="SRPage">
<div id="SRIndex">
<div id="SRResults"></div>
<div class="SRStatus" id="Loading">Loading...</div>
<div class="SRStatus" id="Searching">Searching...</div>
<div class="SRStatus" id="NoMatches">No Matches</div>
</div>
</div>
</div>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structapp__msg__t-members.html">List of all members</a>  </div>
  <div class="headertitle"><div class="title">app_msg_t Struct Reference</div></div>
</div><!--header-->
<div class="contents">

<p>Internal message to be sent for <a class="el" href="class_bluetooth_a2_d_p_sink.html" title="A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example https://github....">BluetoothA2DPSink</a> and <a class="el" href="class_bluetooth_a2_d_p_source.html" title="A2DP Bluetooth Source.">BluetoothA2DPSource</a>.  
 <a href="structapp__msg__t.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_bluetooth_a2_d_p_common_8h_source.html">BluetoothA2DPCommon.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a id="pub-attribs" name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:aa9e22bf7fadbb972d0ea4e8841565d36"><td class="memItemLeft" align="right" valign="top">uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structapp__msg__t.html#aa9e22bf7fadbb972d0ea4e8841565d36">sig</a></td></tr>
<tr class="separator:aa9e22bf7fadbb972d0ea4e8841565d36"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a94101fa8211369ac8234e5052922bf1d"><td class="memItemLeft" align="right" valign="top">uint16_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structapp__msg__t.html#a94101fa8211369ac8234e5052922bf1d">event</a></td></tr>
<tr class="separator:a94101fa8211369ac8234e5052922bf1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a178b4f47f5dec620dd55568a112bed5c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="_bluetooth_a2_d_p_common_8h.html#a9bee258e477be3c0e70d6029ed86a019">app_callback_t</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structapp__msg__t.html#a178b4f47f5dec620dd55568a112bed5c">cb</a></td></tr>
<tr class="separator:a178b4f47f5dec620dd55568a112bed5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad3a963624af293f30fb8125e7b5835b6"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structapp__msg__t.html#ad3a963624af293f30fb8125e7b5835b6">param</a></td></tr>
<tr class="separator:ad3a963624af293f30fb8125e7b5835b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Internal message to be sent for <a class="el" href="class_bluetooth_a2_d_p_sink.html" title="A2DP Bluethooth Sink - We initialize and start the Bluetooth A2DP Sink. The example https://github....">BluetoothA2DPSink</a> and <a class="el" href="class_bluetooth_a2_d_p_source.html" title="A2DP Bluetooth Source.">BluetoothA2DPSource</a>. </p>
</div><h2 class="groupheader">Member Data Documentation</h2>
<a id="a178b4f47f5dec620dd55568a112bed5c" name="a178b4f47f5dec620dd55568a112bed5c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a178b4f47f5dec620dd55568a112bed5c">&#9670;&#160;</a></span>cb</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="_bluetooth_a2_d_p_common_8h.html#a9bee258e477be3c0e70d6029ed86a019">app_callback_t</a> app_msg_t::cb</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>context switch callback </p>

</div>
</div>
<a id="a94101fa8211369ac8234e5052922bf1d" name="a94101fa8211369ac8234e5052922bf1d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a94101fa8211369ac8234e5052922bf1d">&#9670;&#160;</a></span>event</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t app_msg_t::event</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>message event id </p>

</div>
</div>
<a id="ad3a963624af293f30fb8125e7b5835b6" name="ad3a963624af293f30fb8125e7b5835b6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad3a963624af293f30fb8125e7b5835b6">&#9670;&#160;</a></span>param</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* app_msg_t::param</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>parameter area needs to be last </p>

</div>
</div>
<a id="aa9e22bf7fadbb972d0ea4e8841565d36" name="aa9e22bf7fadbb972d0ea4e8841565d36"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa9e22bf7fadbb972d0ea4e8841565d36">&#9670;&#160;</a></span>sig</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t app_msg_t::sig</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>signal to app_task </p>

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li>src/<a class="el" href="_bluetooth_a2_d_p_common_8h_source.html">BluetoothA2DPCommon.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.6
</small></address>
</body>
</html>
