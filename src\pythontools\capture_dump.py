import serial
import time
import numpy as np
import soundfile as sf
from datetime import datetime
import os
import json

def create_python_array_file(samples, filename, sample_rate, timestamp):
    """
    Erstellt eine Python-Datei mit den I2S-Daten als Array-Definition
    """
    with open(filename, "w") as f:
        f.write("# I2S Audio Data Array\n")
        f.write(f"# Generated from ESP32 NodeA I2S output\n")
        f.write(f"# Captured on: {timestamp}\n")
        f.write("# Data format: signed 16-bit (int16_t)\n")
        f.write(f"# Sample count: {len(samples)}\n")
        f.write(f"# Sample rate: {sample_rate} Hz\n")
        f.write(f"# Duration: {len(samples)/sample_rate*1000:.2f} ms\n\n")

        f.write("import numpy as np\n\n")

        # Array als Python Liste
        f.write("# I2S data as Python list (signed 16-bit values)\n")
        f.write("i2s_data_list = [\n")

        # Schreibe Daten in 16er-Gruppen für bessere Lesbarkeit
        for i in range(0, len(samples), 16):
            chunk = samples[i:i+16]
            line = "    " + ", ".join(f"{sample:6d}" for sample in chunk)
            if i + 16 < len(samples):
                line += ","
            f.write(line + "\n")

        f.write("]\n\n")

        # Array als NumPy Array
        f.write("# I2S data as NumPy array (int16)\n")
        f.write("i2s_data_numpy = np.array(i2s_data_list, dtype=np.int16)\n\n")

        # Zusätzliche Informationen
        f.write("# Array properties\n")
        f.write(f"SAMPLE_COUNT = {len(samples)}\n")
        f.write(f"SAMPLE_RATE = {sample_rate}\n")
        f.write(f"DURATION_MS = {len(samples)/sample_rate*1000:.2f}\n")
        f.write(f"DATA_TYPE = 'int16'  # signed 16-bit\n")
        f.write(f"VALUE_RANGE = (-32768, 32767)  # signed 16-bit range\n")
        f.write(f"CAPTURE_TIMESTAMP = '{timestamp}'\n\n")

        # Statistiken
        f.write("# Data statistics\n")
        f.write(f"MIN_VALUE = {min(samples)}\n")
        f.write(f"MAX_VALUE = {max(samples)}\n")
        f.write(f"MEAN_VALUE = {np.mean(samples):.2f}\n")
        f.write(f"RMS_VALUE = {np.sqrt(np.mean(np.array(samples)**2)):.2f}\n\n")

        # Verwendungsbeispiel
        f.write('# Usage example:\n')
        f.write(f'# from i2s_data_{timestamp} import i2s_data_numpy, SAMPLE_RATE\n')
        f.write('# import soundfile as sf\n')
        f.write('# sf.write("reconstructed.wav", i2s_data_numpy, SAMPLE_RATE)\n')

def create_json_array_file(samples, filename):
    """
    Erstellt eine JSON-Datei mit nur den I2S-Daten als Array
    """
    data = {
        "i2s_data_list": samples
    }

    with open(filename, "w") as f:
        json.dump(data, f, indent=2)

def capture_and_create_wav():
    """Capture dump data from NodeA and directly create WAV file"""
    
    print("🎵 NodeA Chirp Capture & WAV Creator")
    print("=" * 40)
    
    # Connect to NodeA
    print("📡 Connecting to NodeA on COM3...")
    ser = serial.Serial('COM3', 115200, timeout=5)
    time.sleep(2)
    
    # Send dump command
    print("📤 Sending 'dump' command...")
    ser.write(b'd\n')
    ser.flush()
    
    # Capture sample data directly to memory
    print("🎯 Capturing sample data...")
    samples = []
    capturing_data = False
    
    try:
        while True:
            line = ser.readline().decode(errors="ignore").strip()
            
            if line:
                print(f"📥 {line}")  # Show progress
                
                # Check if dump data starts
                if "--- DATA START ---" in line:
                    print("✅ Found data start, capturing samples...")
                    capturing_data = True
                    continue
                
                # Check if dump data ends
                if "--- DATA END ---" in line:
                    print(f"✅ Data capture complete! Got {len(samples)} samples")
                    break
                
                # Capture sample data (only numeric lines)
                if capturing_data and line.lstrip('-').isdigit():
                    samples.append(int(line))
                    
                    if len(samples) % 500 == 0:
                        print(f"📊 Captured {len(samples)} samples...")
    
    except Exception as e:
        print(f"❌ Error during capture: {e}")
        return False
    finally:
        ser.close()
        print("🔌 Serial connection closed")
    
    # Check if we got data
    if not samples:
        print("❌ No sample data captured!")
        return False
    
    # Convert to numpy array
    print("🔄 Converting to audio format...")
    signal = np.array(samples, dtype=np.int16)
    
    # Create files with timestamp in the same directory as this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    wav_filename = os.path.join(script_dir, f"chirp_{timestamp}.wav")
    array_filename = os.path.join(script_dir, f"i2s_data_{timestamp}.py")
    json_filename = os.path.join(script_dir, "i2s_data_list.json")

    print(f"💾 Creating WAV file: {wav_filename}")
    print(f"🐍 Creating Python array file: {array_filename}")
    print(f"📄 Creating JSON array file: {json_filename}")

    # WAV parameters (matching NodeA configuration)
    sample_rate = 48000  # 48 kHz

    try:
        # Create WAV file
        sf.write(wav_filename, signal, samplerate=sample_rate, subtype='PCM_16')
        print(f"✅ WAV file created successfully!")

        # Create Python array file
        create_python_array_file(samples, array_filename, sample_rate, timestamp)
        print(f"✅ Python array file created successfully!")

        # Create JSON array file
        create_json_array_file(samples, json_filename)
        print(f"✅ JSON array file created successfully!")

        print(f"📁 WAV path: {wav_filename}")
        print(f"📁 Array path: {array_filename}")
        print(f"📁 JSON path: {json_filename}")
        print(f"📊 Samples: {len(samples)}")
        print(f"⏱️  Duration: {len(samples)/sample_rate:.3f} seconds")
        print(f"🎵 Sample Rate: {sample_rate} Hz")
        return True

    except Exception as e:
        print(f"❌ Error creating files: {e}")
        return False

if __name__ == "__main__":
    try:
        success = capture_and_create_wav()
        if success:
            print("\n🎉 Process completed successfully!")
        else:
            print("\n❌ Process failed!")
    except KeyboardInterrupt:
        print("\n⏹️  Process interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
    
    print("🏁 Exiting...")
    exit()
