################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
CMD_SRCS += \
../cc13x2_cc26x2_nortos.cmd 

SYSCFG_SRCS += \
../Soundpositioning.syscfg 

C_SRCS += \
../ADC.c \
../BatteryVoltage.c \
../ControlMobile.c \
../ControlX0.c \
../DeviceType.c \
../RFCommunication.c \
../RFQueue.c \
../Serial.c \
../SoundReceive.c \
../SoundTransmit.c \
./syscfg/ti_devices_config.c \
./syscfg/ti_radio_config.c \
./syscfg/ti_drivers_config.c \
../Temperature.c \
../gpio.c \
../main.c \
../time.c 

GEN_FILES += \
./syscfg/ti_devices_config.c \
./syscfg/ti_radio_config.c \
./syscfg/ti_drivers_config.c 

GEN_MISC_DIRS += \
./syscfg/ 

C_DEPS += \
./ADC.d \
./BatteryVoltage.d \
./ControlMobile.d \
./ControlX0.d \
./DeviceType.d \
./RFCommunication.d \
./RFQueue.d \
./Serial.d \
./SoundReceive.d \
./SoundTransmit.d \
./syscfg/ti_devices_config.d \
./syscfg/ti_radio_config.d \
./syscfg/ti_drivers_config.d \
./Temperature.d \
./gpio.d \
./main.d \
./time.d 

OBJS += \
./ADC.obj \
./BatteryVoltage.obj \
./ControlMobile.obj \
./ControlX0.obj \
./DeviceType.obj \
./RFCommunication.obj \
./RFQueue.obj \
./Serial.obj \
./SoundReceive.obj \
./SoundTransmit.obj \
./syscfg/ti_devices_config.obj \
./syscfg/ti_radio_config.obj \
./syscfg/ti_drivers_config.obj \
./Temperature.obj \
./gpio.obj \
./main.obj \
./time.obj 

GEN_MISC_FILES += \
./syscfg/ti_radio_config.h \
./syscfg/ti_drivers_config.h \
./syscfg/ti_utils_build_linker.cmd.genlibs \
./syscfg/syscfg_c.rov.xs \
./syscfg/ti_utils_runtime_model.gv \
./syscfg/ti_utils_runtime_Makefile 

GEN_MISC_DIRS__QUOTED += \
"syscfg\" 

OBJS__QUOTED += \
"ADC.obj" \
"BatteryVoltage.obj" \
"ControlMobile.obj" \
"ControlX0.obj" \
"DeviceType.obj" \
"RFCommunication.obj" \
"RFQueue.obj" \
"Serial.obj" \
"SoundReceive.obj" \
"SoundTransmit.obj" \
"syscfg\ti_devices_config.obj" \
"syscfg\ti_radio_config.obj" \
"syscfg\ti_drivers_config.obj" \
"Temperature.obj" \
"gpio.obj" \
"main.obj" \
"time.obj" 

GEN_MISC_FILES__QUOTED += \
"syscfg\ti_radio_config.h" \
"syscfg\ti_drivers_config.h" \
"syscfg\ti_utils_build_linker.cmd.genlibs" \
"syscfg\syscfg_c.rov.xs" \
"syscfg\ti_utils_runtime_model.gv" \
"syscfg\ti_utils_runtime_Makefile" 

C_DEPS__QUOTED += \
"ADC.d" \
"BatteryVoltage.d" \
"ControlMobile.d" \
"ControlX0.d" \
"DeviceType.d" \
"RFCommunication.d" \
"RFQueue.d" \
"Serial.d" \
"SoundReceive.d" \
"SoundTransmit.d" \
"syscfg\ti_devices_config.d" \
"syscfg\ti_radio_config.d" \
"syscfg\ti_drivers_config.d" \
"Temperature.d" \
"gpio.d" \
"main.d" \
"time.d" 

GEN_FILES__QUOTED += \
"syscfg\ti_devices_config.c" \
"syscfg\ti_radio_config.c" \
"syscfg\ti_drivers_config.c" 

C_SRCS__QUOTED += \
"../ADC.c" \
"../BatteryVoltage.c" \
"../ControlMobile.c" \
"../ControlX0.c" \
"../DeviceType.c" \
"../RFCommunication.c" \
"../RFQueue.c" \
"../Serial.c" \
"../SoundReceive.c" \
"../SoundTransmit.c" \
"./syscfg/ti_devices_config.c" \
"./syscfg/ti_radio_config.c" \
"./syscfg/ti_drivers_config.c" \
"../Temperature.c" \
"../gpio.c" \
"../main.c" \
"../time.c" 

SYSCFG_SRCS__QUOTED += \
"../Soundpositioning.syscfg" 


