digraph Model
{
    graph[rankdir=LR]

    // mod.$name=/ti/common/multi_stack_validate

    // mod.$name=/ti/devices/CCFG
    ti_devices_CCFG -> ti_devices_CCFGTemplate0

    // mod.$name=/ti/devices/CCFGTemplate

    // mod.$name=/ti/devices/DriverLib

    // mod.$name=/ti/devices/radioconfig/code_export_param

    // mod.$name=/ti/devices/radioconfig/custom
    ti_devices_radioconfig_custom -> ti_common_multi_stack_validate
    ti_devices_radioconfig_custom -> ti_devices_radioconfig_settings_prop0

    // mod.$name=/ti/devices/radioconfig/rfdesign

    // mod.$name=/ti/devices/radioconfig/settings/prop
    ti_devices_radioconfig_settings_prop0 -> ti_drivers_RF
    ti_devices_radioconfig_settings_prop0 -> ti_devices_radioconfig_rfdesign
    ti_devices_radioconfig_settings_prop0 -> ti_devices_radioconfig_code_export_param0

    // mod.$name=/ti/drivers/ADCBuf
    ADCBUF -> ti_drivers_DMA
    ADCBUF -> ti_drivers_Power
    ADCBUF -> ti_drivers_Board
    ADCBUF -> CONFIG_GPTIMER_0
    ADCBUF -> ADCBUF_SOUND
    ADCBUF -> ADCBUF_TEMPERATURE
    ADCBUF -> ADCBUF_BATTERY_VOLTAGE

    // mod.$name=/ti/drivers/Board
    ti_drivers_Board -> ti_devices_DriverLib

    // mod.$name=/ti/drivers/DMA
    ti_drivers_DMA -> ti_drivers_Board

    // mod.$name=/ti/drivers/PIN
    ti_drivers_PIN -> ti_drivers_Power
    ti_drivers_PIN -> ti_drivers_Board

    // mod.$name=/ti/drivers/Power
    ti_drivers_Power -> ti_devices_CCFG
    ti_drivers_Power -> ti_drivers_Board

    // mod.$name=/ti/drivers/RF
    ti_drivers_RF -> ti_drivers_Temperature
    ti_drivers_RF -> ti_drivers_Power
    ti_drivers_RF -> ti_drivers_Board

    // mod.$name=/ti/drivers/RTOS

    // mod.$name=/ti/drivers/Temperature

    // mod.$name=/ti/drivers/UART
    UART_0 -> ti_drivers_Power
    UART_0 -> ti_drivers_Board
    UART_0 -> CONFIG_PIN_5
    UART_0 -> CONFIG_PIN_6

    // mod.$name=/ti/drivers/adcbuf/ADCBufChanCC26XX
    ADCBUF_SOUND -> CONFIG_PIN_0
    ADCBUF_TEMPERATURE -> CONFIG_PIN_1
    ADCBUF_BATTERY_VOLTAGE -> CONFIG_PIN_2

    // mod.$name=/ti/drivers/timer/GPTimerCC26XX
    CONFIG_GPTIMER_0 -> ti_drivers_Power
    CONFIG_GPTIMER_0 -> ti_drivers_Board
}
