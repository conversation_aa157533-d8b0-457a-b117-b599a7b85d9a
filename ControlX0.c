#include <ControlX0.h>
#include <Devicetype.h>
#include <stdint.h>
#include <stddef.h>
#include <stdio.h>
#include <Serial.h>
#include "time.h"
#include <SoundTransmit.h>
#include <RFCommunication.h>
#include "ti_drivers_config.h"
#include "SoundReceive.h"
#include "Temperature.h"
#include "Batteryvoltage.h"

void AlarmIfForgottenToSwitchOff(void);
void RFTransmitReceivedSound(void);

//*********************************************************************************************************
// X device controller. Acts on received RF commands.
//*********************************************************************************************************
void ControlX0_RunX(void)
{
uint16_t Distance;
while (1)
 {
  AlarmIfForgottenToSwitchOff();
  switch (RFCommunication_CheckReceivedPacket())
  {
   case eRFListenXtoM:
         {
           SoundReceive_SampleSound();
           RFCommunication_TXListenToSound(eDevMobile,eDevX);
           SoundTransmit_GenerateSound(eChirp);
           Distance=SoundReceive_CalculateDistance();
           break;
          }
   case eRFReturnResultsX:
          {
           RFCommunication_TXMeasurements(Distance,BatteryVoltage_GetMyVoltage(),Temperature_GetValueDeciDegrees());
           break;
          }
   case eRFReadSamplesX:
         {
           RFTransmitReceivedSound();
           break;
         }
   case eRFXMeasureXto0:
         {
           RFCommunication_TXListenToSound(eDev0,eDevX);
           SoundTransmit_GenerateSound(eChirp);
           break;
         }
   case  eRFListenXto0:
         {
           SoundReceive_SampleSound();
           RFCommunication_TXMeasurements(SoundReceive_CalculateDistance(),BatteryVoltage_GetMyVoltage(),Temperature_GetValueDeciDegrees());
           break;
         }
   default: {  break; }
   }
}
}

//*********************************************************************************************************
// 0 device controller. Acts on received RF commands.
//*********************************************************************************************************
void ControlX0_Run0(void)
{
uint16_t Distance;
while (1)
 {
  AlarmIfForgottenToSwitchOff();
  switch (RFCommunication_CheckReceivedPacket())
  {
   case eRFListen0toM:
         {
           SoundReceive_SampleSound();
           RFCommunication_TXListenToSound(eDevMobile,eDev0);
           SoundTransmit_GenerateSound(eChirp);
           Distance=SoundReceive_CalculateDistance();
           break;
          }

   case eRFReturnResults0:
          {
            RFCommunication_TXMeasurements(Distance,BatteryVoltage_GetMyVoltage(),Temperature_GetValueDeciDegrees());
            break;
          }
   case eRFReadSamples0:
         {   RFTransmitReceivedSound();
             break;

         }

   case eRFListen0toX:
            {
             SoundReceive_SampleSound();
             RFCommunication_TXListenToSound(eDevX,eDev0);
             SoundTransmit_GenerateSound(eChirp);
             Distance=SoundReceive_CalculateDistance();
             break;
            }
   default: {  break; }
   }
}
}

//*********************************************************************************************************
// transmit the sample array wireless
//*********************************************************************************************************
void RFTransmitReceivedSound(void)
{
 uint16_t Section;
 for (Section=0;Section<NumberOfSampleSections;Section++)
 {
     RFCommunication_TXArraySection(SoundReceive_GetSoundSampleArray(),Section );
     Time_StartSoftTimer(eTimerGeneral);
     while (!Time_IsSoftTimerExpired(eTimerGeneral,20));
 }
}

//*********************************************************************************************************
//  if not used for a while inform user ( beeping).
//*********************************************************************************************************
void AlarmIfForgottenToSwitchOff(void)
{

#define StartBeepingAfter 1000000
#define TimeBetweenBeeps 5000
static uint16_t BeepCount=0;
if(Time_IsSoftTimerExpired(eTimerRFReceived,StartBeepingAfter))  // note : timer is reset if RF communication is received
{
  // it has been a long time since RF command was send,beep every x seconds
  if  (Time_IsSoftTimerExpired(eTimerRFReceived,StartBeepingAfter+BeepCount*TimeBetweenBeeps) ) {  SoundTransmit_GenerateSound(eBeep); BeepCount++; }
} else BeepCount=0;
}
