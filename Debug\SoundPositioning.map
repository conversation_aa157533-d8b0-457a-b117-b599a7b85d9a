******************************************************************************
                  TI ARM Linker PC v20.2.4                     
******************************************************************************
>> Linked Sun Mar  7 13:41:13 2021

OUTPUT FILE NAME:   <SoundPositioning.out>
ENTRY POINT SYMBOL: "resetISR"  address: 00006af9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00058000  00007476  00050b8a  R  X
  GPRAM                 11000000   00002000  00000000  00002000  RW X
  SRAM                  20000000   00014000  000088d4  0000b72c  RW X
  LOG_DATA              90000000   00040000  00000000  00040000  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000072b8   000072b8    r-x
  00000000    00000000    000000d8   000000d8    r-- .intvecs
  000000d8    000000d8    00006d7a   00006d7a    r-x .text
  00006e54    00006e54    00000464   00000464    r-- .const
000072b8    000072b8    00000008   00000008    rw-
  000072b8    000072b8    00000008   00000008    rw- .args
000072c0    000072c0    00000160   00000160    r--
  000072c0    000072c0    00000160   00000160    r-- .cinit
00057fa8    00057fa8    00000058   00000058    r--
  00057fa8    00057fa8    00000058   00000058    r-- .ccfg
20000000    20000000    00000b90   00000000    rw-
  20000000    20000000    00000b90   00000000    rw- .data
20000c00    20000c00    000000d8   00000000    rw-
  20000c00    20000c00    000000d8   00000000    rw- .vtable_ram
20001870    20001870    00000010   00000000    rw-
  20001870    20001870    00000010   00000000    rw- .TI.bound:dmaADCPriControlTableEntry
20001a70    20001a70    0000785c   00000000    rw-
  20001a70    20001a70    00000010   00000000    rw- .TI.bound:dmaADCAltControlTableEntry
  20001a80    20001a80    00004000   00000000    rw- .sysmem
  20005a80    20005a80    0000384c   00000000    rw- .bss
20013c00    20013c00    00000400   00000000    rw-
  20013c00    20013c00    00000400   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000d8     
                  00000000    000000d8     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.intvecs)

.text      0    000000d8    00006d7a     
                  000000d8    000004b0     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_fsmActiveState)
                  00000588    00000200                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_open)
                  00000788    000001e0     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_init)
                  00000968    000001d4     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_dispatchNextCmd)
                  00000b3c    00000198     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_sleep)
                  00000cd4    00000190                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf1)
                  00000e64    0000018c                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:runCalibrateFsm)
                  00000ff0    00000174                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_open)
                  00001164    00000174                      : PowerCC26X2.oem4f (.text:Power_init)
                  000012d8    00000164                      : UARTCC26XX.oem4f (.text:UARTCC26XX_open)
                  0000143c    00000154     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_radioOpDoneCb)
                  00001590    00000154     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_hwiIntFxn)
                  000016e4    00000148     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_fsmSetupState)
                  0000182c    0000013a     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_read)
                  00001966    00000002     driverlib.lib : interrupt.obj (.text:IntDefaultHandler)
                  00001968    00000138     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_abortCmd)
                  00001aa0    00000120     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureXOSCHF)
                  00001bc0    00000114     SoundReceive.obj (.text:CalculateDistance)
                  00001cd4    00000114     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfg)
                  00001de8    00000112     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_decodeOverridePointers)
                  00001efa    00000002                               : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCCpePatchReset)
                  00001efc    00000104                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_hwiCpe0Active)
                  00002000    00000100     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convert)
                  00002100    000000f0     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlStandby)
                  000021f0    000000f0     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_fsmPowerUpState)
                  000022e0    000000ee     main.obj (.text:main)
                  000023ce    00000002     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_defaultCallback)
                  000023d0    000000e8     RFQueue.obj (.text:RFQueue_defineQueue)
                  000024b8    000000e4     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_postCmd)
                  0000259c    000000e0     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_standbyPolicy)
                  0000267c    000000e0     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_write)
                  0000275c    000000d8     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_pendCmd)
                  00002834    000000d8     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_construct)
                  0000290c    00000004     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_getResolution)
                  00002910    000000d4     WirelessCommunication.obj (.text:RF_initRXTX)
                  000029e4    000000d2     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_updatePaConfiguration)
                  00002ab6    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_destruct)
                  00002ab8    000000d0     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_swiHw)
                  00002b88    000000c0     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_releaseDependency)
                  00002c48    000000c0                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initIO)
                  00002d08    000000be     ADC.obj (.text:ReadArrayADCraw)
                  00002dc6    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:clkFxn)
                  00002dc8    000000bc     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_initRadioSetup)
                  00002e84    000000bc     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_construct)
                  00002f40    000000b8     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_setDependency)
                  00002ff8    000000b8                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initHw)
                  000030b0    000000b6     time.obj (.text:InitTimer)
                  00003166    00000002     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:emptyClockFunc)
                  00003168    000000b4     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_startup)
                  0000321c    000000ac                     : ClockPTimer_nortos.oem4f (.text:ClockP_workFuncDynamic)
                  000032c8    000000ac     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_open)
                  00003374    000000ac     driverlib.lib : setup.obj (.text:TrimAfterColdResetWakeupFromShutDown)
                  00003420    000000a4     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_ratRestartChannels)
                  000034c4    000000a0     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_pend)
                  00003564    00000004                     : PowerCC26X2_nortos.oem4f (.text:CPUwfi)
                  00003568    000000a0     SoundTransmit.obj (.text:TimerMatchCallback)
                  00003608    0000009c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_dispatch)
                  000036a4    0000009c     rtsv7M4_T_le_v4SPD16_eabi.lib : memcpy_t2.asm.obj (.text)
                  00003740    00000098     driverlib.lib : setup.obj (.text:NOROM_SetupTrimDevice)
                  000037d8    00000098     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_calculateDispatchTime)
                  00003870    00000098     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeTxFifoFlush)
                  00003908    00000096     rtsv7M4_T_le_v4SPD16_eabi.lib : ull_div_t2.asm.obj (.text)
                  0000399e    00000092     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_writeCancel)
                  00003a30    00000090     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_discardPendCmd)
                  00003ac0    00000088     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_open)
                  00003b48    00000088     driverlib.lib : osc.obj (.text:NOROM_OSCHF_AttemptToSwitchToXosc)
                  00003bd0    00000088     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_cmdDispatchTime)
                  00003c58    00000088     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf2)
                  00003ce0    00000088                      : PowerCC26X2.oem4f (.text:disableLFClockQualifiers)
                  00003d68    00000088                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:updateSubSecInc)
                  00003df0    00000084     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_issueRadioFreeCb)
                  00003e74    00000084                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_verifyGap)
                  00003ef8    00000084     ADC.obj (.text:ReadSingleValueADCmV)
                  00003f7c    00000084     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_close)
                  00004000    00000080                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_hwiFxn)
                  00004080    00000080     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_start)
                  00004100    00000080                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_construct)
                  00004180    00000080     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlSetRechargeBeforePowerDown)
                  00004200    0000007c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_ratFreeChannel)
                  0000427c    0000007a     rtsv7M4_T_le_v4SPD16_eabi.lib : memset_t2.asm.obj (.text)
                  000042f6    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:faultISR)
                  000042f8    00000078                     : ClockPTimer_nortos.oem4f (.text:ClockP_walkQueueDynamic)
                  00004370    00000074     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_resetHw)
                  000043e4    00000074                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_auxISR)
                  00004458    00000072     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_detachOverrides)
                  000044ca    00000070                               : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCAnaDivTxOverride)
                  0000453a    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:intDefaultHandler)
                  0000453c    00000070     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_getCurrentTime)
                  000045ac    0000006e                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_attachOverrides)
                  0000461a    0000006e                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_howToSchedule)
                  00004688    0000006c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipType)
                  000046f4    0000006c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_dispatchNextEvent)
                  00004760    00000068     driverlib.lib : osc.obj (.text:NOROM_OSCHF_SwitchToRcOscTurnOffXosc)
                  000047c8    00000068     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_hwiHw)
                  00004830    00000068     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_post)
                  00004898    00000068                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_start)
                  00004900    00000068     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_lzss.c.obj (.text:decompress:lzss:__TI_decompress_lzss)
                  00004968    00000064     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_control)
                  000049cc    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_isActive)
                  000049d0    00000062     ti_drivers_config.obj (.text:Board_sendExtFlashByte)
                  00004a32    00000062     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_syncCb)
                  00004a94    00000062     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_swiIntFxn)
                  00004af6    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:nmiISR)
                  00004af8    00000060     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_applyRfCorePatch)
                  00004b58    00000060     WirelessCommunication.obj (.text:RXcallback)
                  00004bb8    0000005c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_remove)
                  00004c14    0000005c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_hwiCpe0PowerFsm)
                  00004c70    0000005c     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:startTxFifoEmptyClk)
                  00004ccc    00000058                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_loadDMAControlTableEntry)
                  00004d24    00000058                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_stop)
                  00004d7c    00000058                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_initiateCalibration)
                  00004dd4    00000056     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_defaultSubmitPolicy)
                  00004e2a    00000054     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_adjustRawValues)
                  00004e7e    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : div0.asm.obj (.text)
                  00004e80    00000054     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOff)
                  00004ed4    00000054     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_fsmXOSCState)
                  00004f28    00000050     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_construct)
                  00004f78    0000004e     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_close)
                  00004fc6    00000002     --HOLE-- [fill = 0]
                  00004fc8    0000004c                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_cleanADC)
                  00005014    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_initHw)
                  00005060    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_start)
                  000050ac    0000004c     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOn)
                  000050f8    0000004c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_wakeupNotification)
                  00005144    0000004c     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readCancel)
                  00005190    0000004c                      : UDMACC26XX.oem4f (.text:UDMACC26XX_open)
                  000051dc    0000004a                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_control)
                  00005226    00000002     --HOLE-- [fill = 0]
                  00005228    0000004a     ti_drivers_config.obj (.text:Board_wakeUpExtFlash)
                  00005272    00000006     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_stop)
                  00005278    0000004a     WirelessCommunication.obj (.text:RF_TxPacketBuffer)
                  000052c2    00000002     --HOLE-- [fill = 0]
                  000052c4    00000048     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertAdjustedToMicroVolts)
                  0000530c    00000048     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_construct)
                  00005354    00000048     rf_multiMode_cc13x2.aem4f : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCSynthPowerDown)
                  0000539c    00000048     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputEnable)
                  000053e4    00000048                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_RCOSC_clockFunc)
                  0000542c    00000048                      : PowerCC26X2.oem4f (.text:PowerCC26XX_switchXOSC_HF)
                  00005474    00000048                      : PowerCC26X2.oem4f (.text:switchXOSCHF)
                  000054bc    00000048                      : UARTCC26XX.oem4f (.text:writeFinishedDoCallback)
                  00005504    00000046                      : RingBuf.oem4f (.text:RingBuf_put)
                  0000554a    00000002     --HOLE-- [fill = 0]
                  0000554c    00000044                      : ADCBuf.oem4f (.text:ADCBuf_init)
                  00005590    00000044     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetHwRevision)
                  000055d4    00000044     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_swi)
                  00005618    00000044     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_post)
                  0000565c    00000044                     : SwiP_nortos.oem4f (.text:SwiP_restore)
                  000056a0    00000044     drivers_cc13x2.a : UART.oem4f (.text:UART_init)
                  000056e4    00000044     rtsv7M4_T_le_v4SPD16_eabi.lib : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit:__TI_auto_init_nobinit_nopinit)
                  00005728    00000044     ADC.obj (.text:adcBufCallback)
                  0000576c    00000042     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_swiFxn)
                  000057ae    00000002     --HOLE-- [fill = 0]
                  000057b0    00000040                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertCancel)
                  000057f0    00000040                      : ADCBuf.oem4f (.text:ADCBuf_open)
                  00005830    00000040     driverlib.lib : osc.obj (.text:NOROM_OSC_HPOSCRelativeFrequencyOffsetGet)
                  00005870    00000040                   : timer.obj (.text:NOROM_TimerIntRegister)
                  000058b0    00000040     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:PowerCC26XX_isStableXOSC_HF)
                  000058f0    00000040                      : RingBuf.oem4f (.text:RingBuf_get)
                  00005930    00000040     time.obj (.text:Timer2AInterruptHandler)
                  00005970    00000040     drivers_cc13x2.a : UART.oem4f (.text:UART_open)
                  000059b0    00000040     rf_multiMode_cc13x2.aem4f : rf_patch_cpe_prop.c.cc13x2.ccs.o (.text:configurePropPatch)
                  000059f0    0000003e     WirelessCommunication.obj (.text:RF_StartRX)
                  00005a2e    00000002     --HOLE-- [fill = 0]
                  00005a30    0000003c     UART.obj (.text:InitUart)
                  00005a6c    0000003c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputValue)
                  00005aa8    0000003c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_cmdGet)
                  00005ae4    0000003c                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_cmdStoreEvents)
                  00005b20    00000038     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_configDMA)
                  00005b58    00000038     ti_drivers_config.obj (.text:Board_shutDownExtFlash)
                  00005b90    00000038     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setConfig)
                  00005bc8    00000038     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_checkCmdFsError)
                  00005c00    00000038                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_powerConstraintRelease)
                  00005c38    00000038     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCurrentTick)
                  00005c70    00000038                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_initDevice)
                  00005ca8    00000038     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:notify)
                  00005ce0    00000038                      : UARTCC26XX.oem4f (.text:readData)
                  00005d18    00000038                      : UARTCC26XX.oem4f (.text:writeData)
                  00005d50    00000036     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setNextTick)
                  00005d86    00000036     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_close)
                  00005dbc    00000034     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicksUntilInterrupt)
                  00005df0    00000034     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XXSetLoadMatch)
                  00005e24    00000034                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_configureDebugStall)
                  00005e58    00000034     driverlib.lib : aux_sysif.obj (.text:NOROM_AUXSYSIFOpModeChange)
                  00005e8c    00000034                   : interrupt.obj (.text:NOROM_IntRegister)
                  00005ec0    00000034                   : sys_ctrl.obj (.text:NOROM_SysCtrlIdle)
                  00005ef4    00000034     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_destruct)
                  00005f28    00000034     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:readData2RingBuf)
                  00005f5c    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_setTimeout)
                  00005f60    00000032     time.obj (.text:IsTimerExpired)
                  00005f92    00000030     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_close)
                  00005fc2    00000002     --HOLE-- [fill = 0]
                  00005fc4    00000030                      : PINCC26XX.oem4f (.text:PINCC26XX_setMux)
                  00005ff4    00000030                      : PowerCC26X2.oem4f (.text:Power_releaseConstraint)
                  00006024    00000030                      : PowerCC26X2.oem4f (.text:Power_setConstraint)
                  00006054    00000030     driverlib.lib : timer.obj (.text:TimerIntNumberGet)
                  00006084    00000030     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.text:_c_int00:_c_int00)
                  000060b4    0000002c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_acquireADCSemaphore)
                  000060e0    0000002c                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_releaseADCSemaphore)
                  0000610c    0000002c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_executeDirectImmediateCmd)
                  00006138    0000002c     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setThreshold)
                  00006164    0000002c     rf_multiMode_cc13x2.aem4f : rf_patch_cpe_prop.c.cc13x2.ccs.o (.text:applyPropPatch)
                  00006190    0000002a     drivers_cc13x2.a : List.oem4f (.text:List_insert)
                  000061ba    0000002a     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_searchAndReplacePAOverride)
                  000061e4    00000028                               : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCOverrideSearch)
                  0000620c    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_registerNotify)
                  00006234    00000028     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_calculateDeltaTimeUs)
                  0000625c    00000028                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_runCmd)
                  00006284    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:oscillatorISR)
                  000062ac    00000026                      : List.oem4f (.text:List_put)
                  000062d2    00000026                      : List.oem4f (.text:List_putHead)
                  000062f8    00000026                      : List.oem4f (.text:List_remove)
                  0000631e    00000002     --HOLE-- [fill = 0]
                  00006320    00000024     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicks)
                  00006344    00000024                     : ClockPTimer_nortos.oem4f (.text:ClockP_scheduleNextTick)
                  00006368    00000024                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_dispatch)
                  0000638c    00000024     driverlib.lib : osc.obj (.text:NOROM_OSCHF_TurnOnXosc)
                  000063b0    00000024     rf_multiMode_cc13x2.aem4f : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCDoorbellSendTo)
                  000063d4    00000024                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_cmdAlloc)
                  000063f8    00000024                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_invokeGlobalCallback)
                  0000641c    00000024     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getMaxTicks)
                  00006440    00000024     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:getTdcSemaphore)
                  00006464    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_enable)
                  00006468    00000022     ti_drivers_config.obj (.text:Board_init)
                  0000648a    00000022     drivers_cc13x2.a : List.oem4f (.text:List_get)
                  000064ac    00000020     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_add)
                  000064cc    00000020     driverlib.lib : aux_adc.obj (.text:NOROM_AUXADCEnableSync)
                  000064ec    00000020     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfgMux)
                  0000650c    00000020                      : PowerCC26X2.oem4f (.text:Power_unregisterNotify)
                  0000652c    00000020     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_put)
                  0000654c    00000020     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_clkPowerUp)
                  0000656c    00000020                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_hwiCpe0ChangePhy)
                  0000658c    00000020                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_isStateTransitionAllowed)
                  000065ac    00000020                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_ratIsRunning)
                  000065cc    00000020     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_dynamicStub)
                  000065ec    00000020     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:threadSafeStdbyDisRelease)
                  0000660c    00000020                      : UARTCC26XX.oem4f (.text:threadSafeStdbyDisSet)
                  0000662c    0000001e                      : PINCC26XX.oem4f (.text:PIN_close)
                  0000664a    0000001e                      : PowerCC26X2.oem4f (.text:Power_getTransitionLatency)
                  00006668    0000001e     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_constructBinary)
                  00006686    0000001e                     : SwiP_nortos.oem4f (.text:SwiP_or)
                  000066a4    00000004     rf_multiMode_cc13x2.aem4f : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCOverrideUpdate)
                  000066a8    0000001c     main.obj (.text:GetDeviceIndentifier)
                  000066c4    0000001c     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_destruct)
                  000066e0    0000001c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipFamily)
                  000066fc    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_hwi)
                  00006718    0000001c     WirelessCommunication.obj (.text:RF_TerminateRX)
                  00006734    0000001c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_clkInactivityCallback)
                  00006750    0000001c                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_getPAOverrideOffsetAndValue)
                  0000676c    0000001c                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_powerConstraintSet)
                  00006788    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_disable)
                  000067a4    0000001c     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_initHw)
                  000067c0    0000001a     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_setFunc)
                  000067da    00000006     driverlib.lib : cpu.obj (.text:NOROM_CPUdelay)
                  000067e0    0000001a     SoundTransmit.obj (.text:InitSound)
                  000067fa    0000001a     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:PowerCC26XX_calibrate)
                  00006814    0000001a     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_get)
                  0000682e    00000018                     : ClockPTimer_nortos.oem4f (.text:ClockP_destruct)
                  00006846    00000018                     : ClockPTimer_nortos.oem4f (.text:ClockP_getTimeout)
                  0000685e    00000002     --HOLE-- [fill = 0]
                  00006860    00000018     time.obj (.text:EnableTimerMatch)
                  00006878    00000018     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_inISR)
                  00006890    00000018     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetPackageType)
                  000068a8    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_idleFunc)
                  000068c0    00000018     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_swiFsm)
                  000068d8    00000018     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_startup)
                  000068f0    00000018     drivers_cc13x2.a : UART.oem4f (.text:UART_Params_init)
                  00006908    00000018     rtsv7M4_T_le_v4SPD16_eabi.lib : args_main.c.obj (.text:_args_main)
                  00006920    00000018                                   : ll_mul_t2.asm.obj (.text)
                  00006938    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:switchToTCXO)
                  00006950    00000016     driverlib.lib : chipinfo.obj (.text:NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated)
                  00006966    00000002     --HOLE-- [fill = 0]
                  00006968    00000016     RFQueue.obj (.text:RFQueue_nextEntry)
                  0000697e    00000016     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_restartClockTimeout)
                  00006994    00000004     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_head)
                  00006998    00000016     time.obj (.text:StartTimer)
                  000069ae    00000002     --HOLE-- [fill = 0]
                  000069b0    00000016     WirelessCommunication.obj (.text:TXMeasurements)
                  000069c6    00000016     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:maxbit)
                  000069dc    00000014     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_freqToCounts)
                  000069f0    00000014                      : ADCBuf.oem4f (.text:ADCBuf_Params_init)
                  00006a04    00000004     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_next)
                  00006a08    00000014     WirelessCommunication.obj (.text:IsListenAllReceived)
                  00006a1c    00000014     drivers_cc13x2.a : List.oem4f (.text:List_clearList)
                  00006a30    00000014     driverlib.lib : interrupt.obj (.text:NOROM_IntUnregister)
                  00006a44    00000014     rf_multiMode_cc13x2.aem4f : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCCpeIntGetAndClear)
                  00006a58    00000014     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_calibrate)
                  00006a6c    00000014                      : PowerCC26X2.oem4f (.text:Power_disablePolicy)
                  00006a80    00000014     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_Params_init)
                  00006a94    00000014                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_dbellSyncOnAck)
                  00006aa8    00000014                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_flushCmd)
                  00006abc    00000014                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_ratGetChannel)
                  00006ad0    00000014     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_handleHwi)
                  00006ae4    00000014                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCount64)
                  00006af8    00000014                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:resetISR)
                  00006b0c    00000004     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_defaultExecutionPolicy)
                  00006b10    00000012     time.obj (.text:DisableTimerMatch)
                  00006b22    00000012     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_Params_init)
                  00006b34    00000012     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_Params_init)
                  00006b46    00000012     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_construct)
                  00006b58    00000012     SoundReceive.obj (.text:SampleSound)
                  00006b6a    00000006     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_init)
                  00006b70    00000012     WirelessCommunication.obj (.text:TXListenAll)
                  00006b82    00000002     --HOLE-- [fill = 0]
                  00006b84    00000010     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_Params_init)
                  00006b94    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_getCpuFreq)
                  00006ba4    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_clearInterrupt)
                  00006bb4    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_disable)
                  00006bc4    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_enableInterrupt)
                  00006bd4    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_post)
                  00006be4    00000010     rf_multiMode_cc13x2.aem4f : rfc.c.cc13x2.ccs.o (.text:NOROM_RFCHwIntGetAndClear)
                  00006bf4    00000010     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_getPinCount)
                  00006c04    00000010     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerDisable)
                  00006c14    00000010     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_enablePolicy)
                  00006c24    00000010     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_dbellSubmitCmdAsync)
                  00006c34    00000010                               : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_isClientOwner)
                  00006c44    00000010     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_Params_init)
                  00006c54    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : pre_init.c.obj (.text:_system_pre_init)
                  00006c58    00000010     time.obj (.text:SetNextMatch)
                  00006c68    00000010     SoundTransmit.obj (.text:SoundTransmit)
                  00006c78    00000010     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_Params_init)
                  00006c88    00000010     WirelessCommunication.obj (.text:TXchar)
                  00006c98    00000010     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_Params_init)
                  00006ca8    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:uartPostNotify)
                  00006cb8    0000000e     WirelessCommunication.obj (.text:ClearPacketReceivedFlag)
                  00006cc6    0000000e     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_empty)
                  00006cd4    0000000e                     : QueueP_nortos.oem4f (.text:QueueP_remove)
                  00006ce2    0000000e     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_getSwitchingTimeInUs)
                  00006cf0    0000000e     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_none.c.obj (.text:decompress:none:__TI_decompress_none)
                  00006cfe    00000002     --HOLE-- [fill = 0]
                  00006d00    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_doTick)
                  00006d0c    0000000c                     : ClockPTimer_nortos.oem4f (.text:ClockP_getSystemTickPeriod)
                  00006d18    0000000c     WirelessCommunication.obj (.text:IsPacketReceived)
                  00006d24    0000000c     driverlib.lib : cpu.obj (.text:NOROM_CPUcpsid)
                  00006d30    0000000c                   : cpu.obj (.text:NOROM_CPUcpsie)
                  00006d3c    0000000c     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerRestore)
                  00006d48    0000000c     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_getConstraintMask)
                  00006d54    0000000c                      : PowerCC26X2.oem4f (.text:Power_getDependencyCount)
                  00006d60    0000000c     RFQueue.obj (.text:RFQueue_getDataEntry)
                  00006d6c    0000000c     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_powerConstraintGet)
                  00006d78    0000000c     time.obj (.text:SetCallbackFunctionOnMatch)
                  00006d84    0000000c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_getTrigger)
                  00006d90    0000000c                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getFreq)
                  00006d9c    0000000c     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init)
                  00006da8    0000000a     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_close)
                  00006db2    0000000a                      : ADCBuf.oem4f (.text:ADCBuf_convert)
                  00006dbc    0000000a                      : ADCBuf.oem4f (.text:ADCBuf_convertCancel)
                  00006dc6    0000000a                      : UDMACC26XX.oem4f (.text:UDMACC26XX_hwiIntFxn)
                  00006dd0    0000000a                      : UARTCC26XX.oem4f (.text:readSemCallback)
                  00006dda    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_conversionCallback)
                  00006de2    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_init)
                  00006dea    00000008                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_setLoadValue)
                  00006df2    00000008     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_restore)
                  00006dfa    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_init)
                  00006e02    00000008                      : UARTCC26XX.oem4f (.text:writeSemCallback)
                  00006e0a    00000006     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.text:RF_clkReqAccess)
                  00006e10    00000006     BatteryVoltage.obj (.text:GetBatteryVoltagePercentage)
                  00006e16    00000002     --HOLE-- [fill = 0]
                  00006e18    00000006     CalculateDistance.obj (.text:GetDistanceMM)
                  00006e1e    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readPolling)
                  00006e24    00000006                      : UARTCC26XX.oem4f (.text:UARTCC26XX_writePolling)
                  00006e2a    00000006                      : PowerCC26X2.oem4f (.text:delayUs)
                  00006e30    00000004     Temperature.obj (.text:GetTemperatureDeciDegrees)
                  00006e34    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : exit.c.obj (.text:abort:abort)
                  00006e38    00000004     WirelessCommunication.obj (.text:IsListen0Received)
                  00006e3c    00000004     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureRFCoreClocks)
                  00006e40    00000004     WirelessCommunication.obj (.text:IsMeasureX0Received)
                  00006e44    00000004     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:nopResourceHandler)
                  00006e48    00000002     ti_drivers_config.obj (.text:Board_initHook)
                  00006e4a    00000006     --HOLE-- [fill = 0]
                  00006e50    00000002     WirelessCommunication.obj (.text:TXListen0)

.const     0    00006e54    00000464     
                  00006e54    000001f0     rf_multiMode_cc13x2.aem4f : rf_patch_cpe_prop.c.cc13x2.ccs.o (.const:patchImageProp)
                  00007044    00000054     drivers_cc13x2.a : PowerCC26X2.oem4f (.const:resourceDB)
                  00007098    00000028                      : UARTCC26XX.oem4f (.const:UARTCC26XX_fxnTable)
                  000070c0    00000028     ti_drivers_config.obj (.const:uartCC26XXHWAttrs)
                  000070e8    00000024     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.const:ADCBufCC26X2_fxnTable)
                  0000710c    00000024                      : UART.oem4f (.const:UART_defaultParams)
                  00007130    00000020     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.const:RF_defaultParams)
                  00007150    0000001c     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.const:GPT_LUT)
                  0000716c    00000018                      : ADCBuf.oem4f (.const:ADCBuf_defaultParams)
                  00007184    00000018     ti_drivers_config.obj (.const:BoardGpioInitTable)
                  0000719c    00000018     ti_drivers_config.obj (.const:GPTimerCC26XX_config)
                  000071b4    00000018     ti_drivers_config.obj (.const:gptimerCC26XXHWAttrs)
                  000071cc    00000014     ti_drivers_config.obj (.const:$P$T1$2)
                  000071e0    00000014     ti_drivers_config.obj (.const:PowerCC26X2_config)
                  000071f4    00000014     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:parityType)
                  00007208    00000010     nortos_cc13x2.a : SwiP_nortos.oem4f (.const:SwiP_defaultParams)
                  00007218    00000010     ti_drivers_config.obj (.const:adcbufCC26XXHWAttrs)
                  00007228    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:dataLength)
                  00007238    0000000c     ti_drivers_config.obj (.const:ADCBuf_config)
                  00007244    0000000c     ti_drivers_config.obj (.const:RFCC26XX_hwAttrs)
                  00007250    0000000c     ti_drivers_config.obj (.const:UART_config)
                  0000725c    0000000a     ti_drivers_config.obj (.const)
                  00007266    00000002     --HOLE-- [fill = 0]
                  00007268    00000008     driverlib.lib : aux_sysif.obj (.const:$O1$$)
                  00007270    00000008     ti_drivers_config.obj (.const:$P$T0$1)
                  00007278    00000008     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.const:GPT_DefaultParams)
                  00007280    00000008     ti_drivers_config.obj (.const:PINCC26XX_hwAttrs)
                  00007288    00000008     ti_drivers_config.obj (.const:UDMACC26XX_config)
                  00007290    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:stopBits)
                  00007298    00000008     ti_drivers_config.obj (.const:udmaCC26XXHWAttrs)
                  000072a0    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:.string:rxFifoBytes)
                  000072a6    00000006                      : UARTCC26XX.oem4f (.const:.string:rxFifoThreshold)
                  000072ac    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoBytes)
                  000072b2    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoThreshold)

.binit     0    00000000    00000000     

.cinit     0    000072c0    00000160     
                  000072c0    0000010c     (.cinit..data.load) [load image, compression = lzss]
                  000073cc    0000000c     (__TI_handler_table)
                  000073d8    00000008     (.cinit..TI.bound:dmaADCAltControlTableEntry.load) [load image, compression = zero_init]
                  000073e0    00000008     (.cinit..TI.bound:dmaADCPriControlTableEntry.load) [load image, compression = zero_init]
                  000073e8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000073f0    00000008     (.cinit..vtable_ram.load) [load image, compression = zero_init]
                  000073f8    00000028     (__TI_cinit_table)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.ccfg      0    00057fa8    00000058     
                  00057fa8    00000058     ti_devices_config.obj (.ccfg:retain)

.vtable_ram 
*          0    20000c00    000000d8     UNINITIALIZED
                  20000c00    000000d8     driverlib.lib : interrupt.obj (.vtable_ram)

.data      0    20000000    00000b90     UNINITIALIZED
                  20000000    000004f8     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.data:$O1$$)
                  200004f8    00000210     SoundReceive.obj (.data:Signature)
                  20000708    00000170     drivers_cc13x2.a : PowerCC26X2.oem4f (.data:PowerCC26X2_module)
                  20000878    000000d8     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data:HwiP_dispatchTable)
                  20000950    0000007c     SoundTransmit.obj (.data:ChirpDelay)
                  200009cc    0000007c     SoundTransmit.obj (.data:ChirpState)
                  20000a48    00000028     ti_radio_config.obj (.data:RF_cmdPropRadioDivSetup)
                  20000a70    00000028     ti_radio_config.obj (.data:pOverrides)
                  20000a98    00000024     driverlib.lib : osc.obj (.data:$O1$$)
                  20000abc    00000024     ti_radio_config.obj (.data:RF_cmdPropRx)
                  20000ae0    00000018     ti_radio_config.obj (.data:RF_cmdFs)
                  20000af8    00000018     ti_radio_config.obj (.data:RF_cmdPropTx)
                  20000b10    00000010     ti_radio_config.obj (.data:RF_prop)
                  20000b20    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data:ClockP_defaultParams)
                  20000b2c    0000000c                     : TimerPCC26XX_nortos.oem4f (.data:TimerP_defaultParams)
                  20000b38    0000000c     time.obj (.data)
                  20000b44    00000008     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.data:RFCC26XX_schedulerPolicy)
                  20000b4c    00000008     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data:SemaphoreP_defaultParams)
                  20000b54    00000008     SoundTransmit.obj (.data)
                  20000b5c    00000008     WirelessCommunication.obj (.data)
                  20000b64    00000008     drivers_cc13x2.a : PINCC26XX.oem4f (.data)
                  20000b6c    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data)
                  20000b74    00000006     ti_drivers_config.obj (.data:adcBufCC26XXChannelLut0)
                  20000b7a    00000002     main.obj (.data)
                  20000b7c    00000004     ADC.obj (.data)
                  20000b80    00000004     SoundReceive.obj (.data)
                  20000b84    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data)
                  20000b88    00000003                     : SemaphoreP_nortos.oem4f (.data)
                  20000b8b    00000001     drivers_cc13x2.a : ADCBuf.oem4f (.data)
                  20000b8c    00000001                      : UART.oem4f (.data)
                  20000b8d    00000001     nortos_cc13x2.a : SwiP_nortos.oem4f (.data)
                  20000b8e    00000001                     : TimerPCC26XX_nortos.oem4f (.data)
                  20000b8f    00000001     rf_multiMode_cc13x2.aem4f : rf_patch_cpe_prop.c.cc13x2.ccs.o (.data)

.sysmem    0    20001a80    00004000     UNINITIALIZED
                  20001a80    00000010     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.sysmem)
                  20001a90    00003ff0     --HOLE--

.bss       0    20005a80    0000384c     UNINITIALIZED
                  20005a80    00002ee0     (.common:SoundSamples)
                  20008960    000001b4     (.common:uartCC26XXObjects)
                  20008b14    00000004     drivers_cc13x2.a : PINCC26XX.oem4f (.bss)
                  20008b18    000000f8     WirelessCommunication.obj (.bss:rfObject)
                  20008c10    000000dc     (.common:adcbufCC26XXbjects)
                  20008cec    000000c8     (.common:InternalADCBuffer1)
                  20008db4    000000c8     (.common:InternalADCBuffer2)
                  20008e7c    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss)
                  20008e80    000000a0                     : ClockPTimer_nortos.oem4f (.bss:ClockP_module)
                  20008f20    00000098     (.common:gptimerCC26XXObjects)
                  20008fb8    0000007c     (.common:pinHandleTable)
                  20009034    00000068     WirelessCommunication.obj (.bss:rxDataEntryBuffer)
                  2000909c    00000050     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinSemaphore)
                  200090ec    00000034     WirelessCommunication.obj (.bss:TXpacketBuffer)
                  20009120    00000034     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinSwi)
                  20009154    0000002c     WirelessCommunication.obj (.bss:RXpacket)
                  20009180    00000020     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_readyList)
                  200091a0    00000020     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinGpioConfigTable)
                  200091c0    00000020     WirelessCommunication.obj (.bss:rfParams)
                  200091e0    00000020     ti_drivers_config.obj (.bss:uartCC26XXRingBuffer0)
                  20009200    00000020     (.common:udmaCC26XXObject)
                  20009220    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_hwiStruct)
                  2000923c    0000001c                     : TimerPCC26XX_nortos.oem4f (.bss:TimerP_hwiStruct)
                  20009258    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinHwi)
                  20009274    00000014     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.bss:RF_ratSyncCmd)
                  20009288    0000000d     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss)
                  20009295    00000001     rf_multiMode_cc13x2.aem4f : RFCC26X2_multiMode.c.cc13x2.ccs.o (.bss)
                  20009296    00000002     (.common:DestinationArrayIndex)
                  20009298    0000000c     WirelessCommunication.obj (.bss)
                  200092a4    00000008     WirelessCommunication.obj (.bss:dataQueue)
                  200092ac    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:timerFreq)
                  200092b4    00000004                     : PowerCC26X2_nortos.oem4f (.bss)
                  200092b8    00000004                     : TimerPCC26XX_nortos.oem4f (.bss)
                  200092bc    00000004     (.common:BoardCalculatedCorrelation)
                  200092c0    00000004     (.common:readEntry)
                  200092c4    00000004     (.common:uart)
                  200092c8    00000002     (.common:DestinationArraySize)
                  200092ca    00000001     (.common:RXpacketLength)
                  200092cb    00000001     (.common:driverlib_release_0_59848)

.stack     0    20013c00    00000400     UNINITIALIZED
                  20013c00    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.stack)
                  20013c04    000003fc     --HOLE--

.log_data 
*          0    90000000    00000000     COPY SECTION

__llvm_prf_cnts 
*          0    20000000    00000000     UNINITIALIZED

.TI.bound:dmaADCPriControlTableEntry 
*          0    20001870    00000010     UNINITIALIZED
                  20001870    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCPriControlTableEntry)

.TI.bound:dmaADCAltControlTableEntry 
*          0    20001a70    00000010     UNINITIALIZED
                  20001a70    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCAltControlTableEntry)

.args      0    000072b8    00000008     
                  000072b8    00000008     --HOLE-- [fill = 0]

MODULE SUMMARY

       Module                              code    ro data   rw data
       ------                              ----    -------   -------
    .\
       SoundReceive.obj                    294     0         12536  
       WirelessCommunication.obj           584     0         509    
       ADC.obj                             390     0         408    
       SoundTransmit.obj                   202     0         256    
       time.obj                            388     0         12     
       RFQueue.obj                         266     0         4      
       main.obj                            266     0         2      
       UART.obj                            60      0         4      
       BatteryVoltage.obj                  6       0         0      
       CalculateDistance.obj               6       0         0      
       Temperature.obj                     4       0         0      
    +--+-----------------------------------+-------+---------+---------+
       Total:                              2466    0         13731  
                                                                    
    .\syscfg\
       ti_drivers_config.obj               264     246       878    
       ti_radio_config.obj                 0       0         180    
       ti_devices_config.obj               0       88        0      
    +--+-----------------------------------+-------+---------+---------+
       Total:                              264     334       1058   
                                                                    
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos/lib/ccs/m4f/nortos_cc13x2.a
       ClockPTimer_nortos.oem4f            946     0         192    
       SwiP_nortos.oem4f                   724     16        74     
       TimerPCC26XX_nortos.oem4f           642     0         45     
       HwiPCC26XX_nortos.oem4f             336     0         220    
       SemaphoreP_nortos.oem4f             358     0         11     
       PowerCC26X2_nortos.oem4f            256     0         4      
       startup_cc13x2_cc26x2_ccs.oem4f     26      216       0      
       QueueP_nortos.oem4f                 100     0         0      
    +--+-----------------------------------+-------+---------+---------+
       Total:                              3388    232       546    
                                                                    
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/cc13x2_cc26x2/driverlib/bin/ccs/driverlib.lib
       sys_ctrl.obj                        420     0         0      
       osc.obj                             340     0         36     
       setup.obj                           324     0         0      
       interrupt.obj                       74      0         216    
       chipinfo.obj                        250     0         0      
       prcm.obj                            160     0         0      
       timer.obj                           112     0         0      
       aux_sysif.obj                       52      8         0      
       aux_adc.obj                         32      0         0      
       cpu.obj                             30      0         0      
       driverlib_release.obj               0       0         1      
    +--+-----------------------------------+-------+---------+---------+
       Total:                              1794    8         253    
                                                                    
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/lib/ccs/m4f/drivers_cc13x2.a
       UARTCC26XX.oem4f                    2760    108       0      
       PowerCC26X2.oem4f                   2232    84        368    
       PINCC26XX.oem4f                     1430    0         328    
       ADCBufCC26X2.oem4f                  1542    36        32     
       PowerCC26X2_calibrateRCOSC.oem4f    1400    0         0      
       GPTimerCC26XX.oem4f                 670     36        0      
       List.oem4f                          210     0         0      
       ADCBuf.oem4f                        182     24        1      
       UART.oem4f                          156     36        1      
       UDMACC26XX.oem4f                    168     0         0      
       RingBuf.oem4f                       152     0         0      
    +--+-----------------------------------+-------+---------+---------+
       Total:                              10902   324       730    
                                                                    
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/rf/lib/rf_multiMode_cc13x2.aem4f
       RFCC26X2_multiMode.c.cc13x2.ccs.o   8036    32        1301   
       rf_patch_cpe_prop.c.cc13x2.ccs.o    108     496       1      
       rfc.c.cc13x2.ccs.o                  302     0         0      
    +--+-----------------------------------+-------+---------+---------+
       Total:                              8446    528       1302   
                                                                    
    C:\ti\ccs1011\ccs\tools\compiler\ti-cgt-arm_20.2.4.LTS\lib\rtsv7M4_T_le_v4SPD16_eabi.lib
       memcpy_t2.asm.obj                   156     0         0      
       ull_div_t2.asm.obj                  150     0         0      
       memset_t2.asm.obj                   122     0         0      
       copy_decompress_lzss.c.obj          104     0         0      
       autoinit.c.obj                      68      0         0      
       boot_cortex_m.c.obj                 48      0         0      
       args_main.c.obj                     24      0         0      
       ll_mul_t2.asm.obj                   24      0         0      
       copy_decompress_none.c.obj          14      0         0      
       copy_zero_init.c.obj                12      0         0      
       exit.c.obj                          4       0         0      
       pre_init.c.obj                      4       0         0      
       div0.asm.obj                        2       0         0      
    +--+-----------------------------------+-------+---------+---------+
       Total:                              732     0         0      
                                                                    
       Heap:                               0       0         16384  
       Stack:                              0       0         1024   
       Linker Generated:                   0       352       0      
    +--+-----------------------------------+-------+---------+---------+
       Grand Total:                        27992   1778      35028  


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000073f8 records: 5, size/record: 8, table size: 40
	.data: load addr=000072c0, load size=0000010c bytes, run addr=20000000, run size=00000b90 bytes, compression=lzss
	.TI.bound:dmaADCAltControlTableEntry: load addr=000073d8, load size=00000008 bytes, run addr=20001a70, run size=00000010 bytes, compression=zero_init
	.TI.bound:dmaADCPriControlTableEntry: load addr=000073e0, load size=00000008 bytes, run addr=20001870, run size=00000010 bytes, compression=zero_init
	.bss: load addr=000073e8, load size=00000008 bytes, run addr=20005a80, run size=0000384c bytes, compression=zero_init
	.vtable_ram: load addr=000073f0, load size=00000008 bytes, run addr=20000c00, run size=000000d8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000073cc records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_lzss
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                                               
-------   ----                                                               
0000725f  ADCBUF_BATTERY_VOLTAGE_CONST                                       
0000725c  ADCBUF_CONST                                                       
0000725d  ADCBUF_SOUND_CONST                                                 
0000725e  ADCBUF_TEMPERATURE_CONST                                           
00004e2b  ADCBufCC26X2_adjustRawValues                                       
00004f79  ADCBufCC26X2_close                                                 
000051dd  ADCBufCC26X2_control                                               
00002001  ADCBufCC26X2_convert                                               
000052c5  ADCBufCC26X2_convertAdjustedToMicroVolts                           
000057b1  ADCBufCC26X2_convertCancel                                         
000070e8  ADCBufCC26X2_fxnTable                                              
0000290d  ADCBufCC26X2_getResolution                                         
00006de3  ADCBufCC26X2_init                                                  
00000ff1  ADCBufCC26X2_open                                                  
000069f1  ADCBuf_Params_init                                                 
00006da9  ADCBuf_close                                                       
00007238  ADCBuf_config                                                      
00006db3  ADCBuf_convert                                                     
00006dbd  ADCBuf_convertCancel                                               
00007260  ADCBuf_count                                                       
0000716c  ADCBuf_defaultParams                                               
0000554d  ADCBuf_init                                                        
000057f1  ADCBuf_open                                                        
200092bc  BoardCalculatedCorrelation                                         
00007184  BoardGpioInitTable                                                 
00006469  Board_init                                                         
00006e49  Board_initHook                                                     
000049d1  Board_sendExtFlashByte                                             
00005b59  Board_shutDownExtFlash                                             
00005229  Board_wakeUpExtFlash                                               
00006e35  C$$EXIT                                                            
00007263  CONFIG_GPTIMER_0_CONST                                             
00007264  CONFIG_GPTIMER_1_CONST                                             
00001bc1  CalculateDistance                                                  
20000b38  CallBackMatch                                                      
20000950  ChirpDelay                                                         
20000b58  ChirpIndex                                                         
20000b54  ChirpSize                                                          
200009cc  ChirpState                                                         
00006cb9  ClearPacketReceivedFlag                                            
00006b85  ClockP_Params_init                                                 
000064ad  ClockP_add                                                         
0000530d  ClockP_construct                                                   
0000682f  ClockP_destruct                                                    
00006d01  ClockP_doTick                                                      
00006b95  ClockP_getCpuFreq                                                  
00006d0d  ClockP_getSystemTickPeriod                                         
00006321  ClockP_getTicks                                                    
00005dbd  ClockP_getTicksUntilInterrupt                                      
00006847  ClockP_getTimeout                                                  
000049cd  ClockP_isActive                                                    
00006345  ClockP_scheduleNextTick                                            
00005f5d  ClockP_setTimeout                                                  
00004081  ClockP_start                                                       
00003169  ClockP_startup                                                     
00005273  ClockP_stop                                                        
20000b70  ClockP_tickPeriod                                                  
000042f9  ClockP_walkQueueDynamic                                            
0000321d  ClockP_workFuncDynamic                                             
20009296  DestinationArrayIndex                                              
200092c8  DestinationArraySize                                               
20000b7a  DeviceType                                                         
00006b11  DisableTimerMatch                                                  
00006861  EnableTimerMatch                                                   
00006b23  GPTimerCC26XX_Params_init                                          
00005f93  GPTimerCC26XX_close                                                
0000719c  GPTimerCC26XX_config                                               
00005e25  GPTimerCC26XX_configureDebugStall                                  
00003ac1  GPTimerCC26XX_open                                                 
00006deb  GPTimerCC26XX_setLoadValue                                         
00005061  GPTimerCC26XX_start                                                
00004d25  GPTimerCC26XX_stop                                                 
00007265  GPTimer_count                                                      
00006e11  GetBatteryVoltagePercentage                                        
000066a9  GetDeviceIndentifier                                               
00006e19  GetDistanceMM                                                      
00006e31  GetTemperatureDeciDegrees                                          
00006b35  HwiP_Params_init                                                   
00006ba5  HwiP_clearInterrupt                                                
00004101  HwiP_construct                                                     
000066c5  HwiP_destruct                                                      
00006bb5  HwiP_disable                                                       
00006465  HwiP_enable                                                        
00006bc5  HwiP_enableInterrupt                                               
00006879  HwiP_inISR                                                         
00006bd5  HwiP_post                                                          
00006df3  HwiP_restore                                                       
000067c1  HwiP_setFunc                                                       
20000b84  HwiP_swiPIntNum                                                    
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
0000554d  InitADC                                                            
000067e1  InitSound                                                          
000030b1  InitTimer                                                          
00005a31  InitUart                                                           
20008cec  InternalADCBuffer1                                                 
20008db4  InternalADCBuffer2                                                 
00006e39  IsListen0Received                                                  
00006a09  IsListenAllReceived                                                
00006e41  IsMeasureX0Received                                                
00006d19  IsPacketReceived                                                   
00005f61  IsTimerExpired                                                     
00006a1d  List_clearList                                                     
0000648b  List_get                                                           
00006191  List_insert                                                        
000062ad  List_put                                                           
000062d3  List_putHead                                                       
000062f9  List_remove                                                        
20000b7b  MobileStateMachineState                                            
000064cd  NOROM_AUXADCEnableSync                                             
00005e59  NOROM_AUXSYSIFOpModeChange                                         
00006d25  NOROM_CPUcpsid                                                     
00006d31  NOROM_CPUcpsie                                                     
000067db  NOROM_CPUdelay                                                     
000066e1  NOROM_ChipInfo_GetChipFamily                                       
00004689  NOROM_ChipInfo_GetChipType                                         
00005591  NOROM_ChipInfo_GetHwRevision                                       
00006891  NOROM_ChipInfo_GetPackageType                                      
00005e8d  NOROM_IntRegister                                                  
00006a31  NOROM_IntUnregister                                                
00003b49  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00004761  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
0000638d  NOROM_OSCHF_TurnOnXosc                                             
00005831  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00004e81  NOROM_PRCMPowerDomainsAllOff                                       
000050ad  NOROM_PRCMPowerDomainsAllOn                                        
000044cb  NOROM_RFCAnaDivTxOverride                                          
00006a45  NOROM_RFCCpeIntGetAndClear                                         
00001efb  NOROM_RFCCpePatchReset                                             
000063b1  NOROM_RFCDoorbellSendTo                                            
00006be5  NOROM_RFCHwIntGetAndClear                                          
000061e5  NOROM_RFCOverrideSearch                                            
000066a5  NOROM_RFCOverrideUpdate                                            
00005355  NOROM_RFCSynthPowerDown                                            
00003741  NOROM_SetupTrimDevice                                              
00005ec1  NOROM_SysCtrlIdle                                                  
00004181  NOROM_SysCtrlSetRechargeBeforePowerDown                            
00002101  NOROM_SysCtrlStandby                                               
00006951  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00005871  NOROM_TimerIntRegister                                             
00006465  NoRTOS_start                                                       
00006bf5  PINCC26XX_getPinCount                                              
00007280  PINCC26XX_hwAttrs                                                  
00005fc5  PINCC26XX_setMux                                                   
0000662d  PIN_close                                                          
00000789  PIN_init                                                           
000032c9  PIN_open                                                           
00004bb9  PIN_remove                                                         
00005b91  PIN_setConfig                                                      
0000539d  PIN_setOutputEnable                                                
00005a6d  PIN_setOutputValue                                                 
000053e5  PowerCC26X2_RCOSC_clockFunc                                        
000043e5  PowerCC26X2_auxISR                                                 
00006a59  PowerCC26X2_calibrate                                              
000071e0  PowerCC26X2_config                                                 
00004d7d  PowerCC26X2_initiateCalibration                                    
20000708  PowerCC26X2_module                                                 
000067fb  PowerCC26XX_calibrate                                              
000058b1  PowerCC26XX_isStableXOSC_HF                                        
00006c05  PowerCC26XX_schedulerDisable                                       
00006d3d  PowerCC26XX_schedulerRestore                                       
0000259d  PowerCC26XX_standbyPolicy                                          
0000542d  PowerCC26XX_switchXOSC_HF                                          
00006a6d  Power_disablePolicy                                                
00006c15  Power_enablePolicy                                                 
00006d49  Power_getConstraintMask                                            
00006d55  Power_getDependencyCount                                           
0000664b  Power_getTransitionLatency                                         
000068a9  Power_idleFunc                                                     
00001165  Power_init                                                         
0000620d  Power_registerNotify                                               
00005ff5  Power_releaseConstraint                                            
00002b89  Power_releaseDependency                                            
00006025  Power_setConstraint                                                
00002f41  Power_setDependency                                                
00000b3d  Power_sleep                                                        
0000650d  Power_unregisterNotify                                             
00006cc7  QueueP_empty                                                       
00006815  QueueP_get                                                         
00006995  QueueP_head                                                        
00006b6b  QueueP_init                                                        
00006a05  QueueP_next                                                        
0000652d  QueueP_put                                                         
00006cd5  QueueP_remove                                                      
00007244  RFCC26XX_hwAttrs                                                   
20000b44  RFCC26XX_schedulerPolicy                                           
000023d1  RFQueue_defineQueue                                                
00006d61  RFQueue_getDataEntry                                               
00006969  RFQueue_nextEntry                                                  
00006a81  RF_Params_init                                                     
000059f1  RF_StartRX                                                         
00006719  RF_TerminateRX                                                     
00005279  RF_TxPacketBuffer                                                  
20000ae0  RF_cmdFs                                                           
20000a48  RF_cmdPropRadioDivSetup                                            
20000abc  RF_cmdPropRx                                                       
20000af8  RF_cmdPropTx                                                       
00006b0d  RF_defaultExecutionPolicy                                          
00004dd5  RF_defaultSubmitPolicy                                             
00006aa9  RF_flushCmd                                                        
0000453d  RF_getCurrentTime                                                  
00002911  RF_initRXTX                                                        
00000589  RF_open                                                            
0000275d  RF_pendCmd                                                         
000024b9  RF_postCmd                                                         
00006d6d  RF_powerConstraintGet                                              
00005c01  RF_powerConstraintRelease                                          
0000676d  RF_powerConstraintSet                                              
20000b10  RF_prop                                                            
0000625d  RF_runCmd                                                          
200092ca  RXpacketLength                                                     
00002d09  ReadArrayADCraw                                                    
00003ef9  ReadSingleValueADCmV                                               
00006b47  RingBuf_construct                                                  
000058f1  RingBuf_get                                                        
00005505  RingBuf_put                                                        
00006b59  SampleSound                                                        
00006c45  SemaphoreP_Params_init                                             
00004f29  SemaphoreP_construct                                               
00006669  SemaphoreP_constructBinary                                         
20000b4c  SemaphoreP_defaultParams                                           
00002ab7  SemaphoreP_destruct                                                
000034c5  SemaphoreP_pend                                                    
00005619  SemaphoreP_post                                                    
00006d79  SetCallbackFunctionOnMatch                                         
00006c59  SetNextMatch                                                       
200004f8  Signature                                                          
20000b80  SignatureSize                                                      
20005a80  SoundSamples                                                       
00006c69  SoundTransmit                                                      
00006999  StartTimer                                                         
00006c79  SwiP_Params_init                                                   
00002835  SwiP_construct                                                     
00005ef5  SwiP_destruct                                                      
00006789  SwiP_disable                                                       
00003609  SwiP_dispatch                                                      
00006d85  SwiP_getTrigger                                                    
00006687  SwiP_or                                                            
00004831  SwiP_post                                                          
0000565d  SwiP_restore                                                       
00006e51  TXListen0                                                          
00006b71  TXListenAll                                                        
000069b1  TXMeasurements                                                     
00006c89  TXchar                                                             
00005931  Timer2AInterruptHandler                                            
00003569  TimerMatchCallback                                                 
20000b40  TimerOverflows                                                     
00006c99  TimerP_Params_init                                                 
00002e85  TimerP_construct                                                   
000065cd  TimerP_dynamicStub                                                 
00006ae5  TimerP_getCount64                                                  
00005c39  TimerP_getCurrentTick                                              
00006d91  TimerP_getFreq                                                     
0000641d  TimerP_getMaxTicks                                                 
00005c71  TimerP_initDevice                                                  
00005d51  TimerP_setNextTick                                                 
00006139  TimerP_setThreshold                                                
00004899  TimerP_start                                                       
000068d9  TimerP_startup                                                     
20000b3c  TimerStartTime                                                     
00003f7d  UARTCC26XX_close                                                   
00004969  UARTCC26XX_control                                                 
00007098  UARTCC26XX_fxnTable                                                
00001591  UARTCC26XX_hwiIntFxn                                               
00006dfb  UARTCC26XX_init                                                    
000012d9  UARTCC26XX_open                                                    
0000182d  UARTCC26XX_read                                                    
00005145  UARTCC26XX_readCancel                                              
00006e1f  UARTCC26XX_readPolling                                             
00004a95  UARTCC26XX_swiIntFxn                                               
0000267d  UARTCC26XX_write                                                   
0000399f  UARTCC26XX_writeCancel                                             
00006e25  UARTCC26XX_writePolling                                            
00007261  UART_0_CONST                                                       
000068f1  UART_Params_init                                                   
00007250  UART_config                                                        
00007262  UART_count                                                         
0000710c  UART_defaultParams                                                 
000056a1  UART_init                                                          
00005971  UART_open                                                          
00005d87  UDMACC26XX_close                                                   
00007288  UDMACC26XX_config                                                  
00006dc7  UDMACC26XX_hwiIntFxn                                               
00005191  UDMACC26XX_open                                                    
20014000  __STACK_END                                                        
00000400  __STACK_SIZE                                                       
00004000  __SYSMEM_SIZE                                                      
000073f8  __TI_CINIT_Base                                                    
00007420  __TI_CINIT_Limit                                                   
000073cc  __TI_Handler_Table_Base                                            
000073d8  __TI_Handler_Table_Limit                                           
000056e5  __TI_auto_init_nobinit_nopinit                                     
00004901  __TI_decompress_lzss                                               
00006cf1  __TI_decompress_none                                               
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
00000000  __TI_static_base__                                                 
00006d9d  __TI_zero_init                                                     
00004e7f  __aeabi_idiv0                                                      
00004e7f  __aeabi_ldiv0                                                      
00006921  __aeabi_lmul                                                       
0000427d  __aeabi_memclr                                                     
0000427d  __aeabi_memclr4                                                    
0000427d  __aeabi_memclr8                                                    
000036a5  __aeabi_memcpy                                                     
000036a5  __aeabi_memcpy4                                                    
000036a5  __aeabi_memcpy8                                                    
0000427f  __aeabi_memset                                                     
0000427f  __aeabi_memset4                                                    
0000427f  __aeabi_memset8                                                    
00003909  __aeabi_uldivmod                                                   
ffffffff  __binit__                                                          
000072b8  __c_args__                                                         
00057fa8  __ccfg                                                             
UNDEFED   __mpu_init                                                         
20013c00  __stack                                                            
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
00006909  _args_main                                                         
00006085  _c_int00                                                           
20000a98  _hposcCoeffs                                                       
20001a80  _sys_memory                                                        
UNDEFED   _system_post_cinit                                                 
00006c55  _system_pre_init                                                   
00006e35  abort                                                              
20000b74  adcBufCC26XXChannelLut0                                            
00005729  adcBufCallback                                                     
00007218  adcbufCC26XXHWAttrs                                                
20008c10  adcbufCC26XXbjects                                                 
ffffffff  binit                                                              
00002dc7  clkFxn                                                             
200092cb  driverlib_release_0_59848                                          
20000c00  g_pfnRAMVectors                                                    
00000000  g_pfnVectors                                                       
000071b4  gptimerCC26XXHWAttrs                                               
20008f20  gptimerCC26XXObjects                                               
000022e1  main                                                               
000036a5  memcpy                                                             
00004285  memset                                                             
20000b7c  pDestinationArray                                                  
20000a70  pOverrides                                                         
20008fb8  pinHandleTable                                                     
20000b68  pinLowerBound                                                      
20000b64  pinUpperBound                                                      
200092c0  readEntry                                                          
00006af9  resetISR                                                           
00007044  resourceDB                                                         
00006165  rf_patch_cpe_prop                                                  
200092c4  uart                                                               
20008960  uartCC26XXObjects                                                  
00007298  udmaCC26XXHWAttrs                                                  
20009200  udmaCC26XXObject                                                   


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                                               
-------   ----                                                               
00000000  __TI_static_base__                                                 
00000000  g_pfnVectors                                                       
00000400  __STACK_SIZE                                                       
00000589  RF_open                                                            
00000789  PIN_init                                                           
00000b3d  Power_sleep                                                        
00000ff1  ADCBufCC26X2_open                                                  
00001165  Power_init                                                         
000012d9  UARTCC26XX_open                                                    
00001591  UARTCC26XX_hwiIntFxn                                               
0000182d  UARTCC26XX_read                                                    
00001bc1  CalculateDistance                                                  
00001efb  NOROM_RFCCpePatchReset                                             
00002001  ADCBufCC26X2_convert                                               
00002101  NOROM_SysCtrlStandby                                               
000022e1  main                                                               
000023d1  RFQueue_defineQueue                                                
000024b9  RF_postCmd                                                         
0000259d  PowerCC26XX_standbyPolicy                                          
0000267d  UARTCC26XX_write                                                   
0000275d  RF_pendCmd                                                         
00002835  SwiP_construct                                                     
0000290d  ADCBufCC26X2_getResolution                                         
00002911  RF_initRXTX                                                        
00002ab7  SemaphoreP_destruct                                                
00002b89  Power_releaseDependency                                            
00002d09  ReadArrayADCraw                                                    
00002dc7  clkFxn                                                             
00002e85  TimerP_construct                                                   
00002f41  Power_setDependency                                                
000030b1  InitTimer                                                          
00003169  ClockP_startup                                                     
0000321d  ClockP_workFuncDynamic                                             
000032c9  PIN_open                                                           
000034c5  SemaphoreP_pend                                                    
00003569  TimerMatchCallback                                                 
00003609  SwiP_dispatch                                                      
000036a5  __aeabi_memcpy                                                     
000036a5  __aeabi_memcpy4                                                    
000036a5  __aeabi_memcpy8                                                    
000036a5  memcpy                                                             
00003741  NOROM_SetupTrimDevice                                              
00003909  __aeabi_uldivmod                                                   
0000399f  UARTCC26XX_writeCancel                                             
00003ac1  GPTimerCC26XX_open                                                 
00003b49  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00003ef9  ReadSingleValueADCmV                                               
00003f7d  UARTCC26XX_close                                                   
00004000  __SYSMEM_SIZE                                                      
00004081  ClockP_start                                                       
00004101  HwiP_construct                                                     
00004181  NOROM_SysCtrlSetRechargeBeforePowerDown                            
0000427d  __aeabi_memclr                                                     
0000427d  __aeabi_memclr4                                                    
0000427d  __aeabi_memclr8                                                    
0000427f  __aeabi_memset                                                     
0000427f  __aeabi_memset4                                                    
0000427f  __aeabi_memset8                                                    
00004285  memset                                                             
000042f9  ClockP_walkQueueDynamic                                            
000043e5  PowerCC26X2_auxISR                                                 
000044cb  NOROM_RFCAnaDivTxOverride                                          
0000453d  RF_getCurrentTime                                                  
00004689  NOROM_ChipInfo_GetChipType                                         
00004761  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
00004831  SwiP_post                                                          
00004899  TimerP_start                                                       
00004901  __TI_decompress_lzss                                               
00004969  UARTCC26XX_control                                                 
000049cd  ClockP_isActive                                                    
000049d1  Board_sendExtFlashByte                                             
00004a95  UARTCC26XX_swiIntFxn                                               
00004bb9  PIN_remove                                                         
00004d25  GPTimerCC26XX_stop                                                 
00004d7d  PowerCC26X2_initiateCalibration                                    
00004dd5  RF_defaultSubmitPolicy                                             
00004e2b  ADCBufCC26X2_adjustRawValues                                       
00004e7f  __aeabi_idiv0                                                      
00004e7f  __aeabi_ldiv0                                                      
00004e81  NOROM_PRCMPowerDomainsAllOff                                       
00004f29  SemaphoreP_construct                                               
00004f79  ADCBufCC26X2_close                                                 
00005061  GPTimerCC26XX_start                                                
000050ad  NOROM_PRCMPowerDomainsAllOn                                        
00005145  UARTCC26XX_readCancel                                              
00005191  UDMACC26XX_open                                                    
000051dd  ADCBufCC26X2_control                                               
00005229  Board_wakeUpExtFlash                                               
00005273  ClockP_stop                                                        
00005279  RF_TxPacketBuffer                                                  
000052c5  ADCBufCC26X2_convertAdjustedToMicroVolts                           
0000530d  ClockP_construct                                                   
00005355  NOROM_RFCSynthPowerDown                                            
0000539d  PIN_setOutputEnable                                                
000053e5  PowerCC26X2_RCOSC_clockFunc                                        
0000542d  PowerCC26XX_switchXOSC_HF                                          
00005505  RingBuf_put                                                        
0000554d  ADCBuf_init                                                        
0000554d  InitADC                                                            
00005591  NOROM_ChipInfo_GetHwRevision                                       
00005619  SemaphoreP_post                                                    
0000565d  SwiP_restore                                                       
000056a1  UART_init                                                          
000056e5  __TI_auto_init_nobinit_nopinit                                     
00005729  adcBufCallback                                                     
000057b1  ADCBufCC26X2_convertCancel                                         
000057f1  ADCBuf_open                                                        
00005831  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00005871  NOROM_TimerIntRegister                                             
000058b1  PowerCC26XX_isStableXOSC_HF                                        
000058f1  RingBuf_get                                                        
00005931  Timer2AInterruptHandler                                            
00005971  UART_open                                                          
000059f1  RF_StartRX                                                         
00005a31  InitUart                                                           
00005a6d  PIN_setOutputValue                                                 
00005b59  Board_shutDownExtFlash                                             
00005b91  PIN_setConfig                                                      
00005c01  RF_powerConstraintRelease                                          
00005c39  TimerP_getCurrentTick                                              
00005c71  TimerP_initDevice                                                  
00005d51  TimerP_setNextTick                                                 
00005d87  UDMACC26XX_close                                                   
00005dbd  ClockP_getTicksUntilInterrupt                                      
00005e25  GPTimerCC26XX_configureDebugStall                                  
00005e59  NOROM_AUXSYSIFOpModeChange                                         
00005e8d  NOROM_IntRegister                                                  
00005ec1  NOROM_SysCtrlIdle                                                  
00005ef5  SwiP_destruct                                                      
00005f5d  ClockP_setTimeout                                                  
00005f61  IsTimerExpired                                                     
00005f93  GPTimerCC26XX_close                                                
00005fc5  PINCC26XX_setMux                                                   
00005ff5  Power_releaseConstraint                                            
00006025  Power_setConstraint                                                
00006085  _c_int00                                                           
00006139  TimerP_setThreshold                                                
00006165  rf_patch_cpe_prop                                                  
00006191  List_insert                                                        
000061e5  NOROM_RFCOverrideSearch                                            
0000620d  Power_registerNotify                                               
0000625d  RF_runCmd                                                          
000062ad  List_put                                                           
000062d3  List_putHead                                                       
000062f9  List_remove                                                        
00006321  ClockP_getTicks                                                    
00006345  ClockP_scheduleNextTick                                            
0000638d  NOROM_OSCHF_TurnOnXosc                                             
000063b1  NOROM_RFCDoorbellSendTo                                            
0000641d  TimerP_getMaxTicks                                                 
00006465  HwiP_enable                                                        
00006465  NoRTOS_start                                                       
00006469  Board_init                                                         
0000648b  List_get                                                           
000064ad  ClockP_add                                                         
000064cd  NOROM_AUXADCEnableSync                                             
0000650d  Power_unregisterNotify                                             
0000652d  QueueP_put                                                         
000065cd  TimerP_dynamicStub                                                 
0000662d  PIN_close                                                          
0000664b  Power_getTransitionLatency                                         
00006669  SemaphoreP_constructBinary                                         
00006687  SwiP_or                                                            
000066a5  NOROM_RFCOverrideUpdate                                            
000066a9  GetDeviceIndentifier                                               
000066c5  HwiP_destruct                                                      
000066e1  NOROM_ChipInfo_GetChipFamily                                       
00006719  RF_TerminateRX                                                     
0000676d  RF_powerConstraintSet                                              
00006789  SwiP_disable                                                       
000067c1  HwiP_setFunc                                                       
000067db  NOROM_CPUdelay                                                     
000067e1  InitSound                                                          
000067fb  PowerCC26XX_calibrate                                              
00006815  QueueP_get                                                         
0000682f  ClockP_destruct                                                    
00006847  ClockP_getTimeout                                                  
00006861  EnableTimerMatch                                                   
00006879  HwiP_inISR                                                         
00006891  NOROM_ChipInfo_GetPackageType                                      
000068a9  Power_idleFunc                                                     
000068d9  TimerP_startup                                                     
000068f1  UART_Params_init                                                   
00006909  _args_main                                                         
00006921  __aeabi_lmul                                                       
00006951  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00006969  RFQueue_nextEntry                                                  
00006995  QueueP_head                                                        
00006999  StartTimer                                                         
000069b1  TXMeasurements                                                     
000069f1  ADCBuf_Params_init                                                 
00006a05  QueueP_next                                                        
00006a09  IsListenAllReceived                                                
00006a1d  List_clearList                                                     
00006a31  NOROM_IntUnregister                                                
00006a45  NOROM_RFCCpeIntGetAndClear                                         
00006a59  PowerCC26X2_calibrate                                              
00006a6d  Power_disablePolicy                                                
00006a81  RF_Params_init                                                     
00006aa9  RF_flushCmd                                                        
00006ae5  TimerP_getCount64                                                  
00006af9  resetISR                                                           
00006b0d  RF_defaultExecutionPolicy                                          
00006b11  DisableTimerMatch                                                  
00006b23  GPTimerCC26XX_Params_init                                          
00006b35  HwiP_Params_init                                                   
00006b47  RingBuf_construct                                                  
00006b59  SampleSound                                                        
00006b6b  QueueP_init                                                        
00006b71  TXListenAll                                                        
00006b85  ClockP_Params_init                                                 
00006b95  ClockP_getCpuFreq                                                  
00006ba5  HwiP_clearInterrupt                                                
00006bb5  HwiP_disable                                                       
00006bc5  HwiP_enableInterrupt                                               
00006bd5  HwiP_post                                                          
00006be5  NOROM_RFCHwIntGetAndClear                                          
00006bf5  PINCC26XX_getPinCount                                              
00006c05  PowerCC26XX_schedulerDisable                                       
00006c15  Power_enablePolicy                                                 
00006c45  SemaphoreP_Params_init                                             
00006c55  _system_pre_init                                                   
00006c59  SetNextMatch                                                       
00006c69  SoundTransmit                                                      
00006c79  SwiP_Params_init                                                   
00006c89  TXchar                                                             
00006c99  TimerP_Params_init                                                 
00006cb9  ClearPacketReceivedFlag                                            
00006cc7  QueueP_empty                                                       
00006cd5  QueueP_remove                                                      
00006cf1  __TI_decompress_none                                               
00006d01  ClockP_doTick                                                      
00006d0d  ClockP_getSystemTickPeriod                                         
00006d19  IsPacketReceived                                                   
00006d25  NOROM_CPUcpsid                                                     
00006d31  NOROM_CPUcpsie                                                     
00006d3d  PowerCC26XX_schedulerRestore                                       
00006d49  Power_getConstraintMask                                            
00006d55  Power_getDependencyCount                                           
00006d61  RFQueue_getDataEntry                                               
00006d6d  RF_powerConstraintGet                                              
00006d79  SetCallbackFunctionOnMatch                                         
00006d85  SwiP_getTrigger                                                    
00006d91  TimerP_getFreq                                                     
00006d9d  __TI_zero_init                                                     
00006da9  ADCBuf_close                                                       
00006db3  ADCBuf_convert                                                     
00006dbd  ADCBuf_convertCancel                                               
00006dc7  UDMACC26XX_hwiIntFxn                                               
00006de3  ADCBufCC26X2_init                                                  
00006deb  GPTimerCC26XX_setLoadValue                                         
00006df3  HwiP_restore                                                       
00006dfb  UARTCC26XX_init                                                    
00006e11  GetBatteryVoltagePercentage                                        
00006e19  GetDistanceMM                                                      
00006e1f  UARTCC26XX_readPolling                                             
00006e25  UARTCC26XX_writePolling                                            
00006e31  GetTemperatureDeciDegrees                                          
00006e35  C$$EXIT                                                            
00006e35  abort                                                              
00006e39  IsListen0Received                                                  
00006e41  IsMeasureX0Received                                                
00006e49  Board_initHook                                                     
00006e51  TXListen0                                                          
00007044  resourceDB                                                         
00007098  UARTCC26XX_fxnTable                                                
000070e8  ADCBufCC26X2_fxnTable                                              
0000710c  UART_defaultParams                                                 
0000716c  ADCBuf_defaultParams                                               
00007184  BoardGpioInitTable                                                 
0000719c  GPTimerCC26XX_config                                               
000071b4  gptimerCC26XXHWAttrs                                               
000071e0  PowerCC26X2_config                                                 
00007218  adcbufCC26XXHWAttrs                                                
00007238  ADCBuf_config                                                      
00007244  RFCC26XX_hwAttrs                                                   
00007250  UART_config                                                        
0000725c  ADCBUF_CONST                                                       
0000725d  ADCBUF_SOUND_CONST                                                 
0000725e  ADCBUF_TEMPERATURE_CONST                                           
0000725f  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00007260  ADCBuf_count                                                       
00007261  UART_0_CONST                                                       
00007262  UART_count                                                         
00007263  CONFIG_GPTIMER_0_CONST                                             
00007264  CONFIG_GPTIMER_1_CONST                                             
00007265  GPTimer_count                                                      
00007280  PINCC26XX_hwAttrs                                                  
00007288  UDMACC26XX_config                                                  
00007298  udmaCC26XXHWAttrs                                                  
000072b8  __c_args__                                                         
000073cc  __TI_Handler_Table_Base                                            
000073d8  __TI_Handler_Table_Limit                                           
000073f8  __TI_CINIT_Base                                                    
00007420  __TI_CINIT_Limit                                                   
00057fa8  __ccfg                                                             
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
200004f8  Signature                                                          
20000708  PowerCC26X2_module                                                 
20000950  ChirpDelay                                                         
200009cc  ChirpState                                                         
20000a48  RF_cmdPropRadioDivSetup                                            
20000a70  pOverrides                                                         
20000a98  _hposcCoeffs                                                       
20000abc  RF_cmdPropRx                                                       
20000ae0  RF_cmdFs                                                           
20000af8  RF_cmdPropTx                                                       
20000b10  RF_prop                                                            
20000b38  CallBackMatch                                                      
20000b3c  TimerStartTime                                                     
20000b40  TimerOverflows                                                     
20000b44  RFCC26XX_schedulerPolicy                                           
20000b4c  SemaphoreP_defaultParams                                           
20000b54  ChirpSize                                                          
20000b58  ChirpIndex                                                         
20000b64  pinUpperBound                                                      
20000b68  pinLowerBound                                                      
20000b70  ClockP_tickPeriod                                                  
20000b74  adcBufCC26XXChannelLut0                                            
20000b7a  DeviceType                                                         
20000b7b  MobileStateMachineState                                            
20000b7c  pDestinationArray                                                  
20000b80  SignatureSize                                                      
20000b84  HwiP_swiPIntNum                                                    
20000c00  g_pfnRAMVectors                                                    
20001a80  _sys_memory                                                        
20005a80  SoundSamples                                                       
20008960  uartCC26XXObjects                                                  
20008c10  adcbufCC26XXbjects                                                 
20008cec  InternalADCBuffer1                                                 
20008db4  InternalADCBuffer2                                                 
20008f20  gptimerCC26XXObjects                                               
20008fb8  pinHandleTable                                                     
20009200  udmaCC26XXObject                                                   
20009296  DestinationArrayIndex                                              
200092bc  BoardCalculatedCorrelation                                         
200092c0  readEntry                                                          
200092c4  uart                                                               
200092c8  DestinationArraySize                                               
200092ca  RXpacketLength                                                     
200092cb  driverlib_release_0_59848                                          
20013c00  __stack                                                            
20014000  __STACK_END                                                        
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
ffffffff  __binit__                                                          
ffffffff  binit                                                              
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
UNDEFED   __mpu_init                                                         
UNDEFED   _system_post_cinit                                                 

[353 symbols]
