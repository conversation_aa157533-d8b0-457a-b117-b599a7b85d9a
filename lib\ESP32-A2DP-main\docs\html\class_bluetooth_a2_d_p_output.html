<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: BluetoothA2DPOutput Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="class_bluetooth_a2_d_p_output-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">BluetoothA2DPOutput Class Reference<span class="mlabels"><span class="mlabel">abstract</span></span><div class="ingroups"><a class="el" href="group__a2dp.html">ESP32 A2DP</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Abstract Output Class.  
 <a href="class_bluetooth_a2_d_p_output.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_bluetooth_a2_d_p_output_8h_source.html">BluetoothA2DPOutput.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for BluetoothA2DPOutput:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_bluetooth_a2_d_p_output.png" usemap="#BluetoothA2DPOutput_map" alt=""/>
  <map id="BluetoothA2DPOutput_map" name="BluetoothA2DPOutput_map">
<area href="class_bluetooth_a2_d_p_output_audio_tools.html" title="Output Class using AudioTools library: https://github.com/pschatzmann/arduino-audio-tools." alt="BluetoothA2DPOutputAudioTools" shape="rect" coords="0,56,197,80"/>
<area href="class_bluetooth_a2_d_p_output_default.html" title="Default Output Class providing both the Legacy I2S and the AudioTools I2S functionality." alt="BluetoothA2DPOutputDefault" shape="rect" coords="207,56,404,80"/>
<area href="class_bluetooth_a2_d_p_output_legacy.html" title="Legacy I2S Output Class." alt="BluetoothA2DPOutputLegacy" shape="rect" coords="414,56,611,80"/>
<area href="class_bluetooth_a2_d_p_output_print.html" title="Output Class using Print API:" alt="BluetoothA2DPOutputPrint" shape="rect" coords="621,56,818,80"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ad5ca1c3884eb96fe9681e75d07363882"><td class="memItemLeft" align="right" valign="top"><a id="ad5ca1c3884eb96fe9681e75d07363882"></a>
virtual bool&#160;</td><td class="memItemRight" valign="bottom"><b>begin</b> ()=0</td></tr>
<tr class="separator:ad5ca1c3884eb96fe9681e75d07363882"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a941ce061ea9e1751a8a23c6e8468bf42"><td class="memItemLeft" align="right" valign="top"><a id="a941ce061ea9e1751a8a23c6e8468bf42"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>end</b> ()=0</td></tr>
<tr class="separator:a941ce061ea9e1751a8a23c6e8468bf42"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67fc2cf760ae2a48ac6bfa6ed235f8b8"><td class="memItemLeft" align="right" valign="top"><a id="a67fc2cf760ae2a48ac6bfa6ed235f8b8"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_output.html#a67fc2cf760ae2a48ac6bfa6ed235f8b8">set_output</a> (audio_tools::AudioOutput &amp;output)</td></tr>
<tr class="memdesc:a67fc2cf760ae2a48ac6bfa6ed235f8b8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not implemented. <br /></td></tr>
<tr class="separator:a67fc2cf760ae2a48ac6bfa6ed235f8b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9cdbeeafa4778b4efcf7108ec8b9afae"><td class="memItemLeft" align="right" valign="top"><a id="a9cdbeeafa4778b4efcf7108ec8b9afae"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_output.html#a9cdbeeafa4778b4efcf7108ec8b9afae">set_output</a> (audio_tools::AudioStream &amp;output)</td></tr>
<tr class="memdesc:a9cdbeeafa4778b4efcf7108ec8b9afae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not implemented. <br /></td></tr>
<tr class="separator:a9cdbeeafa4778b4efcf7108ec8b9afae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add4a1457f42f2e6ef8b905bcf4be10dc"><td class="memItemLeft" align="right" valign="top"><a id="add4a1457f42f2e6ef8b905bcf4be10dc"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_output.html#add4a1457f42f2e6ef8b905bcf4be10dc">set_output</a> (Print &amp;output)</td></tr>
<tr class="memdesc:add4a1457f42f2e6ef8b905bcf4be10dc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not implemented. <br /></td></tr>
<tr class="separator:add4a1457f42f2e6ef8b905bcf4be10dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf3417875f028bc97b2409e5a5be310b"><td class="memItemLeft" align="right" valign="top"><a id="abf3417875f028bc97b2409e5a5be310b"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>set_output_active</b> (bool active)=0</td></tr>
<tr class="separator:abf3417875f028bc97b2409e5a5be310b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad115622c27dd0a3eaac9c7501f608a7d"><td class="memItemLeft" align="right" valign="top"><a id="ad115622c27dd0a3eaac9c7501f608a7d"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>set_sample_rate</b> (int rate)=0</td></tr>
<tr class="separator:ad115622c27dd0a3eaac9c7501f608a7d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad01ea858e5b8f36e3a2b9715906af89c"><td class="memItemLeft" align="right" valign="top"><a id="ad01ea858e5b8f36e3a2b9715906af89c"></a>
virtual size_t&#160;</td><td class="memItemRight" valign="bottom"><b>write</b> (const uint8_t *data, size_t len)=0</td></tr>
<tr class="separator:ad01ea858e5b8f36e3a2b9715906af89c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Abstract Output Class. </p>
<dl class="section author"><dt>Author</dt><dd>Phil Schatzmann</dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Apache License Version 2 </dd></dl>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li>src/<a class="el" href="_bluetooth_a2_d_p_output_8h_source.html">BluetoothA2DPOutput.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
