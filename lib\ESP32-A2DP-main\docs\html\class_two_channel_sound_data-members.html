<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">TwoChannelSoundData Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a>, including all inherited members.</p>
<table class="directory">
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>count</b>() (defined in <a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a>)</td><td class="entry"><a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_sound_data.html#ac79933ed3379cf5ef58d5675aa4bf12e">doLoop</a>()</td><td class="entry"><a class="el" href="class_sound_data.html">SoundData</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_two_channel_sound_data.html#a0a39d70aca39dbeca3d20676635b7615">get2ChannelData</a>(int32_t pos, int32_t len, uint8_t *data)</td><td class="entry"><a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>getData</b>(int32_t pos, int32_t len, Frame *data) (defined in <a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a>)</td><td class="entry"><a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>getData</b>(int32_t pos, Frame &amp;channels) (defined in <a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a>)</td><td class="entry"><a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>setData</b>(Frame *data, int32_t len) (defined in <a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a>)</td><td class="entry"><a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>setDataRaw</b>(uint8_t *data, int32_t len) (defined in <a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a>)</td><td class="entry"><a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>setLoop</b>(bool loop) (defined in <a class="el" href="class_sound_data.html">SoundData</a>)</td><td class="entry"><a class="el" href="class_sound_data.html">SoundData</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>TwoChannelSoundData</b>(bool loop=false) (defined in <a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a>)</td><td class="entry"><a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_two_channel_sound_data.html#ae66d339668c00d667a44dcafe6421810">TwoChannelSoundData</a>(Frame *data, int32_t len, bool loop=false)</td><td class="entry"><a class="el" href="class_two_channel_sound_data.html">TwoChannelSoundData</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
