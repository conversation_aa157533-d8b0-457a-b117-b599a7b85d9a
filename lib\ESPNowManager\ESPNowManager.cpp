#include "ESPNowManager.h"

void ESPNowManager::init(bool isSender, ReceiveCallback cb) {
    if(initialized) return;
    
    WiFi.mode(WIFI_STA);
    if(esp_now_init() != ESP_OK) return;
    
    if(!isSender && cb) {
        esp_now_register_recv_cb(cb);
    }
    
    initialized = true;
}

bool ESPNowManager::addPeer(const uint8_t* mac) {
    esp_now_peer_info_t peer = {};
    memcpy(peer.peer_addr, mac, 6);
    peer.channel = 0;
    peer.encrypt = false;
    return (esp_now_add_peer(&peer) == ESP_OK);
}

bool ESPNowManager::send(const uint8_t* mac, const uint8_t* data, size_t len) {
    return (esp_now_send(mac, data, len) == ESP_OK);
}