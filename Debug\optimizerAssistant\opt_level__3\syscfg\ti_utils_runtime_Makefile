#
#  ======== ti_utils_runtime_Makefile ========
#
#  DO NOT EDIT - This file is generated by the SysConfig tool.
#
#  This makefile is used to generate an SVG file from the given
#  DOT file to generate a graphical view of the configuration
#  model. Add a Post-build step to integrate this makefile into
#  the CCS build flow.
#
#  To generate the SVG file, you must install Graphviz into the SDK
#  third_party folder. Download the zip file from the Graphviz website
#  and unzip into the following folder (create the destination folder
#  first):
#
#  https://graphviz.gitlab.io/
#  unzip graphviz-2.38.zip -d <SDK>/source/third-party/graphviz
#
#  Use the following steps to add a post-build command
#
#  Project > Properties
#  CCS Build > Steps (tab)
#
#  Add the following command to invoke this makefile. Substitute the SDK
#  variable name for your installed product. Enter this command in the
#  Post-build steps text box (all on one line):
#
#  ${CCS_UTILS_DIR}/bin/gmake -f syscfg/ti_utils_runtime_Makefile
#  SDK_INSTALL_DIR=${COM_TI_SIMPLELINK_CC13XX_CC26XX_SDK_INSTALL_DIR} all
#

DOT = $(SDK_INSTALL_DIR)/source/third_party/graphviz/release/bin/dot

ifneq (,$(wildcard $(dir $(DOT))))
all: graphviz
endif
all:
	@echo "#"
	@echo "ti-utils build complete"

graphviz:
	@echo "#"
	@echo "Generating Graphviz files"
	$(DOT) -Tsvg -O syscfg/ti_utils_runtime_model.gv
