<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle"><div class="title">BluetoothA2DPSinkCallbacks Member List</div></div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html">BluetoothA2DPSinkCallbacks</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html#a6044c5dc42ff6440445d10f124d11e2c">app_a2d_callback</a>(esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html">BluetoothA2DPSinkCallbacks</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html#ac414ff3449b90390432d7ecc0f798308">app_gap_callback</a>(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t *param)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html">BluetoothA2DPSinkCallbacks</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html#a85efda64ad1405edb8382d24770654a8">app_rc_ct_callback</a>(esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html">BluetoothA2DPSinkCallbacks</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html#a6e82c8cd94b5fe41e557d3c611fd12ec">app_task_handler</a>(void *arg)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html">BluetoothA2DPSinkCallbacks</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html#a9e2c4b56d94c1198b2e5650739b9be1a">audio_data_callback</a>(const uint8_t *data, uint32_t len)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html">BluetoothA2DPSinkCallbacks</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html#a616986984ae0d0deb7fc94f1604e17ef">av_hdl_a2d_evt</a>(uint16_t event, void *p_param)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html">BluetoothA2DPSinkCallbacks</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html#a5562ce0b233c59b91d06bba2d894a9e2">av_hdl_avrc_evt</a>(uint16_t event, void *p_param)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html">BluetoothA2DPSinkCallbacks</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="odd"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html#a1a642fd44abbac9a50832db426f553ed">av_hdl_stack_evt</a>(uint16_t event, void *p_param)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_callbacks.html">BluetoothA2DPSinkCallbacks</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.2
</small></address>
</body>
</html>
