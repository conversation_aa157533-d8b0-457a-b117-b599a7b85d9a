import serial
import time

# Simple version - saves everything to file like your original code
def capture_all_data():
    """Simple capture - saves all serial data to file"""
    
    ser = serial.Serial('COM3', 115200)
    
    # Create filename with timestamp
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"chirp_all_{timestamp}.txt"
    
    print(f"Connected to NodeA on COM3")
    print(f"Saving all output to: {filename}")
    print("Send 'dump' command to NodeA...")
    print("Press Ctrl+C to stop\n")
    
    try:
        with open(filename, "w") as f:
            while True:
                line = ser.readline().decode(errors="ignore")
                print(line, end="")  # Show on screen
                f.write(line)       # Save to file
                f.flush()           # Ensure data is written immediately
                
    except KeyboardInterrupt:
        print(f"\n👋 Stopped. Data saved to: {filename}")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        ser.close()

if __name__ == "__main__":
    capture_all_data()
