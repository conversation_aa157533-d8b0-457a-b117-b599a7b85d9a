#include <stdint.h>
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include "adc.h"
#include <ti/drivers/Board.h>
#include "ti_drivers_config.h"
#include <math.h>


//*********************************************************************************************************
// Return the locally measured temperature
//*********************************************************************************************************

uint16_t Temperature_GetValueDeciDegrees()
{
    uint16_t Voltage = ADC_ReadSingleValueADCmV(ADCBUF_TEMPERATURE);
    float T = (13.582 -sqrt( 184.4707+0.01732*(2230.8-(float)Voltage)))/- 0.00866 + 30.0;
    return((int16_t)(T*10));
}
