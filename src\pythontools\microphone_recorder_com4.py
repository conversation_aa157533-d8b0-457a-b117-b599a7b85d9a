import serial
import time
import numpy as np
import soundfile as sf
from datetime import datetime
import os

def record_from_microphone():
    """Record audio data from ESP32 microphone via COM4 and create WAV file"""
    
    print("🎤 ESP32 Microphone Recorder (COM4)")
    print("=" * 50)
    
    # Connect to ESP32 on COM4
    print("📡 Connecting to ESP32 on COM4...")
    try:
        ser = serial.Serial('COM4', 115200, timeout=10)
        time.sleep(2)
        print("✅ Connected successfully!")
    except Exception as e:
        print(f"❌ Failed to connect to COM4: {e}")
        return False
    
    # Send recording command
    print("📤 Sending 'r' command to start recording...")
    ser.write(b'r\n')
    ser.flush()
    
    # Wait for recording to start
    print("🎯 Waiting for audio data...")
    samples = []
    capturing_data = False
    start_time = time.time()
    
    try:
        while True:
            line = ser.readline().decode(errors="ignore").strip()
            
            if line:
                print(f"📥 {line}")  # Show ESP32 output
                
                # Check if audio data starts
                if "--- AUDIO DATA START ---" in line:
                    print("✅ Audio data capture started...")
                    capturing_data = True
                    start_time = time.time()
                    continue
                
                # Check if audio data ends
                if "--- AUDIO DATA END ---" in line:
                    print(f"✅ Audio capture complete! Got {len(samples)} samples")
                    break
                
                # Capture sample data (only numeric lines)
                if capturing_data and line.lstrip('-').isdigit():
                    samples.append(int(line))
                    
                    # Progress indicator
                    if len(samples) % 48000 == 0:  # Every second at 48kHz
                        elapsed = time.time() - start_time
                        print(f"📊 Captured {len(samples)} samples ({elapsed:.1f}s)")
    
    except KeyboardInterrupt:
        print("\n⏹️  Recording interrupted by user")
        return False
    except Exception as e:
        print(f"❌ Error during capture: {e}")
        return False
    finally:
        ser.close()
        print("🔌 Serial connection closed")
    
    # Check if we got data
    if not samples:
        print("❌ No audio data captured!")
        return False
    
    # Convert to numpy array
    print("🔄 Converting to audio format...")
    signal = np.array(samples, dtype=np.int16)
    
    # Create WAV file in the same directory as this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    wav_filename = os.path.join(script_dir, "microphone_recording.wav")

    print(f"💾 Creating WAV file: {wav_filename}")
    
    # WAV parameters
    sample_rate = 48000  # 48 kHz
    
    try:
        sf.write(wav_filename, signal, samplerate=sample_rate, subtype='PCM_16')
        print(f"✅ WAV file created successfully!")
        print(f"📁 File: {wav_filename}")
        print(f"📊 Samples: {len(samples)}")
        print(f"⏱️  Duration: {len(samples)/sample_rate:.3f} seconds")
        print(f"🎵 Sample Rate: {sample_rate} Hz")
        print(f"🎤 Format: Mono, 16-bit")
        
        # Statistics
        print(f"📈 Signal Range: {np.min(signal)} to {np.max(signal)}")
        print(f"📊 RMS Level: {np.sqrt(np.mean(signal**2)):.1f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating WAV file: {e}")
        return False

def main():
    """Main function with user interaction"""
    print("🎤 ESP32 Microphone Recorder")
    print("📡 Connects to COM4 and records 10-second audio")
    print("⚙️  Config: 48kHz, 16-bit, Mono")
    print("-" * 50)
    
    while True:
        print("\n💡 Options:")
        print("  [r] Start recording")
        print("  [q] Quit")
        
        choice = input("Enter choice: ").lower().strip()
        
        if choice == 'r':
            success = record_from_microphone()
            if success:
                print("\n🎉 Recording completed successfully!")
            else:
                print("\n❌ Recording failed!")
        elif choice == 'q':
            print("👋 Goodbye!")
            break
        else:
            print("❓ Invalid choice. Use 'r' or 'q'.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Program interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
    
    print("🏁 Exiting...")
