<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: A2DPSimpleExponentialVolumeControl Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pro-methods">Protected Member Functions</a> &#124;
<a href="#pro-attribs">Protected Attributes</a> &#124;
<a href="class_a2_d_p_simple_exponential_volume_control-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">A2DPSimpleExponentialVolumeControl Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Exponentional volume control.  
 <a href="class_a2_d_p_simple_exponential_volume_control.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_a2_d_p_volume_control_8h_source.html">A2DPVolumeControl.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for A2DPSimpleExponentialVolumeControl:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_a2_d_p_simple_exponential_volume_control.png" usemap="#A2DPSimpleExponentialVolumeControl_map" alt=""/>
  <map id="A2DPSimpleExponentialVolumeControl_map" name="A2DPSimpleExponentialVolumeControl_map">
<area href="class_a2_d_p_volume_control.html" title="Abstract class for handling of the volume of the audio data." alt="A2DPVolumeControl" shape="rect" coords="0,0,230,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a6af31a3ffaad03ffa06930bbb3d3d285"><td class="memItemLeft" align="right" valign="top"><a id="a6af31a3ffaad03ffa06930bbb3d3d285"></a>
int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>get_volume_factor</b> ()</td></tr>
<tr class="separator:a6af31a3ffaad03ffa06930bbb3d3d285"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae5ff3aa2b2b615e7eb01c8a8f164ef60"><td class="memItemLeft" align="right" valign="top"><a id="ae5ff3aa2b2b615e7eb01c8a8f164ef60"></a>
int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>get_volume_factor_max</b> ()</td></tr>
<tr class="separator:ae5ff3aa2b2b615e7eb01c8a8f164ef60"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abdb9256f6a30b6c716193af7fadeee9c"><td class="memItemLeft" align="right" valign="top"><a id="abdb9256f6a30b6c716193af7fadeee9c"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_enabled</b> (bool enabled)</td></tr>
<tr class="separator:abdb9256f6a30b6c716193af7fadeee9c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a533745afd59e80e2549c9dfa80e05423"><td class="memItemLeft" align="right" valign="top"><a id="a533745afd59e80e2549c9dfa80e05423"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_mono_downmix</b> (bool enabled)</td></tr>
<tr class="separator:a533745afd59e80e2549c9dfa80e05423"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6eb1add5a68fe554c6705cb9fad25545"><td class="memItemLeft" align="right" valign="top"><a id="a6eb1add5a68fe554c6705cb9fad25545"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>update_audio_data</b> (Frame *data, uint16_t frameCount)</td></tr>
<tr class="separator:a6eb1add5a68fe554c6705cb9fad25545"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8f23bd37f10a58fd03cebe5d9b180802"><td class="memItemLeft" align="right" valign="top"><a id="a8f23bd37f10a58fd03cebe5d9b180802"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>update_audio_data</b> (uint8_t *data, uint16_t byteCount)</td></tr>
<tr class="separator:a8f23bd37f10a58fd03cebe5d9b180802"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-methods"></a>
Protected Member Functions</h2></td></tr>
<tr class="memitem:a23700514ed3bb33151ea90033e8dcbea"><td class="memItemLeft" align="right" valign="top"><a id="a23700514ed3bb33151ea90033e8dcbea"></a>
int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>clip</b> (int32_t value)</td></tr>
<tr class="separator:a23700514ed3bb33151ea90033e8dcbea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d3c9b6e1737bea85096ddc58c6cf853"><td class="memItemLeft" align="right" valign="top"><a id="a2d3c9b6e1737bea85096ddc58c6cf853"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_volume</b> (uint8_t volume) override</td></tr>
<tr class="separator:a2d3c9b6e1737bea85096ddc58c6cf853"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-attribs"></a>
Protected Attributes</h2></td></tr>
<tr class="memitem:a7982efce1f1a1ab6c902900f8e0bb696"><td class="memItemLeft" align="right" valign="top"><a id="a7982efce1f1a1ab6c902900f8e0bb696"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>is_volume_used</b> = false</td></tr>
<tr class="separator:a7982efce1f1a1ab6c902900f8e0bb696"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b449f76c7c931cf590732a3d73acaea"><td class="memItemLeft" align="right" valign="top"><a id="a8b449f76c7c931cf590732a3d73acaea"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>mono_downmix</b> = false</td></tr>
<tr class="separator:a8b449f76c7c931cf590732a3d73acaea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc84ddb465e3fed9c0903a9f3e565be8"><td class="memItemLeft" align="right" valign="top"><a id="adc84ddb465e3fed9c0903a9f3e565be8"></a>
int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>volumeFactor</b></td></tr>
<tr class="separator:adc84ddb465e3fed9c0903a9f3e565be8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adf01b0f44d8f482ed5119f088958595e"><td class="memItemLeft" align="right" valign="top"><a id="adf01b0f44d8f482ed5119f088958595e"></a>
int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>volumeFactorMax</b></td></tr>
<tr class="separator:adf01b0f44d8f482ed5119f088958595e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Exponentional volume control. </p>
<dl class="section author"><dt>Author</dt><dd>rbruelma </dd></dl>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li>src/<a class="el" href="_a2_d_p_volume_control_8h_source.html">A2DPVolumeControl.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
