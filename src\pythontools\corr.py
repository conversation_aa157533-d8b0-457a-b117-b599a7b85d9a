import numpy as np
import soundfile as sf
import matplotlib.pyplot as plt
from scipy.signal import correlate, hann

# === Parameter aus deinem C++ Code ===
SAMPLE_RATE = 48000
CHIRP_DURATION = 0.050  # 50 ms
CHIRP_SAMPLES = int(SAMPLE_RATE * CHIRP_DURATION)
F_START = 800.0
F_END = 3500.0
CHIRP_K = (F_END - F_START) / CHIRP_SAMPLES
FADE_SAMPLES = 144

# === Chirp-Signal generieren ===
def generate_sine_chirp():
    n = np.arange(CHIRP_SAMPLES)
    t = n / SAMPLE_RATE
    phase = 2.0 * np.pi * (F_START * t + CHIRP_K * n * t / 2.0)
    chirp = np.sin(phase)

    # Hann-Fenster für Fade-in/out
    ramp = np.ones_like(chirp)
    hann_window = hann(2 * FADE_SAMPLES)
    ramp[:FADE_SAMPLES] = hann_window[:FADE_SAMPLES]
    ramp[-FADE_SAMPLES:] = hann_window[-FADE_SAMPLES:]
    chirp *= ramp

    return chirp

# === WAV-Datei laden ===
def load_wav(filepath):
    signal, fs = sf.read(filepath)
    if fs != SAMPLE_RATE:
        raise ValueError(f"Sample rate mismatch! Expected {SAMPLE_RATE}, got {fs}")
    if signal.ndim > 1:
        signal = signal[:, 0]  # nur erster Kanal
    return signal

# === Korrelation durchführen ===
def correlate_with_chirp(recorded, chirp):
    correlation = correlate(recorded, chirp, mode='valid')
    return correlation

# === Hauptfunktion ===
def main():
    wav_path = "aufnahme.wav"  # Pfad zur aufgenommenen Datei
    recorded = load_wav(wav_path)
    chirp = generate_sine_chirp()

    corr = correlate_with_chirp(recorded, chirp)

    # === Visualisieren ===
    plt.figure(figsize=(10, 6))
    plt.plot(corr)
    plt.title("Korrelation mit Chirp")
    plt.xlabel("Samples")
    plt.ylabel("Korrelationswert")
    plt.grid()
    plt.show()

    # Maxima ausgeben
    max_idx = np.argmax(corr)
    print(f"Maximale Korrelation bei Index {max_idx} (Zeit: {max_idx / SAMPLE_RATE * 1000:.2f} ms)")

if __name__ == "__main__":
    main()
