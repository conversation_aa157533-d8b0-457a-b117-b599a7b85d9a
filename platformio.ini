; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html


[env:nodeA]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
upload_port = COM3
build_flags = 
    -D IS_NODE_A
    -Ilib/AudioUtils
    -Ilib/ESPNowManager
lib_deps = 
    arduino-libraries/ArduinoBLE@1.3.3
lib_extra_dirs = 
    lib
board_build.partitions = default.csv
board_upload.flash_size = 4MB
src_filter = +<nodeA>

[env:nodeB]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
upload_port = COM4
build_flags = 
    -D IS_NODE_B
    -Ilib/AudioUtils
    -Ilib/ESPNowManager
lib_deps = 
    arduino-libraries/ArduinoBLE@1.3.3
lib_extra_dirs = 
    lib
board_build.partitions = default.csv
board_upload.flash_size = 4MB
src_filter = +<nodeB>

[env:nodeA_test]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
upload_port = COM3
build_flags = 
    -D IS_NODE_A
    -Ilib/AudioUtils
    -Ilib/ESPNowManager
lib_deps = 
    arduino-libraries/ArduinoBLE@1.3.3
lib_extra_dirs = 
    lib
board_build.partitions = default.csv
board_upload.flash_size = 4MB
src_filter = +<../test/espnow_comm_test.cpp> -<nodeA> -<nodeB>

[env:nodeB_test]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
upload_port = COM4
build_flags = 
    -D IS_NODE_B
    -Ilib/AudioUtils
    -Ilib/ESPNowManager
lib_deps = 
    arduino-libraries/ArduinoBLE@1.3.3
lib_extra_dirs = 
    lib
board_build.partitions = default.csv
board_upload.flash_size = 4MB
src_filter = +<../test/espnow_comm_test.cpp> -<nodeA> -<nodeB>

[env:nodeB_mic_test]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
upload_port = COM4
build_flags = 
    -D IS_NODE_B
    -Ilib/AudioUtils
    -Ilib/ESPNowManager
    -Isrc/nodeB
lib_deps = 
    arduino-libraries/ArduinoBLE@1.3.3
lib_extra_dirs = 
    lib
board_build.partitions = default.csv
board_upload.flash_size = 4MB
src_filter = +<../test/microphone_test.cpp> +<nodeB/i2s_receiver.cpp> -<nodeB/main.cpp>

[env:bt_speaker]
platform = espressif32
board = esp32dev
framework = arduino
monitor_speed = 115200
lib_deps = 
    # Die Bibliothek ist bereits im lib-Verzeichnis, daher kein externer Verweis nötig
lib_extra_dirs = 
    lib
# Nur die Test-Datei kompilieren, nicht die nodeA und nodeB Dateien
src_filter = +<../test/simple_bt_speaker.cpp> -<nodeA> -<nodeB>


[env:nodeA_uart_test]
platform = espressif32
board = esp32dev
framework = arduino
upload_port = COM3
monitor_speed = 115200
build_flags = -D IS_NODE_A
lib_extra_dirs = lib
src_filter = +<../test/espnow_uart_trigger_test.cpp>

[env:nodeB_uart_test]
platform = espressif32
board = esp32dev
framework = arduino
upload_port = COM4
monitor_speed = 115200
build_flags = -D IS_NODE_B
lib_extra_dirs = lib
src_filter = +<../test/espnow_uart_trigger_test.cpp>
