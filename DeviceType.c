
#include <Devicetype.h>
#include <stdint.h>
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <ti/drivers/Board.h>
#include "ti_drivers_config.h"
#include "gpio.h"

DeviceTypes DeviceType;

//*********************************************************************************************************
// read devicetype from 2 IO pins on the board.
//*********************************************************************************************************
void DeviceType_ReadFromBoard(void)
{
    uint8_t ID;
    ID = Gpio_GetBoardType_ID1*2;
    ID+= Gpio_GetBoardType_ID0;
    switch (ID)
    {
    case 0:   { DeviceType=eDev0;
                break;
              }
    case 1:   { DeviceType=eDevX;
                break;
              }
    case 3:   { DeviceType=eDevMobile;
                break;
              }
    default: { while(1); // not supported
             }
    }
}

