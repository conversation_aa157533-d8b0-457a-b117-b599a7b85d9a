#ifndef UART_H_
#define UART_H_

typedef enum {eSCNone,eSCMeasureMX,eSCMeasureM0,eSCMeasureX0,eSCMeasureAll,eSCReadSamples0,eSCReadSamplesX,eSCReadSamplesM } SerialCommandType;

void Serial_Init(void);
void Serial_PrintString(char* S);
void Serial_PrintInteger(int16_t i);
void Serial_PrintMeasurements(uint8_t Type, uint8_t NumberOfMeasurements,uint16_t P1, uint16_t P2,uint16_t P3,uint16_t P4,uint16_t P5,uint16_t P6,uint16_t P7 );
SerialCommandType Serial_CheckCommand(void);
void Serial_CopyReceivedArraySectionToSerial(void);
void Serial_CopyArraySectionToSerial(uint8_t Section,uint16_t* Array );

#endif /* UART_H_ */
