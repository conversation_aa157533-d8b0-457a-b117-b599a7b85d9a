<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: OneChannelSoundData Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="class_one_channel_sound_data-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">OneChannelSoundData Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>1 Channel data is provided as int16 values  
 <a href="class_one_channel_sound_data.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_sound_data_8h_source.html">SoundData.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for OneChannelSoundData:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_one_channel_sound_data.png" usemap="#OneChannelSoundData_map" alt=""/>
  <map id="OneChannelSoundData_map" name="OneChannelSoundData_map">
<area href="class_sound_data.html" title="Sound data as byte stream. We support TwoChannelSoundData (uint16_t + uint16_t) and OneChannelSoundDa..." alt="SoundData" shape="rect" coords="0,0,145,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a97d80e321f19672e020b84f6e4d203fb"><td class="memItemLeft" align="right" valign="top"><a id="a97d80e321f19672e020b84f6e4d203fb"></a>
&#160;</td><td class="memItemRight" valign="bottom"><b>OneChannelSoundData</b> (bool loop=false, <a class="el" href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">ChannelInfo</a> channelInfo=<a class="el" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0aedf69634e61e7ec5d006874d299bc0d4">Both</a>)</td></tr>
<tr class="separator:a97d80e321f19672e020b84f6e4d203fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a95a08a39b92863edbc6c759e7c98e4ac"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_one_channel_sound_data.html#a95a08a39b92863edbc6c759e7c98e4ac">OneChannelSoundData</a> (int16_t *data, int32_t len, bool loop=false, <a class="el" href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">ChannelInfo</a> channelInfo=<a class="el" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0aedf69634e61e7ec5d006874d299bc0d4">Both</a>)</td></tr>
<tr class="separator:a95a08a39b92863edbc6c759e7c98e4ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac79933ed3379cf5ef58d5675aa4bf12e"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_sound_data.html#ac79933ed3379cf5ef58d5675aa4bf12e">doLoop</a> ()</td></tr>
<tr class="separator:ac79933ed3379cf5ef58d5675aa4bf12e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ed4a75be0bb4dbb42752f5ae83badbb"><td class="memItemLeft" align="right" valign="top">int32_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_one_channel_sound_data.html#a3ed4a75be0bb4dbb42752f5ae83badbb">get2ChannelData</a> (int32_t pos, int32_t len, uint8_t *data)</td></tr>
<tr class="separator:a3ed4a75be0bb4dbb42752f5ae83badbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a439d88a10cdff91c1f857be24dcb5f40"><td class="memItemLeft" align="right" valign="top"><a id="a439d88a10cdff91c1f857be24dcb5f40"></a>
int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>getData</b> (int32_t pos, Frame &amp;frame)</td></tr>
<tr class="separator:a439d88a10cdff91c1f857be24dcb5f40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab3a4acbfe07ee0f82c74305805df4be2"><td class="memItemLeft" align="right" valign="top"><a id="ab3a4acbfe07ee0f82c74305805df4be2"></a>
int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>getData</b> (int32_t pos, int32_t len, int16_t *data)</td></tr>
<tr class="separator:ab3a4acbfe07ee0f82c74305805df4be2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76e9fab6d3b8e5d55742a1b685dcc9a4"><td class="memItemLeft" align="right" valign="top"><a id="a76e9fab6d3b8e5d55742a1b685dcc9a4"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>setData</b> (int16_t *data, int32_t len)</td></tr>
<tr class="separator:a76e9fab6d3b8e5d55742a1b685dcc9a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad30d3ead951e2b25bf6e1049749d8cb8"><td class="memItemLeft" align="right" valign="top"><a id="ad30d3ead951e2b25bf6e1049749d8cb8"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>setDataRaw</b> (uint8_t *data, int32_t len)</td></tr>
<tr class="separator:ad30d3ead951e2b25bf6e1049749d8cb8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed157f28a14d6fd8ef1571db60afd003"><td class="memItemLeft" align="right" valign="top"><a id="aed157f28a14d6fd8ef1571db60afd003"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>setLoop</b> (bool loop)</td></tr>
<tr class="separator:aed157f28a14d6fd8ef1571db60afd003"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>1 Channel data is provided as int16 values </p>
<dl class="section author"><dt>Author</dt><dd>Phil Schatzmann </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Apache License Version 2 </dd></dl>
</div><h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a id="a95a08a39b92863edbc6c759e7c98e4ac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a95a08a39b92863edbc6c759e7c98e4ac">&#9670;&nbsp;</a></span>OneChannelSoundData()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">OneChannelSoundData::OneChannelSoundData </td>
          <td>(</td>
          <td class="paramtype">int16_t *&#160;</td>
          <td class="paramname"><em>data</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>len</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&#160;</td>
          <td class="paramname"><em>loop</em> = <code>false</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">ChannelInfo</a>&#160;</td>
          <td class="paramname"><em>channelInfo</em> = <code><a class="el" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0aedf69634e61e7ec5d006874d299bc0d4">Both</a></code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Constructor for data conisting only of one Channel </p>

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a id="ac79933ed3379cf5ef58d5675aa4bf12e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac79933ed3379cf5ef58d5675aa4bf12e">&#9670;&nbsp;</a></span>doLoop()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">bool SoundData::doLoop </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inherited</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Automatic restart playing on end </p>

</div>
</div>
<a id="a3ed4a75be0bb4dbb42752f5ae83badbb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3ed4a75be0bb4dbb42752f5ae83badbb">&#9670;&nbsp;</a></span>get2ChannelData()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int32_t OneChannelSoundData::get2ChannelData </td>
          <td>(</td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>pos</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int32_t&#160;</td>
          <td class="paramname"><em>len</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint8_t *&#160;</td>
          <td class="paramname"><em>data</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">virtual</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Data is stored in one channel with int16_t data. However we need to provide 2 channels. pos, len and result are in bytes. </p>

<p>Implements <a class="el" href="class_sound_data.html">SoundData</a>.</p>

</div>
</div>
<hr/>The documentation for this class was generated from the following files:<ul>
<li>src/<a class="el" href="_sound_data_8h_source.html">SoundData.h</a></li>
<li>src/SoundData.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
