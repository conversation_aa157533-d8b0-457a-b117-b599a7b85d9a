/**
 *  espnow_uart_trigger_test.cpp
 *
 *  Node‑A (IS_NODE_A):
 *    – wartet auf UART‑Byte 's'
 *    – erzeugt einen <PERSON>s, sendet Byte 's' per ESP‑Now an Node‑B
 *
 *  Node‑B (IS_NODE_B):
 *    – pulst bei jedem Empfang
 *    – gibt das Kommando sowie Zeitstempel über UART aus
 *
 *  MAC‑Adressen sind fest verdrahtet (s. unten).
 */

#include <Arduino.h>
#include <WiFi.h>
#include <esp_now.h>

// --------- Konstanten ----------------------------------------------
constexpr gpio_num_t PULSE_PIN   = GPIO_NUM_12;   // frei wählbar
constexpr uint8_t    CMD_BYTE    = 's';          // ≙ 0x73

static const uint8_t NODEA_MAC[6] = {0x08, 0x3A, 0xF2, 0xB7, 0x50, 0x5C};
static const uint8_t NODEB_MAC[6] = {0x50, 0x02, 0x91, 0x98, 0xFC, 0xBC};

// --------- Hilfsfunktionen -----------------------------------------
void pulse()
{
    gpio_set_level(PULSE_PIN, 1);
    delayMicroseconds(5);          // ~5 µs Breite
    gpio_set_level(PULSE_PIN, 0);
}

// ===================================================================
//                              Node‑A
// ===================================================================
#ifdef IS_NODE_A
void setup()
{
    Serial.begin(115200);
    pinMode(GPIO_NUM_13, OUTPUT);  digitalWrite(GPIO_NUM_13, LOW);

    WiFi.mode(WIFI_STA);
    if (esp_now_init() != ESP_OK) { Serial.println("ESP‑Now init fail"); while (1); }

    esp_now_peer_info_t peer = {};
    memcpy(peer.peer_addr, NODEB_MAC, 6);
    peer.channel = 0;      peer.encrypt = false;
    esp_now_add_peer(&peer);

    Serial.println("Node‑A bereit – sende 's' über UART zum Triggern.");
}

void loop()
{
    if (Serial.available()) {
        int c = Serial.read();
        if (c == CMD_BYTE) {
            gpio_set_level(GPIO_NUM_13, 1);
    delayMicroseconds(5);          // ~5 µs Breite
    gpio_set_level(GPIO_NUM_13, 0);                                    // Logikanalyser‑Puls
            uint8_t payload = CMD_BYTE;
            esp_now_send(NODEB_MAC, &payload, 1);       // 1‑Byte‑Kommando
            Serial.println("CMD 's' gesendet");
        }
    }
}
#endif // IS_NODE_A

// ===================================================================
//                              Node‑B
// ===================================================================
#ifdef IS_NODE_B
void onRecv(const uint8_t *mac, const uint8_t *data, int len)
{
    pulse();                                            // Puls für LA

    Serial.print("Empfangen von ");
    for (int i=0;i<6;i++) { Serial.printf("%02X", mac[i]); if (i<5) Serial.print(":"); }
    Serial.printf("  (%d Byte)  Zeit=%u µs  Data=0x%02X\n",
                  len, (uint32_t)esp_timer_get_time(), data[0]);
}

void setup()
{
    Serial.begin(115200);
    pinMode(PULSE_PIN, OUTPUT);  digitalWrite(PULSE_PIN, LOW);

    WiFi.mode(WIFI_STA);
    if (esp_now_init() != ESP_OK) { Serial.println("ESP‑Now init fail"); while (1); }
    esp_now_register_recv_cb(onRecv);

    Serial.println("Node‑B wartet auf ESP‑Now‑Kommandos …");
}

void loop() { /* nichts */ }
#endif // IS_NODE_B
