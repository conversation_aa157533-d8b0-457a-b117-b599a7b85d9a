******************************************************************************
                  TI ARM Linker PC v20.2.4                     
******************************************************************************
>> Linked Mon Feb 22 19:52:12 2021

OUTPUT FILE NAME:   <SoundPositioning.out>
ENTRY POINT SYMBOL: "resetISR"  address: 00004711


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00058000  00004d4e  000532b2  R  X
  GPRAM                 11000000   00002000  00000000  00002000  RW X
  SRAM                  20000000   00014000  00004ee2  0000f11e  RW X
  LOG_DATA              90000000   00040000  00000000  00040000  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00004c10   00004c10    r-x
  00000000    00000000    000000d8   000000d8    r-- .intvecs
  000000d8    000000d8    0000489e   0000489e    r-x .text
  00004978    00004978    00000298   00000298    r-- .const
00004c10    00004c10    00000008   00000008    rw-
  00004c10    00004c10    00000008   00000008    rw- .args
00004c18    00004c18    000000e0   000000e0    r--
  00004c18    00004c18    000000e0   000000e0    r-- .cinit
00057fa8    00057fa8    00000058   00000058    r--
  00057fa8    00057fa8    00000058   00000058    r-- .ccfg
20000000    20000000    000009ea   00000000    rw-
  20000000    20000000    00000604   00000000    rw- .bss
  20000604    20000604    000003e6   00000000    rw- .data
20000a00    20000a00    000000d8   00000000    rw-
  20000a00    20000a00    000000d8   00000000    rw- .vtable_ram
20001870    20001870    00000010   00000000    rw-
  20001870    20001870    00000010   00000000    rw- .TI.bound:dmaADCPriControlTableEntry
20001a70    20001a70    00004010   00000000    rw-
  20001a70    20001a70    00000010   00000000    rw- .TI.bound:dmaADCAltControlTableEntry
  20001a80    20001a80    00004000   00000000    rw- .sysmem
20013c00    20013c00    00000400   00000000    rw-
  20013c00    20013c00    00000400   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000d8     
                  00000000    000000d8     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.intvecs)

.text      0    000000d8    0000489e     
                  000000d8    000001e0     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_init)
                  000002b8    00000198                      : PowerCC26X2.oem4f (.text:Power_sleep)
                  00000450    00000190                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf1)
                  000005e0    0000018c                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:runCalibrateFsm)
                  0000076c    00000174                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_open)
                  000008e0    00000174                      : PowerCC26X2.oem4f (.text:Power_init)
                  00000a54    00000164                      : UARTCC26XX.oem4f (.text:UARTCC26XX_open)
                  00000bb8    00000154                      : UARTCC26XX.oem4f (.text:UARTCC26XX_hwiIntFxn)
                  00000d0c    0000013a                      : UARTCC26XX.oem4f (.text:UARTCC26XX_read)
                  00000e46    00000002     ti_drivers_config.obj (.text:Board_initHook)
                  00000e48    00000120     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureXOSCHF)
                  00000f68    00000114                      : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfg)
                  0000107c    00000110                      : GPIOCC26XX.oem4f (.text:GPIO_setConfig)
                  0000118c    00000100                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convert)
                  0000128c    00000100     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:aligned_alloc)
                  0000138c    000000f0     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlStandby)
                  0000147c    000000e4     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free)
                  00001560    000000e0     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_standbyPolicy)
                  00001640    000000e0     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_write)
                  00001720    000000d8     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_construct)
                  000017f8    000000c4     drivers_cc13x2.a : GPIOCC26XX.oem4f (.text:GPIO_init)
                  000018bc    000000c0                      : PowerCC26X2.oem4f (.text:Power_releaseDependency)
                  0000197c    000000c0                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initIO)
                  00001a3c    000000bc     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_construct)
                  00001af8    000000b8     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_setDependency)
                  00001bb0    000000b8                      : UARTCC26XX.oem4f (.text:UARTCC26XX_initHw)
                  00001c68    000000b4     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_startup)
                  00001d1c    000000ac                     : ClockPTimer_nortos.oem4f (.text:ClockP_workFuncDynamic)
                  00001dc8    000000ac     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_open)
                  00001e74    000000ac     driverlib.lib : setup.obj (.text:TrimAfterColdResetWakeupFromShutDown)
                  00001f20    000000a0     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_pend)
                  00001fc0    0000009c                     : SwiP_nortos.oem4f (.text:SwiP_dispatch)
                  0000205c    0000009c     rtsv7M4_T_le_v4SPD16_eabi.lib : memcpy_t2.asm.obj (.text)
                  000020f8    00000098     driverlib.lib : setup.obj (.text:NOROM_SetupTrimDevice)
                  00002190    00000098     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeTxFifoFlush)
                  00002228    00000096     rtsv7M4_T_le_v4SPD16_eabi.lib : ull_div_t2.asm.obj (.text)
                  000022be    00000092     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_writeCancel)
                  00002350    00000090     SoundTX.obj (.text:InitTimer2)
                  000023e0    00000088     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_open)
                  00002468    00000088     driverlib.lib : osc.obj (.text:NOROM_OSCHF_AttemptToSwitchToXosc)
                  000024f0    00000088     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:calibrateRcoscHf2)
                  00002578    00000088                      : PowerCC26X2.oem4f (.text:disableLFClockQualifiers)
                  00002600    00000088                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:updateSubSecInc)
                  00002688    00000084                      : UARTCC26XX.oem4f (.text:UARTCC26XX_close)
                  0000270c    00000080                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_hwiFxn)
                  0000278c    00000080     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_start)
                  0000280c    00000080                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_construct)
                  0000288c    00000080     driverlib.lib : sys_ctrl.obj (.text:NOROM_SysCtrlSetRechargeBeforePowerDown)
                  0000290c    0000007a     rtsv7M4_T_le_v4SPD16_eabi.lib : memset_t2.asm.obj (.text)
                  00002986    00000002     driverlib.lib : interrupt.obj (.text:IntDefaultHandler)
                  00002988    00000078     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_walkQueueDynamic)
                  00002a00    00000074     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_resetHw)
                  00002a74    00000074                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_auxISR)
                  00002ae8    00000074     SoundTX.obj (.text:Timer2AInterruptHandler)
                  00002b5c    00000070     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_add)
                  00002bcc    00000070     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:split)
                  00002c3c    0000006c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipType)
                  00002ca8    00000068                   : osc.obj (.text:NOROM_OSCHF_SwitchToRcOscTurnOffXosc)
                  00002d10    00000068     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_post)
                  00002d78    00000068                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_start)
                  00002de0    00000068     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_lzss.c.obj (.text:decompress:lzss:__TI_decompress_lzss)
                  00002e48    00000064     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_control)
                  00002eac    00000062     ti_drivers_config.obj (.text:Board_sendExtFlashByte)
                  00002f0e    00000062     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_swiIntFxn)
                  00002f70    00000060                      : GPIOCC26XX.oem4f (.text:GPIO_write)
                  00002fd0    0000005c                      : PINCC26XX.oem4f (.text:PIN_remove)
                  0000302c    0000005c                      : UARTCC26XX.oem4f (.text:startTxFifoEmptyClk)
                  00003088    00000058                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_loadDMAControlTableEntry)
                  000030e0    00000058                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_stop)
                  00003138    00000058                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_initiateCalibration)
                  00003190    00000054                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_adjustRawValues)
                  000031e4    00000054     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOff)
                  00003238    00000050     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_construct)
                  00003288    00000050     mainNew.obj (.text:main)
                  000032d8    0000004e     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_close)
                  00003326    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_destruct)
                  00003328    0000004c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_cleanADC)
                  00003374    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_initHw)
                  000033c0    0000004c                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_start)
                  0000340c    0000004c     driverlib.lib : prcm.obj (.text:NOROM_PRCMPowerDomainsAllOn)
                  00003458    0000004c     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readCancel)
                  000034a4    0000004c                      : UDMACC26XX.oem4f (.text:UDMACC26XX_open)
                  000034f0    0000004a                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_control)
                  0000353a    00000002     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.text:_nop)
                  0000353c    00000048     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertAdjustedToMicroVolts)
                  00003584    00000048     ti_drivers_config.obj (.text:Board_wakeUpExtFlash)
                  000035cc    00000048     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_construct)
                  00003614    00000048     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputEnable)
                  0000365c    00000048                      : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_RCOSC_clockFunc)
                  000036a4    00000048                      : PowerCC26X2.oem4f (.text:switchXOSCHF)
                  000036ec    00000048                      : UARTCC26XX.oem4f (.text:writeFinishedDoCallback)
                  00003734    00000046                      : RingBuf.oem4f (.text:RingBuf_put)
                  0000377a    00000002     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:clkFxn)
                  0000377c    00000044     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_init)
                  000037c0    00000044     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetHwRevision)
                  00003804    00000044     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_swi)
                  00003848    00000044     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_post)
                  0000388c    00000044                     : SwiP_nortos.oem4f (.text:SwiP_restore)
                  000038d0    00000044     drivers_cc13x2.a : UART.oem4f (.text:UART_init)
                  00003914    00000044     rtsv7M4_T_le_v4SPD16_eabi.lib : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit:__TI_auto_init_nobinit_nopinit)
                  00003958    00000042     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_swiFxn)
                  0000399a    00000002                      : PowerCC26X2.oem4f (.text:emptyClockFunc)
                  0000399c    00000040                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_convertCancel)
                  000039dc    00000040     driverlib.lib : osc.obj (.text:NOROM_OSC_HPOSCRelativeFrequencyOffsetGet)
                  00003a1c    00000040                   : timer.obj (.text:NOROM_TimerIntRegister)
                  00003a5c    00000040     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_get)
                  00003a9c    00000040                      : UART.oem4f (.text:UART_open)
                  00003adc    0000003c     mainNew.obj (.text:InitUart)
                  00003b18    0000003c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setOutputValue)
                  00003b54    00000038                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_configDMA)
                  00003b8c    00000038     ti_drivers_config.obj (.text:Board_shutDownExtFlash)
                  00003bc4    00000038     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_setConfig)
                  00003bfc    00000038     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCurrentTick)
                  00003c34    00000038                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_initDevice)
                  00003c6c    00000038     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:notify)
                  00003ca4    00000038                      : UARTCC26XX.oem4f (.text:readData)
                  00003cdc    00000038                      : UARTCC26XX.oem4f (.text:writeData)
                  00003d14    00000036     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setNextTick)
                  00003d4a    00000036     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_close)
                  00003d80    00000034     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicksUntilInterrupt)
                  00003db4    00000034     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XXSetLoadMatch)
                  00003de8    00000034                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_configureDebugStall)
                  00003e1c    00000034     driverlib.lib : aux_sysif.obj (.text:NOROM_AUXSYSIFOpModeChange)
                  00003e50    00000034                   : interrupt.obj (.text:NOROM_IntRegister)
                  00003e84    00000034                   : sys_ctrl.obj (.text:NOROM_SysCtrlIdle)
                  00003eb8    00000034     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_destruct)
                  00003eec    00000034     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:readData2RingBuf)
                  00003f20    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_hwiIntFxn)
                  00003f50    00000030                      : GPIOCC26XX.oem4f (.text:GPIO_setCallback)
                  00003f80    00000030                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_close)
                  00003fb0    00000030                      : PINCC26XX.oem4f (.text:PINCC26XX_setMux)
                  00003fe0    00000030                      : PowerCC26X2.oem4f (.text:Power_releaseConstraint)
                  00004010    00000030                      : PowerCC26X2.oem4f (.text:Power_setConstraint)
                  00004040    00000030     driverlib.lib : timer.obj (.text:TimerIntNumberGet)
                  00004070    00000030     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.text:_c_int00:_c_int00)
                  000040a0    0000002c     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_acquireADCSemaphore)
                  000040cc    0000002c                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_releaseADCSemaphore)
                  000040f8    0000002c     SoundTX.obj (.text:SoundTransmit)
                  00004124    0000002c     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_setThreshold)
                  00004150    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_registerNotify)
                  00004178    00000028     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_insert)
                  000041a0    00000028     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:oscillatorISR)
                  000041c8    00000026                      : List.oem4f (.text:List_put)
                  000041ee    00000026                      : List.oem4f (.text:List_remove)
                  00004214    00000024     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_getTicks)
                  00004238    00000024                     : ClockPTimer_nortos.oem4f (.text:ClockP_scheduleNextTick)
                  0000425c    00000024                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_dispatch)
                  00004280    00000024     driverlib.lib : osc.obj (.text:NOROM_OSCHF_TurnOnXosc)
                  000042a4    00000024     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_getMaxTicks)
                  000042c8    00000024     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:getTdcSemaphore)
                  000042ec    00000020     ti_drivers_config.obj (.text:Board_init)
                  0000430c    00000020     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_add)
                  0000432c    00000020     driverlib.lib : aux_adc.obj (.text:NOROM_AUXADCEnableSync)
                  0000434c    00000020     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_setIoCfgMux)
                  0000436c    00000020                      : PowerCC26X2.oem4f (.text:Power_unregisterNotify)
                  0000438c    00000020     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_put)
                  000043ac    00000020                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_dynamicStub)
                  000043cc    00000020     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:threadSafeStdbyDisRelease)
                  000043ec    00000020                      : UARTCC26XX.oem4f (.text:threadSafeStdbyDisSet)
                  0000440c    0000001e                      : PINCC26XX.oem4f (.text:PIN_close)
                  0000442a    0000001e                      : PowerCC26X2.oem4f (.text:Power_getTransitionLatency)
                  00004448    0000001e     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_constructBinary)
                  00004466    0000001e                     : SwiP_nortos.oem4f (.text:SwiP_or)
                  00004484    0000001c                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_destruct)
                  000044a0    0000001c     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetChipFamily)
                  000044bc    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_hwi)
                  000044d8    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_disable)
                  000044f4    0000001c     drivers_cc13x2.a : UDMACC26XX.oem4f (.text:UDMACC26XX_initHw)
                  00004510    0000001c     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:free_list_remove)
                  0000452c    0000001a     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:PowerCC26XX_calibrate)
                  00004546    0000001a     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_get)
                  00004560    0000001a                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_createBinary)
                  0000457a    00000018                     : ClockPTimer_nortos.oem4f (.text:ClockP_destruct)
                  00004592    00000002                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:faultISR)
                  00004594    00000018                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_inISR)
                  000045ac    00000018     driverlib.lib : chipinfo.obj (.text:NOROM_ChipInfo_GetPackageType)
                  000045c4    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_idleFunc)
                  000045dc    00000018     nortos_cc13x2.a : TimerPCC26XX_nortos.oem4f (.text:TimerP_startup)
                  000045f4    00000018     drivers_cc13x2.a : UART.oem4f (.text:UART_Params_init)
                  0000460c    00000018     rtsv7M4_T_le_v4SPD16_eabi.lib : args_main.c.obj (.text:_args_main)
                  00004624    00000018                                   : ll_mul_t2.asm.obj (.text)
                  0000463c    00000018     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:switchToTCXO)
                  00004654    00000016     driverlib.lib : chipinfo.obj (.text:NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated)
                  0000466a    00000016     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_create)
                  00004680    00000016                     : SwiP_nortos.oem4f (.text:maxbit)
                  00004696    00000014     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_freqToCounts)
                  000046aa    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:intDefaultHandler)
                  000046ac    00000014     driverlib.lib : interrupt.obj (.text:NOROM_IntUnregister)
                  000046c0    00000014     drivers_cc13x2.a : PowerCC26X2_calibrateRCOSC.oem4f (.text:PowerCC26X2_calibrate)
                  000046d4    00000014                      : PowerCC26X2.oem4f (.text:Power_disablePolicy)
                  000046e8    00000014     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_handleHwi)
                  000046fc    00000014                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getCount64)
                  00004710    00000014                     : startup_cc13x2_cc26x2_ccs.oem4f (.text:resetISR)
                  00004724    00000012     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_Params_init)
                  00004736    00000012     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_Params_init)
                  00004748    00000012     drivers_cc13x2.a : RingBuf.oem4f (.text:RingBuf_construct)
                  0000475a    00000002     nortos_cc13x2.a : startup_cc13x2_cc26x2_ccs.oem4f (.text:nmiISR)
                  0000475c    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_Params_init)
                  0000476c    00000010                     : ClockPTimer_nortos.oem4f (.text:ClockP_getCpuFreq)
                  0000477c    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_clearInterrupt)
                  0000478c    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_disable)
                  0000479c    00000010                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_post)
                  000047ac    00000010     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PINCC26XX_getPinCount)
                  000047bc    00000010     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerDisable)
                  000047cc    00000010     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_enablePolicy)
                  000047dc    00000010     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.text:SemaphoreP_Params_init)
                  000047ec    00000010                     : SwiP_nortos.oem4f (.text:SwiP_Params_init)
                  000047fc    00000010                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_Params_init)
                  0000480c    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:uartPostNotify)
                  0000481c    0000000e     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_empty)
                  0000482a    0000000e                     : QueueP_nortos.oem4f (.text:QueueP_remove)
                  00004838    0000000e     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_decompress_none.c.obj (.text:decompress:none:__TI_decompress_none)
                  00004846    00000002                                   : div0.asm.obj (.text)
                  00004848    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_doTick)
                  00004854    0000000c                     : ClockPTimer_nortos.oem4f (.text:ClockP_getSystemTickPeriod)
                  00004860    0000000c     driverlib.lib : cpu.obj (.text:NOROM_CPUcpsid)
                  0000486c    0000000c                   : cpu.obj (.text:NOROM_CPUcpsie)
                  00004878    0000000c     drivers_cc13x2.a : PINCC26XX.oem4f (.text:PIN_registerIntCb)
                  00004884    0000000c     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:PowerCC26XX_schedulerRestore)
                  00004890    0000000c     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:Power_getConstraintMask)
                  0000489c    0000000c                      : PowerCC26X2.oem4f (.text:Power_getDependencyCount)
                  000048a8    0000000c     nortos_cc13x2.a : SwiP_nortos.oem4f (.text:SwiP_getTrigger)
                  000048b4    0000000c                     : TimerPCC26XX_nortos.oem4f (.text:TimerP_getFreq)
                  000048c0    0000000c     rtsv7M4_T_le_v4SPD16_eabi.lib : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init)
                  000048cc    0000000a     drivers_cc13x2.a : ADCBuf.oem4f (.text:ADCBuf_convertCancel)
                  000048d6    0000000a                      : UDMACC26XX.oem4f (.text:UDMACC26XX_hwiIntFxn)
                  000048e0    0000000a                      : UARTCC26XX.oem4f (.text:readSemCallback)
                  000048ea    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_conversionCallback)
                  000048f2    00000008                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_init)
                  000048fa    00000008                      : GPTimerCC26XX.oem4f (.text:GPTimerCC26XX_setLoadValue)
                  00004902    00000008     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.text:HwiP_restore)
                  0000490a    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_init)
                  00004912    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.text:malloc)
                  0000491a    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:writeSemCallback)
                  00004922    00000006     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.text:ClockP_stop)
                  00004928    00000006     driverlib.lib : cpu.obj (.text:NOROM_CPUdelay)
                  0000492e    00000006     nortos_cc13x2.a : QueueP_nortos.oem4f (.text:QueueP_init)
                  00004934    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.text:UARTCC26XX_readPolling)
                  0000493a    00000006                      : UARTCC26XX.oem4f (.text:UARTCC26XX_writePolling)
                  00004940    00000006                      : PowerCC26X2.oem4f (.text:delayUs)
                  00004946    00000004                      : ADCBufCC26X2.oem4f (.text:ADCBufCC26X2_getResolution)
                  0000494a    00000004     nortos_cc13x2.a : PowerCC26X2_nortos.oem4f (.text:CPUwfi)
                  0000494e    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_isActive)
                  00004952    00000004                     : ClockPTimer_nortos.oem4f (.text:ClockP_setTimeout)
                  00004956    00000004                     : HwiPCC26XX_nortos.oem4f (.text:HwiP_enable)
                  0000495a    00000004                     : QueueP_nortos.oem4f (.text:QueueP_head)
                  0000495e    00000004                     : QueueP_nortos.oem4f (.text:QueueP_next)
                  00004962    00000004                     : SemaphoreP_nortos.oem4f (.text:SemaphoreP_delete)
                  00004966    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : pre_init.c.obj (.text:_system_pre_init)
                  0000496a    00000004                                   : exit.c.obj (.text:abort:abort)
                  0000496e    00000004     drivers_cc13x2.a : PowerCC26X2.oem4f (.text:configureRFCoreClocks)
                  00004972    00000004                      : PowerCC26X2.oem4f (.text:nopResourceHandler)

.const     0    00004978    00000298     
                  00004978    00000054     drivers_cc13x2.a : PowerCC26X2.oem4f (.const:resourceDB)
                  000049cc    00000028     ti_drivers_config.obj (.const:BoardGpioInitTable)
                  000049f4    00000028     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:UARTCC26XX_fxnTable)
                  00004a1c    00000028     ti_drivers_config.obj (.const:uartCC26XXHWAttrs)
                  00004a44    00000024     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.const:ADCBufCC26X2_fxnTable)
                  00004a68    00000024                      : UART.oem4f (.const:UART_defaultParams)
                  00004a8c    0000001c                      : GPTimerCC26XX.oem4f (.const:GPT_LUT)
                  00004aa8    00000018     ti_drivers_config.obj (.const:GPTimerCC26XX_config)
                  00004ac0    00000018     ti_drivers_config.obj (.const:gptimerCC26XXHWAttrs)
                  00004ad8    00000018     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:interruptType)
                  00004af0    00000014     ti_drivers_config.obj (.const:$P$T1$2)
                  00004b04    00000014     ti_drivers_config.obj (.const:GPIOCC26XX_config)
                  00004b18    00000014     ti_drivers_config.obj (.const:PowerCC26X2_config)
                  00004b2c    00000014     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:parityType)
                  00004b40    00000010     nortos_cc13x2.a : SwiP_nortos.oem4f (.const:SwiP_defaultParams)
                  00004b50    00000010     ti_drivers_config.obj (.const:adcbufCC26XXHWAttrs)
                  00004b60    00000010     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:dataLength)
                  00004b70    00000010                      : GPIOCC26XX.oem4f (.const:outPinTypes)
                  00004b80    0000000e     ti_drivers_config.obj (.const)
                  00004b8e    00000002     --HOLE-- [fill = 0]
                  00004b90    0000000c     ti_drivers_config.obj (.const:ADCBuf_config)
                  00004b9c    0000000c     ti_drivers_config.obj (.const:UART_config)
                  00004ba8    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.const:inPinTypes)
                  00004bb4    0000000c                      : GPIOCC26XX.oem4f (.const:outPinStrengths)
                  00004bc0    00000008     driverlib.lib : aux_sysif.obj (.const:$O1$$)
                  00004bc8    00000008     ti_drivers_config.obj (.const:$P$T0$1)
                  00004bd0    00000008     drivers_cc13x2.a : GPTimerCC26XX.oem4f (.const:GPT_DefaultParams)
                  00004bd8    00000008     ti_drivers_config.obj (.const:PINCC26XX_hwAttrs)
                  00004be0    00000008     ti_drivers_config.obj (.const:UDMACC26XX_config)
                  00004be8    00000008     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:stopBits)
                  00004bf0    00000008     ti_drivers_config.obj (.const:udmaCC26XXHWAttrs)
                  00004bf8    00000006     drivers_cc13x2.a : UARTCC26XX.oem4f (.const:.string:rxFifoBytes)
                  00004bfe    00000006                      : UARTCC26XX.oem4f (.const:.string:rxFifoThreshold)
                  00004c04    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoBytes)
                  00004c0a    00000006                      : UARTCC26XX.oem4f (.const:.string:txFifoThreshold)

.binit     0    00000000    00000000     

.cinit     0    00004c18    000000e0     
                  00004c18    00000088     (.cinit..data.load) [load image, compression = lzss]
                  00004ca0    0000000c     (__TI_handler_table)
                  00004cac    00000004     --HOLE-- [fill = 0]
                  00004cb0    00000008     (.cinit..TI.bound:dmaADCAltControlTableEntry.load) [load image, compression = zero_init]
                  00004cb8    00000008     (.cinit..TI.bound:dmaADCPriControlTableEntry.load) [load image, compression = zero_init]
                  00004cc0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004cc8    00000008     (.cinit..vtable_ram.load) [load image, compression = zero_init]
                  00004cd0    00000028     (__TI_cinit_table)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.ccfg      0    00057fa8    00000058     
                  00057fa8    00000058     ti_devices_config.obj (.ccfg:retain)

.bss       0    20000000    00000604     UNINITIALIZED
                  20000000    000001b4     (.common:uartCC26XXObjects)
                  200001b4    000000dc     (.common:adcbufCC26XXbjects)
                  20000290    000000a0     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:ClockP_module)
                  20000330    00000098     (.common:gptimerCC26XXObjects)
                  200003c8    0000007c     (.common:pinHandleTable)
                  20000444    00000050     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinSemaphore)
                  20000494    00000034                      : PINCC26XX.oem4f (.bss:pinSwi)
                  200004c8    00000020     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_readyList)
                  200004e8    00000020     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioCallbackInfo)
                  20000508    00000020                      : PINCC26XX.oem4f (.bss:pinGpioConfigTable)
                  20000528    00000020     ti_drivers_config.obj (.bss:uartCC26XXRingBuffer0)
                  20000548    00000020     (.common:udmaCC26XXObject)
                  20000568    0000001c     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss:SwiP_hwiStruct)
                  20000584    0000001c                     : TimerPCC26XX_nortos.oem4f (.bss:TimerP_hwiStruct)
                  200005a0    0000001c     drivers_cc13x2.a : PINCC26XX.oem4f (.bss:pinHwi)
                  200005bc    0000000d     nortos_cc13x2.a : SwiP_nortos.oem4f (.bss)
                  200005c9    00000001     (.common:driverlib_release_0_59848)
                  200005ca    00000002     --HOLE--
                  200005cc    0000000c     drivers_cc13x2.a : GPIOCC26XX.oem4f (.bss:gpioPinState)
                  200005d8    00000008                      : GPIOCC26XX.oem4f (.bss)
                  200005e0    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss:timerFreq)
                  200005e8    00000004     drivers_cc13x2.a : PINCC26XX.oem4f (.bss)
                  200005ec    00000004     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.bss)
                  200005f0    00000004                     : PowerCC26X2_nortos.oem4f (.bss)
                  200005f4    00000004                     : TimerPCC26XX_nortos.oem4f (.bss)
                  200005f8    00000004     (.common:i)
                  200005fc    00000004     (.common:j)
                  20000600    00000004     (.common:uart)

.vtable_ram 
*          0    20000a00    000000d8     UNINITIALIZED
                  20000a00    000000d8     driverlib.lib : interrupt.obj (.vtable_ram)

.data      0    20000604    000003e6     UNINITIALIZED
                  20000604    00000170     drivers_cc13x2.a : PowerCC26X2.oem4f (.data:PowerCC26X2_module)
                  20000774    000000d8     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data:HwiP_dispatchTable)
                  2000084c    0000007c     SoundTX.obj (.data:ChirpDelay)
                  200008c8    0000007c     SoundTX.obj (.data:ChirpState)
                  20000944    00000024     driverlib.lib : osc.obj (.data:$O1$$)
                  20000968    00000010     ti_drivers_config.obj (.data:gpioCallbackFunctions)
                  20000978    00000010     ti_drivers_config.obj (.data:gpioPinConfigs)
                  20000988    0000000c     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data:ClockP_defaultParams)
                  20000994    0000000c                     : TimerPCC26XX_nortos.oem4f (.data:TimerP_defaultParams)
                  200009a0    00000008     rtsv7M4_T_le_v4SPD16_eabi.lib : _lock.c.obj (.data:$O1$$)
                  200009a8    00000008                                   : memory.c.obj (.data:$O1$$)
                  200009b0    00000008     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data:SemaphoreP_defaultParams)
                  200009b8    00000008     SoundTX.obj (.data)
                  200009c0    00000008     drivers_cc13x2.a : PINCC26XX.oem4f (.data)
                  200009c8    00000008     nortos_cc13x2.a : ClockPTimer_nortos.oem4f (.data)
                  200009d0    00000006     ti_drivers_config.obj (.data:adcBufCC26XXChannelLut0)
                  200009d6    00000001     drivers_cc13x2.a : ADCBuf.oem4f (.data)
                  200009d7    00000001                      : UART.oem4f (.data)
                  200009d8    00000005                      : GPIOCC26XX.oem4f (.data)
                  200009dd    00000003     nortos_cc13x2.a : SemaphoreP_nortos.oem4f (.data)
                  200009e0    00000004     drivers_cc13x2.a : GPIOCC26XX.oem4f (.data:gpioPinTable)
                  200009e4    00000004     nortos_cc13x2.a : HwiPCC26XX_nortos.oem4f (.data)
                  200009e8    00000001                     : SwiP_nortos.oem4f (.data)
                  200009e9    00000001                     : TimerPCC26XX_nortos.oem4f (.data)

.sysmem    0    20001a80    00004000     UNINITIALIZED
                  20001a80    00000010     rtsv7M4_T_le_v4SPD16_eabi.lib : memory.c.obj (.sysmem)
                  20001a90    00003ff0     --HOLE--

.stack     0    20013c00    00000400     UNINITIALIZED
                  20013c00    00000004     rtsv7M4_T_le_v4SPD16_eabi.lib : boot_cortex_m.c.obj (.stack)
                  20013c04    000003fc     --HOLE--

.log_data 
*          0    90000000    00000000     COPY SECTION

__llvm_prf_cnts 
*          0    20000000    00000000     UNINITIALIZED

.TI.bound:dmaADCPriControlTableEntry 
*          0    20001870    00000010     UNINITIALIZED
                  20001870    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCPriControlTableEntry)

.TI.bound:dmaADCAltControlTableEntry 
*          0    20001a70    00000010     UNINITIALIZED
                  20001a70    00000010     drivers_cc13x2.a : ADCBufCC26X2.oem4f (.TI.bound:dmaADCAltControlTableEntry)

.args      0    00004c10    00000008     
                  00004c10    00000008     --HOLE-- [fill = 0]

MODULE SUMMARY

       Module                             code    ro data   rw data
       ------                             ----    -------   -------
    .\
       SoundTX.obj                        304     0         256    
       mainNew.obj                        140     0         12     
    +--+----------------------------------+-------+---------+---------+
       Total:                             444     0         268    
                                                                   
    .\syscfg\
       ti_drivers_config.obj              260     274       910    
       ti_devices_config.obj              0       88        0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             260     362       910    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/kernel/nortos/lib/ccs/m4f/nortos_cc13x2.a
       ClockPTimer_nortos.oem4f           922     0         192    
       SwiP_nortos.oem4f                  724     16        74     
       TimerPCC26XX_nortos.oem4f          642     0         45     
       HwiPCC26XX_nortos.oem4f            294     0         220    
       SemaphoreP_nortos.oem4f            410     0         11     
       PowerCC26X2_nortos.oem4f           256     0         4      
       startup_cc13x2_cc26x2_ccs.oem4f    26      216       0      
       QueueP_nortos.oem4f                100     0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             3374    232       546    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/cc13x2_cc26x2/driverlib/bin/ccs/driverlib.lib
       sys_ctrl.obj                       420     0         0      
       osc.obj                            340     0         36     
       setup.obj                          324     0         0      
       interrupt.obj                      74      0         216    
       chipinfo.obj                       250     0         0      
       prcm.obj                           160     0         0      
       timer.obj                          112     0         0      
       aux_sysif.obj                      52      8         0      
       aux_adc.obj                        32      0         0      
       cpu.obj                            30      0         0      
       driverlib_release.obj              0       0         1      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1794    8         253    
                                                                   
    C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/lib/ccs/m4f/drivers_cc13x2.a
       UARTCC26XX.oem4f                   2760    108       0      
       PowerCC26X2.oem4f                  2096    84        368    
       PINCC26XX.oem4f                    1554    0         328    
       ADCBufCC26X2.oem4f                 1542    36        32     
       PowerCC26X2_calibrateRCOSC.oem4f   1400    0         0      
       GPIOCC26XX.oem4f                   660     64        61     
       GPTimerCC26XX.oem4f                670     36        0      
       UART.oem4f                         156     36        1      
       UDMACC26XX.oem4f                   168     0         0      
       RingBuf.oem4f                      152     0         0      
       ADCBuf.oem4f                       78      0         1      
       List.oem4f                         76      0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             11312   364       791    
                                                                   
    C:\ti\ccs1011\ccs\tools\compiler\ti-cgt-arm_20.2.4.LTS\lib\rtsv7M4_T_le_v4SPD16_eabi.lib
       memory.c.obj                       672     0         8      
       memcpy_t2.asm.obj                  156     0         0      
       ull_div_t2.asm.obj                 150     0         0      
       memset_t2.asm.obj                  122     0         0      
       copy_decompress_lzss.c.obj         104     0         0      
       autoinit.c.obj                     68      0         0      
       boot_cortex_m.c.obj                48      0         0      
       args_main.c.obj                    24      0         0      
       ll_mul_t2.asm.obj                  24      0         0      
       copy_decompress_none.c.obj         14      0         0      
       copy_zero_init.c.obj               12      0         0      
       _lock.c.obj                        2       0         8      
       exit.c.obj                         4       0         0      
       pre_init.c.obj                     4       0         0      
       div0.asm.obj                       2       0         0      
    +--+----------------------------------+-------+---------+---------+
       Total:                             1406    0         16     
                                                                   
       Heap:                              0       0         16384  
       Stack:                             0       0         1024   
       Linker Generated:                  0       220       0      
    +--+----------------------------------+-------+---------+---------+
       Grand Total:                       18590   1186      20192  


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004cd0 records: 5, size/record: 8, table size: 40
	.data: load addr=00004c18, load size=00000088 bytes, run addr=20000604, run size=000003e6 bytes, compression=lzss
	.TI.bound:dmaADCAltControlTableEntry: load addr=00004cb0, load size=00000008 bytes, run addr=20001a70, run size=00000010 bytes, compression=zero_init
	.TI.bound:dmaADCPriControlTableEntry: load addr=00004cb8, load size=00000008 bytes, run addr=20001870, run size=00000010 bytes, compression=zero_init
	.bss: load addr=00004cc0, load size=00000008 bytes, run addr=20000000, run size=00000604 bytes, compression=zero_init
	.vtable_ram: load addr=00004cc8, load size=00000008 bytes, run addr=20000a00, run size=000000d8 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004ca0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_lzss
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                                               
-------   ----                                                               
00004b83  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004b80  ADCBUF_CONST                                                       
00004b81  ADCBUF_SOUND_CONST                                                 
00004b82  ADCBUF_TEMPERATURE_CONST                                           
00003191  ADCBufCC26X2_adjustRawValues                                       
000032d9  ADCBufCC26X2_close                                                 
000034f1  ADCBufCC26X2_control                                               
0000118d  ADCBufCC26X2_convert                                               
0000353d  ADCBufCC26X2_convertAdjustedToMicroVolts                           
0000399d  ADCBufCC26X2_convertCancel                                         
00004a44  ADCBufCC26X2_fxnTable                                              
00004947  ADCBufCC26X2_getResolution                                         
000048f3  ADCBufCC26X2_init                                                  
0000076d  ADCBufCC26X2_open                                                  
00004b90  ADCBuf_config                                                      
000048cd  ADCBuf_convertCancel                                               
00004b84  ADCBuf_count                                                       
0000377d  ADCBuf_init                                                        
000049cc  BoardGpioInitTable                                                 
000042ed  Board_init                                                         
00000e47  Board_initHook                                                     
00002ead  Board_sendExtFlashByte                                             
00003b8d  Board_shutDownExtFlash                                             
00003585  Board_wakeUpExtFlash                                               
0000496b  C$$EXIT                                                            
00004b8b  CONFIG_GPTIMER_0_CONST                                             
00004b8c  CONFIG_GPTIMER_1_CONST                                             
2000084c  ChirpDelay                                                         
200009bc  ChirpIndex                                                         
200009b8  ChirpSize                                                          
200008c8  ChirpState                                                         
0000475d  ClockP_Params_init                                                 
0000430d  ClockP_add                                                         
000035cd  ClockP_construct                                                   
0000457b  ClockP_destruct                                                    
00004849  ClockP_doTick                                                      
0000476d  ClockP_getCpuFreq                                                  
00004855  ClockP_getSystemTickPeriod                                         
00004215  ClockP_getTicks                                                    
00003d81  ClockP_getTicksUntilInterrupt                                      
0000494f  ClockP_isActive                                                    
00004239  ClockP_scheduleNextTick                                            
00004953  ClockP_setTimeout                                                  
0000278d  ClockP_start                                                       
00001c69  ClockP_startup                                                     
00004923  ClockP_stop                                                        
200009cc  ClockP_tickPeriod                                                  
00002989  ClockP_walkQueueDynamic                                            
00001d1d  ClockP_workFuncDynamic                                             
00004b04  GPIOCC26XX_config                                                  
00003f21  GPIO_hwiIntFxn                                                     
000017f9  GPIO_init                                                          
00003f51  GPIO_setCallback                                                   
0000107d  GPIO_setConfig                                                     
00002f71  GPIO_write                                                         
00004725  GPTimerCC26XX_Params_init                                          
00003f81  GPTimerCC26XX_close                                                
00004aa8  GPTimerCC26XX_config                                               
00003de9  GPTimerCC26XX_configureDebugStall                                  
000023e1  GPTimerCC26XX_open                                                 
000048fb  GPTimerCC26XX_setLoadValue                                         
000033c1  GPTimerCC26XX_start                                                
000030e1  GPTimerCC26XX_stop                                                 
00004b8d  GPTimer_count                                                      
00004737  HwiP_Params_init                                                   
0000477d  HwiP_clearInterrupt                                                
0000280d  HwiP_construct                                                     
00004485  HwiP_destruct                                                      
0000478d  HwiP_disable                                                       
00004957  HwiP_enable                                                        
00004595  HwiP_inISR                                                         
0000479d  HwiP_post                                                          
00004903  HwiP_restore                                                       
200009e4  HwiP_swiPIntNum                                                    
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
0000377d  InitADC                                                            
00002351  InitTimer2                                                         
00003add  InitUart                                                           
000041c9  List_put                                                           
000041ef  List_remove                                                        
0000432d  NOROM_AUXADCEnableSync                                             
00003e1d  NOROM_AUXSYSIFOpModeChange                                         
00004861  NOROM_CPUcpsid                                                     
0000486d  NOROM_CPUcpsie                                                     
00004929  NOROM_CPUdelay                                                     
000044a1  NOROM_ChipInfo_GetChipFamily                                       
00002c3d  NOROM_ChipInfo_GetChipType                                         
000037c1  NOROM_ChipInfo_GetHwRevision                                       
000045ad  NOROM_ChipInfo_GetPackageType                                      
00003e51  NOROM_IntRegister                                                  
000046ad  NOROM_IntUnregister                                                
00002469  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002ca9  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
00004281  NOROM_OSCHF_TurnOnXosc                                             
000039dd  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
000031e5  NOROM_PRCMPowerDomainsAllOff                                       
0000340d  NOROM_PRCMPowerDomainsAllOn                                        
000020f9  NOROM_SetupTrimDevice                                              
00003e85  NOROM_SysCtrlIdle                                                  
0000288d  NOROM_SysCtrlSetRechargeBeforePowerDown                            
0000138d  NOROM_SysCtrlStandby                                               
00004655  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
00003a1d  NOROM_TimerIntRegister                                             
00004957  NoRTOS_start                                                       
000047ad  PINCC26XX_getPinCount                                              
00004bd8  PINCC26XX_hwAttrs                                                  
00003fb1  PINCC26XX_setMux                                                   
00004b87  PIN_DRIVE_SPEAKER_A_CONST                                          
00004b88  PIN_DRIVE_SPEAKER_B_CONST                                          
00004b85  PIN_TEST1_CONST                                                    
00004b86  PIN_TEST2_CONST                                                    
00002b5d  PIN_add                                                            
0000440d  PIN_close                                                          
000000d9  PIN_init                                                           
00001dc9  PIN_open                                                           
00004879  PIN_registerIntCb                                                  
00002fd1  PIN_remove                                                         
00003bc5  PIN_setConfig                                                      
00003615  PIN_setOutputEnable                                                
00003b19  PIN_setOutputValue                                                 
0000365d  PowerCC26X2_RCOSC_clockFunc                                        
00002a75  PowerCC26X2_auxISR                                                 
000046c1  PowerCC26X2_calibrate                                              
00004b18  PowerCC26X2_config                                                 
00003139  PowerCC26X2_initiateCalibration                                    
20000604  PowerCC26X2_module                                                 
0000452d  PowerCC26XX_calibrate                                              
000047bd  PowerCC26XX_schedulerDisable                                       
00004885  PowerCC26XX_schedulerRestore                                       
00001561  PowerCC26XX_standbyPolicy                                          
000046d5  Power_disablePolicy                                                
000047cd  Power_enablePolicy                                                 
00004891  Power_getConstraintMask                                            
0000489d  Power_getDependencyCount                                           
0000442b  Power_getTransitionLatency                                         
000045c5  Power_idleFunc                                                     
000008e1  Power_init                                                         
00004151  Power_registerNotify                                               
00003fe1  Power_releaseConstraint                                            
000018bd  Power_releaseDependency                                            
00004011  Power_setConstraint                                                
00001af9  Power_setDependency                                                
000002b9  Power_sleep                                                        
0000436d  Power_unregisterNotify                                             
0000481d  QueueP_empty                                                       
00004547  QueueP_get                                                         
0000495b  QueueP_head                                                        
0000492f  QueueP_init                                                        
0000495f  QueueP_next                                                        
0000438d  QueueP_put                                                         
0000482b  QueueP_remove                                                      
00004749  RingBuf_construct                                                  
00003a5d  RingBuf_get                                                        
00003735  RingBuf_put                                                        
000047dd  SemaphoreP_Params_init                                             
00003239  SemaphoreP_construct                                               
00004449  SemaphoreP_constructBinary                                         
0000466b  SemaphoreP_create                                                  
00004561  SemaphoreP_createBinary                                            
200009b0  SemaphoreP_defaultParams                                           
00004963  SemaphoreP_delete                                                  
00003327  SemaphoreP_destruct                                                
00001f21  SemaphoreP_pend                                                    
00003849  SemaphoreP_post                                                    
000040f9  SoundTransmit                                                      
000047ed  SwiP_Params_init                                                   
00001721  SwiP_construct                                                     
00003eb9  SwiP_destruct                                                      
000044d9  SwiP_disable                                                       
00001fc1  SwiP_dispatch                                                      
000048a9  SwiP_getTrigger                                                    
00004467  SwiP_or                                                            
00002d11  SwiP_post                                                          
0000388d  SwiP_restore                                                       
00002ae9  Timer2AInterruptHandler                                            
000047fd  TimerP_Params_init                                                 
00001a3d  TimerP_construct                                                   
000043ad  TimerP_dynamicStub                                                 
000046fd  TimerP_getCount64                                                  
00003bfd  TimerP_getCurrentTick                                              
000048b5  TimerP_getFreq                                                     
000042a5  TimerP_getMaxTicks                                                 
00003c35  TimerP_initDevice                                                  
00003d15  TimerP_setNextTick                                                 
00004125  TimerP_setThreshold                                                
00002d79  TimerP_start                                                       
000045dd  TimerP_startup                                                     
00002689  UARTCC26XX_close                                                   
00002e49  UARTCC26XX_control                                                 
000049f4  UARTCC26XX_fxnTable                                                
00000bb9  UARTCC26XX_hwiIntFxn                                               
0000490b  UARTCC26XX_init                                                    
00000a55  UARTCC26XX_open                                                    
00000d0d  UARTCC26XX_read                                                    
00003459  UARTCC26XX_readCancel                                              
00004935  UARTCC26XX_readPolling                                             
00002f0f  UARTCC26XX_swiIntFxn                                               
00001641  UARTCC26XX_write                                                   
000022bf  UARTCC26XX_writeCancel                                             
0000493b  UARTCC26XX_writePolling                                            
00004b89  UART_0_CONST                                                       
000045f5  UART_Params_init                                                   
00004b9c  UART_config                                                        
00004b8a  UART_count                                                         
00004a68  UART_defaultParams                                                 
000038d1  UART_init                                                          
00003a9d  UART_open                                                          
00003d4b  UDMACC26XX_close                                                   
00004be0  UDMACC26XX_config                                                  
000048d7  UDMACC26XX_hwiIntFxn                                               
000034a5  UDMACC26XX_open                                                    
20014000  __STACK_END                                                        
00000400  __STACK_SIZE                                                       
00004000  __SYSMEM_SIZE                                                      
00004cd0  __TI_CINIT_Base                                                    
00004cf8  __TI_CINIT_Limit                                                   
00004ca0  __TI_Handler_Table_Base                                            
00004cac  __TI_Handler_Table_Limit                                           
00003915  __TI_auto_init_nobinit_nopinit                                     
00002de1  __TI_decompress_lzss                                               
00004839  __TI_decompress_none                                               
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
00000000  __TI_static_base__                                                 
000048c1  __TI_zero_init                                                     
00004847  __aeabi_idiv0                                                      
00004847  __aeabi_ldiv0                                                      
00004625  __aeabi_lmul                                                       
0000290d  __aeabi_memclr                                                     
0000290d  __aeabi_memclr4                                                    
0000290d  __aeabi_memclr8                                                    
0000205d  __aeabi_memcpy                                                     
0000205d  __aeabi_memcpy4                                                    
0000205d  __aeabi_memcpy8                                                    
0000290f  __aeabi_memset                                                     
0000290f  __aeabi_memset4                                                    
0000290f  __aeabi_memset8                                                    
00002229  __aeabi_uldivmod                                                   
ffffffff  __binit__                                                          
00004c10  __c_args__                                                         
00057fa8  __ccfg                                                             
UNDEFED   __mpu_init                                                         
20013c00  __stack                                                            
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
0000460d  _args_main                                                         
00004071  _c_int00                                                           
20000944  _hposcCoeffs                                                       
200009a0  _lock                                                              
0000353b  _nop                                                               
20001a80  _sys_memory                                                        
UNDEFED   _system_post_cinit                                                 
00004967  _system_pre_init                                                   
200009a4  _unlock                                                            
0000496b  abort                                                              
200009d0  adcBufCC26XXChannelLut0                                            
00004b50  adcbufCC26XXHWAttrs                                                
200001b4  adcbufCC26XXbjects                                                 
0000128d  aligned_alloc                                                      
ffffffff  binit                                                              
0000377b  clkFxn                                                             
200005c9  driverlib_release_0_59848                                          
0000147d  free                                                               
20000a00  g_pfnRAMVectors                                                    
00000000  g_pfnVectors                                                       
20000968  gpioCallbackFunctions                                              
20000978  gpioPinConfigs                                                     
00004ac0  gptimerCC26XXHWAttrs                                               
20000330  gptimerCC26XXObjects                                               
200005f8  i                                                                  
00004ba8  inPinTypes                                                         
200005fc  j                                                                  
00003289  main                                                               
00004913  malloc                                                             
0000128d  memalign                                                           
0000205d  memcpy                                                             
00002915  memset                                                             
00004bb4  outPinStrengths                                                    
00004b70  outPinTypes                                                        
200003c8  pinHandleTable                                                     
200009c4  pinLowerBound                                                      
200009c0  pinUpperBound                                                      
00004711  resetISR                                                           
00004978  resourceDB                                                         
20000600  uart                                                               
20000000  uartCC26XXObjects                                                  
00004bf0  udmaCC26XXHWAttrs                                                  
20000548  udmaCC26XXObject                                                   


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                                               
-------   ----                                                               
00000000  __TI_static_base__                                                 
00000000  g_pfnVectors                                                       
000000d9  PIN_init                                                           
000002b9  Power_sleep                                                        
00000400  __STACK_SIZE                                                       
0000076d  ADCBufCC26X2_open                                                  
000008e1  Power_init                                                         
00000a55  UARTCC26XX_open                                                    
00000bb9  UARTCC26XX_hwiIntFxn                                               
00000d0d  UARTCC26XX_read                                                    
00000e47  Board_initHook                                                     
0000107d  GPIO_setConfig                                                     
0000118d  ADCBufCC26X2_convert                                               
0000128d  aligned_alloc                                                      
0000128d  memalign                                                           
0000138d  NOROM_SysCtrlStandby                                               
0000147d  free                                                               
00001561  PowerCC26XX_standbyPolicy                                          
00001641  UARTCC26XX_write                                                   
00001721  SwiP_construct                                                     
000017f9  GPIO_init                                                          
000018bd  Power_releaseDependency                                            
00001a3d  TimerP_construct                                                   
00001af9  Power_setDependency                                                
00001c69  ClockP_startup                                                     
00001d1d  ClockP_workFuncDynamic                                             
00001dc9  PIN_open                                                           
00001f21  SemaphoreP_pend                                                    
00001fc1  SwiP_dispatch                                                      
0000205d  __aeabi_memcpy                                                     
0000205d  __aeabi_memcpy4                                                    
0000205d  __aeabi_memcpy8                                                    
0000205d  memcpy                                                             
000020f9  NOROM_SetupTrimDevice                                              
00002229  __aeabi_uldivmod                                                   
000022bf  UARTCC26XX_writeCancel                                             
00002351  InitTimer2                                                         
000023e1  GPTimerCC26XX_open                                                 
00002469  NOROM_OSCHF_AttemptToSwitchToXosc                                  
00002689  UARTCC26XX_close                                                   
0000278d  ClockP_start                                                       
0000280d  HwiP_construct                                                     
0000288d  NOROM_SysCtrlSetRechargeBeforePowerDown                            
0000290d  __aeabi_memclr                                                     
0000290d  __aeabi_memclr4                                                    
0000290d  __aeabi_memclr8                                                    
0000290f  __aeabi_memset                                                     
0000290f  __aeabi_memset4                                                    
0000290f  __aeabi_memset8                                                    
00002915  memset                                                             
00002989  ClockP_walkQueueDynamic                                            
00002a75  PowerCC26X2_auxISR                                                 
00002ae9  Timer2AInterruptHandler                                            
00002b5d  PIN_add                                                            
00002c3d  NOROM_ChipInfo_GetChipType                                         
00002ca9  NOROM_OSCHF_SwitchToRcOscTurnOffXosc                               
00002d11  SwiP_post                                                          
00002d79  TimerP_start                                                       
00002de1  __TI_decompress_lzss                                               
00002e49  UARTCC26XX_control                                                 
00002ead  Board_sendExtFlashByte                                             
00002f0f  UARTCC26XX_swiIntFxn                                               
00002f71  GPIO_write                                                         
00002fd1  PIN_remove                                                         
000030e1  GPTimerCC26XX_stop                                                 
00003139  PowerCC26X2_initiateCalibration                                    
00003191  ADCBufCC26X2_adjustRawValues                                       
000031e5  NOROM_PRCMPowerDomainsAllOff                                       
00003239  SemaphoreP_construct                                               
00003289  main                                                               
000032d9  ADCBufCC26X2_close                                                 
00003327  SemaphoreP_destruct                                                
000033c1  GPTimerCC26XX_start                                                
0000340d  NOROM_PRCMPowerDomainsAllOn                                        
00003459  UARTCC26XX_readCancel                                              
000034a5  UDMACC26XX_open                                                    
000034f1  ADCBufCC26X2_control                                               
0000353b  _nop                                                               
0000353d  ADCBufCC26X2_convertAdjustedToMicroVolts                           
00003585  Board_wakeUpExtFlash                                               
000035cd  ClockP_construct                                                   
00003615  PIN_setOutputEnable                                                
0000365d  PowerCC26X2_RCOSC_clockFunc                                        
00003735  RingBuf_put                                                        
0000377b  clkFxn                                                             
0000377d  ADCBuf_init                                                        
0000377d  InitADC                                                            
000037c1  NOROM_ChipInfo_GetHwRevision                                       
00003849  SemaphoreP_post                                                    
0000388d  SwiP_restore                                                       
000038d1  UART_init                                                          
00003915  __TI_auto_init_nobinit_nopinit                                     
0000399d  ADCBufCC26X2_convertCancel                                         
000039dd  NOROM_OSC_HPOSCRelativeFrequencyOffsetGet                          
00003a1d  NOROM_TimerIntRegister                                             
00003a5d  RingBuf_get                                                        
00003a9d  UART_open                                                          
00003add  InitUart                                                           
00003b19  PIN_setOutputValue                                                 
00003b8d  Board_shutDownExtFlash                                             
00003bc5  PIN_setConfig                                                      
00003bfd  TimerP_getCurrentTick                                              
00003c35  TimerP_initDevice                                                  
00003d15  TimerP_setNextTick                                                 
00003d4b  UDMACC26XX_close                                                   
00003d81  ClockP_getTicksUntilInterrupt                                      
00003de9  GPTimerCC26XX_configureDebugStall                                  
00003e1d  NOROM_AUXSYSIFOpModeChange                                         
00003e51  NOROM_IntRegister                                                  
00003e85  NOROM_SysCtrlIdle                                                  
00003eb9  SwiP_destruct                                                      
00003f21  GPIO_hwiIntFxn                                                     
00003f51  GPIO_setCallback                                                   
00003f81  GPTimerCC26XX_close                                                
00003fb1  PINCC26XX_setMux                                                   
00003fe1  Power_releaseConstraint                                            
00004000  __SYSMEM_SIZE                                                      
00004011  Power_setConstraint                                                
00004071  _c_int00                                                           
000040f9  SoundTransmit                                                      
00004125  TimerP_setThreshold                                                
00004151  Power_registerNotify                                               
000041c9  List_put                                                           
000041ef  List_remove                                                        
00004215  ClockP_getTicks                                                    
00004239  ClockP_scheduleNextTick                                            
00004281  NOROM_OSCHF_TurnOnXosc                                             
000042a5  TimerP_getMaxTicks                                                 
000042ed  Board_init                                                         
0000430d  ClockP_add                                                         
0000432d  NOROM_AUXADCEnableSync                                             
0000436d  Power_unregisterNotify                                             
0000438d  QueueP_put                                                         
000043ad  TimerP_dynamicStub                                                 
0000440d  PIN_close                                                          
0000442b  Power_getTransitionLatency                                         
00004449  SemaphoreP_constructBinary                                         
00004467  SwiP_or                                                            
00004485  HwiP_destruct                                                      
000044a1  NOROM_ChipInfo_GetChipFamily                                       
000044d9  SwiP_disable                                                       
0000452d  PowerCC26XX_calibrate                                              
00004547  QueueP_get                                                         
00004561  SemaphoreP_createBinary                                            
0000457b  ClockP_destruct                                                    
00004595  HwiP_inISR                                                         
000045ad  NOROM_ChipInfo_GetPackageType                                      
000045c5  Power_idleFunc                                                     
000045dd  TimerP_startup                                                     
000045f5  UART_Params_init                                                   
0000460d  _args_main                                                         
00004625  __aeabi_lmul                                                       
00004655  NOROM_ThisLibraryIsFor_CC13x2_CC26x2_HwRev20AndLater_HaltIfViolated
0000466b  SemaphoreP_create                                                  
000046ad  NOROM_IntUnregister                                                
000046c1  PowerCC26X2_calibrate                                              
000046d5  Power_disablePolicy                                                
000046fd  TimerP_getCount64                                                  
00004711  resetISR                                                           
00004725  GPTimerCC26XX_Params_init                                          
00004737  HwiP_Params_init                                                   
00004749  RingBuf_construct                                                  
0000475d  ClockP_Params_init                                                 
0000476d  ClockP_getCpuFreq                                                  
0000477d  HwiP_clearInterrupt                                                
0000478d  HwiP_disable                                                       
0000479d  HwiP_post                                                          
000047ad  PINCC26XX_getPinCount                                              
000047bd  PowerCC26XX_schedulerDisable                                       
000047cd  Power_enablePolicy                                                 
000047dd  SemaphoreP_Params_init                                             
000047ed  SwiP_Params_init                                                   
000047fd  TimerP_Params_init                                                 
0000481d  QueueP_empty                                                       
0000482b  QueueP_remove                                                      
00004839  __TI_decompress_none                                               
00004847  __aeabi_idiv0                                                      
00004847  __aeabi_ldiv0                                                      
00004849  ClockP_doTick                                                      
00004855  ClockP_getSystemTickPeriod                                         
00004861  NOROM_CPUcpsid                                                     
0000486d  NOROM_CPUcpsie                                                     
00004879  PIN_registerIntCb                                                  
00004885  PowerCC26XX_schedulerRestore                                       
00004891  Power_getConstraintMask                                            
0000489d  Power_getDependencyCount                                           
000048a9  SwiP_getTrigger                                                    
000048b5  TimerP_getFreq                                                     
000048c1  __TI_zero_init                                                     
000048cd  ADCBuf_convertCancel                                               
000048d7  UDMACC26XX_hwiIntFxn                                               
000048f3  ADCBufCC26X2_init                                                  
000048fb  GPTimerCC26XX_setLoadValue                                         
00004903  HwiP_restore                                                       
0000490b  UARTCC26XX_init                                                    
00004913  malloc                                                             
00004923  ClockP_stop                                                        
00004929  NOROM_CPUdelay                                                     
0000492f  QueueP_init                                                        
00004935  UARTCC26XX_readPolling                                             
0000493b  UARTCC26XX_writePolling                                            
00004947  ADCBufCC26X2_getResolution                                         
0000494f  ClockP_isActive                                                    
00004953  ClockP_setTimeout                                                  
00004957  HwiP_enable                                                        
00004957  NoRTOS_start                                                       
0000495b  QueueP_head                                                        
0000495f  QueueP_next                                                        
00004963  SemaphoreP_delete                                                  
00004967  _system_pre_init                                                   
0000496b  C$$EXIT                                                            
0000496b  abort                                                              
00004978  resourceDB                                                         
000049cc  BoardGpioInitTable                                                 
000049f4  UARTCC26XX_fxnTable                                                
00004a44  ADCBufCC26X2_fxnTable                                              
00004a68  UART_defaultParams                                                 
00004aa8  GPTimerCC26XX_config                                               
00004ac0  gptimerCC26XXHWAttrs                                               
00004b04  GPIOCC26XX_config                                                  
00004b18  PowerCC26X2_config                                                 
00004b50  adcbufCC26XXHWAttrs                                                
00004b70  outPinTypes                                                        
00004b80  ADCBUF_CONST                                                       
00004b81  ADCBUF_SOUND_CONST                                                 
00004b82  ADCBUF_TEMPERATURE_CONST                                           
00004b83  ADCBUF_BATTERY_VOLTAGE_CONST                                       
00004b84  ADCBuf_count                                                       
00004b85  PIN_TEST1_CONST                                                    
00004b86  PIN_TEST2_CONST                                                    
00004b87  PIN_DRIVE_SPEAKER_A_CONST                                          
00004b88  PIN_DRIVE_SPEAKER_B_CONST                                          
00004b89  UART_0_CONST                                                       
00004b8a  UART_count                                                         
00004b8b  CONFIG_GPTIMER_0_CONST                                             
00004b8c  CONFIG_GPTIMER_1_CONST                                             
00004b8d  GPTimer_count                                                      
00004b90  ADCBuf_config                                                      
00004b9c  UART_config                                                        
00004ba8  inPinTypes                                                         
00004bb4  outPinStrengths                                                    
00004bd8  PINCC26XX_hwAttrs                                                  
00004be0  UDMACC26XX_config                                                  
00004bf0  udmaCC26XXHWAttrs                                                  
00004c10  __c_args__                                                         
00004ca0  __TI_Handler_Table_Base                                            
00004cac  __TI_Handler_Table_Limit                                           
00004cd0  __TI_CINIT_Base                                                    
00004cf8  __TI_CINIT_Limit                                                   
00057fa8  __ccfg                                                             
20000000  __start___llvm_prf_cnts                                            
20000000  __stop___llvm_prf_cnts                                             
20000000  uartCC26XXObjects                                                  
200001b4  adcbufCC26XXbjects                                                 
20000330  gptimerCC26XXObjects                                               
200003c8  pinHandleTable                                                     
20000548  udmaCC26XXObject                                                   
200005c9  driverlib_release_0_59848                                          
200005f8  i                                                                  
200005fc  j                                                                  
20000600  uart                                                               
20000604  PowerCC26X2_module                                                 
2000084c  ChirpDelay                                                         
200008c8  ChirpState                                                         
20000944  _hposcCoeffs                                                       
20000968  gpioCallbackFunctions                                              
20000978  gpioPinConfigs                                                     
200009a0  _lock                                                              
200009a4  _unlock                                                            
200009b0  SemaphoreP_defaultParams                                           
200009b8  ChirpSize                                                          
200009bc  ChirpIndex                                                         
200009c0  pinUpperBound                                                      
200009c4  pinLowerBound                                                      
200009cc  ClockP_tickPeriod                                                  
200009d0  adcBufCC26XXChannelLut0                                            
200009e4  HwiP_swiPIntNum                                                    
20000a00  g_pfnRAMVectors                                                    
20001a80  _sys_memory                                                        
20013c00  __stack                                                            
20014000  __STACK_END                                                        
ffffffff  __TI_pprof_out_hndl                                                
ffffffff  __TI_prof_data_size                                                
ffffffff  __TI_prof_data_start                                               
ffffffff  __binit__                                                          
ffffffff  binit                                                              
UNDEFED   ITM_flush                                                          
UNDEFED   ITM_restore                                                        
UNDEFED   __mpu_init                                                         
UNDEFED   _system_post_cinit                                                 

[290 symbols]
