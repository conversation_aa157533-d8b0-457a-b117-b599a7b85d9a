<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: src/SoundData.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_68267d1309a1af8e8297ef4c3efbcdba.html">src</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">SoundData.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">// Licensed under the Apache License, Version 2.0 (the &quot;License&quot;);</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment">// you may not use this file except in compliance with the License.</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment">// You may obtain a copy of the License at</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160; </div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment">//     http://www.apache.org/licenses/LICENSE-2.0</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment">// Unless required by applicable law or agreed to in writing, software</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment">// distributed under the License is distributed on an &quot;AS IS&quot; BASIS,</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment">// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment">// See the License for the specific language governing permissions and</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment">// limitations under the License.</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment">//</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment">// Copyright 2020 Phil Schatzmann</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160; </div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160; </div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &lt;stdint.h&gt;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &lt;stdbool.h&gt;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="preprocessor">#include &lt;algorithm&gt;</span>    <span class="comment">// std::min</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160; </div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="keyword">struct </span>__attribute__((packed)) Frame {</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;  int16_t channel1;</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;  int16_t channel2;</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160; </div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;  Frame(<span class="keywordtype">int</span> v=0){</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;    channel1 = channel2 = v;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;  }</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;  </div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;  Frame(<span class="keywordtype">int</span> ch1, <span class="keywordtype">int</span> ch2){</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;    channel1 = ch1;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;    channel2 = ch2;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;  }</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160; </div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;};</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160; </div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="comment">// support for legacy name;</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="keyword">using</span> Channels = Frame;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160; </div>
<div class="line"><a name="l00050"></a><span class="lineno"><a class="line" href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">   50</a></span>&#160;<span class="keyword">enum</span> <a class="code" href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">ChannelInfo</a> {</div>
<div class="line"><a name="l00051"></a><span class="lineno"><a class="line" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0aedf69634e61e7ec5d006874d299bc0d4">   51</a></span>&#160;    <a class="code" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0aedf69634e61e7ec5d006874d299bc0d4">Both</a>, </div>
<div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0a9d4d8b0b72fc2659da772d761a3c5ecb">   52</a></span>&#160;    <a class="code" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0a9d4d8b0b72fc2659da772d761a3c5ecb">Left</a>, </div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;    <a class="code" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0ad48f7af8c070184f3774c8e85854eb66">Right</a> </div>
<div class="line"><a name="l00054"></a><span class="lineno"><a class="line" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0ad48f7af8c070184f3774c8e85854eb66">   54</a></span>&#160;};</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160; </div>
<div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="class_sound_data.html">   72</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_sound_data.html">SoundData</a> {</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;  <span class="keyword">public</span>:</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;     <span class="keyword">virtual</span> int32_t get2ChannelData(int32_t pos, int32_t len, uint8_t *data) = 0;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;     <span class="keyword">virtual</span> int32_t getData(int32_t pos, Frame &amp;channels) = 0;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;     <span class="keyword">virtual</span> <span class="keywordtype">void</span> setDataRaw( uint8_t* data, int32_t len) = 0;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;     <span class="keywordtype">bool</span> <a class="code" href="class_sound_data.html#ac79933ed3379cf5ef58d5675aa4bf12e">doLoop</a>();</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;     <span class="keywordtype">void</span> setLoop(<span class="keywordtype">bool</span> loop);</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160; </div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;  <span class="keyword">private</span>:</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;     <span class="keywordtype">bool</span> automatic_loop;</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;};</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160; </div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160; </div>
<div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="class_two_channel_sound_data.html">   94</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_two_channel_sound_data.html">TwoChannelSoundData</a> : <span class="keyword">public</span> <a class="code" href="class_sound_data.html">SoundData</a> {</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;<span class="keyword">public</span>:</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;    <a class="code" href="class_two_channel_sound_data.html">TwoChannelSoundData</a>(<span class="keywordtype">bool</span> loop=<span class="keyword">false</span>);</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;    <a class="code" href="class_two_channel_sound_data.html">TwoChannelSoundData</a>(Frame *data, int32_t len, <span class="keywordtype">bool</span> loop=<span class="keyword">false</span>);</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;    <span class="keywordtype">void</span> setData( Frame *data, int32_t len);</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;    <span class="keywordtype">void</span> setDataRaw( uint8_t* data, int32_t len);</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;    int32_t getData(int32_t pos, int32_t len, Frame *data);</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;    int32_t getData(int32_t pos, Frame &amp;channels);</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;    int32_t <a class="code" href="class_two_channel_sound_data.html#a0a39d70aca39dbeca3d20676635b7615">get2ChannelData</a>(int32_t pos, int32_t len, uint8_t *data);</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;    <span class="comment">// the number of frames</span></div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;    int32_t count(){</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;      <span class="keywordflow">return</span> len;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;    }</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;<span class="keyword">private</span>:</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;    Frame* data;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;    int32_t len; <span class="comment">// length of all data in base unit of subclass</span></div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;};</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160; </div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160; </div>
<div class="line"><a name="l00118"></a><span class="lineno"><a class="line" href="class_one_channel_sound_data.html">  118</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_one_channel_sound_data.html">OneChannelSoundData</a> : <span class="keyword">public</span> <a class="code" href="class_sound_data.html">SoundData</a> {</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;  <span class="keyword">public</span>:</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;    <a class="code" href="class_one_channel_sound_data.html">OneChannelSoundData</a>(<span class="keywordtype">bool</span> loop=<span class="keyword">false</span>, <a class="code" href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">ChannelInfo</a> channelInfo=<a class="code" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0aedf69634e61e7ec5d006874d299bc0d4">Both</a>);</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    <a class="code" href="class_one_channel_sound_data.html">OneChannelSoundData</a>(int16_t *data, int32_t len, <span class="keywordtype">bool</span> loop=<span class="keyword">false</span>, <a class="code" href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">ChannelInfo</a> channelInfo=<a class="code" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0aedf69634e61e7ec5d006874d299bc0d4">Both</a>);</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;    <span class="keywordtype">void</span> setData( int16_t *data, int32_t len);</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;    <span class="keywordtype">void</span> setDataRaw( uint8_t* data, int32_t len);</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;    int32_t getData(int32_t pos, int32_t len, int16_t *data);</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;    int32_t getData(int32_t pos, Frame &amp;frame);</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;    int32_t <a class="code" href="class_one_channel_sound_data.html#a3ed4a75be0bb4dbb42752f5ae83badbb">get2ChannelData</a>(int32_t pos, int32_t len, uint8_t *data);</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;  <span class="keyword">private</span>:</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    int16_t* data;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;    int32_t len;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;    <a class="code" href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">ChannelInfo</a> channelInfo;</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;};</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160; </div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160; </div>
<div class="line"><a name="l00139"></a><span class="lineno"><a class="line" href="class_one_channel8_bit_sound_data.html">  139</a></span>&#160;<span class="keyword">class </span><a class="code" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a> : <span class="keyword">public</span> <a class="code" href="class_sound_data.html">SoundData</a> {</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;  <span class="keyword">public</span>:</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    <a class="code" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a>(<span class="keywordtype">bool</span> loop=<span class="keyword">false</span>, <a class="code" href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">ChannelInfo</a> channelInfo=<a class="code" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0aedf69634e61e7ec5d006874d299bc0d4">Both</a>);</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;    <a class="code" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a>(int8_t *data, int32_t len, <span class="keywordtype">bool</span> loop=<span class="keyword">false</span>, <a class="code" href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">ChannelInfo</a> channelInfo=<a class="code" href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0aedf69634e61e7ec5d006874d299bc0d4">Both</a>);</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <span class="keywordtype">void</span> setData( int8_t *data, int32_t len);</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;    <span class="keywordtype">void</span> setDataRaw( uint8_t* data, int32_t len);</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;    int32_t getData(int32_t pos, int32_t len, int8_t *data);</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;    int32_t getData(int32_t pos, Frame &amp;frame);</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;    int32_t <a class="code" href="class_one_channel8_bit_sound_data.html#a0d7a1fdf7cc8aee06c158b7dc72d491d">get2ChannelData</a>(int32_t pos, int32_t len, uint8_t *data);</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;  <span class="keyword">private</span>:</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;    int8_t* data;</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;    int32_t len;</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;    <a class="code" href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">ChannelInfo</a> channelInfo;</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;};</div>
<div class="ttc" id="aclass_one_channel8_bit_sound_data_html"><div class="ttname"><a href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a></div><div class="ttdoc">1 Channel data is provided as signed int8 values.</div><div class="ttdef"><b>Definition:</b> SoundData.h:139</div></div>
<div class="ttc" id="aclass_one_channel8_bit_sound_data_html_a0d7a1fdf7cc8aee06c158b7dc72d491d"><div class="ttname"><a href="class_one_channel8_bit_sound_data.html#a0d7a1fdf7cc8aee06c158b7dc72d491d">OneChannel8BitSoundData::get2ChannelData</a></div><div class="ttdeci">int32_t get2ChannelData(int32_t pos, int32_t len, uint8_t *data)</div><div class="ttdef"><b>Definition:</b> SoundData.cpp:206</div></div>
<div class="ttc" id="aclass_one_channel_sound_data_html"><div class="ttname"><a href="class_one_channel_sound_data.html">OneChannelSoundData</a></div><div class="ttdoc">1 Channel data is provided as int16 values</div><div class="ttdef"><b>Definition:</b> SoundData.h:118</div></div>
<div class="ttc" id="aclass_one_channel_sound_data_html_a3ed4a75be0bb4dbb42752f5ae83badbb"><div class="ttname"><a href="class_one_channel_sound_data.html#a3ed4a75be0bb4dbb42752f5ae83badbb">OneChannelSoundData::get2ChannelData</a></div><div class="ttdeci">int32_t get2ChannelData(int32_t pos, int32_t len, uint8_t *data)</div><div class="ttdef"><b>Definition:</b> SoundData.cpp:124</div></div>
<div class="ttc" id="aclass_sound_data_html"><div class="ttname"><a href="class_sound_data.html">SoundData</a></div><div class="ttdoc">Sound data as byte stream. We support TwoChannelSoundData (uint16_t + uint16_t) and OneChannelSoundDa...</div><div class="ttdef"><b>Definition:</b> SoundData.h:72</div></div>
<div class="ttc" id="aclass_sound_data_html_ac79933ed3379cf5ef58d5675aa4bf12e"><div class="ttname"><a href="class_sound_data.html#ac79933ed3379cf5ef58d5675aa4bf12e">SoundData::doLoop</a></div><div class="ttdeci">bool doLoop()</div><div class="ttdef"><b>Definition:</b> SoundData.cpp:21</div></div>
<div class="ttc" id="aclass_two_channel_sound_data_html"><div class="ttname"><a href="class_two_channel_sound_data.html">TwoChannelSoundData</a></div><div class="ttdoc">Data is provided in two channels of int16 data: so len is in 4 byte entries (int16 + int16)</div><div class="ttdef"><b>Definition:</b> SoundData.h:94</div></div>
<div class="ttc" id="aclass_two_channel_sound_data_html_a0a39d70aca39dbeca3d20676635b7615"><div class="ttname"><a href="class_two_channel_sound_data.html#a0a39d70aca39dbeca3d20676635b7615">TwoChannelSoundData::get2ChannelData</a></div><div class="ttdeci">int32_t get2ChannelData(int32_t pos, int32_t len, uint8_t *data)</div><div class="ttdef"><b>Definition:</b> SoundData.cpp:78</div></div>
<div class="ttc" id="agroup__a2dp_html_gadd07e8b0b75b5153b83a4580f2d5c6c0"><div class="ttname"><a href="group__a2dp.html#gadd07e8b0b75b5153b83a4580f2d5c6c0">ChannelInfo</a></div><div class="ttdeci">ChannelInfo</div><div class="ttdoc">Channel Information.</div><div class="ttdef"><b>Definition:</b> SoundData.h:50</div></div>
<div class="ttc" id="agroup__a2dp_html_ggadd07e8b0b75b5153b83a4580f2d5c6c0a9d4d8b0b72fc2659da772d761a3c5ecb"><div class="ttname"><a href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0a9d4d8b0b72fc2659da772d761a3c5ecb">Left</a></div><div class="ttdeci">@ Left</div><div class="ttdef"><b>Definition:</b> SoundData.h:52</div></div>
<div class="ttc" id="agroup__a2dp_html_ggadd07e8b0b75b5153b83a4580f2d5c6c0ad48f7af8c070184f3774c8e85854eb66"><div class="ttname"><a href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0ad48f7af8c070184f3774c8e85854eb66">Right</a></div><div class="ttdeci">@ Right</div><div class="ttdef"><b>Definition:</b> SoundData.h:53</div></div>
<div class="ttc" id="agroup__a2dp_html_ggadd07e8b0b75b5153b83a4580f2d5c6c0aedf69634e61e7ec5d006874d299bc0d4"><div class="ttname"><a href="group__a2dp.html#ggadd07e8b0b75b5153b83a4580f2d5c6c0aedf69634e61e7ec5d006874d299bc0d4">Both</a></div><div class="ttdeci">@ Both</div><div class="ttdef"><b>Definition:</b> SoundData.h:51</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
