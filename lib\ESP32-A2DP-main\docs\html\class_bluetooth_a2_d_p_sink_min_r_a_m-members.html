<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">BluetoothA2DPSinkMinRAM Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_bluetooth_a2_d_p_sink_min_r_a_m.html">BluetoothA2DPSinkMinRAM</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a22d52952a8ac8c78a483a53c2006a387">activate_pin_code</a>(bool active)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>address_validator</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_a2d_callback</b>(esp_a2d_cb_event_t event, esp_a2d_cb_param_t *param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_alloc_meta_buffer</b>(esp_avrc_ct_cb_param_t *param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_gap_callback</b>(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t *param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_rc_ct_callback</b>(esp_avrc_ct_cb_event_t event, esp_avrc_ct_cb_param_t *param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_send_msg</b>(app_msg_t *msg) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_task_handle</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a361f80944f06806b7e42302f95171675">app_task_handler</a>(void *arg)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_task_queue</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_task_shut_down</b>(void) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_task_start_up</b>(void) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>app_work_dispatch</b>(app_callback_t p_cback, uint16_t event, void *p_params, int param_len) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>app_work_dispatched</b>(app_msg_t *msg) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>audio_data_callback</b>(const uint8_t *data, uint32_t len) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>audio_state</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>audio_state_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>audio_state_obj</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>audio_type</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>av_hdl_a2d_evt</b>(uint16_t event, void *p_param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>av_hdl_avrc_evt</b>(uint16_t event, void *p_param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>av_hdl_stack_evt</b>(uint16_t event, void *p_param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>av_new_track</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>av_notify_evt_handler</b>(uint8_t event_id, uint32_t event_parameter) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>avrc_metadata_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>avrc_metadata_flags</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a2a383635d7b050833f56ee79867716bd">BluetoothA2DPSink</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>BluetoothA2DPSinkMinRAM</b>()=default (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_min_r_a_m.html">BluetoothA2DPSinkMinRAM</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_min_r_a_m.html">BluetoothA2DPSinkMinRAM</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bt_connected</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bt_dis_connected</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bt_i2s_task_shut_down</b>(void) override (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_min_r_a_m.html">BluetoothA2DPSinkMinRAM</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_min_r_a_m.html">BluetoothA2DPSinkMinRAM</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bt_i2s_task_start_up</b>(void) override (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_min_r_a_m.html">BluetoothA2DPSinkMinRAM</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_min_r_a_m.html">BluetoothA2DPSinkMinRAM</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>bt_name</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>bt_volumechange</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>clean_last_connection</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5d4707195d0d6e79b65bef4ed48a57c2">confirm_pin_code</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a43369961e9858cf99798e9c1b6a634b9">confirm_pin_code</a>(int code)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>connect_to_last_device</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>connection_rety_count</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>connection_state</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>connection_state_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>connection_state_obj</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>data_received</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#aa6601d3c57e37f77bfdd03a3ef6231e2">debounce</a>(void(*cb)(void), int ms)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>debounce_ms</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>default_volume_control</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#ab63e627832d6377be32dd700130bf0d8">disconnect</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5a91e49987a2e39c09fc6c2a64feaed6">end</a>(bool release_memory=false)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>end_in_progress</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>esp_spp_mode</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">static</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>event_queue_size</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>event_stack_size</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>execute_avrc_command</b>(int cmd) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a42e01689353026b1ef9883fd5d32f00c">fast_forward</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a74eadbd69b5c7adf1b190c7e41b75b10">get_audio_state</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a77600cb1e36b7814eb9b4126cdec62d4">get_audio_type</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a513b32676d8fc248bb481180f832ef97">get_connection_state</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ac08fff859e0ccfbb12cbb6b119dba438">get_current_peer_address</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>get_last_connection</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#ac21e1dbd2f5f475da871a7e778ba1a40">get_last_peer_address</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5a770be98d977a8df916d8cc044b310c">get_last_rssi</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aca1119f20d2321fb950ae859000cce7b">get_volume</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>handle_audio_cfg</b>(uint16_t event, void *p_param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>handle_audio_state</b>(uint16_t event, void *p_param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>handle_connection_state</b>(uint16_t event, void *p_param) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>has_last_connection</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>i2s_channels</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>i2s_config</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a90e76786cf62555379796e63f4499951">i2s_mclk_pin_select</a>(const uint8_t pin)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>i2s_port</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>i2s_ringbuffer_size</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>i2s_stack_size</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>i2s_task_handler</b>(void *arg) override (defined in <a class="el" href="class_bluetooth_a2_d_p_sink_min_r_a_m.html">BluetoothA2DPSinkMinRAM</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_min_r_a_m.html">BluetoothA2DPSinkMinRAM</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>i2s_task_priority</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ab59e6c716d9b5aaed69272e7d2a3d12a">i2s_write_data</a>(const uint8_t *data, size_t item_size)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>init_bluetooth</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>init_i2s</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>init_nvs</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>is_auto_reconnect</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a19842c9bedcfd1d88e4f62f7a2523db7">is_connected</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>is_connecting</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>is_i2s_output</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>is_pin_code_active</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>is_reconnect</b>(esp_a2d_disc_rsn_t type) (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>is_start_disabled</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>is_volume_used</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>last_bda_nvs_name</b>() (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>last_connection</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>last_rssi_delta</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a791432e5c800e75fb11b858071cff651">log_free_heap</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>m_a2d_audio_state_str</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>m_a2d_conn_state_str</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>m_pkt_cnt</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a296fb7aaf8d8e78991d9d505353de94f">next</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aa6967e9329939596c62f16e8686cac13">pause</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>peer_bd_addr</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a3719138f63afaeed06b63cc48ea79335">pin_code</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>pin_code_int</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>pin_code_request</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>pin_code_str</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>pin_config</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aafd2afad1960db8ab73d7c6977aeb686">play</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>player_init</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a341024c18eabdb06c734c2242d5ba505">previous</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#aca9eebdad8d5525cb3dc1406e2c455c2">reconnect</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>reconnect_on_normal_disconnect</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a9ee01e6d11ee3c6c546a510029a23a12">rewind</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>rssi_active</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>rssi_callbak</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>s_bt_i2s_task_handle</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>s_ringbuf_i2s</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>s_volume</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>s_volume_lock</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>s_volume_notify</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a09a8b269e2a936c5517bd9f88f666a1c">sample_rate</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>sample_rate_callback</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ad25155f02bad11da6c130aae00c8ab9c">set_address_validator</a>(bool(*callBack)(esp_bd_addr_t remote_bda))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ac7ba90ebbed89f38e292de7a0ac1bdb0">set_auto_reconnect</a>(bool reconnect, bool afterNormalDisconnect=false, int count=AUTOCONNECT_TRY_NUM)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a537d576b12d1158eb0681a6195b258de">BluetoothA2DPCommon::set_auto_reconnect</a>(bool active)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a8281af353148544a0612f8f7c4d511b1">set_avrc_metadata_attribute_mask</a>(int flags)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#aac9074521c80d7574a855f30b8301d13">set_avrc_metadata_callback</a>(void(*callback)(uint8_t, const uint8_t *))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a9ebe4927600f29318133b5f11e0ab7f8">set_bits_per_sample</a>(int bps)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#abc8a4564e135ace22d31c2231f1a0696">set_channels</a>(i2s_channel_t channels)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a9fdeaf51df336473114a5f8d593703b1">set_connected</a>(bool active)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a3061ae4cc5bd536094f5fa836fffc081">set_event_queue_size</a>(int size)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a6b32be65a83a744869c8a9565a170cf3">set_event_stack_size</a>(int size)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a39329de792f43f90a16f9ab2ee62814f">set_i2s_config</a>(i2s_config_t i2s_config)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ab4e52dff7ef08f17cfce4e011cdd6542">set_i2s_port</a>(i2s_port_t i2s_num)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a5e5d13f82d40279028631248b51e1382">set_i2s_ringbuffer_size</a>(int size)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a73e1da11ce7d4ebc3bc3a3368feb5d7f">set_i2s_stack_size</a>(int size)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ad9261a4a966cfc552ce31059029ed0de">set_i2s_task_priority</a>(UBaseType_t prio)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>set_last_connection</b>(esp_bd_addr_t bda) (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a624040cce89a4a2f66495f57db6c1457">set_mono_downmix</a>(bool enabled)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a5f13ecf541393c21a5a489235bad27fb">set_on_audio_state_changed</a>(void(*callBack)(esp_a2d_audio_state_t state, void *), void *obj=nullptr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#aa79cff78c075c9273ea2b5c03f052fcd">set_on_connection_state_changed</a>(void(*callBack)(esp_a2d_connection_state_t state, void *), void *obj=nullptr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af65219e635fadbbc90f4663b33abd3e0">set_on_data_received</a>(void(*callBack)())</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#ac245103d3d5c47b0414c2de21c0d52a7">set_on_volumechange</a>(void(*callBack)(int))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af7ce8131af4f085e94eb81e3fcbfc4af">set_pin_config</a>(i2s_pin_config_t pin_config)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a452b509a46930ab1ed58188a4181c67b">set_rssi_active</a>(bool active)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a97c80237061c6e80d7c6e1e5e45773c8">set_rssi_callback</a>(void(*callback)(esp_bt_gap_cb_param_t::read_rssi_delta_param &amp;rssi))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a89d2694f880a2db22344b97b466c9a9d">set_sample_rate_callback</a>(void(*callback)(uint16_t rate))</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>set_scan_mode_connectable</b>(bool connectable) (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a4f94426ff4899c437d31623e013cf7a5">set_stream_reader</a>(void(*callBack)(const uint8_t *, uint32_t), bool i2s_output=true)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a14d982730e2b9ea772fe9ede1563ed22">set_swap_lr_channels</a>(bool swap)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a68f9e168839f0faeb72705ccabbb6b7a">set_task_priority</a>(UBaseType_t priority)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a507e30ececfdc4382af60a0319cdaf1b">set_volume</a>(uint8_t volume)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a7757ddbf424aeb909dc952d7c40fc241">set_volume_control</a>(A2DPVolumeControl *ptr)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#af85e99324638d1c814e221ac4ba815dd">start</a>(const char *name, bool auto_reconect)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a189424ab5dc8c44f00b461e9392a2ce8">start</a>(const char *name)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a37dcbcd418b84310ccedf3330e44834f">stop</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>stream_reader</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>swap_left_right</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>task_priority</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a2b78346084e12feeea035d006e7cf07a">to_str</a>(esp_a2d_connection_state_t state)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a38d70707790dec91d63da2006f2ff17a">to_str</a>(esp_a2d_audio_state_t state)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#afa76a15aa8922301e72a745b540b040c">to_str</a>(esp_bd_addr_t bda)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>try_reconnect_max_count</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a6fec0cfd3d0d9017b7ffcf82630ab89a">volume_control</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">protected</span><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>volume_control_ptr</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>volume_value</b> (defined in <a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a>)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">protected</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_min_r_a_m.html#af584c3b4db5ccc746cf59b95c977be27">write_ringbuf</a>(const uint8_t *data, size_t size)</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink_min_r_a_m.html">BluetoothA2DPSinkMinRAM</a></td><td class="entry"><span class="mlabel">inline</span><span class="mlabel">virtual</span></td></tr>
  <tr><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html#a4bbbd1a2c9c85004afaa7c6dbad45322">~BluetoothA2DPCommon</a>()=default</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_common.html">BluetoothA2DPCommon</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr class="even"><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html#a0f83dea1a97baeb360e4e1221c0aeaa9">~BluetoothA2DPSink</a>()</td><td class="entry"><a class="el" href="class_bluetooth_a2_d_p_sink.html">BluetoothA2DPSink</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
