#pragma once
#include "AudioUtils.h"
#include <cstdint>
#include <cstddef>
#include <cmath>

class ChirpGenerator {
public:
    ChirpGenerator();
    const int16_t* generate();
    std::size_t size() const;
    void transmitChirp(void (*outputFunction)(int16_t));

private:
    static constexpr uint32_t NUM_SAMPLES = CHIRP_TOTAL;  // Use CHIRP_TOTAL (1440 samples)
    int16_t chirp[NUM_SAMPLES];
    bool generated = false;

    // Sine chirp parameters - maximaler Schalldruck ohne Clipping
    static constexpr int16_t CHIRP_AMPLITUDE = 32767;  // ±32767 (int16_t voll aussteuern)
    static constexpr uint32_t FADE_SAMPLES = 144;      // 3ms fade at 48kHz (144 samples)

    void generateSineChirp();
};
