#include <Arduino.h>
#include "i2s_sender.h"
#include <ESPNowManager.h>
#include <ChirpGenerator.h>

const uint8_t RECEIVER_MAC[] = {0x50, 0x02, 0x91, 0x98, 0xFC, 0xBC};
const uint8_t SYNC_COMMAND = 0xAA;  // Sync command byte
I2SSender i2s;
ESPNowManager espNow;
ChirpGenerator chirpGen;

// Counter for debug output
uint32_t cycleCount = 0;

void setup() {
    Serial.begin(115200);
    while(!Serial);
    
    Serial.println("\n\n--- NodeA (Transmitter) Starting ---");
    
    // Print own MAC address
    uint8_t macAddr[6];
    WiFi.macAddress(macAddr);
    Serial.printf("My MAC: %02X:%02X:%02X:%02X:%02X:%02X\n", 
                 macAddr[0], macAddr[1], macAddr[2], macAddr[3], macAddr[4], macAddr[5]);
    
    Serial.printf("Target MAC: %02X:%02X:%02X:%02X:%02X:%02X\n", 
                 RECEIVER_MAC[0], RECEIVER_MAC[1], RECEIVER_MAC[2], 
                 RECEIVER_MAC[3], RECEIVER_MAC[4], RECEIVER_MAC[5]);
    
    Serial.println("Initializing ESP-NOW...");
    espNow.init(true);
    
    Serial.println("Adding peer...");
    bool peerAdded = espNow.addPeer(RECEIVER_MAC);
    Serial.printf("Peer added: %s\n", peerAdded ? "success" : "FAILED");
    
    Serial.println("Initializing I2S...");
    i2s.init();
    
    Serial.println("Initializing chirp generator...");
    const int16_t* chirp = chirpGen.generate();
    Serial.printf("Chirp size: %u samples\n", chirpGen.size());
    Serial.printf("First sample: %d, Last sample: %d\n",
                 chirp[0], chirp[chirpGen.size()-1]);

    Serial.println("\n=== NodeA initialized and ready ===");
    Serial.println("Send commands via Serial Monitor:");
    Serial.println("  'chirp' or 'c' - Send chirp sequence");
    Serial.println("  'dump' or 'd'  - Dump I2S data (decimal)");
    Serial.println("  'hex' or 'x'   - Dump I2S data (hex)");
    Serial.println("  'help' or 'h'  - Show help");
    Serial.println("  'info' or 'i'  - Show chirp info");
    Serial.println("=====================================\n");
}

void dumpI2SData() {
    Serial.println("\n=== I2S Data Dump for WAV Creation ===");
    Serial.printf("Sample Rate: %u Hz\n", SAMPLE_RATE);
    Serial.printf("Samples: %u\n", chirpGen.size());
    Serial.printf("Duration: %.3f ms\n", CHIRP_SEC * 1000);
    Serial.println("Format: 16-bit signed PCM");
    Serial.println("Data (copy this for WAV creation):");
    Serial.println("--- DATA START ---");

    const int16_t* chirp = chirpGen.generate();

    // Output data in a format that's easy to copy and convert to WAV
    // Format: one sample per line as decimal values
    for(uint32_t i = 0; i < chirpGen.size(); i++) {
        Serial.println(chirp[i]);

        // Add a small delay every 100 samples to prevent UART buffer overflow
        if (i % 100 == 99) {
            delay(10);
        }
    }

    Serial.println("--- DATA END ---");
    Serial.println("Copy the data between START and END markers.");
    Serial.println("Each line contains one 16-bit sample value.");
    Serial.printf("Total samples: %u\n", chirpGen.size());
    Serial.println("=====================================\n");
}

void dumpI2SDataHex() {
    Serial.println("\n=== I2S Data Dump (Hexadecimal) ===");
    Serial.printf("Sample Rate: %u Hz\n", SAMPLE_RATE);
    Serial.printf("Samples: %u\n", chirpGen.size());
    Serial.printf("Duration: %.3f ms\n", CHIRP_SEC * 1000);
    Serial.println("Format: 16-bit signed PCM (little-endian hex)");
    Serial.println("--- HEX DATA START ---");

    const int16_t* chirp = chirpGen.generate();

    // Output data as hexadecimal bytes (little-endian)
    for(uint32_t i = 0; i < chirpGen.size(); i++) {
        uint16_t sample = static_cast<uint16_t>(chirp[i]);
        Serial.printf("%02X%02X ", sample & 0xFF, (sample >> 8) & 0xFF);

        // New line every 16 samples for readability
        if ((i + 1) % 16 == 0) {
            Serial.println();
        }

        // Add delay every 100 samples to prevent UART buffer overflow
        if (i % 100 == 99) {
            delay(10);
        }
    }

    Serial.println("\n--- HEX DATA END ---");
    Serial.println("Copy hex data for binary WAV creation.");
    Serial.printf("Total bytes: %u\n", chirpGen.size() * 2);
    Serial.println("===================================\n");
}

void sendChirpSequence() {
    cycleCount++;

    Serial.printf("\n--- Sending Chirp Sequence %u ---\n", cycleCount);

    // Send sync pulse with 0xAA command
    Serial.println("Sending sync command...");
    bool sendResult = espNow.send(RECEIVER_MAC, &SYNC_COMMAND, 1);
    Serial.printf("Sync send result: %s\n", sendResult ? "success" : "FAILED");

    // Transmit chirp
    Serial.println("Sending chirp...");
    i2s.send(chirpGen.generate(), chirpGen.size());

    // Fade-out
    Serial.println("Sending fade-out...");
    int16_t fade[50];
    const int16_t* lastSample = chirpGen.generate() + chirpGen.size() - 1;
    for(int i = 0; i < 50; i++) {
        fade[i] = *lastSample * (50 - i) / 50;
    }
    i2s.send(fade, 50);

    Serial.println("Chirp sequence completed.");
}

void loop() {
    // Check for serial commands
    if (Serial.available() > 0) {
        String command = Serial.readStringUntil('\n');
        command.trim(); // Remove whitespace

        if (command.equalsIgnoreCase("chirp") || command.equalsIgnoreCase("c")) {
            sendChirpSequence();
        } else if (command.equalsIgnoreCase("help") || command.equalsIgnoreCase("h")) {
            Serial.println("\nAvailable commands:");
            Serial.println("  chirp (or c)  - Send chirp sequence");
            Serial.println("  help (or h)   - Show this help message");
            Serial.println("  info (or i)   - Show chirp information");
            Serial.println("  dump (or d)   - Dump I2S data to UART (decimal)");
            Serial.println("  hex (or x)    - Dump I2S data to UART (hexadecimal)");
        } else if (command.equalsIgnoreCase("dump") || command.equalsIgnoreCase("d")) {
            dumpI2SData();
        } else if (command.equalsIgnoreCase("hex") || command.equalsIgnoreCase("x")) {
            dumpI2SDataHex();
        } else if (command.equalsIgnoreCase("info") || command.equalsIgnoreCase("i")) {
            Serial.println("\nChirp Information:");
            Serial.printf("  Sample Rate: %u Hz\n", SAMPLE_RATE);
            Serial.printf("  Duration: %.1f ms\n", CHIRP_SEC * 1000);
            Serial.printf("  Samples: %u\n", chirpGen.size());
            Serial.printf("  Start Freq: %.0f Hz\n", F_START);
            Serial.printf("  End Freq: %.0f Hz\n", F_END);
            Serial.printf("  Frequency Slope: %.2f Hz/sample\n", CHIRP_K);
        } else if (command.length() > 0) {
            Serial.printf("Unknown command: '%s'\n", command.c_str());
            Serial.println("Type 'help' for available commands.");
        }
    }

    // Small delay to prevent excessive CPU usage
    delay(10);
}
