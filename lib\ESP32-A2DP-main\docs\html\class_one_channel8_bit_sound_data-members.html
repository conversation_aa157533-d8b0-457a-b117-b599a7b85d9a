<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">OneChannel8BitSoundData Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"><a class="el" href="class_sound_data.html#ac79933ed3379cf5ef58d5675aa4bf12e">doLoop</a>()</td><td class="entry"><a class="el" href="class_sound_data.html">SoundData</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_one_channel8_bit_sound_data.html#a0d7a1fdf7cc8aee06c158b7dc72d491d">get2ChannelData</a>(int32_t pos, int32_t len, uint8_t *data)</td><td class="entry"><a class="el" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>getData</b>(int32_t pos, int32_t len, int8_t *data) (defined in <a class="el" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a>)</td><td class="entry"><a class="el" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>getData</b>(int32_t pos, Frame &amp;frame) (defined in <a class="el" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a>)</td><td class="entry"><a class="el" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>OneChannel8BitSoundData</b>(bool loop=false, ChannelInfo channelInfo=Both) (defined in <a class="el" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a>)</td><td class="entry"><a class="el" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a></td><td class="entry"></td></tr>
  <tr><td class="entry"><a class="el" href="class_one_channel8_bit_sound_data.html#a056d8a555b92a29189b64d609450dd4d">OneChannel8BitSoundData</a>(int8_t *data, int32_t len, bool loop=false, ChannelInfo channelInfo=Both)</td><td class="entry"><a class="el" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>setData</b>(int8_t *data, int32_t len) (defined in <a class="el" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a>)</td><td class="entry"><a class="el" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0"><td class="entry"><b>setDataRaw</b>(uint8_t *data, int32_t len) (defined in <a class="el" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a>)</td><td class="entry"><a class="el" href="class_one_channel8_bit_sound_data.html">OneChannel8BitSoundData</a></td><td class="entry"><span class="mlabel">virtual</span></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry"><b>setLoop</b>(bool loop) (defined in <a class="el" href="class_sound_data.html">SoundData</a>)</td><td class="entry"><a class="el" href="class_sound_data.html">SoundData</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
