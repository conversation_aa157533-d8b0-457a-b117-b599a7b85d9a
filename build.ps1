param (
    [string]$command = "help"
)

$PORT_A = "COM3"
$PORT_B = "COM4"

# Update this path to match your PlatformIO installation
$PIO = "C:\Users\<USER>\.platformio\penv\Scripts\pio.exe"
# If you're using VS Code with PlatformIO extension, you might need:
# $PIO = "C:\Users\<USER>\.platformio\penv\Scripts\platformio.exe"

function Kill-PortProcesses {
    param (
        [string]$port
    )
    Write-Host "Checking for processes using $port..."
    $processes = Get-CimInstance -Query "SELECT * FROM Win32_Process" | Where-Object {$_.CommandLine -like "*$port*"}
    
    foreach ($process in $processes) {
        Write-Host "Killing process $($process.ProcessId) ($($process.Name)) using $port..."
        Stop-Process -Id $process.ProcessId -Force
        Start-Sleep -Milliseconds 500
    }
}

function Build-A {
    Write-Host "Building NodeA..."
    & $PIO run -e nodeA
}

function Build-B {
    Write-Host "Building NodeB..."
    & $PIO run -e nodeB
}

function Upload-A {
    Write-Host "Preparing to upload to NodeA..."
    Kill-PortProcesses -port $PORT_A
    Write-Host "Uploading to NodeA..."
    & $PIO run -e nodeA -t upload
}

function Upload-B {
    Write-Host "Preparing to upload to NodeB..."
    Kill-PortProcesses -port $PORT_B
    Write-Host "Uploading to NodeB..."
    & $PIO run -e nodeB -t upload
}

function Test-A {
    Write-Host "Preparing to upload test to NodeA..."
    Kill-PortProcesses -port $PORT_A
    Write-Host "Uploading test to NodeA..."
    & $PIO run -e nodeA_test -t upload
}

function Test-B {
    Write-Host "Preparing to upload test to NodeB..."
    Kill-PortProcesses -port $PORT_B
    Write-Host "Uploading test to NodeB..."
    & $PIO run -e nodeB_test -t upload
}

function Test-Mic {
    Write-Host "Preparing to upload microphone test to NodeB..."
    Kill-PortProcesses -port $PORT_B
    Write-Host "Uploading microphone test to NodeB..."
    & $PIO run -e nodeB_mic_test -t upload
}

function Record-Microphone {
    Write-Host "Starting microphone recording from ESP32 (COM4)..."
    Kill-PortProcesses -port $PORT_B
    Start-Sleep -Seconds 1

    # Check if Python script exists
    if (Test-Path "src\pythontools\microphone_recorder_com4.py") {
        Write-Host "Running microphone recorder script..."
        python src\pythontools\microphone_recorder_com4.py
    } else {
        Write-Host "Error: microphone_recorder_com4.py not found!"
        Write-Host "Please ensure the Python script is in src\pythontools\ directory."
    }
}

function Upload-Mic-Recorder {
    Write-Host "Uploading microphone recorder firmware and starting recording..."
    Kill-PortProcesses -port $PORT_B
    Write-Host "Uploading microphone recorder to ESP32..."
    & $PIO run -e microphone_recorder -t upload
    Start-Sleep -Seconds 3
    Write-Host "Starting Python recorder script..."
    Record-Microphone
}

function Debug-Microphone {
    Write-Host "Starting microphone debug tool..."
    Kill-PortProcesses -port $PORT_B
    Start-Sleep -Seconds 1

    # Check if Python script exists
    if (Test-Path "src\pythontools\debug_microphone.py") {
        Write-Host "Running microphone debug script..."
        python src\pythontools\debug_microphone.py
    } else {
        Write-Host "Error: debug_microphone.py not found!"
        Write-Host "Please ensure the Python script is in src\pythontools\ directory."
    }
}

function Monitor-A {
    Write-Host "Monitoring NodeA..."
    & $PIO device monitor -p $PORT_A -b 115200
}

function Monitor-B {
    Write-Host "Monitoring NodeB..."
    & $PIO device monitor -p $PORT_B -b 115200
}

function Test-Speed {
    Write-Host "Uploading speed test to NodeA & NodeB..."
    Kill-PortProcesses -port $PORT_A
    Kill-PortProcesses -port $PORT_B
    & $PIO run -e nodeA_speed_test -t upload
    & $PIO run -e nodeB_speed_test -t upload
}




function Clean {
    Write-Host "Cleaning build files..."
    & $PIO run -t clean
}

function Build-All {
    Build-A
    Build-B
}

function Upload-All {
    Write-Host "Preparing to upload to all nodes..."
    Kill-PortProcesses -port $PORT_A
    Kill-PortProcesses -port $PORT_B
    Write-Host "Uploading to NodeA and NodeB only (no test environments)..."
    # Use the --environment flag to explicitly specify which environments to upload
    & $PIO run --environment nodeA --environment nodeB -t upload
}
function Test-Uart {
    Write-Host "Uploading UART‑trigger test to NodeA & NodeB..."
    Kill-PortProcesses -port $PORT_A
    Kill-PortProcesses -port $PORT_B
    & $PIO run -e nodeA_uart_test -t upload
    & $PIO run -e nodeB_uart_test -t upload
}
function Test-All {
    Write-Host "Preparing to upload test to all nodes..."
    Kill-PortProcesses -port $PORT_A
    Kill-PortProcesses -port $PORT_B
    Write-Host "Uploading test to NodeA..."
    & $PIO run -e nodeA_test -t upload
    Write-Host "Uploading test to NodeB..."
    & $PIO run -e nodeB_test -t upload
}

function Capture-Chirp {
    Write-Host "Capturing chirp data from NodeA and creating WAV file..."
    Kill-PortProcesses -port $PORT_A
    Start-Sleep -Seconds 1

    # Check if Python script exists
    if (Test-Path "src\pythontools\capture_dump.py") {
        Write-Host "Running Python capture script..."
        python src\pythontools\capture_dump.py
    } elseif (Test-Path "capture_dump.py") {
        Write-Host "Running Python capture script..."
        python capture_dump.py
    } else {
        Write-Host "Error: capture_dump.py not found!"
        Write-Host "Please ensure the Python script is in the project directory."
    }
}

function Start-Correlation {
    Write-Host "Running correlation analysis on WAV files..."

    # Check if correlation script exists
    if (Test-Path "src\pythontools\corr.py") {
        Write-Host "Found correlation script, running analysis..."

        # Store current location
        $currentLocation = Get-Location

        try {
            # Change to pythontools directory
            Set-Location "src\pythontools"
            Write-Host "Changed to directory: $(Get-Location)"

            # Run the correlation script
            python corr.py
        }
        finally {
            # Always return to original directory
            Set-Location $currentLocation
            Write-Host "Returned to directory: $(Get-Location)"
        }
    } else {
        Write-Host "Error: corr.py not found!"
        Write-Host "Please ensure the correlation script is in src\pythontools\ directory."
    }
}

function Show-Help {
    Write-Host "Available commands:"
    Write-Host "  .\build.ps1 build-a      - Build NodeA"
    Write-Host "  .\build.ps1 build-b      - Build NodeB"
    Write-Host "  .\build.ps1 build-all    - Build both nodes"
    Write-Host "  .\build.ps1 upload-a     - Upload to NodeA (COM3)"
    Write-Host "  .\build.ps1 upload-b     - Upload to NodeB (COM4)"
    Write-Host "  .\build.ps1 upload-all   - Upload to both nodes"
    Write-Host "  .\build.ps1 test-a       - Upload test to NodeA (COM3)"
    Write-Host "  .\build.ps1 test-b       - Upload test to NodeB (COM4)"
    Write-Host "  .\build.ps1 test-all     - Upload test to both nodes"
    Write-Host "  .\build.ps1 test-mic     - Upload microphone test to NodeB (COM4)"
    Write-Host "  .\build.ps1 record-mic   - Start microphone recording (Python script)"
    Write-Host "  .\build.ps1 mic-recorder - Upload firmware & start recording"
    Write-Host "  .\build.ps1 debug-mic    - Debug microphone output (see raw data)"
    Write-Host "  .\build.ps1 capture      - Capture chirp data and create WAV file"
    Write-Host "  .\build.ps1 corr         - Run correlation analysis on WAV files"
    Write-Host "  .\build.ps1 monitor-a    - Monitor NodeA serial output"
    Write-Host "  .\build.ps1 monitor-b    - Monitor NodeB serial output"
    Write-Host "  .\build.ps1 clean        - Clean build files"
    Write-Host "  .\build.ps1 help         - Show this help message"
}

switch ($command.ToLower()) {
    "build-a" { Build-A }
    "build-b" { Build-B }
    "build-all" { Build-All }
    "upload-a" { Upload-A }
    "upload-b" { Upload-B }
    "upload-all" { Upload-All }
    "test-a" { Test-A }
    "test-b" { Test-B }
    "test-all" { Test-All }
    "test-uart" { Test-Uart }
    "test-mic" { Test-Mic }
    "test-speed" { Test-Speed }
    "record-mic" { Record-Microphone }
    "mic-recorder" { Upload-Mic-Recorder }
    "debug-mic" { Debug-Microphone }
    "capture" { Capture-Chirp }
    "corr" { Start-Correlation }
    "monitor-a" { Monitor-A }
    "monitor-b" { Monitor-B }
    "clean" { Clean }
    default { Show-Help }
}










