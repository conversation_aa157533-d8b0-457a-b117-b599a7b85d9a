#include <stdint.h>
#include <stddef.h>
#include <stdio.h>
#include <stdlib.h>
#include <ti/drivers/Board.h>
#include "ti_drivers_config.h"
#include "time.h"
#include "gpio.h"
#include "SoundTransmit.h"

#define ArraySize(x)  (sizeof(x) / sizeof((x)[0]))

int16_t BeepDelay[]= { 200,8000,200,8000,200,8000,200,8000,200,8000,200,8000,200,8000,200,8000,200,8000,200,8000,200,8000,200,
                       8000,200,8000,200,8000,200,8000,200,8000,200,8000,200,8000,200,8000,200,8000};
int16_t BeepState[]= { 1,0,-1,0,1,0,-1,0,1,0,-1,0,1,0,-1,0,1,0,-1,0,1,0,-1,0,1,0,-1,0,1,0,-1,0,1,0,-1,0,1,0,-1,0};

int16_t ChirpDelay[]= { 800, 533, 800, 533, 800, 800, 800, 800, 800, 1067, 800, 1067, 1067, 1067, 1067, 1333, 1067, 1333, 1067,
                        1333, 1333, 1333, 1333, 1600, 1333, 1600, 1333, 1600, 1600, 1600, 1000};
int16_t ChirpState[]= { 1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,1,-1,0};

int16_t volatile ChirpIndex=0;
int16_t *DelayArray;
int16_t *StateArray;
int16_t ArraySize;

void TimerMatchCallback(void);

//*********************************************************************************************************
// Initialize Sound transmit
//*********************************************************************************************************
void SoundTransmit_Init(void)
{
 Time_SetCallbackFunctionOnMatch(TimerMatchCallback);
}

//*********************************************************************************************************
// transmit a specified sound
//*********************************************************************************************************
void SoundTransmit_GenerateSound(SoundTypes Type)
{
    ChirpIndex=0;
    switch (Type)
    {
    case eChirp: {
                   DelayArray = ChirpDelay;
                   StateArray =ChirpState;
                   ArraySize= ArraySize(ChirpDelay);
                   break;
                 }
    default :    {
                   DelayArray = BeepDelay;
                   StateArray =BeepState;
                   ArraySize= ArraySize(BeepDelay);
                   break;
                 }
    }
    Time_EnableMatch();
    while (ChirpIndex<ArraySize);
}

//*********************************************************************************************************
// interrupt function that drives speaker
//*********************************************************************************************************

void TimerMatchCallback(void)
{
   Time_SetNextMatch(DelayArray[ChirpIndex]);
   switch (StateArray[ChirpIndex])
          {
            case 1 : {  Gpio_SetSpeakerOutput1;
                        Gpio_ClearSpeakerOutput2;
                        break; }
            case -1: {  Gpio_ClearSpeakerOutput1;
                        Gpio_SetSpeakerOutput2;
                        break; }
            default : { Gpio_ClearSpeakerOutput1;
                        Gpio_ClearSpeakerOutput2;
                        break; }
          }
  ChirpIndex++;
  if (ChirpIndex>=ArraySize)  Time_DisableMatch();
}
