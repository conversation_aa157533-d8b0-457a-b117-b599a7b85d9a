#include "i2s_sender.h"

void I2SSender::init() {
    i2s_config_t config = {
        .mode = static_cast<i2s_mode_t>(I2S_MODE_MASTER | I2S_MODE_TX),
        .sample_rate = SAMPLE_RATE,
        .bits_per_sample = I2S_BITS_PER_SAMPLE_16BIT,
        .channel_format = I2S_CHANNEL_FMT_ONLY_LEFT, // Änderung zu Stereo
        .communication_format = I2S_COMM_FORMAT_STAND_I2S,
        .intr_alloc_flags = ESP_INTR_FLAG_LEVEL1,
        .dma_buf_count = 8,
        .dma_buf_len = 1024,
        .use_apll = false,
        .tx_desc_auto_clear = true
    };
    
    i2s_driver_install(I2S_NUM_0, &config, 0, nullptr);
    i2s_set_pin(I2S_NUM_0, &pinConfig);
    clearBuffer();
}

void I2SSender::send(const int16_t* data, size_t samples) {
    size_t bytesWritten;
    i2s_write(I2S_NUM_0, data, samples * sizeof(int16_t), &bytesWritten, portMAX_DELAY);
}

void I2SSender::clearBuffer() {
    int16_t zero = 0;
    for(int i = 0; i < 1024; i++) {
        size_t bytesWritten;
        i2s_write(I2S_NUM_0, &zero, sizeof(zero), &bytesWritten, 0);
    }
}
