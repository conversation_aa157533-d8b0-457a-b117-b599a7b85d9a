<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<launchConfiguration type="com.ti.ccstudio.debug.launchType.device.debugging">
<stringAttribute key="com.ti.ccstudio.debug.debugModel.ATTR_DEBUGGER_PROPERTIES.CC1312R1F3.ccxml.Texas Instruments XDS110 USB Debug Probe/Cortex_M4_0" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot; ?&gt;&#10;&lt;PropertyValues&gt;&#10;&#10;  &lt;property id=&quot;AutoRunToLabelOnRestart&quot;&gt;&#10;    &lt;curValue&gt;0&lt;/curValue&gt;&#10;  &lt;/property&gt;&#10;&#10;  &lt;property id=&quot;ConnectOnStartup&quot;&gt;&#10;    &lt;curValue&gt;1&lt;/curValue&gt;&#10;  &lt;/property&gt;&#10;&#10;  &lt;property id=&quot;EnableInstalledBreakpoint&quot;&gt;&#10;    &lt;curValue&gt;1&lt;/curValue&gt;&#10;  &lt;/property&gt;&#10;&#10;  &lt;property id=&quot;IgnoreSoftLaunchFailures&quot;&gt;&#10;    &lt;curValue&gt;0&lt;/curValue&gt;&#10;  &lt;/property&gt;&#10;&#10;&lt;/PropertyValues&gt;&#10;"/>
<setAttribute key="com.ti.ccstudio.debug.debugModel.ATTR_HIDE_CONFIG_ELEMENT_TYPES">
<setEntry value="BOARD"/>
<setEntry value="BYPASSED_CPU"/>
<setEntry value="BYPASSED_ROUTER"/>
<setEntry value="CONNECTION"/>
<setEntry value="DEVICE"/>
<setEntry value="NON_DEBUG_CPU"/>
<setEntry value="NO_DRIVER"/>
<setEntry value="ROUTER"/>
<setEntry value="SUBPATH"/>
<setEntry value="SYSTEM"/>
</setAttribute>
<stringAttribute key="com.ti.ccstudio.debug.debugModel.ATTR_PROGRAM.CC1312R1F3.ccxml.Texas Instruments XDS110 USB Debug Probe/Cortex_M4_0" value="${build_artifact:SoundPositioningNew}"/>
<stringAttribute key="com.ti.ccstudio.debug.debugModel.ATTR_PROJECT.CC1312R1F3.ccxml.Texas Instruments XDS110 USB Debug Probe/Cortex_M4_0" value="SoundPositioningNew"/>
<stringAttribute key="com.ti.ccstudio.debug.debugModel.ATTR_TARGET_CONFIG" value="${target_config_active_default:SoundPositioningNew}"/>
<stringAttribute key="com.ti.ccstudio.debug.debugModel.MRU_PROGRAM.CC1312R1F3.ccxml.Texas Instruments XDS110 USB Debug Probe/Cortex_M4_0" value="D:\_ArchiefTemse\SoundPositioning\codecomposer\SoundPositioningNew\Debug\SoundPositioningNew.out"/>
<listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_PATHS">
<listEntry value="/SoundPositioningNew"/>
</listAttribute>
<listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_TYPES">
<listEntry value="4"/>
</listAttribute>
<stringAttribute key="org.eclipse.debug.core.source_locator_id" value="com.ti.ccstudio.debug.sourceLocator"/>
<stringAttribute key="org.eclipse.debug.core.source_locator_memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#13;&#10;&lt;sourceLookupDirector&gt;&#13;&#10;&lt;sourceContainers duplicates=&quot;false&quot;&gt;&#13;&#10;&lt;container memento=&quot;&amp;lt;?xml version=&amp;quot;1.0&amp;quot; encoding=&amp;quot;UTF-8&amp;quot; standalone=&amp;quot;no&amp;quot;?&amp;gt;&amp;#13;&amp;#10;&amp;lt;default/&amp;gt;&amp;#13;&amp;#10;&quot; typeId=&quot;org.eclipse.debug.core.containerType.default&quot;/&gt;&#13;&#10;&lt;container memento=&quot;&amp;lt;?xml version=&amp;quot;1.0&amp;quot; encoding=&amp;quot;UTF-8&amp;quot; standalone=&amp;quot;no&amp;quot;?&amp;gt;&amp;#13;&amp;#10;&amp;lt;cpuSpecificContainer cpuName=&amp;quot;Texas Instruments XDS110 USB Debug Probe/Cortex_M4_0&amp;quot;&amp;gt;&amp;#13;&amp;#10;&amp;lt;childContainerEntry childMemento=&amp;quot;&amp;amp;lt;?xml version=&amp;amp;quot;1.0&amp;amp;quot; encoding=&amp;amp;quot;UTF-8&amp;amp;quot; standalone=&amp;amp;quot;no&amp;amp;quot;?&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;amp;lt;project name=&amp;amp;quot;SoundPositioningNew&amp;amp;quot; referencedProjects=&amp;amp;quot;true&amp;amp;quot;/&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;quot; childType=&amp;quot;org.eclipse.debug.core.containerType.project&amp;quot;/&amp;gt;&amp;#13;&amp;#10;&amp;lt;childContainerEntry childMemento=&amp;quot;&amp;amp;lt;?xml version=&amp;amp;quot;1.0&amp;amp;quot; encoding=&amp;amp;quot;UTF-8&amp;amp;quot; standalone=&amp;amp;quot;no&amp;amp;quot;?&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;amp;lt;default/&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;quot; childType=&amp;quot;org.eclipse.debug.core.containerType.default&amp;quot;/&amp;gt;&amp;#13;&amp;#10;&amp;lt;childContainerEntry childMemento=&amp;quot;&amp;amp;lt;?xml version=&amp;amp;quot;1.0&amp;amp;quot; encoding=&amp;amp;quot;UTF-8&amp;amp;quot; standalone=&amp;amp;quot;no&amp;amp;quot;?&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;amp;lt;productsSource/&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;quot; childType=&amp;quot;com.ti.ccstudio.debug.containerType.products.source&amp;quot;/&amp;gt;&amp;#13;&amp;#10;&amp;lt;childContainerEntry childMemento=&amp;quot;&amp;amp;lt;?xml version=&amp;amp;quot;1.0&amp;amp;quot; encoding=&amp;amp;quot;UTF-8&amp;amp;quot; standalone=&amp;amp;quot;no&amp;amp;quot;?&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;amp;lt;deviceLibrarySource/&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;quot; childType=&amp;quot;com.ti.ccstudio.debug.containerType.device.library.source&amp;quot;/&amp;gt;&amp;#13;&amp;#10;&amp;lt;childContainerEntry childMemento=&amp;quot;&amp;amp;lt;?xml version=&amp;amp;quot;1.0&amp;amp;quot; encoding=&amp;amp;quot;UTF-8&amp;amp;quot; standalone=&amp;amp;quot;no&amp;amp;quot;?&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;amp;lt;librarySource/&amp;amp;gt;&amp;amp;#13;&amp;amp;#10;&amp;quot; childType=&amp;quot;com.ti.ccstudio.debug.containerType.library.source&amp;quot;/&amp;gt;&amp;#13;&amp;#10;&amp;lt;/cpuSpecificContainer&amp;gt;&amp;#13;&amp;#10;&quot; typeId=&quot;com.ti.ccstudio.debug.containerType.cpu.specific&quot;/&gt;&#13;&#10;&lt;/sourceContainers&gt;&#13;&#10;&lt;/sourceLookupDirector&gt;&#13;&#10;"/>
<stringAttribute key="org.eclipse.dsf.launch.MEMORY_BLOCKS" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#13;&#10;&lt;memoryBlockExpressionList context=&quot;reserved-for-future-use&quot;/&gt;&#13;&#10;"/>
</launchConfiguration>
