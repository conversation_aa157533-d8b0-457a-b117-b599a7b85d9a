#include <ControlMobile.h>
#include <controlX0.h>
#include <Devicetype.h>
#include "ADC.h"
#include "Serial.h"
#include "time.h"
#include <NoRTOS.h>
#include <SoundTransmit.h>
#include <RFCommunication.h>
#include "ti_drivers_config.h"
#include "gpio.h"
#include "SoundReceive.h"
#include "BatteryVoltage.h"


int main(void)
{
 Board_init();
 Gpio_Init();
 ADC_Init();
 NoRTOS_start();
 Time_Init();
 SoundTransmit_Init();
 Serial_Init();
 RFCommunication_init();
 Time_StartSoftTimer(eTimerGeneral);  while (!Time_IsSoftTimerExpired(eTimerGeneral,1000));
 DeviceType_ReadFromBoard();
 SoundTransmit_GenerateSound(eBeep);
 if (DeviceType==eDevMobile) ControlMobile_Run();
 if (DeviceType==eDev0) ControlX0_Run0();
 if (DeviceType==eDevX)  ControlX0_RunX();
}

