<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: VolumeControl Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="class_volume_control-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VolumeControl Class Reference<span class="mlabels"><span class="mlabel">abstract</span></span></div>  </div>
</div><!--header-->
<div class="contents">

<p>Abstract class for handling of the volume of the audio data.  
 <a href="class_volume_control.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_volume_control_8h_source.html">VolumeControl.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VolumeControl:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_volume_control.png" usemap="#VolumeControl_map" alt=""/>
  <map id="VolumeControl_map" name="VolumeControl_map">
<area href="class_default_volume_control.html" title="Default implementation for handling of the volume of the audio data." alt="DefaultVolumeControl" shape="rect" coords="0,56,197,80"/>
<area href="class_linear_volume_control.html" title="The simplest possible implementation of a VolumeControl." alt="LinearVolumeControl" shape="rect" coords="207,56,404,80"/>
<area href="class_no_volume_control.html" title="Keeps the audio data as is -&gt; no volume control!" alt="NoVolumeControl" shape="rect" coords="414,56,611,80"/>
<area href="class_simple_exponential_volume_control.html" title="Exponentional volume control." alt="SimpleExponentialVolumeControl" shape="rect" coords="621,56,818,80"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a9e15076af1e887882b31d983825f260c"><td class="memItemLeft" align="right" valign="top"><a id="a9e15076af1e887882b31d983825f260c"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><b>update_audio_data</b> (Frame *data, uint16_t frameCount, uint8_t volume, bool mono_downmix, bool is_volume_used)</td></tr>
<tr class="separator:a9e15076af1e887882b31d983825f260c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3294ddb250b3086697b774c31616f6e7"><td class="memItemLeft" align="right" valign="top"><a id="a3294ddb250b3086697b774c31616f6e7"></a>
virtual int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>get_volume_factor</b> (uint8_t volume)=0</td></tr>
<tr class="separator:a3294ddb250b3086697b774c31616f6e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32b0571c7f4ecea4b8d153be83e6860b"><td class="memItemLeft" align="right" valign="top"><a id="a32b0571c7f4ecea4b8d153be83e6860b"></a>
virtual int32_t&#160;</td><td class="memItemRight" valign="bottom"><b>get_volume_factor_max</b> ()</td></tr>
<tr class="separator:a32b0571c7f4ecea4b8d153be83e6860b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Abstract class for handling of the volume of the audio data. </p>
<dl class="section author"><dt>Author</dt><dd>Phil Schatzmann </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Apache License Version 2 </dd></dl>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li>src/<a class="el" href="_volume_control_8h_source.html">VolumeControl.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
