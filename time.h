
#ifndef TIME_H_
#define TIME_H_
#include <stdbool.h>

typedef enum {eTimerRFReceived=0,eTimerGeneral=1,eTimerMeasureBase=2} SoftTimers;

void Time_Init(void);

void Time_DisableMatch();
void Time_EnableMatch();
void Time_SetNextMatch(uint16_t Ticks16MHz);
void Time_SetCallbackFunctionOnMatch(void (*Callback)(void));

void Time_StartSoftTimer(SoftTimers timer);
bool Time_IsSoftTimerExpired(SoftTimers timer,uint32_t delayms);

#endif /* TIME_H_ */
