<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.9.1"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Arduino A2DP: BluetoothA2DPOutputLegacy Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Arduino A2DP
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.1 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="class_bluetooth_a2_d_p_output_legacy-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">BluetoothA2DPOutputLegacy Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Legacy I2S Output Class.  
 <a href="class_bluetooth_a2_d_p_output_legacy.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="_bluetooth_a2_d_p_output_8h_source.html">BluetoothA2DPOutput.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for BluetoothA2DPOutputLegacy:</div>
<div class="dyncontent">
 <div class="center">
  <img src="class_bluetooth_a2_d_p_output_legacy.png" usemap="#BluetoothA2DPOutputLegacy_map" alt=""/>
  <map id="BluetoothA2DPOutputLegacy_map" name="BluetoothA2DPOutputLegacy_map">
<area href="class_bluetooth_a2_d_p_output.html" title="Abstract Output Class." alt="BluetoothA2DPOutput" shape="rect" coords="0,0,176,24"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ab38c26d01e92b1c74d7b36dbda7b5183"><td class="memItemLeft" align="right" valign="top"><a id="ab38c26d01e92b1c74d7b36dbda7b5183"></a>
bool&#160;</td><td class="memItemRight" valign="bottom"><b>begin</b> () override</td></tr>
<tr class="separator:ab38c26d01e92b1c74d7b36dbda7b5183"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a316bf08773d057f2a6b3a8452af95e2d"><td class="memItemLeft" align="right" valign="top"><a id="a316bf08773d057f2a6b3a8452af95e2d"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>end</b> () override</td></tr>
<tr class="separator:a316bf08773d057f2a6b3a8452af95e2d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67fc2cf760ae2a48ac6bfa6ed235f8b8"><td class="memItemLeft" align="right" valign="top"><a id="a67fc2cf760ae2a48ac6bfa6ed235f8b8"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_output.html#a67fc2cf760ae2a48ac6bfa6ed235f8b8">set_output</a> (audio_tools::AudioOutput &amp;output)</td></tr>
<tr class="memdesc:a67fc2cf760ae2a48ac6bfa6ed235f8b8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not implemented. <br /></td></tr>
<tr class="separator:a67fc2cf760ae2a48ac6bfa6ed235f8b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9cdbeeafa4778b4efcf7108ec8b9afae"><td class="memItemLeft" align="right" valign="top"><a id="a9cdbeeafa4778b4efcf7108ec8b9afae"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_output.html#a9cdbeeafa4778b4efcf7108ec8b9afae">set_output</a> (audio_tools::AudioStream &amp;output)</td></tr>
<tr class="memdesc:a9cdbeeafa4778b4efcf7108ec8b9afae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not implemented. <br /></td></tr>
<tr class="separator:a9cdbeeafa4778b4efcf7108ec8b9afae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add4a1457f42f2e6ef8b905bcf4be10dc"><td class="memItemLeft" align="right" valign="top"><a id="add4a1457f42f2e6ef8b905bcf4be10dc"></a>
virtual void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="class_bluetooth_a2_d_p_output.html#add4a1457f42f2e6ef8b905bcf4be10dc">set_output</a> (Print &amp;output)</td></tr>
<tr class="memdesc:add4a1457f42f2e6ef8b905bcf4be10dc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Not implemented. <br /></td></tr>
<tr class="separator:add4a1457f42f2e6ef8b905bcf4be10dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af99cf5a50727a219d6cb0a88115f0870"><td class="memItemLeft" align="right" valign="top"><a id="af99cf5a50727a219d6cb0a88115f0870"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_output_active</b> (bool active) override</td></tr>
<tr class="separator:af99cf5a50727a219d6cb0a88115f0870"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afa454038ff736688c09dde6ecaa01316"><td class="memItemLeft" align="right" valign="top"><a id="afa454038ff736688c09dde6ecaa01316"></a>
void&#160;</td><td class="memItemRight" valign="bottom"><b>set_sample_rate</b> (int rate) override</td></tr>
<tr class="separator:afa454038ff736688c09dde6ecaa01316"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1f1b2c9610d272ce6fb539350aefc651"><td class="memItemLeft" align="right" valign="top"><a id="a1f1b2c9610d272ce6fb539350aefc651"></a>
size_t&#160;</td><td class="memItemRight" valign="bottom"><b>write</b> (const uint8_t *data, size_t len) override</td></tr>
<tr class="separator:a1f1b2c9610d272ce6fb539350aefc651"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Legacy I2S Output Class. </p>
<dl class="section author"><dt>Author</dt><dd>Phil Schatzmann </dd></dl>
<dl class="section copyright"><dt>Copyright</dt><dd>Apache License Version 2 </dd></dl>
</div><hr/>The documentation for this class was generated from the following files:<ul>
<li>src/<a class="el" href="_bluetooth_a2_d_p_output_8h_source.html">BluetoothA2DPOutput.h</a></li>
<li>src/BluetoothA2DPOutput.cpp</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.1
</small></address>
</body>
</html>
