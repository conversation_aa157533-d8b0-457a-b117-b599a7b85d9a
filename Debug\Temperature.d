# FIXED

Temperature.obj: ../Temperature.c
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdint.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_ti_config.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/linkage.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_stdint40.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/stdint.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/cdefs.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_types.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_types.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_stdint.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_stdint.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stddef.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdio.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdarg.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdlib.h
Temperature.obj: ../adc.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdlib.h
Temperature.obj: C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/Board.h
Temperature.obj: D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Debug/syscfg/ti_drivers_config.h
Temperature.obj: C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/DeviceFamily.h
Temperature.obj: C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/PIN.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdbool.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/math.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_defs.h
Temperature.obj: C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_limits.h

../Temperature.c: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_ti_config.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/linkage.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_stdint40.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/cdefs.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_types.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_types.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/sys/_stdint.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stddef.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdio.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdarg.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdlib.h: 
../adc.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdlib.h: 
C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/Board.h: 
D:/_ArchiefTemse/SoundPositioning/codecomposer/SoundPositioningNew/Debug/syscfg/ti_drivers_config.h: 
C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/devices/DeviceFamily.h: 
C:/ti/simplelink_cc13x2_26x2_sdk_4_40_04_04/source/ti/drivers/PIN.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/stdbool.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/math.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/_defs.h: 
C:/ti/ccs1011/ccs/tools/compiler/ti-cgt-arm_20.2.4.LTS/include/machine/_limits.h: 
